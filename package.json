{"name": "emessa7", "version": "1.0.0", "description": "W plikach konfiguracyjnych nie przechowujemy ważnych informacji lub informacji, które identyfikują programistę.", "private": true, "directories": {"test": "tests"}, "scripts": {"clean": "dotnet clean", "tailwind-once": "npx tailwindcss -i ./src/EMessa.Web/wwwroot/css/app.css -o ./src/EMessa.Web/wwwroot/css/app.min.css", "tailwind-once-full": "npx tailwindcss -i ./src/EMessa.Web/wwwroot/css/app.css -o ./src/EMessa.Web/wwwroot/css/app.min.css --full", "tailwind-watch": "npx tailwindcss -i ./src/EMessa.Web/wwwroot/css/app.css -o ./src/EMessa.Web/wwwroot/css/app.min.css --watch --poll", "tailwind-once-minify": "npx tailwindcss --full -i ./src/EMessa.Web/wwwroot/css/app.css -o ./src/EMessa.Web/wwwroot/css/app.min.css --minify", "tailwind-once-2": "npx tailwindcss -i ./src/EMessa.Web/wwwroot/css/app.css -i ./src/EMessa.Web/wwwroot/css/syncfusion-custom.css -o ./src/EMessa.Web/wwwroot/css/app.min.css", "tailwind-once-all": "npx tailwindcss -i \"./src/EMessa.Web/wwwroot/css/*.css\" -o ./src/EMessa.Web/wwwroot/css/app.min.css", "has-pending-migration": "bash ./has-pending-migration.sh", "add-migration": "bash ./add-migration.sh", "rm-migration": "bash ./rm-migration.sh", "update-db": "bash ./update-db.sh", "does:need:migrations": "dotnet ef migrations has-pending-model-changes -p src/EMessa.DAL -s src/EMessa.Web", "diff": "git diff origin/develop > diff.txt", "test": "bun test", "test:typescript": "bun test ./tests/TypeScriptTests"}, "dependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.3", "postcss-cli": "^11.0.1", "tailwindcss": "^3.4.17"}, "devDependencies": {"@types/node": "^20.14.9", "bun-types": "latest"}}