using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EMessa.Base.Extensions;
public static class StringExtensions
{
    /// <summary>
    /// Converts a string to its Base64 representation.
    /// </summary>
    /// <param name="value">The string to convert.</param>
    /// <returns>The Base64 encoded string, or null if the input is null.</returns>
    /// <exception cref="ArgumentNullException">Thrown when the input value is null.</exception>
    public static string ToBase64(this string value)
    {
        if (value == null) return null;
        return System.Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(value));
    }
    public static string FromBase64(this string base64Value)
    {
        if (base64Value == null) return null;
        var bytes = System.Convert.FromBase64String(base64Value);
        return System.Text.Encoding.UTF8.GetString(bytes);
    }
}
