using System.ComponentModel.DataAnnotations;

namespace Messa.Core.BL.eMessa.Sales.ViewModels;

public enum SaleStatus
{
    /// <summary>
    /// Nowa promocja
    /// </summary>
    [Display(Name = "Nowa", Description = "Nowa promocja", ShortName = "N")]
    New = 0,

    /// <summary>
    /// Aktywna promocja
    /// </summary>
    [Display(Name = "Aktywna", Description = "Promocja aktywna ", ShortName = "A")]
    Active = 1,

    /// <summary>
    /// Zamknięta promocja
    /// </summary>
    [Display(Name = "Zamknięta", Description = "Promocja zamknięta", ShortName = "Z")]
    Closed = 2,

    /// <summary>
    /// Anulowana promocja
    /// </summary>
    [Display(Name = "Anulowana", Description = "Promocja anulowana", ShortName = "C")]
    Canceled = 3,
}
