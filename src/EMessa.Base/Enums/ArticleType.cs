using System.ComponentModel.DataAnnotations;

namespace EMessa.Base.Enums;

/// <summary>
/// Rodzaj produktu
/// </summary>
public enum ArticleType
{
    /// <summary>
    /// Produkt własny
    /// </summary>
    [Display(Name ="Produkt własny")]
    Complex = 0,

    /// <summary>
    /// Towar handlowy
    /// </summary>
    [Display(Name = "Towar handlowy")]
    Trade = 1,

    /// <summary>
    /// Usługa
    /// </summary>
    [Display(Name = "Usługa")]
    Service = 2
}
