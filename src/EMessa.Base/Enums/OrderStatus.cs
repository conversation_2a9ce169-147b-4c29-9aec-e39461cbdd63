using System.ComponentModel.DataAnnotations;

namespace EMessa.Base.Enums;

/// <summary>
/// Status Zamówienia
/// </summary>
public enum OrderStatus
{
    /// <summary>
    /// Nowe
    /// </summary>
    [Display(Name = "Nowe")]
    New = 0,

    /// <summary>
    /// Zapisane
    /// </summary>
    [Display(Name = "Zapisane")]
    Saved = 1,

    /// <summary>
    /// Przesłane do realizacji przez klienta
    /// </summary>
    [Display(Name = "Przesłane", Description = "Zamówienie jest przesłane przez klienta do realizacji")]
    Sent = 2,

    /// <summary>
    /// Zatwierdzone do realizacji przez wykonawcę (przesłanie do produkcji)
    /// </summary>
    [Display(Name = "Zatwierdzone", Description = "Zamówienie jest zatwierdzone do realizacji przez producenta")]
    Accepted = 3,

    /// <summary>
    /// Status po imporcie zatwierdzonego zamówienia do systemu Messa, ale przed dodaniem do kolejki produkcyjnej. Aktualizowany z systemu Messa.
    /// </summary>
    [Display(Name = "W przygotowaniu")]
    Preparation = 4,

    /// <summary>
    /// Zamówienie zaplanowane do produkcji, określona data produkcji (jeśli możliwa do określania)
    /// </summary>
    [Display(Name = "Produkcja")]
    Production = 5,

    /// <summary>
    /// W magazynie, wyprodukowano
    /// </summary>
    [Display(Name = "Magazyn")]
    Warehouse = 6,

    /// <summary>
    /// W transporcie, dodane do transportu, ten status jest nadawany jeśli zamówienie jest wykonane i dodane do transportu
    /// </summary>
    [Display(Name = "Transport")]
    Transport = 7,

    /// <summary>
    /// Dostarczone - zakończenie zamówienia
    /// </summary>
    [Display(Name = "Dostarczone")]
    Delivered = 8,

    /// <summary>
    /// Odrzcucone/wycofane
    /// </summary>
    [Display(Name = "Odrzucone")]
    Rejected = 9,

    /// <summary>
    /// Usunięte
    /// </summary>
    [Display(Name = "Usunięte")]
    Deleted = 10,

    /// <summary>
    /// Nieokreślone
    /// </summary>
    [Display(Name = "Nieokreślony")]
    Unknown = 100
}
