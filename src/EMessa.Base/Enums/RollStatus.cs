using System.ComponentModel.DataAnnotations;

namespace EMessa.Base.Enums;

public enum RollStatus
{
    /// <summary>
    /// Nowy - oznaczany kolorem zielonym w UI.
    /// </summary>
    [Display(Name = "Nowy")]
    New = 0,

    /// <summary>
    /// Zarejestrowany - oznaczany kolorem brązowym w UI.
    /// </summary>
    [Display(Name = "Zarejestrowany")]
    Registered = 1,

    /// <summary>
    /// Aktywny - oznaczany kolorem niebieskim w UI.
    /// </summary>
    [Display(Name = "Aktywny")]
    Active = 2,

    /// <summary>
    /// Zamknięty - oznaczany kolorem czarnym w UI.
    /// </summary>
    [Display(Name = "Zamknięty")]
    Closed = 3,

    /// <summary>
    /// Wysłany - oznaczany kolorem szarym w UI.
    /// </summary>
    [Display(Name = "Wysłany")]
    Sent = 4,

    /// <summary>
    /// Nieokreślony - oznaczany kolorem czerwonym w UI.
    /// </summary>
    [Display(Name = "Nieokreślony")]
    Unknown = 100
}
