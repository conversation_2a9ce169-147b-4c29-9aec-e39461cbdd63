using System.ComponentModel.DataAnnotations;

namespace EMessa.Base.Enums
{

    /// <summary>
    /// Rodzaj zamówienia
    /// </summary>
    public enum OrderType
    {
        /// <summary>
        /// Zamówienie
        /// </summary>
        [Display(Name = "Zamówienie")]
        Order = 0,

        /// <summary>
        /// Koszyk
        /// </summary>
        [Display(Name = "Koszyk")]
        ShoppingCart = 1,

        /// <summary>
        /// Oferta
        /// </summary>
        [Display(Name = "Oferta")]
        Offer = 2,

        [Display(Name = "Zapytanie ofertowe")]
        Inquiry = 3
    }
}
