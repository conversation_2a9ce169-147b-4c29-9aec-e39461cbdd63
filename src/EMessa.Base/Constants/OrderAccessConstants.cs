using EMessa.Base.Enums;

namespace EMessa.Base.Constants;

public static class OrderAccessConstants
{
    /// <summary>
    /// Ordered list of statuses used to determine the minimal order status (includes only selected statuses).
    /// </summary>
    public static readonly List<OrderStatus> OrderedStatuses =
    [
        OrderStatus.Unknown,
        OrderStatus.New,
        OrderStatus.Sent,
        OrderStatus.Accepted,
        OrderStatus.Preparation,
        OrderStatus.Production,
        OrderStatus.Warehouse,
        OrderStatus.Transport,
        OrderStatus.Delivered
    ];

    public static readonly OrderStatus[] TradeCustomersOrders =
    [
        OrderStatus.Sent,
        OrderStatus.Accepted,
        OrderStatus.Rejected,
        OrderStatus.Preparation,
        OrderStatus.Production,
        OrderStatus.Warehouse,
        OrderStatus.Transport,
        OrderStatus.Delivered
    ];

    public static readonly OrderStatus[] TradeOwnOrders =
    [
        OrderStatus.New,
        OrderStatus.Saved,
        OrderStatus.Sent,
        OrderStatus.Accepted,
        OrderStatus.Rejected,
        OrderStatus.Preparation,
        OrderStatus.Production,
        OrderStatus.Warehouse,
        OrderStatus.Transport,
        OrderStatus.Delivered
    ];

    public static readonly OrderStatus[] AllowedStatusesForEdit =
    [
        OrderStatus.New,
        OrderStatus.Rejected
    ];

    public static readonly OrderStatus[] AllowedStatusesForReject =
    [
        OrderStatus.New,
        OrderStatus.Saved,
        OrderStatus.Sent,
        OrderStatus.Accepted,
        OrderStatus.Preparation
    ];

    public static readonly OrderStatus[] AllowedStatusesForSend =
    [
        OrderStatus.New,
        OrderStatus.Saved,
        OrderStatus.Rejected
    ];

    public static readonly OrderStatus[] AllowedStatusesForAccept =
    [
        OrderStatus.Sent
    ];

    public static readonly OrderStatus[] AllowedStatusesForDelete =
    [
        OrderStatus.New,
        OrderStatus.Rejected
    ];

    public static readonly OrderStatus[] AllowedTargetStatusesForAPI =
    [
        OrderStatus.Preparation,
        OrderStatus.Production,
        OrderStatus.Warehouse,
        OrderStatus.Transport,
        OrderStatus.Delivered,
        OrderStatus.Rejected
    ];

    public static readonly OrderStatus[] AllowedSourceStatusesForAPI =
    [
        OrderStatus.Accepted,
        OrderStatus.Preparation,
        OrderStatus.Production,
        OrderStatus.Warehouse,
        OrderStatus.Transport,
        OrderStatus.Delivered,
        OrderStatus.Rejected
    ];

    public static readonly Dictionary<string, OrderStatus[]> RoleAvailableStatuses = new()
    {
        [Role.Administrator] = Enum.GetValues<OrderStatus>(),
        [Role.Client] = [
            OrderStatus.New,
            OrderStatus.Saved,
            OrderStatus.Sent,
            OrderStatus.Accepted,
            OrderStatus.Rejected,
            OrderStatus.Preparation,
            OrderStatus.Production,
            OrderStatus.Warehouse,
            OrderStatus.Transport,
            OrderStatus.Delivered
        ],
        [Role.ClientManager] = [
            OrderStatus.New,
            OrderStatus.Saved,
            OrderStatus.Sent,
            OrderStatus.Accepted,
            OrderStatus.Rejected,
            OrderStatus.Preparation,
            OrderStatus.Production,
            OrderStatus.Warehouse,
            OrderStatus.Transport,
            OrderStatus.Delivered
        ],
        [Role.Trade] = [
            OrderStatus.New,
            OrderStatus.Saved,
            OrderStatus.Sent,
            OrderStatus.Accepted,
            OrderStatus.Rejected,
            OrderStatus.Preparation,
            OrderStatus.Production,
            OrderStatus.Warehouse,
            OrderStatus.Transport,
            OrderStatus.Delivered
        ],
        [Role.TradeManager] = [
            OrderStatus.New,
            OrderStatus.Saved,
            OrderStatus.Sent,
            OrderStatus.Accepted,
            OrderStatus.Rejected,
            OrderStatus.Preparation,
            OrderStatus.Production,
            OrderStatus.Warehouse,
            OrderStatus.Transport,
            OrderStatus.Delivered
        ],
        [Role.Production] = [
            OrderStatus.Accepted,
            OrderStatus.Rejected,
            OrderStatus.Preparation,
            OrderStatus.Production,
            OrderStatus.Warehouse,
            OrderStatus.Transport,
            OrderStatus.Delivered
        ],
        [Role.SaleManager] = [
            OrderStatus.Sent,
            OrderStatus.Accepted,
            OrderStatus.Rejected,
            OrderStatus.Preparation,
            OrderStatus.Production,
            OrderStatus.Warehouse,
            OrderStatus.Transport,
            OrderStatus.Delivered
        ],
    };

    public static OrderStatus[] GetStatusesForRole(params string[] role)
    {
        var statuses = new List<OrderStatus>();

        foreach (var r in role)
        {
            if (RoleAvailableStatuses.TryGetValue(r, out var roleStatuses))
            {
                statuses.AddRange(roleStatuses);
            }
        }

        return [.. statuses.Distinct()];
    }

    public static OrderStatus GetLowestStatus(IEnumerable<OrderStatus> statuses)
    {
        foreach (var orderedStatus in OrderedStatuses)
        {
            if (statuses.Contains(orderedStatus))
                return orderedStatus;
        }

        throw new ArgumentException("No known status found in the collection.");
    }
}
