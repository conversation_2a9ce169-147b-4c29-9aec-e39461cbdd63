using EMessa.Base.Models;

namespace EMessa.Base.Constants;

/// <summary>
/// Cacheed Dictionary Country - Id, Code
/// </summary>
public static class CountriesConstans
{
    private static Dictionary<int, string> _countryCodes = new();

    public static void SetCodes(Dictionary<int, string> codes)
    {
        _countryCodes = new Dictionary<int, string>(codes);
    }

    /// <summary>
    /// Returns Country Code for cuntry Id
    /// </summary>
    /// <param name="id">Country Id</param>
    /// <returns></returns>
    public static string GetCode(int id)
    {
        return _countryCodes.TryGetValue(id, out var code) ? code.ToLower() : "";
    }

    public static List<CountryCurrenciesDataModel> CountryCurrenciesData =>
    [
        new CountryCurrenciesDataModel("Albania Lek", "Lek", "ALL", "Albania"),
        new CountryCurrenciesDataModel("Afghanistan Afghani", "؋", "AFN", "Afghanistan"),
        new CountryCurrenciesDataModel("Argentina Peso", "$", "ARS", "Argentina"),
        new CountryCurrenciesDataModel("Aruba Guilder", "ƒ", "AWG", "Aruba"),
        new CountryCurrenciesDataModel("Australia Dollar", "$", "AUD", "Australia"),
        new CountryCurrenciesDataModel("Azerbaijan Manat", "₼", "AZN", "Azerbaijan"),
        new CountryCurrenciesDataModel("Bahamas Dollar", "$", "BSD", "Bahamas"),
        new CountryCurrenciesDataModel("Barbados Dollar", "$", "BBD", "Barbados"),
        new CountryCurrenciesDataModel("Belarus Ruble", "Br", "BYN", "Belarus"),
        new CountryCurrenciesDataModel("Belize Dollar", "BZ$", "BZD", "Belize"),
        new CountryCurrenciesDataModel("Bermuda Dollar", "$", "BMD", "Bermuda"),
        new CountryCurrenciesDataModel("Bolivia Bolíviano", "$b", "BOB", "Bolivia"),
        new CountryCurrenciesDataModel("Bosnia and Herzegovina Convertible Mark", "KM", "BAM", "Bosnia and Herzegovina"),
        new CountryCurrenciesDataModel("Botswana Pula", "P", "BWP", "Botswana"),
        new CountryCurrenciesDataModel("Bulgaria Lev", "лв", "BGN", "Bulgaria"),
        new CountryCurrenciesDataModel("Brazil Real", "R$", "BRL", "Brazil"),
        new CountryCurrenciesDataModel("Brunei Darussalam Dollar", "$", "BND", "Brunei"),
        new CountryCurrenciesDataModel("Cambodia Riel", "៛", "KHR", "Cambodia"),
        new CountryCurrenciesDataModel("Canada Dollar", "$", "CAD", "Canada"),
        new CountryCurrenciesDataModel("Cayman Islands Dollar", "$", "KYD", "Cayman Islands"),
        new CountryCurrenciesDataModel("Chile Peso", "$", "CLP", "Chile"),
        new CountryCurrenciesDataModel("China Yuan Renminbi", "¥", "CNY", "China"),
        new CountryCurrenciesDataModel("Colombia Peso", "$", "COP", "Colombia"),
        new CountryCurrenciesDataModel("Costa Rica Colon", "₡", "CRC", "Costa Rica"),
        new CountryCurrenciesDataModel("Croatia Kuna", "kn", "HRK", "Croatia"),
        new CountryCurrenciesDataModel("Cuba Peso", "₱", "CUP", "Cuba"),
        new CountryCurrenciesDataModel("Czech Republic Koruna", "Kč", "CZK", "Czech Republic"),
        new CountryCurrenciesDataModel("Denmark Krone", "kr", "DKK", "Denmark"),
        new CountryCurrenciesDataModel("Dominican Republic Peso", "RD$", "DOP", "Dominican Republic"),
        new CountryCurrenciesDataModel("East Caribbean Dollar", "$", "XCD", "East Caribbean"),
        new CountryCurrenciesDataModel("Egypt Pound", "£", "EGP", "Egypt"),
        new CountryCurrenciesDataModel("El Salvador Colon", "$", "SVC", "El Salvador"),
        new CountryCurrenciesDataModel("Euro", "€", "EUR", "Eurozone"),
        new CountryCurrenciesDataModel("Falkland Islands (Malvinas) Pound", "£", "FKP", "Falkland Islands"),
        new CountryCurrenciesDataModel("Fiji Dollar", "$", "FJD", "Fiji"),
        new CountryCurrenciesDataModel("Ghana Cedi", "¢", "GHS", "Ghana"),
        new CountryCurrenciesDataModel("Gibraltar Pound", "£", "GIP", "Gibraltar"),
        new CountryCurrenciesDataModel("Guatemala Quetzal", "Q", "GTQ", "Guatemala"),
        new CountryCurrenciesDataModel("Guernsey Pound", "£", "GGP", "Guernsey"),
        new CountryCurrenciesDataModel("Guyana Dollar", "$", "GYD", "Guyana"),
        new CountryCurrenciesDataModel("Honduras Lempira", "L", "HNL", "Honduras"),
        new CountryCurrenciesDataModel("Hong Kong Dollar", "$", "HKD", "Hong Kong"),
        new CountryCurrenciesDataModel("Hungary Forint", "Ft", "HUF", "Hungary"),
        new CountryCurrenciesDataModel("Iceland Krona", "kr", "ISK", "Iceland"),
        new CountryCurrenciesDataModel("India Rupee", "", "INR", "India"),
        new CountryCurrenciesDataModel("Indonesia Rupiah", "Rp", "IDR", "Indonesia"),
        new CountryCurrenciesDataModel("Iran Rial", "﷼", "IRR", "Iran"),
        new CountryCurrenciesDataModel("Isle of Man Pound", "£", "IMP", "Isle of Man"),
        new CountryCurrenciesDataModel("Israel Shekel", "₪", "ILS", "Israel"),
        new CountryCurrenciesDataModel("Jamaica Dollar", "J$", "JMD", "Jamaica"),
        new CountryCurrenciesDataModel("Japan Yen", "¥", "JPY", "Japan"),
        new CountryCurrenciesDataModel("Jersey Pound", "£", "JEP", "Jersey"),
        new CountryCurrenciesDataModel("Kazakhstan Tenge", "лв", "KZT", "Kazakhstan"),
        new CountryCurrenciesDataModel("Korea (North) Won", "₩", "KPW", "North Korea"),
        new CountryCurrenciesDataModel("Korea (South) Won", "₩", "KRW", "South Korea"),
        new CountryCurrenciesDataModel("Kyrgyzstan Som", "лв", "KGS", "Kyrgyzstan"),
        new CountryCurrenciesDataModel("Laos Kip", "₭", "LAK", "Laos"),
        new CountryCurrenciesDataModel("Lebanon Pound", "£", "LBP", "Lebanon"),
        new CountryCurrenciesDataModel("Liberia Dollar", "$", "LRD", "Liberia"),
        new CountryCurrenciesDataModel("Macedonia Denar", "ден", "MKD", "North Macedonia"),
        new CountryCurrenciesDataModel("Malaysia Ringgit", "RM", "MYR", "Malaysia"),
        new CountryCurrenciesDataModel("Mauritius Rupee", "₨", "MUR", "Mauritius"),
        new CountryCurrenciesDataModel("Mexico Peso", "$", "MXN", "Mexico"),
        new CountryCurrenciesDataModel("Mongolia Tughrik", "₮", "MNT", "Mongolia"),
        new CountryCurrenciesDataModel("Mozambique Metical", "MT", "MZN", "Mozambique"),
        new CountryCurrenciesDataModel("Namibia Dollar", "$", "NAD", "Namibia"),
        new CountryCurrenciesDataModel("Nepal Rupee", "₨", "NPR", "Nepal"),
        new CountryCurrenciesDataModel("Netherlands Antilles Guilder", "ƒ", "ANG", "Netherlands Antilles"),
        new CountryCurrenciesDataModel("New Zealand Dollar", "$", "NZD", "New Zealand"),
        new CountryCurrenciesDataModel("Nicaragua Cordoba", "C$", "NIO", "Nicaragua"),
        new CountryCurrenciesDataModel("Nigeria Naira", "₦", "NGN", "Nigeria"),
        new CountryCurrenciesDataModel("Norway Krone", "kr", "NOK", "Norway"),
        new CountryCurrenciesDataModel("Oman Rial", "﷼", "OMR", "Oman"),
        new CountryCurrenciesDataModel("Pakistan Rupee", "₨", "PKR", "Pakistan"),
        new CountryCurrenciesDataModel("Panama Balboa", "B/.", "PAB", "Panama"),
        new CountryCurrenciesDataModel("Paraguay Guarani", "Gs", "PYG", "Paraguay"),
        new CountryCurrenciesDataModel("Peru Sol", "S/.", "PEN", "Peru"),
        new CountryCurrenciesDataModel("Philippines Peso", "₱", "PHP", "Philippines"),
        new CountryCurrenciesDataModel("Poland Zloty", "zł", "PLN", "Poland"),
        new CountryCurrenciesDataModel("Qatar Riyal", "﷼", "QAR", "Qatar"),
        new CountryCurrenciesDataModel("Romania Leu", "lei", "RON", "Romania"),
        new CountryCurrenciesDataModel("Russia Ruble", "₽", "RUB", "Russia"),
        new CountryCurrenciesDataModel("Saint Helena Pound", "£", "SHP", "Saint Helena"),
        new CountryCurrenciesDataModel("Saudi Arabia Riyal", "﷼", "SAR", "Saudi Arabia"),
        new CountryCurrenciesDataModel("Serbia Dinar", "Дин.", "RSD", "Serbia"),
        new CountryCurrenciesDataModel("Seychelles Rupee", "₨", "SCR", "Seychelles"),
        new CountryCurrenciesDataModel("Singapore Dollar", "$", "SGD", "Singapore"),
        new CountryCurrenciesDataModel("Solomon Islands Dollar", "$", "SBD", "Solomon Islands"),
        new CountryCurrenciesDataModel("Somalia Shilling", "S", "SOS", "Somalia"),
        new CountryCurrenciesDataModel("South Africa Rand", "R", "ZAR", "South Africa"),
        new CountryCurrenciesDataModel("Sri Lanka Rupee", "₨", "LKR", "Sri Lanka"),
        new CountryCurrenciesDataModel("Sweden Krona", "kr", "SEK", "Sweden"),
        new CountryCurrenciesDataModel("Switzerland Franc", "CHF", "CHF", "Switzerland"),
        new CountryCurrenciesDataModel("Suriname Dollar", "$", "SRD", "Suriname"),
        new CountryCurrenciesDataModel("Syria Pound", "£", "SYP", "Syria"),
        new CountryCurrenciesDataModel("Taiwan New Dollar", "NT$", "TWD", "Taiwan"),
        new CountryCurrenciesDataModel("Thailand Baht", "฿", "THB", "Thailand"),
        new CountryCurrenciesDataModel("Trinidad and Tobago Dollar", "TT$", "TTD", "Trinidad and Tobago"),
        new CountryCurrenciesDataModel("Turkey Lira", "", "TRY", "Turkey"),
        new CountryCurrenciesDataModel("Tuvalu Dollar", "$", "TVD", "Tuvalu"),
        new CountryCurrenciesDataModel("Ukraine Hryvnia", "₴", "UAH", "Ukraine"),
        new CountryCurrenciesDataModel("United Kingdom Pound", "£", "GBP", "United Kingdom"),
        new CountryCurrenciesDataModel("United States Dollar", "$", "USD", "United States"),
        new CountryCurrenciesDataModel("Uruguay Peso", "$U", "UYU", "Uruguay"),
        new CountryCurrenciesDataModel("Uzbekistan Som", "лв", "UZS", "Uzbekistan"),
        new CountryCurrenciesDataModel("Venezuela Bolívar", "Bs", "VEF", "Venezuela"),
        new CountryCurrenciesDataModel("Viet Nam Dong", "₫", "VND", "Vietnam"),
        new CountryCurrenciesDataModel("Yemen Rial", "﷼", "YER", "Yemen"),
        new CountryCurrenciesDataModel("Zimbabwe Dollar", "Z$", "ZWD", "Zimbabwe")
    ];

    public static List<string> AllIsoCurrencyCodes =>
        CountryCurrenciesData
            .Select(x => x.IsoCode)
            .Distinct()
            .ToList();
}
