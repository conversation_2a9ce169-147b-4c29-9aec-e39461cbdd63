namespace EMessa.Base.Constants;

public static class Role
{
    public const string Administrator = "Administrator";
    public const string Client = "Client";
    public const string ClientManager = "ClientManager";
    public const string TradeManager = "TradeManager";
    public const string Trade = "Trade";
    public const string Production = "Production";
    public const string SaleManager = "SaleManager";

    public static string MaxRole(List<string> roles)
    {
        if (roles.Contains(Administrator)) return Administrator;
        if (roles.Contains(Production)) return Production;
        if (roles.Contains(TradeManager)) return TradeManager;
        if (roles.Contains(Trade)) return Trade;
        if (roles.Contains(ClientManager)) return ClientManager;
        if (roles.Contains(Client)) return Client;
        return "";
    }

    public static bool HasAnyRole(List<string> roles, params string[] requiredRoles)
    {
        return roles.Any(requiredRoles.Contains);
    }
}