using EMessa.Base.Constants;
using EMessa.Core.Localizer;
using FluentValidation;

namespace EMessa.Core.Validators;

public static class CustomValidators
{
    public static IRuleBuilderOptions<T, string> ValidCurrencyCode<T>(
        this IRuleBuilder<T, string> ruleBuilder, string fieldName)
    {
        return ruleBuilder
            .NotEmpty().WithMessage("Pole {0} jest wymagane.".Tr(fieldName))
            .Matches(@"^[A-Za-z]{3}$").WithMessage("Pole {0} musi składać się z 3 liter.".Tr(fieldName))
            .Must(code => string.IsNullOrEmpty(code) || CountriesConstans.AllIsoCurrencyCodes.Contains(code.ToUpper()))
            .WithMessage("Kod waluty musi być poprawnym kodem ISO 4217.".Tr());
    }
}
