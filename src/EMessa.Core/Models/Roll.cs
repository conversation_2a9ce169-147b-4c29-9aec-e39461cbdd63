using EMessa.Base.Enums;
using System.Text.Json.Serialization;

namespace EMessa.Core.Models;

public class Roll
{
    [JsonPropertyName("Id")]
    public long RollId { get; set; }

    [JsonPropertyName("OrigNr")] public string OrigNo { get; set; } = null!; // Numer producenta krążka
    [JsonPropertyName("Nr")] public int RollNo { get; set; }
    public bool Closed { get; set; }
    public RollStatus Status { get; set; }

    public string Coat { get; set; } = null!;
    public string CoatCode { get; set; } = null!;
    public string Color { get; set; } = null!;
    public string ColorCode { get; set; } = null!;
    public decimal Thick { get; set; }
    public string ThickCode { get; set; } = null!;
    public decimal Weight { get; set; }
    public decimal CurrentWeight { get; set; }

    /// <summary>Efektywność: waga blachy (kg/mb)</summary>
    public decimal Efficiency { get; set; }

    public bool Checked { get; set; }
    public DateTime RollRegisteredDate { get; set; }
    public string Supplier { get; set; } = null!;
    public string LabelFilePath { get; set; } = "";
    public DateTime CreatedDate { get; set; }
}

