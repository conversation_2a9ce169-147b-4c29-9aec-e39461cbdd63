using EMessa.Base.Constants;
using EMessa.Base.Enums;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Articles;
using EMessa.DAL.Entities.Countries;
using EMessa.DAL.Entities.Customers;
using EMessa.DAL.Entities.FilterAttributes;
using EMessa.DAL.Entities.Identity;
using EMessa.DAL.Entities.Users;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Linq.Expressions;

namespace EMessa.Core.Data
{
    public class DatabaseSeeder(
        ILogger<DatabaseSeeder> logger,
        IConfiguration configuration,
        UserManager<ApplicationUser> userManager,
        RoleManager<ApplicationRole> roleManager,
        IDbContextFactory<ApplicationDbContext> dbContextFactory)
        : IDatabaseSeeder
    {
        private readonly string _appOwnerShortName = configuration["AppOwnerShortName"] ?? "Firma";

        public async Task SeedAsync()
        {
            try
            {
                await AddRolesAsync();

                var adminEmail = configuration["AdminEmail"];
                var adminPassword = configuration["AdminPassword"];

                if (string.IsNullOrEmpty(adminEmail))
                {
                    throw new NullReferenceException("Admin email is missing in config file.");
                }

                if (string.IsNullOrEmpty(adminPassword))
                {
                    throw new NullReferenceException("Admin password is missing in config file.");
                }

                await using var dbContext = await dbContextFactory.CreateDbContextAsync();

                await SeedConfiguration(dbContext);
                await SeedBranches(dbContext);
                await SeedFactories(dbContext);
                await SeedCountries(dbContext);
                await SeedCustomer(dbContext);

                await SeedFilterAttributes(dbContext);

                var adminUser = await userManager.FindByNameAsync(adminEmail);
                if (adminUser == null)
                {
                    adminUser = new ApplicationUser()
                    {
                        Email = adminEmail,
                        UserName = adminEmail,
                        EmailConfirmed = true,
                        PhoneNumberConfirmed = true
                    };

                    adminUser.PasswordHash = userManager.PasswordHasher.HashPassword(adminUser, adminPassword);

                    await userManager.CreateAsync(adminUser);
                    await userManager.AddToRoleAsync(adminUser, Role.Administrator);

                    var customer = await dbContext.Customers.FirstOrDefaultAsync(x => x.ShortName == _appOwnerShortName);
                    if (customer == null)
                    {
                        throw new NullReferenceException("Customer not found.");
                    }
                    var adminUserProfile = new ApplicationUserProfile()
                    {
                        UserName = adminEmail,
                        Email = adminEmail,
                        FirstName = "Admin",
                        LastName = "Admin",
                        Active = true,
                        UserType = UserTypes.Employee,
                        CustomerId = customer.Id,
                        UserBranches = new List<UserBranch> { new UserBranch { BranchId = dbContext.Branches.First().Id } },
                        FirstPhoneNumber = "*********",
                        SecondPhoneNumber = "*********"

                    };

                    await dbContext.UserProfiles.AddAsync(adminUserProfile);
                    await dbContext.SaveChangesAsync();
                }

                await SeedUnits(dbContext);
                await SeedArticles(dbContext);
                await SeedCategories(dbContext);
                await AddPagesAsync(dbContext);
                await SeedOptions(dbContext);
                await SeedMessages(dbContext);

            }
            catch (Exception e)
            {
                logger.LogError(e, e.Message);
                throw;
            }
        }

        private async Task SeedFilterAttributes(ApplicationDbContext dbContext)
        {
            logger.LogInformation("Seed filter attributes");
            if (!dbContext.FilterAttributes.Any(x => x.Code == "RYNKOL"))
            {
                var rynkol = new FilterAttribute { Code = "RYNKOL", Name = "Kolor rynny", IsActive = true, DisplayName = "Kolor", OrdinaryNumber = 1 };
                dbContext.FilterAttributes.Add(rynkol);
                await dbContext.SaveChangesAsync();

                dbContext.FilterAttributeValues.Add(new FilterAttributeValue { FilterAttributeId = rynkol.Id, Code = "RAL3005", Value = "RAL 3005", DisplayValue = "RAL 3005" });
                dbContext.FilterAttributeValues.Add(new FilterAttributeValue { FilterAttributeId = rynkol.Id, Code = "RAL3009", Value = "RAL 3009", DisplayValue = "RAL 3009" });
            }

            if (!dbContext.FilterAttributes.Any(x => x.Code == "RYNROZ"))
            {
                var rynroz = new FilterAttribute { Code = "RYNROZ", Name = "Rozmiar rynny", IsActive = true, DisplayName = "Rozmiar rynny", OrdinaryNumber = 2 };
                dbContext.FilterAttributes.Add(rynroz);
                await dbContext.SaveChangesAsync();

                dbContext.FilterAttributeValues.Add(new FilterAttributeValue { FilterAttributeId = rynroz.Id, Code = "125", Value = "125", DisplayValue = "125" });
                dbContext.FilterAttributeValues.Add(new FilterAttributeValue { FilterAttributeId = rynroz.Id, Code = "150", Value = "150", DisplayValue = "150" });
            }
            await dbContext.SaveChangesAsync();
        }

        private async Task SeedCustomer(ApplicationDbContext dbContext)
        {
            logger.LogInformation("Seed customers");
            if (!dbContext.Customers.Any(x => x.ShortName == _appOwnerShortName))
            {
                var firstCustomer = new Customer
                {
                    IsAppOwner = true,

                    ShortName = _appOwnerShortName,
                    Name = "Firma blachowa",
                    IsActive = true,
                    IsPrivate = false,
                    Address = "Ulica",
                    City = "Miasto",
                    Email = "<EMAIL>",
                    PostCode = "10-100",
                    TaxIdentificationNumber = "5330909029",
                    NationalOfficialBusinessRegister = "*********",
                    CountryId = dbContext.Countries.First().Id,
                    FactoryId = dbContext.Factories.First().Id,
                    BranchId = dbContext.Branches.First().Id,

                    //Fax = "*********",
                    //Phone = "*********", 
                };
                dbContext.Customers.Add(firstCustomer);
                await dbContext.SaveChangesAsync();
                var loction = new CustomerLocalization
                {
                    Name = "Główna",
                    Address = "Ulica",
                    City = "Miasto",
                    Email = "",
                    IsActive = true,
                    IsDefault = true,
                    Phone = "*********",
                    PostCode = "10-100",
                    CustomerId = firstCustomer.Id
                };
                dbContext.CustomersLocalizations.Add(loction);
                await dbContext.SaveChangesAsync();
            }
        }

        private async Task SeedCountries(ApplicationDbContext dbContext)
        {
            logger.LogInformation("Seed countries");

            TryAddCountry(dbContext, "PL", "Polska", "pl", "PL", "PLN");
            TryAddCountry(dbContext, "DE", "Niemcy", "de", "DE", "EUR");
            TryAddCountry(dbContext, "GB", "Wielka Brytania", "en", "GB", "GBP");
            TryAddCountry(dbContext, "HU", "Węgry", "hu", "HU", "HUF");
            TryAddCountry(dbContext, "CZ", "Czechy", "cs", "CZ", "CZK");
            TryAddCountry(dbContext, "SK", "Słowacja", "sk", "SK", "EUR");
            TryAddCountry(dbContext, "UA", "Ukraina", "uk", "UA", "UAH");
            TryAddCountry(dbContext, "AT", "Austria", "de", "AT", "EUR");

            await dbContext.SaveChangesAsync();
        }

        private void TryAddCountry(ApplicationDbContext dbContext, string code, string name, string lang, string vat, string currency)
        {
            bool codeExists = dbContext.Countries.Any(x => x.Code == code);
            bool nameExists = dbContext.Countries.Any(x => x.Name == name);

            if (!codeExists && !nameExists)
            {
                dbContext.Countries.Add(new Country
                {
                    Code = code,
                    Name = name,
                    LangCode = lang,
                    VatCode = vat,
                    CurrencyCode = currency
                });

                logger.LogInformation($"Dodano kraj: {code} - {name}");
            }
            else
            {
                logger.LogDebug($"Pomijam kraj (już istnieje): {code} - {name}");
            }
        }


        private async Task SeedFactories(ApplicationDbContext dbContext)
        {
            logger.LogInformation("Seed factories");
            if (!dbContext.Factories.Any(x => x.Code == "Główny"))
            {
                await dbContext.Factories.AddAsync(new DAL.Entities.Factories.Factory
                {
                    Code = "Główny",
                    Name = "Zakład główny",
                    Address = "Ulica",
                    City = "Miasto",
                    Email = "<EMAIL>",
                    PostCode = "10-100"
                });
                await dbContext.SaveChangesAsync();
            }
        }

        private async Task SeedBranches(ApplicationDbContext dbContext)
        {
            logger.LogInformation("Seed branches");
            if (!dbContext.Branches.Any(x => x.Code == "Główny"))
            {
                dbContext.Branches.Add(new DAL.Entities.Branches.Branch
                {
                    Code = "Główny",
                    Name = "Główny",
                    IsActive = true,
                    Address = "Ulica",
                    City = "Miasto",
                    Email = "<EMAIL>",
                    PostCode = "10-100"
                });
                await dbContext.SaveChangesAsync();
            }

        }

        //private async Task Upsert<T>(ApplicationDbContext dbContext, T entity) where T : class
        //{
        //    var entry = dbContext.Entry(entity);
        //    if (entry.State == EntityState.Detached)
        //    {
        //        dbContext.Set<T>().Add(entity);
        //    }
        //    else
        //    {
        //        entry.State = EntityState.Modified;
        //    }

        //    await dbContext.SaveChangesAsync();
        //}
        private async Task SeedConfiguration(ApplicationDbContext dbContext)
        {
            // seed dla konfiguracji aplikacji
            logger.LogInformation("Seed configurations");
            if (!dbContext.Configurations.Any(x => x.Code == ConfigurationCodes.SiteName.ToString()))
            {
                await dbContext.Configurations.AddAsync(new DAL.Entities.Configurations.Configuration
                {
                    Code = ConfigurationCodes.SiteName.ToString(),
                    Value = "EMessa Firma dachowa",
                    Name = "Nazwa strony",
                    Type = ConfigurationValueTypes.Text.ToString()
                });
            }

            if (!dbContext.Configurations.Any(x => x.Code == ConfigurationCodes.EMessaUserCode.ToString()))
            {
                await dbContext.Configurations.AddAsync(new DAL.Entities.Configurations.Configuration
                {
                    Code = ConfigurationCodes.EMessaUserCode.ToString(),
                    Value = _appOwnerShortName,
                    Name = "Kod firmy",
                    Type = ConfigurationValueTypes.Text.ToString()
                });
            }

            if (!dbContext.Configurations.Any(x => x.Code == ConfigurationCodes.EMessaUserName.ToString()))
            {
                await dbContext.Configurations.AddAsync(new DAL.Entities.Configurations.Configuration
                {
                    Code = ConfigurationCodes.EMessaUserName.ToString(),
                    Value = "Firma dachowa",
                    Name = "Nazwa firmy",
                    Type = ConfigurationValueTypes.Text.ToString()
                });
            }

            if (!dbContext.Configurations.Any(x => x.Code == ConfigurationCodes.GeneralSaleTermsFilePath.ToString()))
            {
                await dbContext.Configurations.AddAsync(new DAL.Entities.Configurations.Configuration
                {
                    Code = ConfigurationCodes.GeneralSaleTermsFilePath.ToString(),
                    Value = "",
                    Name = "Ogólne warunki sprzedaży",
                    Type = ConfigurationValueTypes.Text.ToString()
                });
            }

            if (!dbContext.Configurations.Any(x => x.Code == ConfigurationCodes.RegulationsFilePath.ToString()))
            {
                await dbContext.Configurations.AddAsync(new DAL.Entities.Configurations.Configuration
                {
                    Code = ConfigurationCodes.RegulationsFilePath.ToString(),
                    Value = "",
                    Name = "Regulamin",
                    Type = ConfigurationValueTypes.Text.ToString()
                });
            }


            if (!dbContext.Configurations.Any(x => x.Code == ConfigurationCodes.PassResetMessageCode.ToString()))
            {
                await dbContext.Configurations.AddAsync(new DAL.Entities.Configurations.Configuration
                {
                    Code = ConfigurationCodes.PassResetMessageCode.ToString(),
                    Value = "MessageResetPassword",
                    Name = "Wiadomośc resetowania hasła",
                    Type = ConfigurationValueTypes.Text.ToString()
                });
            }

            if (!dbContext.Configurations.Any(x => x.Code == ConfigurationCodes.NewUserMessageCode.ToString()))
            {
                await dbContext.Configurations.AddAsync(new DAL.Entities.Configurations.Configuration
                {
                    Code = ConfigurationCodes.NewUserMessageCode.ToString(),
                    Value = ConfigurationCodes.NewUserMessageCode.ToString(),
                    Name = "Wiadomość zarejestrowania nowego użytkownika",
                    Type = ConfigurationValueTypes.Text.ToString()
                });
            }

            if (!dbContext.Configurations.Any(x => x.Code == ConfigurationCodes.EmailResetAppUrl.ToString()))
            {
                await dbContext.Configurations.AddAsync(new DAL.Entities.Configurations.Configuration
                {
                    Code = ConfigurationCodes.EmailResetAppUrl.ToString(),
                    Value = string.Empty,
                    Name = "Domena adresu linku resetowania hasła",
                    Type = ConfigurationValueTypes.Text.ToString()
                });
            }

            if (!dbContext.Configurations.Any(x => x.Code == ConfigurationCodes.GeminiModel.ToString()))
            {
                await dbContext.Configurations.AddAsync(new DAL.Entities.Configurations.Configuration
                {
                    Code = ConfigurationCodes.GeminiModel.ToString(),
                    Value = "gemini-2.0-flash",
                    Name = "Model tłumacza 'Gemini AI'",
                    Type = ConfigurationValueTypes.Text.ToString()
                });
            }

            if (!dbContext.Configurations.Any(x => x.Code == ConfigurationCodes.GeminiUrl.ToString()))
            {
                await dbContext.Configurations.AddAsync(new DAL.Entities.Configurations.Configuration
                {
                    Code = ConfigurationCodes.GeminiUrl.ToString(),
                    Value = "https://generativelanguage.googleapis.com/v1beta",
                    Name = "Url tłumacza 'Gemini AI'",
                    Type = ConfigurationValueTypes.Text.ToString()
                });
            }

            if (!dbContext.Configurations.Any(x => x.Code == ConfigurationCodes.AutoTranslateEnabled.ToString()))
            {
                await dbContext.Configurations.AddAsync(new DAL.Entities.Configurations.Configuration
                {
                    Code = ConfigurationCodes.AutoTranslateEnabled.ToString(),
                    Value = "True",
                    Name = "Włącz tłumacza (true/false)",
                    Type = ConfigurationValueTypes.Boolean.ToString()
                });
            }
            
            await dbContext.SaveChangesAsync();
        }

        private async Task SeedMessages(ApplicationDbContext db)
        {
            logger.LogInformation("Seed Messages");

            var messageRestorePwd = await db.Messages.SingleOrDefaultAsync(
                x => x.Code == ConfigurationCodes.MessageResetPassword.ToString()
            );
            if (messageRestorePwd == null)
            {
                messageRestorePwd = new DAL.Entities.Messages.Message
                {
                    Code = ConfigurationCodes.MessageResetPassword.ToString(),
                    Name = "Resetowanie hasła użytkownika",
                    Subject = " {CompanyName} -- Resetowanie hasła.",
                    Body = string.Join(
                        Environment.NewLine,
                        "<p>W systemie zgłoszono prośbę o zresetowanie hasła.</p>",
                        "<p>Kliknij <a href='{EmailResetAppUrl}'>tutaj</a> aby zresetować hasło.</p>",
                        "<br/><br/>",
                        "<p>Pozdrawiamy<br/>",
                        "{SysCompanyName}",
                        "</p>"
                    )
                };
                db.Messages.Add(messageRestorePwd);
            }

            var messageNewUser = await db.Messages.SingleOrDefaultAsync(
                x => x.Code == ConfigurationCodes.NewUserMessageCode.ToString()
            );
            if (messageNewUser == null)
            {
                messageNewUser = new DAL.Entities.Messages.Message
                {
                    Code = ConfigurationCodes.NewUserMessageCode.ToString(),
                    Name = "Wiadomość zarejestrowania nowego użytkownika",
                    Subject = " {SysAppName} -- Rejestracja nowego użytkownika.",
                    Body = string.Join(
                        Environment.NewLine,
                        "<p><p>W systemie {SysAppName} zostało zarejestrowane nowe konto na Twoje dane.</p>",
                        "<p>Kliknij <a href='{EmailResetAppUrl}'>tutaj</a> aby ustawić hasło do konta.</p>",
                        "<br/><br/>",
                        "<p>Pozdrawiamy<br/>",
                        "{SysCompanyName}",
                        "</p>"
                    )
                };

                db.Messages.Add(messageNewUser);
            }

            await db.SaveChangesAsync();
        }
        private async Task SeedOptions(ApplicationDbContext db)
        {
            var opCoat = await db.Options.SingleOrDefaultAsync(x => x.Code == "POW");
            if (opCoat == null)
            {
                opCoat = new DAL.Entities.Options.Option { Code = "POW", Name = "Powłoka", InIndex = true, No = 1, HideEditor = false, UseDefaultValue = true };
                db.Options.Add(opCoat);
                await db.SaveChangesAsync();
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opCoat.Id, Code = "POL", Value = "POLIESTER POŁYSK (25µm)", No = 1, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opCoat.Id, Code = "MAT", Value = "POLIESTER ULTRAMAT (35µm)", No = 2, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opCoat.Id, Code = "HDX", Value = "GRANITE HDX (55µm)", No = 3, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opCoat.Id, Code = "STORM", Value = "GRANITE STORM (50µm)", No = 4, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
            }
            await db.SaveChangesAsync();

            var opColor = await db.Options.SingleOrDefaultAsync(x => x.Code == "KOL");
            if (opColor == null)
            {
                opColor = new DAL.Entities.Options.Option { Code = "KOL", Name = "Kolor", InIndex = true, No = 2, HideEditor = false, UseDefaultValue = true };
                db.Options.Add(opColor);
                await db.SaveChangesAsync();
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opColor.Id, Code = "3005", Value = "RAL 3005", No = 1, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opColor.Id, Code = "3009", Value = "RAL 3009", No = 2, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opColor.Id, Code = "3011", Value = "RAL 3011", No = 3, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opColor.Id, Code = "5005", Value = "RAL 5005", No = 4, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opColor.Id, Code = "6005", Value = "RAL 6005", No = 5, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opColor.Id, Code = "6020", Value = "RAL 6020", No = 6, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opColor.Id, Code = "7016", Value = "RAL 7016", No = 7, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opColor.Id, Code = "8004", Value = "RAL 8004", No = 8, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opColor.Id, Code = "8017", Value = "RAL 8017", No = 9, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opColor.Id, Code = "9002", Value = "RAL 9002", No = 10, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opColor.Id, Code = "9006", Value = "RAL 9006", No = 11, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opColor.Id, Code = "9007", Value = "RAL 9007", No = 12, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opColor.Id, Code = "9010", Value = "RAL 9010", No = 13, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opColor.Id, Code = "9016", Value = "RAL 9016", No = 14, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
            }
            await db.SaveChangesAsync();
            var opThickness = await db.Options.SingleOrDefaultAsync(x => x.Code == "GR");
            if (opThickness == null)
            {
                opThickness = new DAL.Entities.Options.Option { Code = "GR", Name = "Grubość", InIndex = true, No = 3, HideEditor = false, UseDefaultValue = true };
                db.Options.Add(opThickness);
                await db.SaveChangesAsync();
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opThickness.Id, Code = "0,5", Value = "0.5 mm", No = 1, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opThickness.Id, Code = "0,6", Value = "0.6 mm", No = 2, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opThickness.Id, Code = "0,7", Value = "0.7 mm", No = 3, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opThickness.Id, Code = "0,45", Value = "0.45 mm", No = 4, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opThickness.Id, Code = "1", Value = "1.0 mm", No = 6, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
            }
            await db.SaveChangesAsync();

            var opMod = await db.Options.SingleOrDefaultAsync(x => x.Code == "MOD");
            if (opMod == null)
            {
                opMod = new DAL.Entities.Options.Option { Code = "MOD", Name = "Moduł", InIndex = true, No = 4, HideEditor = false, UseDefaultValue = true };
                db.Options.Add(opMod);
                await db.SaveChangesAsync();
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opMod.Id, Code = "350", Value = "350 mm", No = 1, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opMod.Id, Code = "400", Value = "400 mm", No = 2, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
            }
            await db.SaveChangesAsync();

            var opFoil = await db.Options.SingleOrDefaultAsync(x => x.Code == "FOL");
            if (opFoil == null)
            {
                opFoil = new DAL.Entities.Options.Option { Code = "FOL", Name = "Folia", InIndex = true, No = 5, HideEditor = false, UseDefaultValue = true };
                db.Options.Add(opFoil);
                await db.SaveChangesAsync();
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opFoil.Id, Code = "T", Value = "TAK", No = 1, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opFoil.Id, Code = "N", Value = "NIE", No = 2, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
            }
            await db.SaveChangesAsync();

            var opFilc = await db.Options.SingleOrDefaultAsync(x => x.Code == "FILC");
            if (opFilc == null)
            {
                opFilc = new DAL.Entities.Options.Option { Code = "FILC", Name = "Antykondensat", InIndex = true, No = 6, HideEditor = false, UseDefaultValue = true };
                db.Options.Add(opFilc);
                await db.SaveChangesAsync();
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opFilc.Id, Code = "T", Value = "TAK", No = 1, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
                db.OptionValues.Add(new DAL.Entities.Options.OptionValue { OptionId = opFilc.Id, Code = "N", Value = "NIE", No = 2, IsDefault = false, EmbossZoneAddition = 0, WeightFactor = 0 });
            }
            await db.SaveChangesAsync();

            // TODO OptionCodesConstans.EmbossHeightCode = "WP";
        }

        private async Task SeedArticles(ApplicationDbContext dbContext)
        {
            var articles = await dbContext.Articles.ToListAsync();
            var units = await dbContext.ArticleUnits.ToListAsync();
            if (!articles.Any())
            {
                List<Article> newArticles =
                [
                    new()
                    {
                        Code = "TETRA", Name = "BLACHODACHÓWKA TETRA", Type = ArticleType.Complex, IsActive = true,
                        ArticleUnitId = units.FirstOrDefault(x => x.Code == "m2")?.Id
                    },
                    new()
                    {
                        Code = "KUMA", Name = "BLACHODACHÓWKA KUMA", Type = ArticleType.Complex, IsActive = true,
                        ArticleUnitId = units.FirstOrDefault(x => x.Code == "m2")?.Id
                    },
                    new()
                    {
                        Code = "T-8", Name = "BLACHA TRAPEZOWA T-8", Type = ArticleType.Complex, IsActive = true,
                        ArticleUnitId = units.FirstOrDefault(x => x.Code == "m2")?.Id
                    },
                    new()
                    {
                        Code = "T-20", Name = "BLACHA TRAPEZOWA T-20", Type = ArticleType.Complex, IsActive = true,
                        ArticleUnitId = units.FirstOrDefault(x => x.Code == "m2")?.Id
                    },
                    new()
                    {
                        Code = "T-35", Name = "BLACHA TRAPEZOWA T-35", Type = ArticleType.Complex, IsActive = true,
                        ArticleUnitId = units.FirstOrDefault(x => x.Code == "m2")?.Id
                    },

                    new()
                    {
                        Code = "OKDACH1", Name = "OKNO DACHOWE 1", Type = ArticleType.Trade, IsActive = true,
                        ArticleUnitId = units.FirstOrDefault(x => x.Code == "szt.")?.Id
                    },
                    new()
                    {
                        Code = "OKDACH2", Name = "OKNO DACHOWE 2", Type = ArticleType.Trade, IsActive = true,
                        ArticleUnitId = units.FirstOrDefault(x => x.Code == "szt.")?.Id
                    }
                ];
                dbContext.Articles.AddRange(newArticles);
                await dbContext.SaveChangesAsync();
            }
        }

        private async Task SeedCategories(ApplicationDbContext dbContext)
        {

            var categories = await dbContext.Categories.AsTracking().ToListAsync();


            if (categories.All(x => x.Code != "root"))
            {
                var rootCategory = await dbContext.Categories.SingleOrDefaultAsync(x => x.Code == SystemConstants.RootCategoryCode);
                if (rootCategory == null)
                {
                    rootCategory = new DAL.Entities.Categories.Category() { Code = "root", Name = "Wszystkie", IsActive = true, Description = "Kategoria główna", OrdinaryNumber = 1 };
                }

                if (categories.All(x => x.Code != "BDACH"))
                {
                    rootCategory.SubCategories.Add(new DAL.Entities.Categories.Category() { Code = "BDACH", Name = "Blachodachówki", IsActive = true, Description = "Blachodachówki", OrdinaryNumber = 1 });
                }

                if (categories.All(x => x.Code != "BTRAP"))
                {
                    rootCategory.SubCategories.Add(new DAL.Entities.Categories.Category() { Code = "BTRAP", Name = "Blachy trapezowe", IsActive = true, Description = "", OrdinaryNumber = 2 });
                }

                if (categories.All(x => x.Code != "BPAN"))
                {
                    rootCategory.SubCategories.Add(new DAL.Entities.Categories.Category() { Code = "BPAN", Name = "Panele dachowe", IsActive = true, Description = "", OrdinaryNumber = 3 });
                }

                if (categories.All(x => x.Code != "BPL"))
                {
                    rootCategory.SubCategories.Add(new DAL.Entities.Categories.Category() { Code = "BPL", Name = "Blachy płaskie", IsActive = true, Description = "", OrdinaryNumber = 4 });
                }

                if (categories.All(x => x.Code != "BOBR"))
                {
                    rootCategory.SubCategories.Add(new DAL.Entities.Categories.Category() { Code = "BOBR", Name = "Obróbki blacharskie", IsActive = true, Description = "", OrdinaryNumber = 5 });
                }

                if (categories.All(x => x.Code != "BRYN"))
                {
                    var rynCategory = new DAL.Entities.Categories.Category() { Code = "BRYN", Name = "Systemy rynnowe", IsActive = true, Description = "", OrdinaryNumber = 6 };
                    if (categories.All(x => x.Code != "BRYNPLAST"))
                    {
                        rynCategory.SubCategories.Add(new DAL.Entities.Categories.Category() { Code = "BRYNPLAST", Name = "Rynny z tworzyw sztucznych", IsActive = true, Description = "", OrdinaryNumber = 1 });
                    }

                    if (categories.All(x => x.Code != "BRYNSTAL"))
                    {
                        rynCategory.SubCategories.Add(new DAL.Entities.Categories.Category() { Code = "BRYNSTAL", Name = "Rynny stalowe", IsActive = true, Description = "", OrdinaryNumber = 2 });
                    }
                    rootCategory.SubCategories.Add(rynCategory);
                }
                if (categories.Any(x => x.Code == "OKNA"))
                {
                    rootCategory.SubCategories.Add(new DAL.Entities.Categories.Category() { Code = "OKNA", Name = "Okna", IsActive = true, Description = "", OrdinaryNumber = 7 });
                }
                if (categories.Any(x => x.Code == "AKC"))
                {
                    rootCategory.SubCategories.Add(new DAL.Entities.Categories.Category() { Code = "AKC", Name = "Akcesoria", IsActive = true, Description = "", OrdinaryNumber = 8 });
                }

                if (rootCategory.Id == 0)
                {
                    dbContext.Categories.Add(rootCategory);
                }

                await dbContext.SaveChangesAsync();

                var articles = await dbContext.Articles.ToListAsync();

                var bdachCategory = await dbContext.Categories.Include(x => x.Articles).AsTracking().SingleOrDefaultAsync(x => x.Code == "BDACH");
                if (bdachCategory != null)
                {

                    if (articles.Any(x => x.Code.ToLower() == "tetra") && !bdachCategory.Articles.Any(x => x.Code.ToLower() == "tetra"))
                    {
                        bdachCategory.Articles.Add(articles.First(x => x.Code.ToLower() == "tetra"));
                    }

                    if (articles.Any(x => x.Code.ToLower() == "kuma") && !bdachCategory.Articles.Any(x => x.Code.ToLower() == "kuma"))
                    {
                        bdachCategory.Articles.Add(articles.First(x => x.Code.ToLower() == "kuma"));
                    }
                }

                await dbContext.SaveChangesAsync();

                var trapCategory = await dbContext.Categories.Include(x => x.Articles).AsTracking().SingleOrDefaultAsync(x => x.Code == "BTRAP");
                if (trapCategory != null)
                {

                    if (articles.Any(x => x.Code.ToLower() == "t-8") && !trapCategory.Articles.Any(x => x.Code.ToLower() == "t-8"))
                    {
                        trapCategory.Articles.Add(articles.First(x => x.Code.ToLower() == "t-8"));
                    }
                    if (articles.Any(x => x.Code.ToLower() == "t-20") && !trapCategory.Articles.Any(x => x.Code.ToLower() == "t-20"))
                    {
                        trapCategory.Articles.Add(articles.First(x => x.Code.ToLower() == "t-20"));
                    }
                    if (articles.Any(x => x.Code.ToLower() == "t-35") && !trapCategory.Articles.Any(x => x.Code.ToLower() == "t-35"))
                    {
                        trapCategory.Articles.Add(articles.First(x => x.Code.ToLower() == "t-35"));
                    }
                }

                await dbContext.SaveChangesAsync();

                var oknaCategory = await dbContext.Categories.Include(x => x.Articles).AsTracking().SingleOrDefaultAsync(x => x.Code == "OKNA");
                if (oknaCategory != null)
                {
                    if (articles.Any(x => x.Code.ToLower() == "okdach1") && !oknaCategory.Articles.Any(x => x.Code.ToLower() == "okdach1"))
                    {
                        oknaCategory.Articles.Add(articles.First(x => x.Code.ToLower() == "okdach1"));
                    }
                    if (articles.Any(x => x.Code.ToLower() == "okdach2") && !oknaCategory.Articles.Any(x => x.Code.ToLower() == "okdach2"))
                    {
                        oknaCategory.Articles.Add(articles.First(x => x.Code.ToLower() == "okdach2"));
                    }
                }

                await dbContext.SaveChangesAsync();
            }
        }

        private async Task AddRolesAsync()
        {
            var roleNames = new[]
            {
                Role.Administrator,
                Role.TradeManager,
                Role.Trade,
                Role.ClientManager,
                Role.Client,
                Role.Production,
                Role.SaleManager
            };

            foreach (var roleName in roleNames)
            {
                var role = await roleManager.FindByNameAsync(roleName);
                if (role == null)
                {
                    role = new ApplicationRole(roleName);
                    await roleManager.CreateAsync(role);
                }
            }
        }



        private async Task AddPagesAsync(ApplicationDbContext dbContext)
        {
            var link = "start";
            if (dbContext.Pages.SingleOrDefault(x => x.Link == link) == null)
            {
                var user = await dbContext.UserProfiles.FirstAsync();
                await dbContext.Pages.AddAsync(new DAL.Entities.Pages.Page { Title = "Strona główna", Link = link, Content = "Strona główna", CreatedById = user.Id, CreatedDate = DateTime.UtcNow, Description = "Strona główna", EditedById = user.Id, LastEditDate = DateTime.UtcNow, IsDefault = true, IsPublic = true, IsPublished = true });
                await dbContext.SaveChangesAsync();
            }
        }

        private async Task SeedUnits(ApplicationDbContext dbContext)
        {
            await UpsertEntityAsync<ArticleUnit>(dbContext, new ArticleUnit { Code = "m2", Name = "m2" }, x => x.Code == "m2");
            await UpsertEntityAsync<ArticleUnit>(dbContext, new ArticleUnit { Code = "szt.", Name = "szt." }, x => x.Code == "szt.");
            await UpsertEntityAsync<ArticleUnit>(dbContext, new ArticleUnit { Code = "mb", Name = "mb" }, x => x.Code == "mb");
        }

        private async Task UpsertEntityAsync<TEntity>(ApplicationDbContext dbContext, TEntity entity, Expression<Func<TEntity, bool>> checkExistingExpression) where TEntity : class
        {
            var existingEntity = await dbContext.Set<TEntity>().FirstOrDefaultAsync(checkExistingExpression);
            if (existingEntity == null)
            {
                dbContext.Set<TEntity>().Add(entity);
                await dbContext.SaveChangesAsync();
            }

        }
    }
}
