<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>
		<Configurations>Debug;Release;BDP_Develop</Configurations>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="13.0.1" />
		<PackageReference Include="ClosedXML" Version="0.104.2" />
		<PackageReference Include="FluentValidation" Version="11.11.0" />
		<PackageReference Include="Magick.NET-Q16-AnyCPU" Version="14.7.0" />
		<PackageReference Include="MailKit" Version="4.11.0" />
		<PackageReference Include="MathParser.org-mXparser" Version="6.1.0" />
		<PackageReference Include="MediatR" Version="12.4.1" />
		<PackageReference Include="Microsoft.AspNetCore.Identity" Version="2.2.0" />
		<PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.8" />
		<PackageReference Include="Microsoft.AspNetCore.WebUtilities" Version="8.0.8" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.8" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.8" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.8" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.1" />
		<PackageReference Include="Microsoft.Extensions.Identity.Stores" Version="8.0.8" />
		<PackageReference Include="Moq" Version="4.20.72" />
		<PackageReference Include="NCalcSync" Version="5.3.1" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="NGus" Version="1.0.12" />
		<PackageReference Include="Serilog" Version="4.0.2" />
		<PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
		<PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
		<PackageReference Include="Sieve" Version="2.5.5" />
		<PackageReference Include="Syncfusion.Blazor.Data" Version="27.1.51" />
		<PackageReference Include="Syncfusion.Blazor.Grid" Version="27.1.51" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Features\Accounts\Queries\ValidatePassword\" />
		<Folder Include="Features\ArticleCategory\Exceptions\" />
		<Folder Include="Features\Configurations\Commands\Delete\" />
		<Folder Include="Features\OptionValues\Queries\GetAllValuesForOptionWihoutArticle\" />
		<Folder Include="Features\Profiles\Queries\" />
		<Folder Include="Features\Users\Commands\DeleteBranch\" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\EMessa.Base\EMessa.Base.csproj" />
		<ProjectReference Include="..\EMessa.DAL\EMessa.DAL.csproj" />
		<ProjectReference Include="..\MessaApi\MessaApi.csproj" />
		<ProjectReference Include="..\OptApi\OptApi.csproj" />
		<ProjectReference Include="..\ViesService\ViesService.csproj" />
	</ItemGroup>

</Project>
