using System.Text.Json;
using System.Text.Json.Serialization;

namespace EMessa.Core.Common;

public static class Print
{
    private static readonly JsonSerializerOptions DefaultOptions = new()
    {
        WriteIndented = false,
        ReferenceHandler = ReferenceHandler.IgnoreCycles
    };

    private static readonly JsonSerializerOptions IndentedOptions = new()
    {
        WriteIndented = true,
        ReferenceHandler = ReferenceHandler.IgnoreCycles
    };

    public static void WriteInline<T>(T value, ConsoleColor color = ConsoleColor.Cyan)
    {
        var originalColor = Console.ForegroundColor;
        Console.ForegroundColor = color;
        var output = GetOutputString(value, IndentedOptions);
        Console.WriteLine(output);
        Console.ForegroundColor = originalColor;
    }
    // Print.WriteInline(user);
    // Print.WriteInline(user, ConsoleColor.Yellow);

    public static void WriteInline<T>(string label, T value, ConsoleColor color = ConsoleColor.Cyan)
    {
        var originalColor = Console.ForegroundColor;
        Console.ForegroundColor = color;
        Console.Write($"{label}: ");
        Console.WriteLine(JsonSerializer.Serialize(value, DefaultOptions));
        Console.ForegroundColor = originalColor;
    }
    // Print.WriteInline("User data", user);
    // Print.WriteInline("User data", user, ConsoleColor.Yellow);

    public static void WriteLine<T>(T value, ConsoleColor color = ConsoleColor.Cyan)
    {
        var originalColor = Console.ForegroundColor;
        Console.ForegroundColor = color;
        var output = GetOutputString(value, IndentedOptions);
        Console.WriteLine(output);
        Console.ForegroundColor = originalColor;
    }
    // Print.WriteLine(user);
    // Print.WriteLine(user, ConsoleColor.Yellow);

    public static void WriteLine<T>(string label, T value, ConsoleColor color = ConsoleColor.Cyan)
    {
        var originalColor = Console.ForegroundColor;
        Console.ForegroundColor = color;
        Console.WriteLine($"{label}:");
        Console.WriteLine(JsonSerializer.Serialize(value, DefaultOptions));
        Console.ForegroundColor = originalColor;
    }
    // Print.WriteLine("User data", user);
    // Print.WriteLine("User data", user, ConsoleColor.Yellow);

    public static void WriteIndented<T>(T value, ConsoleColor color = ConsoleColor.Cyan)
    {
        var originalColor = Console.ForegroundColor;
        Console.ForegroundColor = color;
        var output = GetOutputString(value, IndentedOptions);
        Console.WriteLine(output);
        Console.ForegroundColor = originalColor;
    }
    // Print.WriteIndented(user);
    // Print.WriteIndented(user, ConsoleColor.Yellow);

    public static void WriteIndented<T>(string label, T value, ConsoleColor color = ConsoleColor.Cyan)
    {
        var originalColor = Console.ForegroundColor;
        Console.ForegroundColor = color;
        Console.WriteLine($"{label}:");
        Console.WriteLine(JsonSerializer.Serialize(value, IndentedOptions));
        Console.ForegroundColor = originalColor;
    }
    // Print.WriteIndented("User data", user);
    // Print.WriteIndented("User data", user, ConsoleColor.Yellow);

    public static void WriteListInLine<T>(IEnumerable<T> values, ConsoleColor color = ConsoleColor.Cyan)
    {
        var originalColor = Console.ForegroundColor;
        Console.ForegroundColor = color;
        foreach (var item in values)
        {
            Console.WriteLine(JsonSerializer.Serialize(item, DefaultOptions));
        }

        Console.ForegroundColor = originalColor;
    }

    public static void WriteListInLine<T>(string label, IEnumerable<T> values,
        ConsoleColor color = ConsoleColor.Cyan)
    {
        var originalColor = Console.ForegroundColor;
        Console.ForegroundColor = color;
        Console.WriteLine($"{label}:");
        foreach (var item in values)
        {
            Console.WriteLine(JsonSerializer.Serialize(item, DefaultOptions));
        }

        Console.ForegroundColor = originalColor;
    }

    private static string GetOutputString(object? value, JsonSerializerOptions options)
    {
        return value is string stringValue
            ? stringValue
            : value == null
                ? "null"
                : JsonSerializer.Serialize(value, options);
    }

    public static string? SafeSubstring(this string? str, int startIndex, int length) =>
        str == null ? null :
        startIndex >= str.Length ? string.Empty :
        startIndex + length > str.Length ? str[startIndex..] :
        str[startIndex..(startIndex + length)];

    public static string? SafeSubstring(this string? str, int length) =>
        str?.Length > length ? str[..length] : str;

    public static string? SafeSlice(this string? str, int? start = null, int? end = null)
    {
        if (str == null) return null;

        var startIdx = start ?? 0;
        var endIdx = end ?? str.Length;

        if (startIdx < 0) startIdx = str.Length + startIdx;
        if (endIdx < 0) endIdx = str.Length + endIdx;

        startIdx = Math.Max(0, Math.Min(startIdx, str.Length));
        endIdx = Math.Max(0, Math.Min(endIdx, str.Length));

        return startIdx >= endIdx ? string.Empty : str[startIdx..endIdx];
    }
}