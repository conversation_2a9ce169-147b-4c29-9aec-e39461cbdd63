namespace EMessa.Core.Common
{
    public static class ListExtensions
    {
        public static string String(this List<string> data, string separator = ". ")
        {
            return string.Join(separator, data);
        }

        /// <summary>
        /// Produces cartesian product of IEnumerable values
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="sequences"></param>
        /// <returns></returns>
        public static IEnumerable<IEnumerable<T>> CartesianProduct<T>(this IEnumerable<IEnumerable<T>> sequences)
        {
            // base case: 
            IEnumerable<IEnumerable<T>> result = new[] { Enumerable.Empty<T>() };
            
            foreach (var sequence in sequences)
            {
                var s = sequence; // don't close over the loop variable 
                // recursive case: use SelectMany to build the new product out of the old one 
                result =
                  from seq in result
                  from item in s
                  select seq.Concat(new[] { item });
            }
            return result;
        }

        ///// <summary>
        ///// Produces cartesian product of List values
        ///// </summary>
        ///// <typeparam name="T"></typeparam>
        ///// <param name="sequences"></param>
        ///// <returns></returns>
        //public static List<List<T>> CartesianProduct<T>(this List<List<T>> sequences)
        //{
        //    // base case: 
        //    IEnumerable<IEnumerable<T>> result = new[] { Enumerable.Empty<T>() };
        //    foreach (var sequence in sequences)
        //    {
        //        var s = sequence; // don't close over the loop variable 
        //        // recursive case: use SelectMany to build the new product out of the old one 
        //        result =
        //          from seq in result
        //          from item in s
        //          select seq.Concat(new[] { item });
        //    }
        //    return result.to;
        //}
    }
}
