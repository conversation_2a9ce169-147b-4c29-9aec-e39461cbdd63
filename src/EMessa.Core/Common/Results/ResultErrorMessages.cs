using EMessa.Core.Localizer;

namespace EMessa.Core.Common.Results;

/// <summary>
/// Zawiera standardowe komunikaty błędów używane w wynikach operacji (np. w klasie <see cref="Result{T}"/>).
/// Komunikaty są automatycznie tłumaczone przy użyciu rozszerzenia <c>Tr()</c>.
/// </summary>
public static class ResultErrorMessages
{
    /// <summary>
    /// Operacja nie powiodła się.
    /// </summary>
    public static string OperationFailed => "Operacja nie powiodła się.".Tr();

    /// <summary>
    /// Nie znaleziono elementu.
    /// </summary>
    public static string NotFound => "Nie znaleziono elementu.".Tr();

    /// <summary>
    /// Brak dostępu.
    /// </summary>
    public static string Unauthorized => "Brak dostępu.".Tr();

    /// <summary>
    /// Wystąpił nieoczekiwany błąd.
    /// </summary>
    public static string UnexpectedError => "Wystąpił nieoczekiwany błąd.".Tr();

    /// <summary>
    /// Wystąpił nieoczekiwany błąd z wiadomością.
    /// </summary>
    public static string UnexpectedErrorWithMessage(string errorMessage)
    {
        return "Wystąpił nieoczekiwany błąd".Tr() + $": {errorMessage}";
    }
}
