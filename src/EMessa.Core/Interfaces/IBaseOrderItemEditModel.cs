using EMessa.Base.Enums;
using EMessa.Core.Features.Sales.Queries.CommonResponses;
using EMessa.Core.Features.ShoppingCart.Models;

namespace EMessa.Core.Interfaces;

public interface IBaseOrderItemEditModel
{
    OrderItemEditArticleModel Article { get; set; }
    int OrderItemId { get; set; }
    int ArticleId { get; set; }
    string ArticleName { get; set; }
    ArticleType ArticleType { get; set; }
    decimal Quantity { get; set; }
    List<ArticleOptionEditModel> OptionValues { get; set; }
    List<RequestedSheetEditModel> RequestedSheets { get; set; }
    List<ArticleSheetEditModel> ViewOrderItemSheets { get; set; }
    string Code { get; set; }
    string ArticleBaseCode { get; set; }
    string UnitWeight { get; set; }
    string ArticleUnit { get; set; }
    decimal ArticleWeight { get; set; }
    string Comments { get; set; }
    bool ProfileEditable { get; set; }
    bool RequireProfile { get; set; }
    decimal SumM2 { get; set; }
    decimal SumMb { get; set; }
    int SumQuantity { get; set; }
    decimal Weight { get; set; }
    bool DraftEditable { get; set; }
    string? DraftOriginalFileName { get; set; }
    string? DraftHashedFileName { get; set; }
    string? DraftDrawingSource { get; set; }
    bool IsSaleArticle { get; set; }
    SaleArticleResponse? SaleArticle { get; set; }
    decimal SaleAvailableLength { get; set; }
    decimal SaleAvailableWeight { get; set; }
    string NewIndex { get; }
}
