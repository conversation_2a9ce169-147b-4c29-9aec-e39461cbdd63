namespace EMessa.Core.Interfaces;

public interface IFileStorageService
{
    /// <summary>
    /// Zapisuje plik binarny w docelowym systemie plików.
    /// Sposób zapisu, struktura katalogów oraz obsługa nazw plików zależą od konkretnej implementacji.
    /// </summary>
    /// <param name="fileContent">Zawartość pliku jako tablica bajtów.</param>
    /// <param name="fileName">Oryginalna nazwa pliku (może być użyta do logowania lub metadanych).</param>
    /// <param name="fileExtension">Rozszerzenie pliku (bez kropki, np. "png", "jpg").</param>
    /// <param name="fileStorageType">Rodzaj repozytorium, gdzie zostanie zapisany plik.</param>
    /// <param name="cancellationToken">Token umożliwiający anulowanie operacji asynchronicznej.</param>
    /// <returns>Relatywna ścieżka do zapisanego pliku (np. "25/06/abc123.png").</returns>
    /// <remarks>
    /// Wyjątki rzucane przez tę metodę zależą od konkretnej implementacji.
    /// </remarks>
    /// <exception cref="Exception"/>
    Task<string> SaveFileAsync(byte[] fileContent, string fileName, string fileExtension, FileStorageType fileStorageType, CancellationToken cancellationToken);

    /// <summary>
    /// Odczytuje plik binarny z docelowego systemu plików.
    /// </summary>
    /// <param name="relativePath">Relatywna ścieżka do pliku. Ścieżka powinna być zgodna z tą, którą zwraca <see cref="SaveFileAsync"/> (np. "25/06/abc123.png").</param>
    /// <param name="fileStorageType">Rodzaj repozytorium, gdzie zostanie zapisany plik.</param>
    /// <param name="cancellationToken">Token umożliwiający anulowanie operacji asynchronicznej.</param>
    /// <returns>Zawartość pliku jako tablica bajtów.</returns>
    /// <remarks>
    /// Wyjątki zależą od implementacji — może wystąpić np. błąd dostępu do pliku lub brak pliku.
    /// </remarks>
    /// <exception cref="FileNotFoundException"/>
    /// <exception cref="Exception"/>
    Task<byte[]> GetFileAsync(string relativePath, FileStorageType fileStorageType, CancellationToken cancellationToken);

    void DeleteFile(string relativePath, FileStorageType fileStorageType);
}

public enum FileStorageType
{
    Drafts,
    SaleImages
}
