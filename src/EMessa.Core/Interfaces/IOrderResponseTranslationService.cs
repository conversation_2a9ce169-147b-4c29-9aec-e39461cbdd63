using EMessa.Core.Features.Orders.Queries.Common;
using EMessa.DAL.Data;

namespace EMessa.Core.Interfaces;

public interface IOrderResponseTranslationService
{
    Task<IEnumerable<TOrderResponse>> TranslateArticleNamesAsync<TOrderResponse>(
        ApplicationDbContext dbContext,
        IEnumerable<TOrderResponse> orderResponses,
        CancellationToken cancellationToken)
        where TOrderResponse : OrderResponse;
    Task<TOrderResponse> TranslateArticleNamesAsync<TOrderResponse>(
        ApplicationDbContext dbContext,
        TOrderResponse orderResponses,
        CancellationToken cancellationToken)
        where TOrderResponse : OrderResponse;
}