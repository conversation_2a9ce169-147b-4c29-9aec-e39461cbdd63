using EMessa.Base.Enums;
using EMessa.Core.Features.Sales.Queries.GetSaleRolls;
using EMessa.Core.Models;

namespace EMessa.Core.Interfaces;

public interface IRollDataService
{
    Task<IEnumerable<Roll>> GetRollsByIdsAsync(IEnumerable<long> rollIds, CancellationToken cancellationToken);
    Task<IEnumerable<Roll>> GetRollsByNosAsync(IEnumerable<int> rollNos, CancellationToken cancellationToken);
    Task<IEnumerable<Roll>> GetRollsByOptionsAsync(string colorCode, string coatCode, string thickCode, CancellationToken cancellationToken, RollStatus maxRollStatus = RollStatus.Registered);
    Task<Roll> GetRollByNoAsync(int rollNo, CancellationToken cancellationToken);
    Task UpdateRollStatusAsync(List<SaleRollResponse> saleRolls, CancellationToken cancellationToken);
}
