using EMessa.Base.Enums;
using EMessa.Core.Features.AppState;
using EMessa.Core.Features.Orders.Queries.Common;
using Microsoft.AspNetCore.Components;

namespace EMessa.Core.Interfaces;

public interface IAppStateService
{
    public OrdersState OrdersState { get; set; }
    public CategoriesState CategoriesState { get; set; }

    public GridUIState GridState { get; set; }
    public int GetUserProfileId();
    public int? GetCustomerId();
    public UserData UserData { get; set; }

    public LockOrderResponse? OwnLockedOrder { get; }
    public void SetOwnLockedOrder(ComponentBase? source, LockOrderResponse? lockedOrder);

    public AppScreenSize AppScreenSize { get; protected set; }
    public int ScreenWidth { get; protected set; }
    public int ScreenHeight { get; protected set; }
    public bool IsSmallScreen { get; }
    public bool IsMediumScreen { get; }
    public bool IsLargeScreen { get; }

    public event Action<ComponentBase?, string>? StateChanged;

    public void SetLoggedUser(
        ComponentBase? source, 
        int userProfileId, 
        string firstName, 
        string lastName,
        bool firstConfiguration,
        int? customerId, 
        List<string> roles, 
        List<int> branchIds, 
        int? factoryId,
        string email
    );

    public void UserLoggedIn(ComponentBase source);

    public AppScreenSize SetScreenSize(ComponentBase? source, int width, int height);

    public void SetLastOrderStatus(ComponentBase? source, OrderStatus status);

    public void SetCatalogVisibility(ComponentBase? source, bool visible);

    public void SetShoppingCart(ComponentBase source);

    public bool GetCategoryExpanded(int categoryId);
    public void SetCategoryExpanded(int categoryId, bool expanded);
}