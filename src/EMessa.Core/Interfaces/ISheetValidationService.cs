using EMessa.Core.Features.OrderItems.Commands.ValidateSheet;
using EMessa.DAL.Entities;

namespace EMessa.Core.Interfaces;

public class SheetValidationData : AbstractArticleSheet;

public interface ISheetValidationService
{
    public List<ArticleSheetValidationResult> ValidateRequestedSheet(
        ArticleSheetValidationModel sheetValidationModel,
        IArticleSheet requestedSheet
    );

    public List<ArticleSheetValidationResult> ValidateRequestedSheetWithSplit(
        ArticleSheetValidationModel sheetValidationModel,
        IArticleSheet requestedSheet,
        int splitN
    );
}