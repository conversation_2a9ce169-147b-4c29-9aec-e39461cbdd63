using EMessa.Core.Interfaces;
using Microsoft.Extensions.Configuration;

namespace EMessa.Core.Services.FileStorage;

public class FilePathProvider(IConfiguration configuration) : IFilePathProvider
{
    public string GetBasePath(FileStorageType storageType) => storageType switch
    {
        FileStorageType.Drafts => configuration["FileUploadSettings:DraftsPath"] ?? throw new InvalidOperationException("DraftsPath not configured"),
        FileStorageType.SaleImages => configuration["FileUploadSettings:SaleImagesPath"] ?? throw new InvalidOperationException("SaleImagePath not configured"),
        _ => throw new ArgumentOutOfRangeException(nameof(storageType), storageType, null)
    };
}
