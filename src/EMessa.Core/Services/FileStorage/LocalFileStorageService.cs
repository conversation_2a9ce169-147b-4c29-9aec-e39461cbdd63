using EMessa.Core.Interfaces;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Services.FileStorage;

public class LocalFileStorageService(
    ILogger<LocalFileStorageService> logger,
    IFilePathProvider filePathProvider)
    : IFileStorageService
{
    /// <summary>
    /// Zapisuje plik binarny do systemu plików lokalnych w strukturze folderów zbudowanej na podstawie aktualnej daty (format: YY/MM).
    /// Generuje unikalną nazwę pliku na bazie GUID, do którego dokleja podane rozszerzenie.
    /// Tworzy brakujące katalogi, jeśli nie istnieją.
    /// </summary>
    /// <param name="fileContent">Zawartość pliku w postaci tablicy bajtów.</param>
    /// <param name="fileName">Oryginalna nazwa pliku (używana tylko do logowania błędów).</param>
    /// <param name="fileExtension">Rozszerzenie pliku bez kropki (np. "png", "jpg").</param>
    /// <param name="fileStorageType">Rodzaj repozytorium, gdzie zostanie zapisany plik.</param>
    /// <param name="cancellationToken">Token anulowania operacji asynchronicznej.</param>
    /// <returns>Relatywna ścieżka do zapisanego pliku względem katalogu bazowego (np. "sale/25/06/abc123.png").</returns>
    /// <exception cref="ArgumentException">
    /// Rzucany, gdy:
    /// - <paramref name="fileContent"/> jest puste lub null,
    /// - <paramref name="fileExtension"/> jest pusty lub null,
    /// - <paramref name="targetBasePath"/> jest pusty lub null.
    /// </exception>
    /// <exception cref="IOException">
    /// Może zostać rzucony przez <c>File.WriteAllBytesAsync</c>, jeśli wystąpi problem z zapisem pliku.
    /// </exception>
    /// <exception cref="UnauthorizedAccessException">
    /// Może zostać rzucony przez <c>File.WriteAllBytesAsync</c>, jeśli brak dostępu do katalogu lub pliku.
    /// </exception>
    /// <exception cref="Exception">
    /// Rzucany, gdy wystąpi inny nieoczekiwany błąd zapisu.
    /// </exception>
    public async Task<string> SaveFileAsync(
        byte[] fileContent,
        string fileName,
        string fileExtension,
        FileStorageType fileStorageType,
        CancellationToken cancellationToken)
    {
        if (fileContent == null || fileContent.Length == 0)
            throw new ArgumentException("Plik jest pusty.");

        if (string.IsNullOrWhiteSpace(fileExtension))
            throw new ArgumentException("Brak rozszerzenia pliku.");

        var targetBasePath = filePathProvider.GetBasePath(fileStorageType);
        if (targetBasePath is null)
        {
            logger.LogError("FileUploadSettings.SaleImagePath is not set in appsettings.json");
            throw new InvalidOperationException(
                "FileUploadSettings.SaleImagePath is not set in appsettings.json");
        }

        var now = DateTime.UtcNow;
        var year = now.ToString("yy");
        var month = now.ToString("MM");

        var fullDirectoryPath = Path.Combine(targetBasePath, year, month);
        Directory.CreateDirectory(fullDirectoryPath);

        var newUniqueFileName = $"{Guid.NewGuid():N}.{fileExtension.ToLowerInvariant()}";
        var fullPath = Path.Combine(fullDirectoryPath, newUniqueFileName);

        try
        {
            await File.WriteAllBytesAsync(fullPath, fileContent, cancellationToken);
            logger.LogInformation("Zapisano plik: {FullPath}", fullPath);

            // Zwracamy tylko ścieżkę względem base path, np. "25/06/abc123.png"
            var relativePath = Path.Combine(year, month, newUniqueFileName);
            return relativePath;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Błąd podczas zapisu pliku: {FullPath} | oryginalna nazwa pliku: {fileName}", fullPath, fileName);
            throw;
        }
    }

    /// <summary>
    /// Odczytuje plik binarny z lokalnego systemu plików na podstawie relatywnej ścieżki względem katalogu bazowego.
    /// </summary>
    /// <param name="relativePath">Relatywna ścieżka do pliku względem katalogu bazowego (np. "25/06/abc123.png").</param>
    /// <param name="fileStorageType">Rodzaj repozytorium, z którego plik ma zostać odczytany.</param>
    /// <param name="cancellationToken">Token anulowania operacji asynchronicznej.</param>
    /// <returns>Zawartość pliku jako tablica bajtów.</returns>
    /// <exception cref="ArgumentException">Rzucany, gdy <paramref name="relativePath"/> jest pusty lub null.</exception>
    /// <exception cref="InvalidOperationException">Rzucany, gdy baza ścieżka dla danego typu storage nie jest skonfigurowana.</exception>
    /// <exception cref="FileNotFoundException">Rzucany, gdy plik nie istnieje pod podaną ścieżką.</exception>
    /// <exception cref="Exception">Rzucany, gdy wystąpi inny błąd podczas odczytu pliku.</exception>
    public async Task<byte[]> GetFileAsync(string relativePath, FileStorageType fileStorageType, CancellationToken cancellationToken)
    {
        var fullPath = GetFullPath(relativePath, fileStorageType);

        EnsureFileExists(fullPath);

        try
        {
            var fileBytes = await File.ReadAllBytesAsync(fullPath, cancellationToken);
            logger.LogInformation("Odczytano plik: {FullPath}", fullPath);
            return fileBytes;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Błąd podczas odczytu pliku: {FullPath}", fullPath);
            throw;
        }
    }

    public void DeleteFile(string relativePath, FileStorageType fileStorageType)
    {
        var fullPath = GetFullPath(relativePath, fileStorageType);

        EnsureFileExists(fullPath);

        try
        {
            File.Delete(fullPath);
            return;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Błąd podczas usuwania pliku: {FullPath}", fullPath);
            throw;
        }
    }

    private string GetFullPath(string relativePath, FileStorageType fileStorageType)
    {
        var basePath = filePathProvider.GetBasePath(fileStorageType);
        if (string.IsNullOrWhiteSpace(basePath))
        {
            logger.LogError("Base path dla storageType {FileStorageType} nie jest skonfigurowany.", fileStorageType);
            throw new InvalidOperationException($"Base path dla storageType {fileStorageType} nie jest skonfigurowany.");
        }
        return Path.Combine(basePath, relativePath);
    }

    private void EnsureFileExists(string fullPath)
    {
        if (!File.Exists(fullPath))
        {
            logger.LogWarning("Plik nie istnieje: {FullPath}", fullPath);
            throw new FileNotFoundException("Plik nie został znaleziony.", fullPath);
        }
    }
}