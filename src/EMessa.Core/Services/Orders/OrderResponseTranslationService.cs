using EMessa.Core.Features.Orders.Queries.Common;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Globalization;

namespace EMessa.Core.Services.Orders;

public class OrderResponseTranslationService(
    ILogger<OrderResponseTranslationService> logger)
    : IOrderResponseTranslationService
{
    public async Task<IEnumerable<TOrderResponse>> TranslateArticleNamesAsync<TOrderResponse>(
        ApplicationDbContext dbContext,
        IEnumerable<TOrderResponse> orderResponses,
        CancellationToken cancellationToken)
        where TOrderResponse : OrderResponse
    {
        try
        {
            var languageCode = CultureInfo.CurrentCulture.Name;

            logger.LogDebug("Translating article names for language code: {LanguageCode}", languageCode);

            var allArticleIds = orderResponses.SelectMany(oi => oi.OrderItems.Select(oia => oia.ArticleId)).ToList();

            if (allArticleIds.Count == 0)
            {
                logger.LogDebug("No article IDs found in order responses, returning original order responses without translation.");
                return orderResponses;
            }

            var articleTranslations = await dbContext.ArticleTranslations
                .Where(at =>
                    allArticleIds.Contains(at.ArticleId) &&
                    at.LanguageCode == languageCode)
                .GroupBy(at => at.ArticleId)
                .ToDictionaryAsync(
                    g => g.Key,
                    g => g.Select(at => at.Name).First(),
                    cancellationToken);

            logger.LogDebug("Found {Count} article translations for language code: {LanguageCode}", articleTranslations.Count, languageCode);

            foreach (var orderItem in orderResponses.SelectMany(o => o.OrderItems))
            {
                if (articleTranslations.TryGetValue(orderItem.ArticleId, out var translated) &&
                    !string.IsNullOrWhiteSpace(translated))
                {
                    orderItem.ArticleTranslatedName = translated;
                }
                else
                {
                    logger.LogDebug("No translation found for ArticleId: {ArticleId} in language {LanguageCode}", orderItem.ArticleId, languageCode);
                    orderItem.ArticleTranslatedName = orderItem.ArticleName;
                }
            }


            logger.LogDebug("Successfully translated article names for {Count} orders", orderResponses.Count());

            return orderResponses;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while translating article names.");

            foreach (var orderItem in orderResponses.SelectMany(o => o.OrderItems))
                orderItem.ArticleTranslatedName = orderItem.ArticleName;

            logger.LogWarning("Using original article names due to translation error.");

            return orderResponses;
        }
    }

    public async Task<TOrderResponse> TranslateArticleNamesAsync<TOrderResponse>(
       ApplicationDbContext dbContext,
       TOrderResponse orderResponses,
       CancellationToken cancellationToken)
       where TOrderResponse : OrderResponse
    {
        var result = await TranslateArticleNamesAsync(dbContext, [orderResponses], cancellationToken);

        return result.First();
    }
}
