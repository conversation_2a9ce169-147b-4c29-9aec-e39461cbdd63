using System.Collections.Concurrent;
using EMessa.Core.Interfaces;

namespace EMessa.Core.Services.Orders;

public class OrderWatchService : IOrderWatchService
{
    private readonly ConcurrentDictionary<int, ConcurrentDictionary<object, Func<int, Task>>> _subscriptions = new();

    public void Subscribe(object subscriber, int orderId, Func<int, Task> callback)
    {
        var subscribers = _subscriptions.GetOrAdd(orderId, _ => new ConcurrentDictionary<object, Func<int, Task>>());
        subscribers[subscriber] = callback;
    }

    public void Unsubscribe(object subscriber, int orderId)
    {
        if (_subscriptions.TryGetValue(orderId, out var subscribers))
        {
            subscribers.TryRemove(subscriber, out _);
        }
    }

    public async Task NotifyOrderChanged(int orderId)
    {
        if (_subscriptions.TryGetValue(orderId, out var subscribers))
        {
            foreach (var callback in subscribers.Values)
            {
                await callback(orderId);
            }
        }
    }
}
