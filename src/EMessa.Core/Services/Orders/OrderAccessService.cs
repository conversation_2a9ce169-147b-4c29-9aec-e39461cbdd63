using EMessa.Base.Constants;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Orders;
using EMessa.DAL.Entities.Users;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Services.Orders;

public class OrderAccessService(
    ILogger<OrderAccessService> logger,
    IDbContextFactory<ApplicationDbContext> dbFactory,
    IAppStateService appStateService)
    : IOrderAccessService
{
    public async Task<bool> CanUserAccessOrderItem(string hashFileName, int userId)
    {
        await using var dbContext = await dbFactory.CreateDbContextAsync();

        // Pobieramy customerId związane z plikiem, jeśli istnieje
        var customerId = await dbContext.OrderItems
            .Where(oi => oi.DraftHashedFileName == hashFileName)
            .Select(oi => (int?)oi.Order.CustomerId)
            .FirstOrDefaultAsync();

        // Jeśli customerId jest null (nie znaleziono pliku), dostęp jest domyślnie dozwolony
        if (customerId == null)
            return true;

        // Sprawdzamy, czy użytkownik ma dostęp do klienta
        return await dbContext.CustomerUsers
            .AnyAsync(cu => cu.CustomerId == customerId && cu.UserProfileId == userId);
    }

    public async Task<IQueryable<Order>> GetAccessibleOrdersAsync(ApplicationDbContext dbContext, CancellationToken cancellationToken = default)
    {
        var userData = appStateService.UserData;

        try
        {
            logger.LogDebug(
                "Starting GetAccessibleOrdersAsync for UserProfileId: {UserProfileId}, Roles: {Roles}", userData.UserProfileId, string.Join(", ", userData.Roles));

            var baseQuery = dbContext.Orders.AsQueryable();
            IQueryable<Order>? result = null;

            // Admin – pełny dostęp
            if (userData.HasAdminRole)
            {
                logger.LogInformation("User has admin role – full access granted.");
                return baseQuery;
            }

            ApplicationUserProfile? userProfile;
            try
            {
                userProfile = await dbContext.UserProfiles
                    .Include(up => up.UserLocalizations)
                    .Include(up => up.CustomerUsers)
                    .FirstOrDefaultAsync(up => up.Id == userData.UserProfileId, cancellationToken);

                if (userProfile is null)
                    return baseQuery.Where(x => false);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "UserProfile not found for Id: {UserProfileId}", userData.UserProfileId);
                throw;
            }
            logger.LogDebug("Loaded UserProfile with Id: {UserProfileId}", userData.UserProfileId);


            foreach (var role in userData.Roles)
            {
                var allowedStatuses = OrderAccessConstants.GetStatusesForRole(role);
                if (allowedStatuses.Length == 0)
                {
                    logger.LogWarning("No allowed statuses configured for role: {Role}", role);
                    continue;
                }

                logger.LogDebug("Processing role {Role} that has {Count} allowed statuses.", role, allowedStatuses.Length);

                IQueryable<Order>? roleOrders = null;

                switch (role)
                {
                    case Role.Client:
                        roleOrders = baseQuery
                            .Where(o =>
                                o.CustomerId == userData.CustomerId &&
                                o.CreatedById == userData.UserProfileId &&
                                userProfile.UserLocalizations.Select(ul => ul.CustomerLocalizationId).Contains(o.CustomerLocalizationId) &&
                                allowedStatuses.Contains(o.Status));
                        break;

                    case Role.ClientManager:
                        roleOrders = baseQuery
                            .Where(o =>
                                o.CustomerId == userData.CustomerId &&
                                allowedStatuses.Contains(o.Status));
                        break;

                    case Role.Trade:
                    case Role.TradeManager:
                        roleOrders = baseQuery
                            .Where(o =>
                                (
                                    userProfile.CustomerUsers.Select(cu => cu.CustomerId).Contains(o.CustomerId) &&
                                    userData.BranchIds.Contains(o.BranchId) &&
                                    OrderAccessConstants.TradeCustomersOrders.Contains(o.Status)
                                ) ||
                                (
                                    o.CreatedById == userData.UserProfileId &&
                                    OrderAccessConstants.TradeOwnOrders.Contains(o.Status)
                                ));
                        break;

                    case Role.Production:
                        roleOrders = baseQuery
                            .Where(o =>
                                o.FactoryId == userData.FactoryId &&
                                allowedStatuses.Contains(o.Status));
                        break;
                }

                if (roleOrders != null)
                {
                    logger.LogDebug("Orders query added for role: {Role}", role);
                    result = result?.Union(roleOrders) ?? roleOrders;
                }
            }

            if (result != null)
            {
                logger.LogDebug("Accessible orders query generated successfully.");
                return result.Distinct();
            }
            else
            {
                logger.LogInformation("No orders accessible for user with UserProfileId: {UserProfileId}", userData.UserProfileId);
                return baseQuery.Where(x => false);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error while generating accessible orders for UserProfileId: {UserProfileId}", userData.UserProfileId);
            throw;
        }
    }
}