using System.Collections.Concurrent;
using EMessa.Base.Constants;
using EMessa.Core.Features.Translations.Commands.ReloadTranslations;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.ArticleValidators;
using EMessa.DAL.Entities.Translations;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Services
{
    public class TranslationManagerService(
        IMultiTranslationService multiTranslationService,
        IDbContextFactory<ApplicationDbContext> dbFactory,
        ILogger<TranslationManagerService> logger,
        IMediator mediator)
        : ITranslationManagerService
    {
        private static readonly ConcurrentQueue<string> _missingKeys = new();
        private static readonly HashSet<string> _uniqueKeys = [];
        private static readonly SemaphoreSlim _missingKeysSemaphore = new(1, 1);

        private static bool _autoTranslating;
        private const int BatchSize = 5;
        private const int DelayMilliseconds = 500;

        public void EnqueueMissingKey(string key)
        {
            if (string.IsNullOrWhiteSpace(key))
            {
                logger.LogWarning("Próbowano dodać pusty klucz '{Key}'.", key);
                return;
            }

            lock (_uniqueKeys)
            {
                if (_uniqueKeys.Add(key)) // Zapewniamy unikalność klucza
                {
                    _missingKeys.Enqueue(key);
                }
            }
        }

        public async Task ProcessMissingKeysAsync(CancellationToken cancellationToken)
        {
            if (_missingKeys.IsEmpty)
                return;

            await _missingKeysSemaphore.WaitAsync(cancellationToken);
            try
            {
                List<string> keysToProcess = [];
                while (_missingKeys.TryDequeue(out var key))
                {
                    keysToProcess.Add(key);
                }

                if (keysToProcess.Count > 0)
                {
                    await AddBaseTextAsync(keysToProcess, cancellationToken);
                }

                lock (_uniqueKeys)
                {
                    _uniqueKeys.Clear();
                }
            }
            finally
            {
                _missingKeysSemaphore.Release();
            }
        }

        private async Task AddBaseTextAsync(List<string> keys, CancellationToken cancellationToken)
        {
            try
            {
                await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

                var existingKeys = await dbContext.Translations
                    .Where(t => keys.Contains(t.BaseText))
                    .Select(t => t.BaseText)
                    .ToListAsync(cancellationToken);

                var keysToAdd = keys.Except(existingKeys).ToList();

                if (keysToAdd.Count == 0)
                {
                    return;
                }

                var newTranslations = keysToAdd.Select(key => new Translation(key));

                await dbContext.Translations.AddRangeAsync(newTranslations, cancellationToken);
                await dbContext.SaveChangesAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Błąd podczas zapisywania tekstów bazowych dla tłumaczeń: {Keys}.",
                    string.Join(", ", keys));
                return;
            }

            try
            {
                await mediator.Send(new ReloadTranslationsCommand(), cancellationToken);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Błąd podczas przeładowania translacji po dodaniu bazowych tłumaczeń.");
            }
        }

        public async Task UpdateTranslationsAsync()
        {
            // Pobierz listę wspieranych kodów języków
            var supportedLangCodeSet = GetSupportedLangCodes();
            if (supportedLangCodeSet == null)
                return;

            try
            {
                await using var dbContext = await dbFactory.CreateDbContextAsync();

                // Pobierz wszystkie istniejące translacje wraz z ich obecnymi wpisami
                var translations = await dbContext.Translations
                    .Include(t => t.TranslationEntries)
                    .ToListAsync();

                var entriesToAdd = new List<TranslationEntry>();

                // Iteruj przez każdą translację (BaseText)
                foreach (var translation in translations)
                {
                    // Stwórz zbiór istniejących kodów języków dla tej konkretnej translacji
                    var existingLangCodes = translation.TranslationEntries
                        .Select(te => te.LanguageCode)
                        .ToHashSet();

                    // Sprawdź każdy wspierany język
                    foreach (var supportedCode in supportedLangCodeSet)
                    {
                        // 5. Jeśli wpis dla danego języka nie istnieje, przygotuj go do dodania
                        if (!existingLangCodes.Contains(supportedCode))
                        {
                            if (supportedCode == "pl") // Dla języka pl dodaj tłumaczenie jako BaseText
                                entriesToAdd.Add(
                                    new TranslationEntry(translation.Id, supportedCode, translation.BaseText,
                                        translate: false, verify: true));
                            else
                                entriesToAdd.Add(
                                    new TranslationEntry(translation.Id, supportedCode));

                            logger.LogDebug(
                                "Przygotowano do dodania wpis dla TranslationId: {TranslationId} | Klucz: '{BaseText}' | Język: {LanguageCode}",
                                translation.Id, translation.BaseText, supportedCode);
                        }
                    }
                }

                // Jeśli znaleziono brakujące wpisy, dodaj je do bazy danych
                if (entriesToAdd.Count > 0)
                {
                    logger.LogInformation(
                        "Znaleziono {Count} brakujących wpisów tłumaczeń. Dodawanie do bazy danych...",
                        entriesToAdd.Count);

                    await dbContext.TranslationEntries.AddRangeAsync(entriesToAdd);

                    var affectedRows = await dbContext.SaveChangesAsync();

                    logger.LogInformation("Pomyślnie dodano {AffectedRows} nowych wpisów TranslationEntry.",
                        affectedRows);
                }
                else
                {
                    logger.LogInformation("Wszystkie translacje posiadają wpisy dla wszystkich wspieranych języków.");
                }
            }
            catch (DbUpdateException dbEx)
            {
                logger.LogError(dbEx, "Błąd podczas zapisu zmian do bazy danych. Możliwe naruszenie ograniczeń.");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Wystąpił nieoczekiwany błąd podczas aktualizacji tłumaczeń.");
            }

            logger.LogInformation("Zakończono synchronizację wpisów tłumaczeń dla języków: [ {Languages} ]",
                string.Join(", ", supportedLangCodeSet));
        }

        private HashSet<string>? GetSupportedLangCodes()
        {
            var supportedLangCodeSet = SystemConstants.SupportedCulturesList.ToHashSet();

            if (supportedLangCodeSet.Count != 0)
                return supportedLangCodeSet;

            logger.LogWarning(
                "Brak zdefiniowanych wspieranych języków w konfiguracji. Przerywam aktualizację tłumaczeń.");
            return null;
        }

        public async Task AutoTranslateAsync(CancellationToken cancellationToken)
        {
            if (_autoTranslating)
                return;

            await UpdateTranslationsAsync();

            _autoTranslating = true;

            await AutoTranslateUi(cancellationToken);

            await AutoTranslateArticleValidatorConditions(cancellationToken);

            _autoTranslating = false;
        }

        private async Task AutoTranslateUi(CancellationToken cancellationToken)
        {
            // Pobierz listę wspieranych kodów języków
            var supportedLangCodeSet = GetSupportedLangCodes();
            if (supportedLangCodeSet == null)
                return;

            try
            {
                await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

                // Pobierz wszystkie istniejące translacje gdzie brakuje tłumaczeń
                var translations = await dbContext
                    .Translations
                    .Where(t => t.TranslationEntries.Any(te =>
                        te.Translate
                        && (te.TranslatedText == null || te.TranslatedText.Trim() == string.Empty)
                        && supportedLangCodeSet.Contains(te.LanguageCode)))
                    .Select(t => new Translation
                    {
                        Id = t.Id,
                        BaseText = t.BaseText,
                        TranslationEntries = t.TranslationEntries
                            .Where(te =>
                                te.Translate
                                && (te.TranslatedText == null || te.TranslatedText.Trim() == string.Empty)
                                && supportedLangCodeSet.Contains(te.LanguageCode))
                            .ToList()
                    })
                    .OrderBy(t => t.Id)
                    .Take(BatchSize)
                    .ToListAsync(cancellationToken: cancellationToken);

                foreach (var translation in translations)
                {
                    if (cancellationToken.IsCancellationRequested)
                        return;

                    var languageCodes = translation.TranslationEntries.Select(te => te.LanguageCode).ToHashSet();

                    var autoTranslations = await multiTranslationService.Translate(translation.BaseText, languageCodes);
                    if (autoTranslations is { Succeeded: true, Data: var languageTranslations })
                    {
                        foreach (var languageTranslation in languageTranslations)
                        {
                            var translationEntry = translation.TranslationEntries
                                .SingleOrDefault(te => te.LanguageCode == languageTranslation.Key);
                            if (translationEntry is null)
                                continue;
                            translationEntry.TranslatedText = languageTranslation.Value;
                            translationEntry.Translate = false;
                            translationEntry.Verify = true;
                            dbContext.Update(translationEntry);
                        }
                    }

                    await Task.Delay(TimeSpan.FromMilliseconds(DelayMilliseconds), cancellationToken);
                }

                await dbContext.SaveChangesAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Wystąpił nieoczekiwany błąd podczas tłumaczenia UI.");
            }
        }

        private async Task AutoTranslateArticleValidatorConditions(CancellationToken cancellationToken)
        {
            var articleValidatorConditionTranslations =
                await GetArticleValidatorConditionTranslationsAsync(BatchSize, cancellationToken);
            
            if (articleValidatorConditionTranslations.Count == 0)
                return;

            try
            {
                foreach (var translation in articleValidatorConditionTranslations)
                {
                    if (cancellationToken.IsCancellationRequested)
                        return;

                    var errorTranslations = await multiTranslationService
                        .TranslateWithHtml(translation.ErrorMessage, translation.LanguageCodes, true);

                    if (!string.IsNullOrEmpty(translation.ErrorMessage))
                        await Task.Delay(TimeSpan.FromMilliseconds(DelayMilliseconds), cancellationToken);

                    if (!errorTranslations.Succeeded) continue;

                    var confirmTranslations = await multiTranslationService
                        .TranslateWithHtml(translation.ConfirmMessage, translation.LanguageCodes, true);

                    await UpdateArticleValidatorConditionTranslationsAsync(
                        translation,
                        errorTranslations.Data,
                        confirmTranslations.Data,
                        cancellationToken
                    );

                    if (!string.IsNullOrEmpty(translation.ConfirmMessage))
                        await Task.Delay(TimeSpan.FromMilliseconds(DelayMilliseconds), cancellationToken);
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Wystąpił nieoczekiwany błąd podczas tłumaczenia warunków walidatora artykułów.");
            }
        }

        private async Task<List<ArticleValidatorConditionTranslationDto>> GetArticleValidatorConditionTranslationsAsync(
            int count = 1,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var supportedLanguages = GetSupportedLangCodes();
                if (supportedLanguages == null || !supportedLanguages.Any())
                    return [];

                await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

                // Pobierz warunki wraz z ich tłumaczeniami w jednym zapytaniu
                var conditions = await dbContext
                    .ArticleValidatorConditions
                    .Where(c => !string.IsNullOrEmpty(c.ErrorMessage) || !string.IsNullOrEmpty(c.ConfirmMessage))
                    .Include(x => x.Translations)
                    .AsNoTracking()
                    .ToListAsync(cancellationToken);

                // Przygotuj wynik z brakującymi tłumaczeniami
                return conditions
                    .Select(c => CreateArticleValidatorConditionTranslationDto(c, supportedLanguages))
                    .Where(r => r.LanguageCodes.Count > 0) // Filtruj tylko te z brakującymi tłumaczeniami
                    .OrderBy(r => r.ArticleValidatorConditionId)
                    .Take(count)
                    .ToList();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Wystąpił błąd podczas pobierania warunków walidatora artykułów do tłumaczenia");
                return [];
            }
        }

        private ArticleValidatorConditionTranslationDto CreateArticleValidatorConditionTranslationDto(
            ArticleValidatorCondition condition,
            HashSet<string> supportedLanguages)
        {
            // Pobierz istniejące języki tłumaczeń dla tego warunku
            var existingLanguages = condition.Translations
                .Select(t => t.LanguageCode)
                .ToHashSet();

            // Znajdź brakujące języki
            var missingLanguages = supportedLanguages
                .Except(existingLanguages)
                .ToList();

            return new ArticleValidatorConditionTranslationDto
            {
                ArticleValidatorConditionId = condition.Id,
                ErrorMessage = condition.ErrorMessage,
                ConfirmMessage = condition.ConfirmMessage,
                LanguageCodes = missingLanguages
            };
        }

        internal class ArticleValidatorConditionTranslationDto
        {
            public int ArticleValidatorConditionId { get; set; }
            public string ErrorMessage { get; set; } = "";
            public string ConfirmMessage { get; set; } = "";
            public List<string> LanguageCodes { get; set; } = [];
        }

        private async Task UpdateArticleValidatorConditionTranslationsAsync(
            ArticleValidatorConditionTranslationDto translation,
            Dictionary<string, string> errorTranslations,
            Dictionary<string, string> confirmTranslations,
            CancellationToken cancellationToken)
        {
            try
            {
                await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

                foreach (var languageCode in translation.LanguageCodes)
                {
                    var errorTranslation = errorTranslations.GetValueOrDefault(languageCode, "");
                    var confirmTranslation = confirmTranslations.GetValueOrDefault(languageCode, "");

                    var existingTranslation = await dbContext
                        .ArticleValidatorConditionTranslations
                        .FirstOrDefaultAsync(x =>
                            x.ArticleValidatorConditionId == translation.ArticleValidatorConditionId
                            && x.LanguageCode == languageCode, cancellationToken);

                    if (existingTranslation == null)
                    {
                        dbContext.ArticleValidatorConditionTranslations.Add(new ArticleValidatorConditionTranslation
                        {
                            ArticleValidatorConditionId = translation.ArticleValidatorConditionId,
                            LanguageCode = languageCode,
                            ErrorMessage = errorTranslation,
                            ConfirmMessage = confirmTranslation
                        });
                    }
                    else
                    {
                        existingTranslation.ErrorMessage = errorTranslation;
                        existingTranslation.ConfirmMessage = confirmTranslation;
                        dbContext.ArticleValidatorConditionTranslations.Update(existingTranslation);
                    }
                }

                await dbContext.SaveChangesAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Błąd podczas aktualizacji tłumaczeń warunków walidatora artykułu");
            }
        }
    }
}