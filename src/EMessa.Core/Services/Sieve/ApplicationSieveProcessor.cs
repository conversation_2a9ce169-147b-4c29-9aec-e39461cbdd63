using EMessa.Core.Features.ArticleOptionValuesRestrictions.Queries.GetArticleOptionsRestrictionsBySieve;
using EMessa.Core.Features.ArticleOptionValuesRestrictions.Queries.GetOptionsRestrictionsForEdit;
using EMessa.Core.Features.Articles.Queries.GetAllBySieve;
using EMessa.Core.Features.ArticleValidators.Queries.GetAllArticleValidatorCondition;
using EMessa.Core.Features.Branches.Queries.GetAll;
using EMessa.Core.Features.Configurations.Queries.GetAll;
using EMessa.Core.Features.CustomerGroups.Queries.GetAll;
using EMessa.Core.Features.Customers.Queries.GetAll;
using EMessa.Core.Features.Factories.Queries.GetAll;
using EMessa.Core.Features.Messages.Queries.GetAll;
using EMessa.Core.Features.Notifications.Queries.GetConfirmations;
using EMessa.Core.Features.Orders.Queries.GetAllOrders;
using EMessa.Core.Features.Pages.Queries.GetAll;
using EMessa.Core.Features.Sales.Queries.CommonResponses;
using EMessa.Core.Features.Sales.Queries.GetAllSales;
using EMessa.Core.Features.Sales.Queries.GetSaleArticles;
using EMessa.Core.Features.Sales.Queries.GetSaleOrderItems;
using EMessa.Core.Features.Sales.Queries.GetSaleRolls;
using EMessa.Core.Features.Translations.Queries.GetAllTranslationEntries;
using EMessa.Core.Features.Users.Queries.GetAllSieve;
using EMessa.Core.Models;
using EMessa.DAL.Entities.Articles;
using EMessa.DAL.Entities.ArticleValidators;
using EMessa.DAL.Entities.Branches;
using EMessa.DAL.Entities.Configurations;
using EMessa.DAL.Entities.Countries;
using EMessa.DAL.Entities.Customers;
using EMessa.DAL.Entities.Factories;
using EMessa.DAL.Entities.Messages;
using EMessa.DAL.Entities.Notifications;
using EMessa.DAL.Entities.Pages;
using EMessa.DAL.Entities.Sales;
using EMessa.DAL.Entities.Translations;
using Microsoft.Extensions.Options;
using Sieve.Models;
using Sieve.Services;
using System.Reflection;

namespace EMessa.Core.Services.Sieve;

public class ApplicationSieveProcessor(
    IOptions<SieveOptions> options,
    ISieveCustomSortMethods sieveCustomSort,
    ISieveCustomFilterMethods sieveCustomFilter)
    : SieveProcessor(options, sieveCustomSort, sieveCustomFilter)
{
    protected override SievePropertyMapper MapProperties(SievePropertyMapper mapper)
    {
        var configTypes = Assembly.GetExecutingAssembly()
            .GetTypes()
            .Where(t => t is { IsClass: true, IsAbstract: false } && typeof(ISieveConfiguration).IsAssignableFrom(t));

        foreach (var type in configTypes)
        {
            if (Activator.CreateInstance(type) is ISieveConfiguration configInstance)
            {
                configInstance.Configure(mapper);
            }
        }

        return mapper;
    }
}

public partial class SieveCustomFilterMethods : ISieveCustomFilterMethods
{
    // Nazwa tej metody będzie nazwą filtra w SieveModel
    public IQueryable<TranslationEntry> GlobalSearch(
        IQueryable<TranslationEntry> source, string op, string[] values) =>
            TranslationEntriesSieveConfiguration.GlobalSearchFilter(source, op, values);

    public IQueryable<Factory> GlobalSearch(
        IQueryable<Factory> source, string op, string[] values) =>
            FactoriesSieveConfiguration.GlobalSearchFilter(source, op, values);

    public IQueryable<Branch> GlobalSearch(
        IQueryable<Branch> source, string op, string[] values) =>
            BranchesSieveConfiguration.GlobalSearchFilter(source, op, values);

    public IQueryable<Customer> GlobalSearch(
        IQueryable<Customer> source, string op, string[] values) =>
            CustomersSieveConfiguration.GlobalSearchFilter(source, op, values);

    public IQueryable<Message> GlobalSearch(
        IQueryable<Message> source, string op, string[] values) =>
            MessagesSieveConfiguration.GlobalSearchFilter(source, op, values);

    public IQueryable<Country> GlobalSearch(
        IQueryable<Country> source, string op, string[] values) =>
            CountriesSieveConfiguration.GlobalSearchFilter(source, op, values);

    public IQueryable<ApplicationUserProfileWithRoles> GlobalSearch(
        IQueryable<ApplicationUserProfileWithRoles> source, string op, string[] values) =>
            GetAllUsersSieveConfiguration.GlobalSearchFilter(source, op, values);

    public IQueryable<ApplicationUserProfileWithRoles> Locations(
        IQueryable<ApplicationUserProfileWithRoles> source, string op, string[] values) =>
            GetAllUsersSieveConfiguration.LocationsFilter(source, op, values);

    public IQueryable<ApplicationUserProfileWithRoles> Branches(
        IQueryable<ApplicationUserProfileWithRoles> source, string op, string[] values) =>
            GetAllUsersSieveConfiguration.BranchesFilter(source, op, values);

    public IQueryable<ApplicationUserProfileWithRoles> Roles(
        IQueryable<ApplicationUserProfileWithRoles> source, string op, string[] values) =>
            GetAllUsersSieveConfiguration.RolesFilter(source, op, values);

    public IQueryable<ArticleValidatorCondition> GlobalSearch(
        IQueryable<ArticleValidatorCondition> source, string op, string[] values) =>
            GetAllArticleValidatorConditionSieve.GlobalSearchFilter(source, op, values);

    public IQueryable<Notification> GlobalSearch(
        IQueryable<Notification> source, string op, string[] values) =>
            NotificationsSieveConfiguration.GlobalSearchFilter(source, op, values);

    public IQueryable<Notification> CreatedBy(
        IQueryable<Notification> source, string op, string[] values) =>
            NotificationsSieveConfiguration.CreatedByFilter(source, op, values);

    public IQueryable<Page> GlobalSearch(
        IQueryable<Page> source, string op, string[] values) =>
        PagesSieveConfiguration.GlobalSearchFilter(source, op, values);

    public IQueryable<Page> CreatedBy(
        IQueryable<Page> source, string op, string[] values) =>
            PagesSieveConfiguration.CreatedByFilter(source, op, values);

    public IQueryable<GetNotificationConfirmationsResponse> GlobalSearch(
        IQueryable<GetNotificationConfirmationsResponse> source, string op, string[] values) =>
            NotificationConfirmationsSieveConfiguration.GlobalSearchFilter(source, op, values);

    public IQueryable<Configuration> GlobalSearch(
        IQueryable<Configuration> source, string op, string[] values) =>
            ConfigurationsSieveConfiguration.GlobalSearchFilter(source, op, values);

    public IQueryable<Sale> GlobalSearch(
        IQueryable<Sale> source, string op, string[] values) =>
            SalesSieveConfiguration.GlobalSearchFilter(source, op, values);

    public IQueryable<Sale> DateFromToActive(
        IQueryable<Sale> source, string op, string[] values) =>
            SalesSieveConfiguration.DateFromToActiveFilter(source, op, values);

    public IQueryable<Sale> SaleSateInFrom(
        IQueryable<Sale> source, string op, string[] values) =>
            SalesSieveConfiguration.SaleSateInFromFilter(source, op, values);

    public IQueryable<Sale> SaleDateInTo(
        IQueryable<Sale> source, string op, string[] values) =>
            SalesSieveConfiguration.SaleDateInToFilter(source, op, values);

    public IQueryable<Sale> DateFrom(
        IQueryable<Sale> source, string op, string[] values) =>
            SalesSieveConfiguration.DateFromFilter(source, op, values);

    public IQueryable<Sale> DateTo(
        IQueryable<Sale> source, string op, string[] values) =>
            SalesSieveConfiguration.DateToFilter(source, op, values);

    public IQueryable<SaleRollResponse> GlobalSearch(
        IQueryable<SaleRollResponse> source, string op, string[] values) =>
            SaleRollsSieveConfiguration.GlobalSearchFilter(source, op, values);

    public IQueryable<Article> GlobalSearch(
        IQueryable<Article> source, string op, string[] values) =>
            ArticlesSieveConfiguration.GlobalSearchFilter(source, op, values);

    public IQueryable<ArticleResponseForSale> GlobalSearch(
        IQueryable<ArticleResponseForSale> source, string op, string[] values) =>
            SaleArticleSieveConfiguration.GlobalSearchFilter(source, op, values);

    public IQueryable<GetAllCustomerGroupsResponse> GlobalSearch(
        IQueryable<GetAllCustomerGroupsResponse> source, string op, string[] values) =>
            CustomerGroupsSieveConfiguration.GlobalSearchFilter(source, op, values);

    public IQueryable<EditArticleOptionRestrictionResponse> GlobalSearch(
        IQueryable<EditArticleOptionRestrictionResponse> source, string op, string[] values) =>
            ArticleOptionsRestrictionsSieveConfiguration.GlobalSearchFilter(source, op, values);

    public IQueryable<OrderItemResponseForSale> GlobalSearch(
        IQueryable<OrderItemResponseForSale> source, string op, string[] values) =>
            GetSaleOrderItemSieveConfiguration.GlobalSearchFilter(source, op, values);

    public IQueryable<GetAllOrdersResponse> GlobalSearch(
        IQueryable<GetAllOrdersResponse> source, string op, string[] values) =>
            OrdersSieveConfiguration.GlobalSearchFilter(source, op, values);

    public IQueryable<GetAllOrdersResponse> CreatedDate(
        IQueryable<GetAllOrdersResponse> source, string op, string[] values) =>
            OrdersSieveConfiguration.CreatedDateFilter(source, op, values);
}

public class SieveCustomSortMethods : ISieveCustomSortMethods
{
    // Nazwa tej metody będzie nazwą sortowania w SieveModel
}