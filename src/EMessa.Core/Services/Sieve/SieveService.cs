using AutoMapper;
using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Sieve.Models;
using Sieve.Services;

namespace EMessa.Core.Services.Sieve;

public class SieveService(
    IMapper mapper,
    ISieveProcessor sieveProcessor,
    ILogger<SieveService> logger)
    : ISieveService
{
    public async Task<ListResult<TResponse>> ExecuteSieveAsync<TEntity, TResponse>(
        SieveModel sieveModel,
        IQueryable<TEntity> entities,
        CancellationToken cancellationToken)
    {
        try
        {
            var filteredEntities = await sieveProcessor
                .Apply(sieveModel, entities)
                .ToListAsync(cancellationToken: cancellationToken);

            var totalCount = await sieveProcessor
                .Apply(sieveModel, entities, applyPagination: false, applySorting: false)
                .CountAsync(cancellationToken: cancellationToken);

            var response = mapper.Map<List<TResponse>>(filteredEntities);

            return ListResult<TResponse>.Success(response, totalCount);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Błąd podczas operacji Sieve na kolekcji encji typu {EntityType}", typeof(TEntity).Name);
            return ListResult<TResponse>.Failure(ResultErrorMessages.UnexpectedErrorWithMessage(ex.Message));
        }
    }

    public ListResult<TResponse> ExecuteSieve<TEntity, TResponse>(
        SieveModel sieveModel,
        IQueryable<TEntity> entities,
        CancellationToken cancellationToken)
    {
        try
        {
            var filteredEntities = sieveProcessor
                .Apply(sieveModel, entities)
                .ToList();

            var totalCount = sieveProcessor
                .Apply(sieveModel, entities, applyPagination: false, applySorting: false)
                .Count();

            var response = mapper.Map<List<TResponse>>(filteredEntities);

            return ListResult<TResponse>.Success(response, totalCount);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Błąd podczas operacji Sieve na kolekcji encji typu {EntityType}", typeof(TEntity).Name);
            return ListResult<TResponse>.Failure(ResultErrorMessages.UnexpectedErrorWithMessage(ex.Message));
        }
    }
}
