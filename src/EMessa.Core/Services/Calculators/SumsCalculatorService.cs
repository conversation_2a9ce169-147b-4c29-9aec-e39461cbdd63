using EMessa.DAL.Entities.Orders;
using EMessa.Core.Features.ShoppingCart.Models;
using EMessa.Base.Constants;
using EMessa.Core.Common;
using EMessa.Core.Localizer;
using EMessa.DAL.Entities;

namespace EMessa.Core.Services.Calculators;

/// <summary>
/// Calculates sums of article for own products, sheet line fuirst or all sum first
/// </summary>
public interface ISumsCalculatorService
{
    public AbstractOrderItemEditModel RecalculateOrderItem(AbstractOrderItemEditModel item, decimal articleUnitWeight,
        string articleUnit);

    public OrderItem RecalculateOrderItem(OrderItem orderItem, decimal articleUnitWeight, string articleUnit);
    public void Sum(List<CalculatingSumSheet> sheets, out decimal sumMb, out decimal sumM2, out int sumQuantity);

    public void Sum(List<AbstractArticleSheet> sheets, decimal articleUnitWeight, string articleUnit, out decimal sumMb,
        out decimal sumM2, out int sumQuantity, out decimal weight);
}

public abstract class AbstractSumsCalculator
{
    protected decimal RecalculateWeight(decimal sumMb, decimal sumM2, int sumQuantity, string articleUnit,
        decimal articleUnitWeight)
    {
        var weight = articleUnit.ToLower() switch
        {
            UnitCode.Mb => SystemRound.RoundingSums(articleUnitWeight * sumMb),
            UnitCode.M2 => SystemRound.RoundingSums(articleUnitWeight * sumM2),
            UnitCode.Szt => SystemRound.RoundingWeight(articleUnitWeight * sumQuantity),
            _ => throw new ArgumentOutOfRangeException(
                nameof(articleUnit),
                string.IsNullOrEmpty(articleUnit) ? "Brak jednostki miary".Tr() : articleUnit,
                "Nieobsługiwana jednostka miary".Tr())
        };
        return weight;
    }
}

/// <summary>
/// Sumowanie i zaokrąglanie do n2 linii wymiarów a potem sumowanie tych wartości i zaokrąglenie do n2
/// </summary>
public class SumsCalculatorLineFirstService : AbstractSumsCalculator, ISumsCalculatorService
{
    public AbstractOrderItemEditModel RecalculateOrderItem(
        AbstractOrderItemEditModel orderItem,
        decimal articleUnitWeight,
        string articleUnit)
    {
        var sheets = orderItem.RequestedSheets.SelectMany(x => x.OrderItemSheets).ToList();

        orderItem.SumQuantity = sheets.Sum(x => x.Quantity);
        // było orderItem.SumMb = SystemRound.RoundingSums(sheets.Sum(x => SystemRound.RoundingSums(x.Quantity * x.Length)));
        orderItem.SumMb = SystemRound.RoundingSums(sheets.Sum(x => x.Quantity * x.Length));
        // było orderItem.SumM2 = SystemRound.RoundingSums(sheets.Sum(x => SystemRound.RoundingSums(x.Quantity * x.Length * x.Width)));
        orderItem.SumM2 = SystemRound.RoundingSums(sheets.Sum(x => x.Quantity * x.Length * x.Width));
        orderItem.Weight = RecalculateWeight(orderItem.SumMb, orderItem.SumM2, orderItem.SumQuantity, articleUnit,
            articleUnitWeight);

        return orderItem;
    }

    public OrderItem RecalculateOrderItem(
        OrderItem orderItem,
        decimal articleUnitWeight,
        string articleUnit)
    {
        var sheets = orderItem.RequestedSheets.SelectMany(x => x.OrderItemSheets).ToList();

        orderItem.SumQuantity = sheets.Sum(x => x.Quantity);
        // było orderItem.SumMb = SystemRound.RoundingSums(sheets.Sum(x => SystemRound.RoundingSums(x.Quantity * x.Length)));
        orderItem.SumMb = SystemRound.RoundingSums(sheets.Sum(x => x.Quantity * x.Length));
        // było orderItem.SumM2 = SystemRound.RoundingSums(sheets.Sum(x => SystemRound.RoundingSums(x.Quantity * x.Length * x.Width)));
        orderItem.SumM2 = SystemRound.RoundingSums(sheets.Sum(x => x.Quantity * x.Length * x.Width));
        orderItem.Weight = RecalculateWeight(orderItem.SumMb, orderItem.SumM2, orderItem.SumQuantity, articleUnit,
            articleUnitWeight);

        return orderItem;
    }

    public void Sum(List<CalculatingSumSheet> sheets, out decimal sumMb, out decimal sumM2, out int sumQuantity)
    {
        sumQuantity = sheets.Sum(x => x.Quantity);
        // było orderItem.SumMb = SystemRound.RoundingSums(sheets.Sum(x => SystemRound.RoundingSums(x.Quantity * x.Length)));
        sumMb = SystemRound.RoundingSums(sheets.Sum(x => x.Quantity * x.Length));
        // było orderItem.SumM2 = SystemRound.RoundingSums(sheets.Sum(x => SystemRound.RoundingSums(x.Quantity * x.Length * x.Width)));
        sumM2 = SystemRound.RoundingSums(sheets.Sum(x => x.Quantity * x.Length * x.Width));
    }

    public void Sum(
        List<AbstractArticleSheet> sheets,
        decimal articleUnitWeight,
        string articleUnit,
        out decimal sumMb,
        out decimal sumM2,
        out int sumQuantity,
        out decimal weight)
    {
        sumQuantity = sheets.Sum(x => x.Quantity);
        // było RoundOfRound orderItem.SumMb = SystemRound.RoundingSums(sheets.Sum(x => SystemRound.RoundingSums(x.Quantity * x.Length)));
        sumMb = SystemRound.RoundingSums(sheets.Sum(x => x.Quantity * x.Length));
        // było RoundOfRound orderItem.SumM2 = SystemRound.RoundingSums(sheets.Sum(x => SystemRound.RoundingSums(x.Quantity * x.Length * x.Width)));
        sumM2 = SystemRound.RoundingSums(sheets.Sum(x => x.Quantity * x.Length * x.Width));
        weight = RecalculateWeight(sumMb, sumM2, sumQuantity, articleUnit, articleUnitWeight);
    }
}

/// <summary>
/// Sumowanie wszystkich wymiarów i potem zaokrąglenie do n2
/// </summary>
public class SumCalculatorSumFirstService : AbstractSumsCalculator, ISumsCalculatorService
{
    public AbstractOrderItemEditModel RecalculateOrderItem(
        AbstractOrderItemEditModel orderItem,
        decimal articleUnitWeight,
        string articleUnit)
    {
        var orderItemSheets = orderItem.RequestedSheets.SelectMany(x => x.OrderItemSheets).ToList();

        orderItem.SumQuantity = orderItemSheets.Sum(x => x.Quantity);
        orderItem.SumMb = Math.Round(
            orderItemSheets.Sum(x => x.Quantity * x.Length),
            SystemConstants.RoundingSumsDecimals,
            MidpointRounding.AwayFromZero);
        orderItem.SumM2 = Math.Round(
            orderItemSheets.Sum(x => x.Quantity * x.Length * x.Width),
            SystemConstants.RoundingSumsDecimals,
            MidpointRounding.AwayFromZero);

        orderItem.Weight = RecalculateWeight(orderItem.SumMb, orderItem.SumM2, orderItem.SumQuantity, articleUnit,
            articleUnitWeight);

        return orderItem;
    }

    public OrderItem RecalculateOrderItem(OrderItem orderItem, decimal articleUnitWeight, string articleUnit)
    {
        var orderItemSheets = orderItem.RequestedSheets.SelectMany(x => x.OrderItemSheets).ToList();

        orderItem.SumQuantity = orderItemSheets.Sum(x => x.Quantity);
        orderItem.SumMb = Math.Round(
            orderItemSheets.Sum(x => x.Quantity * x.Length),
            SystemConstants.RoundingSumsDecimals,
            MidpointRounding.AwayFromZero);
        orderItem.SumM2 = Math.Round(
            orderItemSheets.Sum(x => x.Quantity * x.Length * x.Width),
            SystemConstants.RoundingSumsDecimals,
            MidpointRounding.AwayFromZero);

        orderItem.Weight = RecalculateWeight(orderItem.SumMb, orderItem.SumM2, orderItem.SumQuantity, articleUnit,
            articleUnitWeight);

        return orderItem;
    }

    public void Sum(
        List<CalculatingSumSheet> sheets,
        out decimal sumMb,
        out decimal sumM2,
        out int sumQuantity)
    {
        sumQuantity = sheets.Sum(x => x.Quantity);
        sumMb = Math.Round(
            sheets.Sum(x => x.Quantity * x.Length),
            SystemConstants.RoundingSumsDecimals,
            MidpointRounding.AwayFromZero);
        sumM2 = Math.Round(
            sheets.Sum(x => x.Quantity * x.Length * x.Width),
            SystemConstants.RoundingSumsDecimals,
            MidpointRounding.AwayFromZero);
    }

    public void Sum(
        List<AbstractArticleSheet> sheets,
        decimal articleUnitWeight,
        string articleUnit,
        out decimal sumMb,
        out decimal sumM2,
        out int sumQuantity,
        out decimal weight)
    {
        sumQuantity = sheets.Sum(x => x.Quantity);
        sumMb = Math.Round(
            sheets.Sum(x => x.Quantity * x.Length),
            SystemConstants.RoundingSumsDecimals,
            MidpointRounding.AwayFromZero);
        sumM2 = Math.Round(
            sheets.Sum(x => x.Quantity * x.Length * x.Width),
            SystemConstants.RoundingSumsDecimals,
            MidpointRounding.AwayFromZero);

        weight = RecalculateWeight(sumMb, sumM2, sumQuantity, articleUnit, articleUnitWeight);
    }
}

public class CalculatingSumSheet
{
    public int Quantity { get; set; }
    public decimal Length { get; set; }
    public decimal Width { get; set; }
}