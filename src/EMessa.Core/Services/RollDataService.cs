using EMessa.Base.Enums;
using EMessa.Core.Features.Sales.Queries.GetSaleRolls;
using EMessa.Core.Interfaces;
using EMessa.Core.Models;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace EMessa.Core.Services;

public class RollDataService(
    ILogger<RollDataService> logger,
    HttpClient httpClient)
    : IRollDataService
{
    public async Task<IEnumerable<Roll>> GetRollsByIdsAsync(IEnumerable<long> rollIds, CancellationToken cancellationToken)
    {
        EnsureBaseAddress();
        var response = await httpClient.PostAsJsonAsync("api/rolls/get/id/list", rollIds, cancellationToken);
        return await ProcessResponse<IEnumerable<Roll>>(response, cancellationToken, "Failed to deserialize rolls from response.");
    }

    public async Task<IEnumerable<Roll>> GetRollsByNosAsync(IEnumerable<int> rollNos, CancellationToken cancellationToken)
    {
        EnsureBaseAddress();
        var response = await httpClient.PostAsJsonAsync("api/rolls/get/no/list", rollNos, cancellationToken);
        return await ProcessResponse<IEnumerable<Roll>>(response, cancellationToken, "Failed to deserialize rolls from response.");
    }

    public async Task<Roll> GetRollByNoAsync(int rollNo, CancellationToken cancellationToken)
    {
        EnsureBaseAddress();
        var response = await httpClient.GetAsync($"api/rolls/get/no/{rollNo}", cancellationToken);
        var roll = await ProcessResponse<Roll>(response, cancellationToken, "Failed to deserialize roll from response.");
        return roll;
    }

    public async Task<IEnumerable<Roll>> GetRollsByOptionsAsync(
        string colorCode,
        string coatCode,
        string thickCode,
        CancellationToken cancellationToken,
        RollStatus maxRollStatus = RollStatus.Active)
    {
        EnsureBaseAddress();

        var request = new
        {
            ColorCode = colorCode,
            CoatCode = coatCode,
            Thick = thickCode,
            Status = maxRollStatus
        };

        var response = await httpClient.PostAsJsonAsync("api/rolls/find", request, cancellationToken);
        return await ProcessResponse<IEnumerable<Roll>>(response, cancellationToken, "Failed to deserialize rolls from response.");
    }

    public async Task UpdateRollStatusAsync(List<SaleRollResponse> saleRolls, CancellationToken cancellationToken)
    {
        if (saleRolls.Count == 0)
            return;

        var rollIds = saleRolls.Select(s => s.RollId).ToList();
        var rolls = await GetRollsByIdsAsync(rollIds, cancellationToken);
        var rollDictionary = rolls.ToDictionary(r => r.RollId);

        foreach (var saleRoll in saleRolls)
        {
            if (rollDictionary.TryGetValue(saleRoll.RollId, out var roll))
            {
                saleRoll.Status = roll.Status; // same 
                saleRoll.Weight = roll.Weight; // same
                saleRoll.CurrentWeight = roll.CurrentWeight;
                saleRoll.Efficiency = roll.Efficiency;
            }
            else
            {
                logger.LogWarning("Roll with ID {RollId} not found in the fetched rolls.", saleRoll.RollId);
            }
        }
    }

    private async Task<T> ProcessResponse<T>(
        HttpResponseMessage response,
        CancellationToken cancellationToken,
        string errorMessage)
    {
        try
        {
            response.EnsureSuccessStatusCode();
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            var result = JsonSerializer.Deserialize<T>(responseContent, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

            if (result is null)
            {
                logger.LogError("{ErrorMessage}: {ResponseContent}", errorMessage, responseContent);
                throw new InvalidOperationException(errorMessage);
            }

            return result;
        }
        catch (JsonException ex)
        {
            logger.LogError(ex, "JSON deserialization failed. {ErrorMessage}", ex.Message);
            throw new InvalidOperationException("Failed to deserialize response.", ex);
        }
        catch (HttpRequestException ex)
        {
            logger.LogError(ex, "HTTP request failed. {ErrorMessage}", ex.Message);
            throw;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            throw;
        }
    }

    private void EnsureBaseAddress()
    {
        if (httpClient.BaseAddress is null)
        {
            logger.LogError("{Service} | HttpClient BaseAddress is not set.", nameof(RollDataService));
            throw new InvalidOperationException("HttpClient BaseAddress is not set.");
        }
    }
}