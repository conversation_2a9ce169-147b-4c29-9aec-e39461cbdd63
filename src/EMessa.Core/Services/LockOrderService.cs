using AutoMapper;
using EMessa.Core.Common.Results;
using EMessa.Core.Features.Orders.Queries.Common;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Orders;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Services;

public interface ILockOrderService
{
    Task<IResult<LockOrderResponse?>> TryLockOrderAsync(
        int orderId, int userId, CancellationToken cancellationToken = default);
    Task<IResult<LockOrderResponse?>> TryUnlockOrderLockAsync(int orderId, int userId,
        CancellationToken cancellationToken = default);
    Task<IResult<LockOrderResponse?>> GetOrderLockAsync(int orderId, int userId,
        CancellationToken cancellationToken = default);
    Task<IResult<LockOrderResponse?>> GetUserLockAsync(int userId, CancellationToken cancellationToken = default);
    Task<IResult> UnlockUserLocksAsync(int userId, CancellationToken cancellationToken = default);
    Task<IResult<LockOrderResponse?>> TryToggleLockOrderAsync(
        int orderId, int userId, CancellationToken cancellationToken = default);
}

//NOTE Timeout cleanup - może warto mieć job, który czyści expired locks:
// // Background service lub Hangfire job
// await dbContext.LockOrders
//  .Where(x => x.ExpiresAt < DateTime.UtcNow)
//  .ExecuteDeleteAsync();

public class LockOrderService(
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IOrderNotificationService orderNotificationService,
    IMapper mapper,
    ILogger<LockOrderService> logger) : ILockOrderService
{
    private const int LockTimeoutMinutes = 10;

    public async Task<IResult<LockOrderResponse?>> TryLockOrderAsync(int orderId, int userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            // Check if a lock already exists for this order
            var existingLock = await dbContext.LockOrders
                .Include(x => x.LockedBy)
                .Include(x => x.Order)
                .FirstOrDefaultAsync(x => x.OrderId == orderId, cancellationToken);

            if (existingLock != null)
            {
                var lockResponse = mapper.Map<LockOrderResponse>(existingLock);
                lockResponse.IsLockedByOtherUser = existingLock.LockedByUserProfileId != userId;

                var message = lockResponse.IsLockedByOtherUser
                    ? $"Edycja zamówienia jest zablokowana przez użytkownika: {lockResponse.LockedBy}"
                    : "Masz już zablokowaną edycję tego zamówienia.";

                return await Result<LockOrderResponse?>.SuccessAsync(lockResponse, message);
            }

            // Check if the user has another order locked
            var existingUserLock = await dbContext.LockOrders
                .Include(x => x.Order)
                .FirstOrDefaultAsync(x => x.LockedByUserProfileId == userId, cancellationToken);

            if (existingUserLock != null)
            {
                var userLockResponse = mapper.Map<LockOrderResponse>(existingUserLock);
                return await Result<LockOrderResponse?>.SuccessAsync(
                    userLockResponse,
                    $"Masz już zablokowaną edycję innego zamówienia: {userLockResponse.OrderNo}");
            }

            // Create a new lock
            var lockOrder = new LockOrder
            {
                OrderId = orderId,
                LockedByUserProfileId = userId,
                ExpiresAt = DateTime.UtcNow.AddMinutes(LockTimeoutMinutes)
            };

            dbContext.LockOrders.Add(lockOrder);
            await dbContext.SaveChangesAsync(cancellationToken);

            await orderNotificationService.NotifyOrderChangedAsync(orderId);

            var createdLock = await dbContext.LockOrders
                .Include(x => x.LockedBy)
                .Include(x => x.Order)
                .FirstAsync(x => x.Id == lockOrder.Id, cancellationToken);

            var createdLockResponse = mapper.Map<LockOrderResponse>(createdLock);
            return await Result<LockOrderResponse?>.SuccessAsync(createdLockResponse, "Zablokowano edycję zamówienia");
        }
        catch (DbUpdateException ex) when (ex.InnerException?.Message.Contains("IX_LockOrders") == true)
        {
            var existingLockResult = await GetOrderLockAsync(orderId, userId, cancellationToken);
            if (existingLockResult is { Succeeded: true, Data: not null })
            {
                var existingLock = existingLockResult.Data;
                existingLock.IsLockedByOtherUser = existingLock.LockedByUserProfileId != userId;
                return await Result<LockOrderResponse?>.SuccessAsync(
                    existingLock,
                    "Zamówienie zostało zablokowane przez innego użytkownika");
            }

            return await Result<LockOrderResponse?>.FailAsync("Wystąpił błąd podczas blokowania edycji zamówienia");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Wystąpił błąd podczas blokowania edycji zamówienia");
            return await Result<LockOrderResponse?>.FailAsync("Wystąpił błąd podczas blokowania edycji zamówienia");
        }
    }

    public async Task<IResult<LockOrderResponse?>> TryUnlockOrderLockAsync(int orderId, int userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var existingLock = await dbContext.LockOrders
                .Include(x => x.LockedBy)
                .Include(x => x.Order)
                .FirstOrDefaultAsync(x => x.OrderId == orderId, cancellationToken);

            if (existingLock == null)
            {
                // No lock found for this order, which is a successful state for unlocking.
                return await Result<LockOrderResponse?>.SuccessAsync((LockOrderResponse?)null,
                    "Nie znaleziono blokady do usunięcia.");
            }

            // Check if the lock belongs to the current user
            if (existingLock.LockedByUserProfileId == userId)
            {
                // It's the user's lock, so remove it
                dbContext.LockOrders.Remove(existingLock);
                await dbContext.SaveChangesAsync(cancellationToken);

                // Notify that the order is now available
                await orderNotificationService.NotifyOrderChangedAsync(orderId);

                return await Result<LockOrderResponse?>.SuccessAsync(null, "Odblokowano edycję zamówienia.");
            }

            // The lock belongs to someone else, return the lock info
            var response = mapper.Map<LockOrderResponse>(existingLock);
            response.IsLockedByOtherUser = true;

            return await Result<LockOrderResponse?>.SuccessAsync(
                response,
                $"Edycja zamówienia jest zablokowana przez innego użytkownika: {response.LockedBy}");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Wystąpił błąd podczas odblokowywania edycji zamówienia");
            return await Result<LockOrderResponse?>.FailAsync(
                "Wystąpił błąd podczas odblokowywania edycji zamówienia.");
        }
    }

    public async Task<IResult<LockOrderResponse?>> GetOrderLockAsync(int orderId, int userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var lockOrder = await dbContext.LockOrders
                .Include(x => x.LockedBy)
                .Include(x => x.Order)
                .FirstOrDefaultAsync(x => x.OrderId == orderId, cancellationToken);

            if (lockOrder == null)
            {
                return await Result<LockOrderResponse?>.SuccessAsync((LockOrderResponse?)null);
            }

            var lockResponse = mapper.Map<LockOrderResponse>(lockOrder);
            lockResponse.IsLockedByOtherUser = lockOrder.LockedByUserProfileId != userId;

            return await Result<LockOrderResponse?>.SuccessAsync(lockResponse);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Wystąpił błąd podczas pobierania blokady zamówienia");
            return await Result<LockOrderResponse?>.FailAsync("Wystąpił błąd podczas pobierania blokady zamówienia.");
        }
    }

    public async Task<IResult<LockOrderResponse?>> GetUserLockAsync(int userId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var lockOrder = await dbContext.LockOrders
                .Include(x => x.LockedBy)
                .Include(x => x.Order)
                .FirstOrDefaultAsync(x => x.LockedByUserProfileId == userId, cancellationToken);

            if (lockOrder == null)
            {
                return await Result<LockOrderResponse?>.SuccessAsync((LockOrderResponse?)null);
            }

            var lockResponse = mapper.Map<LockOrderResponse>(lockOrder);

            return await Result<LockOrderResponse?>.SuccessAsync(lockResponse);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Wystąpił błąd podczas pobierania blokady użytkownika");
            return await Result<LockOrderResponse?>.FailAsync("Wystąpił błąd podczas pobierania blokady użytkownika.");
        }
    }

    public async Task<IResult> UnlockUserLocksAsync(int userId, CancellationToken cancellationToken = default)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var locksToRemove = await dbContext.LockOrders
                .Where(x => x.LockedByUserProfileId == userId)
                .Select(x => x.OrderId)
                .ToListAsync(cancellationToken);

            if (locksToRemove.Count == 0)
            {
                return await Result.SuccessAsync("Nie znaleziono blokad do usunięcia");
            }

            await dbContext.LockOrders
                .Where(x => x.LockedByUserProfileId == userId)
                .ExecuteDeleteAsync(cancellationToken);

            foreach (var orderId in locksToRemove)
            {
                await orderNotificationService.NotifyOrderChangedAsync(orderId);
            }

            return await Result.SuccessAsync("Odblokowano edycję zamówienia");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Wystąpił błąd podczas usuwania blokad edycji zamówienia");
            return await Result.FailAsync("Wystąpił błąd podczas usuwania blokady edycji zamówienia");
        }
    }

    public async Task<IResult<LockOrderResponse?>> TryToggleLockOrderAsync(
        int orderId, int userId, CancellationToken cancellationToken = default)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            // Sprawdź czy istnieje lock na tym orderze
            var existingLock = await dbContext.LockOrders
                .Include(x => x.LockedBy)
                .Include(x => x.Order)
                .FirstOrDefaultAsync(x => x.OrderId == orderId, cancellationToken);

            // Jeśli lock istnieje
            if (existingLock != null)
            {
                // Sprawdź czy lock należy do tego samego użytkownika
                if (existingLock.LockedByUserProfileId == userId)
                {
                    // Usuń lock i zwróć null
                    await dbContext.LockOrders
                        .Where(x => x.OrderId == orderId && x.LockedByUserProfileId == userId)
                        .ExecuteDeleteAsync(cancellationToken);

                    await orderNotificationService.NotifyOrderChangedAsync(orderId);

                    return await Result<LockOrderResponse?>.SuccessAsync(null, "Odblokowano edycję zamówienia");
                }

                // Lock należy do innego użytkownika - zwróć istniejący lock
                var otherLockResponse = mapper.Map<LockOrderResponse>(existingLock);
                otherLockResponse.IsLockedByOtherUser = true; // userId != existingLock.LockedByUserProfileId

                return await Result<LockOrderResponse?>.SuccessAsync(
                    otherLockResponse,
                    $"Edycja zamówienia jest zablokowana przez użytkownika: {otherLockResponse.LockedBy}");
            }

            // Lock nie istnieje - sprawdź czy użytkownik ma już jakiś inny lock
            var existingUserLock = await dbContext.LockOrders
                .Include(x => x.Order)
                .FirstOrDefaultAsync(x => x.LockedByUserProfileId == userId, cancellationToken);

            if (existingUserLock != null)
            {
                var userLockResponse = mapper.Map<LockOrderResponse>(existingUserLock);
                userLockResponse.IsLockedByOtherUser = false; // to lock tego samego użytkownika

                return await Result<LockOrderResponse?>.SuccessAsync(
                    userLockResponse,
                    $"Masz już zablokowaną edycję innego zamówienia: {userLockResponse.OrderNo}");
            }

            // Utwórz nowy lock
            var lockOrder = new LockOrder
            {
                OrderId = orderId,
                LockedByUserProfileId = userId,
                ExpiresAt = DateTime.UtcNow.AddMinutes(LockTimeoutMinutes)
            };

            dbContext.LockOrders.Add(lockOrder);
            await dbContext.SaveChangesAsync(cancellationToken);

            await orderNotificationService.NotifyOrderChangedAsync(orderId);

            // Pobierz utworzony lock z includes
            var createdLock = await dbContext.LockOrders
                .Include(x => x.LockedBy)
                .Include(x => x.Order)
                .FirstAsync(x => x.Id == lockOrder.Id, cancellationToken);

            var createdLockResponse = mapper.Map<LockOrderResponse>(createdLock);
            createdLockResponse.IsLockedByOtherUser = false; // lock należy do tego użytkownika

            return await Result<LockOrderResponse?>.SuccessAsync(createdLockResponse, "Zablokowano edycję zamówienia");
        }
        catch (DbUpdateException ex) when (ex.InnerException?.Message.Contains("IX_LockOrders") == true)
        {
            // Race condition - ktoś zdążył utworzyć lock w międzyczasie
            var existingLockResult = await GetOrderLockAsync(orderId, userId, cancellationToken);
            if (existingLockResult is { Succeeded: true, Data: not null })
            {
                var existingLock = existingLockResult.Data;
                existingLock.IsLockedByOtherUser = existingLock.LockedByUserProfileId != userId;
                return await Result<LockOrderResponse?>.SuccessAsync(
                    existingLock,
                    "Zamówienie zostało zablokowane przez innego użytkownika");
            }

            return await Result<LockOrderResponse?>.FailAsync("Wystąpił błąd podczas blokowania edycji zamówienia");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Wystąpił błąd podczas blokowania edycji zamówienia");
            return await Result<LockOrderResponse?>.FailAsync(
                "Wystąpił błąd podczas przełączania blokady edycji zamówienia");
        }
    }
}