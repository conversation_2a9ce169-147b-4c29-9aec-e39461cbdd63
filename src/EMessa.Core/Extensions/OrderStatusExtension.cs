using EMessa.Base.Constants;
using EMessa.Base.Enums;
using EMessa.DAL.Entities.Orders;

namespace EMessa.Core.Extensions;

public static class OrderStatusExtension
{
    public static OrderStatus GetLowestOrderItemStatus(this IEnumerable<OrderItem> orderItems)
    {
        if (orderItems is null)
            throw new ArgumentNullException(nameof(orderItems));

        var statuses = orderItems
            .Select(item => item.Status)
            .Distinct()
            .ToHashSet();

        foreach (var orderedStatus in OrderAccessConstants.OrderedStatuses)
        {
            if (statuses.Contains(orderedStatus))
                return orderedStatus;
        }

        throw new ArgumentException("No valid order status found in the collection.", nameof(orderItems));
    }

    public static OrderStatus GetLowestOrderItemStatus(this Order order)
    {
        if (order is null)
            throw new ArgumentNullException(nameof(order));
        return order.OrderItems.GetLowestOrderItemStatus();
    }

    public static bool CanSetOrderStatus(this Order order, OrderStatus newStatus)
    {
        if (order is null)
            throw new ArgumentNullException(nameof(order));

        var lowestStatus = order.GetLowestOrderItemStatus();

        var lowestStatusIndex = OrderAccessConstants.OrderedStatuses.IndexOf(lowestStatus);
        var newStatusIndex = OrderAccessConstants.OrderedStatuses.IndexOf(newStatus);

        if (lowestStatusIndex == -1 || newStatusIndex == -1)
            throw new ArgumentException("Status not found in OrderedStatuses.");

        return lowestStatusIndex >= newStatusIndex;
    }
}
