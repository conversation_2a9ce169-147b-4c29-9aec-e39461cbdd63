using EMessa.Base.Enums;
using OptApi.ArticleStates;

namespace EMessa.Core.Extensions;

public static class ArticleStateExtension
{
    public static ArticleQuantityLevel GetQuantityLevelForCollection(this IEnumerable<ArticleStateDTO> stocks)
    {
        decimal totalQuantity = stocks.Sum(s => s.QuantityNet);
        decimal totalMin = stocks.Sum(s => s.MinQuantity);
        decimal totalMax = stocks.Sum(s => s.MaxQuantity);

        if (totalQuantity <= 0)
            return ArticleQuantityLevel.None;

        if (totalMax > 0)
        {
            if (totalQuantity >= totalMax)
                return ArticleQuantityLevel.High;

            if (totalQuantity <= totalMin)
                return ArticleQuantityLevel.Low;

            return ArticleQuantityLevel.Medium;
        }
        else
        {
            // brak max quantity
            if (totalQuantity <= totalMin)
                return ArticleQuantityLevel.Low;

            return ArticleQuantityLevel.High;
        }
    }
}
