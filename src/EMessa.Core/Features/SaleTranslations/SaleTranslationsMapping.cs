using AutoMapper;
using EMessa.Core.Features.SaleTranslations.Commands.AddEdit;
using EMessa.Core.Features.SaleTranslations.Queries.GetSaleTranslations;
using EMessa.DAL.Entities.Sales;

namespace EMessa.Core.Features.SaleTranslations;

public class SaleTranslationsMapping : Profile
{
    public SaleTranslationsMapping()
    {
        CreateMap<AddEditSaleTranslationCommand, SaleTranslation>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ReverseMap();

        CreateMap<SaleTranslation, GetSaleTranslationsResponse>()
            .ReverseMap();

        CreateMap<GetSaleTranslationsResponse, AddEditSaleTranslationCommand>();
    }
}
