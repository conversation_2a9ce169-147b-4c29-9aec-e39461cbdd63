using EMessa.Core.Features.Sales.Commands.Common;
using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.SaleTranslations.Commands.AddEdit;

public class AddEditSaleTranslationValidatorBase : AbstractValidator<AddEditSaleTranslationCommand>
{
    public AddEditSaleTranslationValidatorBase()
    {
        RuleFor(x => x.SaleId)
            .GreaterThan(0).WithMessage("Nie wybrano żadnej {0}.".Tr("promocji"))
            .CustomAsync(ValidateSaleIdAsync);

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Pole {0} jest wymagane.".Tr("Nazwa"))
            .MaximumLength(220).WithMessage("Pole {0} może mieć maks. długość {1} znaków.".Tr("Nazwa", "220"));

        RuleFor(x => x.Description);

        RuleFor(x => x.LanguageCode)
            .NotEmpty().WithMessage("Pole {0} jest wymagane.".Tr("Kod języka"))
            .CustomAsync(ValidateTranslationUniqueAsync);
    }

    protected virtual Task ValidateSaleIdAsync(
    int saleId, ValidationContext<AddEditSaleTranslationCommand> context, CancellationToken cancellationToken)
        => Task.CompletedTask;

    protected virtual Task ValidateTranslationUniqueAsync(
        string? languageCode, ValidationContext<AddEditSaleTranslationCommand> context, CancellationToken cancellationToken)
            => Task.CompletedTask;
}

public class AddEditSaleTranslationValidator(
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ILogger logger)
    : AddEditSaleTranslationValidatorBase
{

    protected override async Task ValidateSaleIdAsync(
    int saleId, ValidationContext<AddEditSaleTranslationCommand> context, CancellationToken cancellationToken) =>
        await CommonSaleValidator.ValidateSaleIdAsync(
            logger, dbContextFactory, saleId, context, cancellationToken);

    protected override async Task ValidateTranslationUniqueAsync(
        string? languageCode, ValidationContext<AddEditSaleTranslationCommand> context, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var translationExist = await dbContext.SaleTranslations
                .AnyAsync(x =>
                (x.LanguageCode == languageCode && x.SaleId == context.InstanceToValidate.SaleId) && x.Id != context.InstanceToValidate.Id,
                cancellationToken);

            if (translationExist)
            {
                context.AddFailure("Kod języka", "Tłumaczenie tej promocji we wskazanym języku już istnieje.".Tr());
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            throw;
        }
    }
}
