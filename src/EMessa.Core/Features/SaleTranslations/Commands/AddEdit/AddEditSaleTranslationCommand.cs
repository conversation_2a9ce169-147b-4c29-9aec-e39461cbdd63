using AutoMapper;
using EMessa.Core.Common.Results;
using EMessa.Core.Features.Sales.Commands.Common;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Sales;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.SaleTranslations.Commands.AddEdit;

public class AddEditSaleTranslationCommand : IRequest<IResult<int>>, ISaleIdCommand
{
    public int Id { get; set; }
    public int SaleId { get; set; }
    public string Name { get; set; } = "";
    public string? Description { get; set; } = "";
    public string LanguageCode { get; set; } = "";
}

public class AddEditMessageTranslationCommandHandler(
    ILogger<AddEditMessageTranslationCommandHandler> logger,
    IMapper mapper,
    IDbContextFactory<ApplicationDbContext> dbContextFactory)
    : IRequestHandler<AddEditSaleTranslationCommand, IResult<int>>
{
    public async Task<IResult<int>> Handle(AddEditSaleTranslationCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var validationResult = await new AddEditSaleTranslationValidator(dbContextFactory, logger)
                .ValidateAsync(request, cancellationToken);

            if (!validationResult.IsValid)
                return await Result<int>.FailAsync(validationResult);

            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            SaleTranslation? pageTranslationEntity;

            if (request.Id > 0) // Tryb edycji
            {
                pageTranslationEntity = await dbContext.SaleTranslations
                    .AsTracking()
                    .SingleOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

                if (pageTranslationEntity is null)
                {
                    logger.LogWarning("Dokonano próby edycji nieistniejącego tłumaczenia promocji o ID: {PageTranslationId}", request.Id);
                    return await Result<int>.FailAsync(ResultErrorMessages.NotFound);
                }

                mapper.Map(request, pageTranslationEntity);
            }
            else // Tryb dodawania
            {
                pageTranslationEntity = mapper.Map<SaleTranslation>(request);
                await dbContext.SaleTranslations.AddAsync(pageTranslationEntity, cancellationToken);
            }

            await dbContext.SaveChangesAsync(cancellationToken);
            return await Result<int>.SuccessAsync(pageTranslationEntity.Id);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result<int>.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}
