using EMessa.Base.Constants;
using EMessa.Core.Interfaces;
using EMessa.DAL.Entities.Sales;

namespace EMessa.Core.Features.SaleTranslations;

public static class SaleTranslationHelper
{
    public static async Task AddMissingTranslationsAsync(Sale sale, IMultiTranslationService multiTranslationService, bool autoTranslate = true)
    {
        var langs = SystemConstants.SupportedCultures.Where(
            x => !x.Code.Equals(SystemConstants.DefaultLanguageCode, StringComparison.OrdinalIgnoreCase)).Select(x => x.Code).ToList();

        var missingLangs = langs.Where(culture => !sale.Translations.Any(x => x.LanguageCode == culture)).ToList();

        Dictionary<string, string> translatedNames = new();
        Dictionary<string, string> translatedDescriptions = new();

        if (autoTranslate)
        {
            var translatedNamesTask = multiTranslationService.Translate(sale.Name, missingLangs);
            var translatedDescriptionsTask = multiTranslationService.Translate(sale.Description ?? string.Empty, missingLangs);

            await Task.WhenAll(translatedNamesTask, translatedDescriptionsTask);

            var translatedNameResult = await translatedNamesTask;
            var translatedSubjectResult = await translatedDescriptionsTask;

            translatedNames = translatedNameResult.Data;
            translatedDescriptions = translatedSubjectResult.Data;
        }

        foreach (var lang in missingLangs)
        {
            var newTranslation = new SaleTranslation
            {
                SaleId = sale.Id,
                LanguageCode = lang,
                Name = sale.Name,
                Description = sale.Description
            };

            if (autoTranslate)
            {
                if (translatedNames.TryGetValue(lang, out var translatedName))
                {
                    newTranslation.Name = translatedName;
                }
                if (translatedDescriptions != null &&
                    translatedDescriptions.TryGetValue(lang, out var translatedDescription))
                {
                    newTranslation.Description = translatedDescription;
                }
            }

            sale.Translations.Add(newTranslation);
        }
    }
}
