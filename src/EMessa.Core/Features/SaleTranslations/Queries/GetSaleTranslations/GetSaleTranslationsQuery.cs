using AutoMapper;
using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.SaleTranslations.Queries.GetSaleTranslations;

public record GetSaleTranslationsQuery(int SaleId) : IRequest<IResult<List<GetSaleTranslationsResponse>>>;

public partial class GetSaleTranslationsQueryHandler(
    ILogger<GetSaleTranslationsQueryHandler> logger,
    IMapper mapper,
    IMultiTranslationService multiTranslationService,
    IDbContextFactory<ApplicationDbContext> dbFactory)
    : IRequestHandler<GetSaleTranslationsQuery, IResult<List<GetSaleTranslationsResponse>>>
{
    public async Task<IResult<List<GetSaleTranslationsResponse>>> Handle(GetSaleTranslationsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

            var translations = await dbContext.SaleTranslations
                .Include(x => x.Sale)
                .Where(x => x.SaleId == request.SaleId)
                .AsTracking()
                .ToListAsync(cancellationToken);

            await SaleTranslationHelper.AddMissingTranslationsAsync(translations.First().Sale, multiTranslationService);

            await dbContext.SaveChangesAsync(cancellationToken);

            var result = translations.Select(mapper.Map<GetSaleTranslationsResponse>).ToList();

            return await Result<List<GetSaleTranslationsResponse>>.SuccessAsync(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Błąd pobrania tłumaczeń");
            return await Result<List<GetSaleTranslationsResponse>>.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}
