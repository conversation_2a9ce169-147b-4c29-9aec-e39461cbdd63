using EMessa.Core.Common;
using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Customers;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Sieve.Models;

namespace EMessa.Core.Features.CustomerLocalizations.Queries.Get;

public class GetCustomerLocalizationsQuery : SieveGetAllQueryBase<GetCustomerLocalizationsResponse>
{
    public int CustomerId { get; set; }
    public GetCustomerLocalizationsQuery(int customerId, SieveModel sieveModel) : base(sieveModel)
    {
        CustomerId = customerId;
    }

    public GetCustomerLocalizationsQuery(int customerId) : base()
    {
        CustomerId = customerId;
    }
}

public class GetCustomerLocalizationsQueryHandler(
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ISieveService sieveService)
    : IRequestHandler<GetCustomerLocalizationsQuery, ListResult<GetCustomerLocalizationsResponse>>
{
    public async Task<ListResult<GetCustomerLocalizationsResponse>> Handle(GetCustomerLocalizationsQuery request, CancellationToken cancellationToken)
    {
        await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

        var factories = dbContext
            .CustomersLocalizations
            .Where(x => x.CustomerId == request.CustomerId)
            .AsNoTracking()
            .AsQueryable();

        var response = await sieveService
            .ExecuteSieveAsync<CustomerLocalization, GetCustomerLocalizationsResponse>(
                request.SieveModel,
                factories,
                cancellationToken);

        return response;
    }
}