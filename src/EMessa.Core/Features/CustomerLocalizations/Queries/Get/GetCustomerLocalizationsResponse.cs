using EMessa.Base.Enums;

namespace EMessa.Core.Features.CustomerLocalizations.Queries.Get;

public class GetCustomerLocalizationsResponse
{
    public int Id { get; set; }
    public LocalizationType Type { get; init; }
    public int CustomerId { get; set; }
    public string Name { get; set; } = null!;
    public string Address { get; set; } = null!;
    public string City { get; set; } = null!;
    public string PostCode { get; set; } = null!;
    public string Phone { get; set; } = null!;
    public string Fax { get; set; } = null!;
    public string Email { get; set; } = null!;
    public bool IsDefault { get; set; }
    public bool IsActive { get; set; }
}