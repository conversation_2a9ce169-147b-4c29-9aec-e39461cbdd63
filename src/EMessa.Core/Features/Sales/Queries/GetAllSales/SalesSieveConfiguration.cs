using EMessa.Core.Common;
using EMessa.Core.Helpers;
using EMessa.DAL.Entities.Sales;
using Messa.Core.BL.eMessa.Sales.ViewModels;
using Sieve.Services;
using System.Globalization;

namespace EMessa.Core.Features.Sales.Queries.GetAllSales;

public class SalesSieveConfiguration : ISieveConfiguration
{
    public void Configure(SievePropertyMapper mapper)
    {
        mapper.Property<Sale>(x => x.Id).CanFilter().CanSort();
        mapper.Property<Sale>(x => x.Name).CanFilter().CanSort();
        mapper.Property<Sale>(x => x.Description).CanFilter().CanSort();
        mapper.Property<Sale>(x => x.Status).CanFilter().CanSort();
        mapper.Property<Sale>(x => x.DateFrom).CanSort();
        mapper.Property<Sale>(x => x.DateTo).CanSort();
        mapper.Property<Sale>(x => x.DefinedWeight).CanFilter().CanSort();
        mapper.Property<Sale>(x => x.Coat.Value).HasName(nameof(GetAllSalesResponse.Coat)).CanFilter().CanSort();
        mapper.Property<Sale>(x => x.Color.Value).HasName(nameof(GetAllSalesResponse.Color)).CanFilter().CanSort();
        mapper.Property<Sale>(x => x.Thick.Value).HasName(nameof(GetAllSalesResponse.Thick)).CanFilter().CanSort();
        mapper.Property<Sale>(x => x.CoatId).CanFilter().CanSort();
        mapper.Property<Sale>(x => x.ColorId).CanFilter().CanSort();
        mapper.Property<Sale>(x => x.ThickId).CanFilter().CanSort();
    }

    public static IQueryable<Sale> GlobalSearchFilter(
        IQueryable<Sale> source, string op, string[] values)
    {
        var value = values.FirstOrDefault();
        if (string.IsNullOrEmpty(value))
            return source;

        // Szukaj statusów, których DisplayName zawiera wartość
        var matchingStatusIds = Enum.GetValues(typeof(SaleStatus))
            .Cast<SaleStatus>()
            .Where(status => status.GetDisplayName().Contains(value, StringComparison.OrdinalIgnoreCase))
            .Select(status => (int)status)
            .ToList();

        return op switch
        {
            "@=*" => source.Where(x =>
                   x.Id.ToString().Contains(value, StringComparison.CurrentCultureIgnoreCase) ||
                   x.Name.Contains(value, StringComparison.CurrentCultureIgnoreCase) ||
                   x.Description.Contains(value, StringComparison.CurrentCultureIgnoreCase) ||
                   matchingStatusIds.Contains((int)x.Status) ||
                   x.DefinedWeight.ToString().Contains(value, StringComparison.CurrentCultureIgnoreCase) ||
                   x.Coat.Value.Contains(value, StringComparison.CurrentCultureIgnoreCase) ||
                   x.Color.Value.Contains(value, StringComparison.CurrentCultureIgnoreCase) ||
                   x.Thick.Value.Contains(value, StringComparison.CurrentCultureIgnoreCase)),
            _ => source
        };
    }

    public static IQueryable<Sale> DateFromToActiveFilter(
        IQueryable<Sale> source, string op, string[] values)
    {
        var value = values.FirstOrDefault();
        if (string.IsNullOrEmpty(value) || !bool.TryParse(value, out var dateActive))
        {
            return source;
        }

        return op switch
        {
            "==" => source.Where(s => (DateTime.Now > s.DateFrom && DateTime.Now < s.DateTo) == dateActive),
            "!=" => source.Where(s => (DateTime.Now > s.DateFrom && DateTime.Now < s.DateTo) != dateActive),
            _ => source
        };
    }

    public static IQueryable<Sale> SaleSateInFromFilter(
        IQueryable<Sale> source, string op, string[] values)
    {

        if (values.Length != 1 || !DateTime.TryParseExact(
                values[0],
                "yyyy-MM-ddTHH:mm:ss.fffffffZ",
                CultureInfo.InvariantCulture,
                DateTimeStyles.AssumeUniversal | DateTimeStyles.AdjustToUniversal,
                out DateTime dateValue)) // Convert to UTC DateTime
        {
            return source;
        }

        return op switch
        {
            ">=" => source.Where(p => p.DateTo >= dateValue),
            _ => source
        };
    }

    public static IQueryable<Sale> SaleDateInToFilter(
        IQueryable<Sale> source, string op, string[] values)
    {
        if (values.Length != 1 || !DateTime.TryParseExact(
                values[0],
                "yyyy-MM-ddTHH:mm:ss.fffffffZ",
                CultureInfo.InvariantCulture,
                DateTimeStyles.AssumeUniversal | DateTimeStyles.AdjustToUniversal,
                out DateTime dateValue)) // Convert to UTC DateTime
        {
            return source;
        }

        return op switch
        {
            "<=" => source.Where(p => p.DateFrom <= dateValue),
            _ => source
        };
    }

    public static IQueryable<Sale> DateFromFilter(
        IQueryable<Sale> source, string op, string[] values) =>
            SieveDateFiltersHelper.DateOnlyFilter(source, x => x.DateFrom, op, values);

    public static IQueryable<Sale> DateToFilter(
        IQueryable<Sale> source, string op, string[] values) =>
            SieveDateFiltersHelper.DateOnlyFilter(source, x => x.DateTo, op, values);
}
