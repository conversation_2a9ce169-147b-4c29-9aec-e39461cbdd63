using AutoMapper;
using AutoMapper.QueryableExtensions;
using EMessa.Base.Constants;
using EMessa.Core.Common;
using EMessa.Core.Common.Results;
using EMessa.Core.Extensions;
using EMessa.Core.Features.Sales.Queries.CommonResponses;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using MediatR;
using Messa.Core.BL.eMessa.Sales.ViewModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Sieve.Models;

namespace EMessa.Core.Features.Sales.Queries.GetSaleArticlesByArticleId;

public class GetSaleArticlesByArticleIdQuery : SieveGetAllQueryBase<SaleArticleResponse>
{
    public int ArticleId { get; set; }

    public GetSaleArticlesByArticleIdQuery(int articleId, SieveModel sieveModel) : base(sieveModel)
    {
        ArticleId = articleId;
    }

    public GetSaleArticlesByArticleIdQuery(int articleId) : base()
    {
        ArticleId = articleId;
    }
}

public class GetSaleByArticleIdQueryHandler(
    IAppStateService appStateService,
    ILogger<GetSaleByArticleIdQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ISieveService sieveService,
    IRollDataService rollDataService,
    IMapper mapper)
    : IRequestHandler<GetSaleArticlesByArticleIdQuery, ListResult<SaleArticleResponse>>
{
    public async Task<ListResult<SaleArticleResponse>> Handle(
        GetSaleArticlesByArticleIdQuery request,
        CancellationToken cancellationToken)
    {
        var loggedUser = appStateService.UserData;

        if (!loggedUser.HasAnyRole(Role.Administrator, Role.Client))
        {
            return new ListResult<SaleArticleResponse>([]);
        }

        try
        {
            request.SieveModel.EnsureOrderByIfNoSorts(nameof(SaleArticleResponse.Id), true);

            var dateNow = DateTime.UtcNow;
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var salesArticlesQuery = dbContext.SalesArticles
                .Include(x => x.Sale)
                .Include(x => x.Sale).ThenInclude(s => s.SalesCustomers)
                .Include(x => x.Sale).ThenInclude(s => s.Rolls)
                .Include(x => x.Sale).ThenInclude(s => s.Prices)
                .Include(x => x.Sale).ThenInclude(s => s.Color)
                .Include(x => x.Sale).ThenInclude(s => s.Coat)
                .Include(x => x.Sale).ThenInclude(s => s.Thick)
                .Include(x => x.Sale).ThenInclude(s => s.Translations)
                .Include(x => x.Sale).ThenInclude(s => s.OrderItems)
                .Where(x => x.ArticleId == request.ArticleId
                            && x.Sale.Status == SaleStatus.Active
                            && x.Sale.DateFrom <= dateNow
                            && x.Sale.DateTo >= dateNow
                );

            if (!loggedUser.HasAdminRole)
            {
                salesArticlesQuery = salesArticlesQuery
                    .Where(x => x.Sale.SalesCustomers.Any(sc => sc.CustomerId == loggedUser.CustomerId));
            }

            var projectedQuery = salesArticlesQuery
                .AsNoTracking()
                .ProjectTo<SaleArticleResponse>(mapper.ConfigurationProvider)
                .AsQueryable();

            var response = await sieveService
                .ExecuteSieveAsync<SaleArticleResponse, SaleArticleResponse>(
                    request.SieveModel,
                    projectedQuery,
                    cancellationToken);

            var allSaleRolls = response.Data.SelectMany(sale => sale.Rolls).ToList();

            try
            {
                await rollDataService.UpdateRollStatusAsync(allSaleRolls, cancellationToken);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error occurred while updating roll statuses for ArticleId: {ArticleId}",
                    request.ArticleId);
            }

            return response;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while getting sale articles for ArticleId: {ArticleId}",
                request.ArticleId);
            return ListResult<SaleArticleResponse>.Failure(ResultErrorMessages.UnexpectedError);
        }
    }
}