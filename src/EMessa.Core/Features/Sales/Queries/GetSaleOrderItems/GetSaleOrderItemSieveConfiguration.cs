using EMessa.Core.Common;
using EMessa.Core.Features.Sales.Queries.CommonResponses;
using Sieve.Services;

namespace EMessa.Core.Features.Sales.Queries.GetSaleOrderItems;

public class GetSaleOrderItemSieveConfiguration : ISieveConfiguration
{
    public void Configure(SievePropertyMapper mapper)
    {
        mapper.Property<OrderItemResponseForSale>(x => x.OrderItemId).CanFilter().CanSort();
        mapper.Property<OrderItemResponseForSale>(x => x.OrderId).CanFilter().CanSort();
        mapper.Property<OrderItemResponseForSale>(x => x.OrderItemWeight).CanFilter().CanSort();
        mapper.Property<OrderItemResponseForSale>(x => x.OrderItemIndex).CanFilter().CanSort();
        mapper.Property<OrderItemResponseForSale>(x => x.OrderItemArticleName).CanFilter().CanSort();
        mapper.Property<OrderItemResponseForSale>(x => x.OrderItemQuantity).CanFilter().CanSort();
        mapper.Property<OrderItemResponseForSale>(x => x.OrderStatus).CanFilter().CanSort();
        mapper.Property<OrderItemResponseForSale>(x => x.OrderType).CanFilter().CanSort();
        mapper.Property<OrderItemResponseForSale>(x => x.OrderCustomerShortName).CanFilter().CanSort();
        mapper.Property<OrderItemResponseForSale>(x => x.OrderCustomerLocalizationName).CanFilter().CanSort();
    }

    public static IQueryable<OrderItemResponseForSale> GlobalSearchFilter(
        IQueryable<OrderItemResponseForSale> source, string op, string[] values)
    {
        var value = values.FirstOrDefault();
        if (string.IsNullOrEmpty(value))
            return source;

        return op switch
        {
            "@=" => source.Where(x =>
                x.OrderItemId.ToString().Contains(value) ||
                x.OrderId.ToString().Contains(value) ||
                x.OrderItemIndex.Contains(value) ||
                x.OrderItemArticleName.Contains(value) ||
                x.OrderItemQuantity.ToString().Contains(value) ||
                x.OrderItemWeight.ToString().Contains(value) ||
                x.OrderCustomerShortName.Contains(value) ||
                x.OrderCustomerLocalizationName.Contains(value)
            ),
            _ => source
        };
    }
}