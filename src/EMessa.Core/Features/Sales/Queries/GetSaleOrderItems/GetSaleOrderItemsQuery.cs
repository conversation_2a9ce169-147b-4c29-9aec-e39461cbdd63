using AutoMapper;
using AutoMapper.QueryableExtensions;
using EMessa.Core.Common;
using EMessa.Core.Common.Results;
using EMessa.Core.Extensions;
using EMessa.Core.Features.Sales.Queries.CommonResponses;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Sieve.Models;

namespace EMessa.Core.Features.Sales.Queries.GetSaleOrderItems;

public class GetSaleOrderItemsQuery : SieveGetAllQueryBase<OrderItemResponseForSale>
{
    public int SaleId { get; set; }
    public GetSaleOrderItemsQuery(int saleId, SieveModel sieveModel) : base(sieveModel)
    {
        SaleId = saleId;
    }

    public GetSaleOrderItemsQuery(int saleId) : base()
    {
        SaleId = saleId;
    }
}

public class GetSaleOrderItemsQueryHandler(
    ILogger<GetSaleOrderItemsQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IOrderAccessService orderAccessService,
    ISieveService sieveService,
    IMapper mapper)
        : IRequestHandler<GetSaleOrderItemsQuery, ListResult<OrderItemResponseForSale>>
{
    public async Task<ListResult<OrderItemResponseForSale>> Handle(GetSaleOrderItemsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            request.SieveModel.EnsureOrderByIfNoSorts(nameof(OrderItemResponseForSale.OrderId), true);

            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var ordersQuery = await orderAccessService.GetAccessibleOrdersAsync(dbContext, cancellationToken);

            var orderItemsQuery = ordersQuery
                .SelectMany(x => x.OrderItems)
                .Where(x => x.SaleId == request.SaleId)
                .Include(x => x.Order)
                    .ThenInclude(x => x.Customer)
                .Include(x => x.Order)
                    .ThenInclude(x => x.CustomerLocalization)
                .Include(x => x.Article)
                .AsNoTracking()
                .ProjectTo<OrderItemResponseForSale>(mapper.ConfigurationProvider)
                .AsQueryable();

            var response = await sieveService
                .ExecuteSieveAsync<OrderItemResponseForSale, OrderItemResponseForSale>(
                    request.SieveModel,
                    orderItemsQuery,
                    cancellationToken);

            return response;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while getting sale order items for SaleId: {SaleId}", request.SaleId);
            return ListResult<OrderItemResponseForSale>.Failure(ResultErrorMessages.UnexpectedError);
        }
    }
}