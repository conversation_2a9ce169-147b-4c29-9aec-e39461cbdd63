using AutoMapper;
using EMessa.Core.Common.Results;
using EMessa.DAL.Data;
using MediatR;
using Messa.Core.BL.eMessa.Sales.ViewModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Queries.GetSalesForCustomer;

public record GetSalesForCustomerQuery(int CustomerId) : IRequest<ListResult<GetSalesForCustomerResponse>>;

public class GetSaleByIdQueryHandler(
    ILogger<GetSaleByIdQueryHandler> logger,
    IMapper mapper,
    IDbContextFactory<ApplicationDbContext> dbFactory)
    : IRequestHandler<GetSalesForCustomerQuery, ListResult<GetSalesForCustomerResponse>>
{
    public async Task<ListResult<GetSalesForCustomerResponse>> Handle(GetSalesForCustomerQuery request, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);
            var sale = await dbContext.Sales
                .Include(x => x.SalesArticles)
                    .ThenInclude(x => x.Article)
                .Include(x => x.SalesCustomers)
                    .ThenInclude(x => x.Customer)
                .Include(x => x.Rolls)
                .Include(x => x.Prices)
                .Include(x => x.Coat)
                .Include(x => x.Color)
                .Include(x => x.Thick)
                .Include(x => x.Translations)
                .Where(x => x.Status == SaleStatus.Active
                            && x.SalesCustomers.Any(x => x.CustomerId == request.CustomerId))
                .ToListAsync(cancellationToken);

            if (sale is null)// ? [] nigdy nie jest null
            {
                return ListResult<GetSalesForCustomerResponse>.Failure(ResultErrorMessages.NotFound);
            }

            var result = mapper.Map<List<GetSalesForCustomerResponse>>(sale) ?? [];

            return ListResult<GetSalesForCustomerResponse>.Success(result, result.Count);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return ListResult<GetSalesForCustomerResponse>.Failure(ResultErrorMessages.UnexpectedError);
        }
    }
}
