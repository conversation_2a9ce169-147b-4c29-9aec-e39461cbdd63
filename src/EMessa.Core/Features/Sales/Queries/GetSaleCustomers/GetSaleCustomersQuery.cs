using EMessa.Core.Common;
using EMessa.Core.Common.Results;
using EMessa.Core.Extensions;
using EMessa.Core.Features.Customers.Queries.GetAll;
using EMessa.Core.Features.Sales.Queries.GetAllSales;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Customers;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Sieve.Models;

namespace EMessa.Core.Features.Sales.Queries.GetSaleCustomers;

public class GetSaleCustomersQuery : SieveGetAllQueryBase<GetAllCustomersResponse>
{
    public int SaleId { get; set; }
    public GetSaleCustomersQuery(int saleId, SieveModel sieveModel) : base(sieveModel)
    {
        SaleId = saleId;
    }

    public GetSaleCustomersQuery(int saleId) : base()
    {
        SaleId = saleId;
    }
}

public class GetCustomersSaleQueryHandler(
    ILogger<GetCustomersSaleQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ISieveService sieveService)
        : IRequestHandler<GetSaleCustomersQuery, ListResult<GetAllCustomersResponse>>
{
    public async Task<ListResult<GetAllCustomersResponse>> Handle(GetSaleCustomersQuery request, CancellationToken cancellationToken)
    {
        try
        {
            request.SieveModel.EnsureOrderByIfNoSorts(nameof(GetAllCustomersResponse.Id), true);

            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var customersQuery = dbContext.SalesCustomers
                .Include(x => x.Customer)
                    .ThenInclude(x => x.Branch)
                .Where(x => x.SaleId == request.SaleId)
                .Select(x => x.Customer)
                .AsNoTracking()
                .AsQueryable();

            var response = await sieveService
                .ExecuteSieveAsync<Customer, GetAllCustomersResponse>(
                    request.SieveModel,
                    customersQuery,
                    cancellationToken);

            return response;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving customers for sale with ID {SaleId}.", request.SaleId);
            return ListResult<GetAllCustomersResponse>.Failure(ResultErrorMessages.UnexpectedError);
        }
    }
}