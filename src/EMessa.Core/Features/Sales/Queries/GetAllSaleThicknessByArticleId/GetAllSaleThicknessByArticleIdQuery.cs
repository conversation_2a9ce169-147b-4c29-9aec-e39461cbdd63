using AutoMapper;
using AutoMapper.QueryableExtensions;
using EMessa.Base.Constants;
using EMessa.Core.Common.Results;
using EMessa.Core.Features.OptionValues.Queries.GetAllValueForOption;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using MediatR;
using Messa.Core.BL.eMessa.Sales.ViewModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Queries.GetAllSaleThicknessByArticleId;

public record GetAllSaleThicknessByArticleIdQuery(int ArticleId)
    : IRequest<Result<List<GetAllValuesForOptionResponse>>>;

public class GetAllSaleThicknessByArticleIdQueryHandler(
    IAppStateService appStateService,
    ILogger<GetAllSaleThicknessByArticleIdQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IMapper mapper)
    : IRequestHandler<GetAllSaleThicknessByArticleIdQuery, Result<List<GetAllValuesForOptionResponse>>>
{
    public async Task<Result<List<GetAllValuesForOptionResponse>>> Handle(GetAllSaleThicknessByArticleIdQuery request,
        CancellationToken cancellationToken)
    {
        var loggedUser = appStateService.UserData;

        if (!loggedUser.HasAnyRole(Role.Administrator, Role.Client))
        {
            logger.LogWarning("Customer unauthorized {CustomerId}.", loggedUser.CustomerId);
            return await Result<List<GetAllValuesForOptionResponse>>.FailAsync(ResultErrorMessages.Unauthorized);
        }

        try
        {
            var dateNow = DateTime.UtcNow;
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var salesArticlesQuery = dbContext.SalesArticles
                .AsNoTracking()
                .AsQueryable()
                .AsSplitQuery()
                .Include(x => x.Sale).ThenInclude(s => s.SalesCustomers)
                .Include(x => x.Sale).ThenInclude(s => s.Thick)
                .Where(x => x.ArticleId == request.ArticleId
                            && x.Sale.Status == SaleStatus.Active
                            && x.Sale.DateFrom <= dateNow
                            && x.Sale.DateTo >= dateNow
                );

            if (!loggedUser.HasAdminRole)
            {
                salesArticlesQuery = salesArticlesQuery
                    .Where(x =>
                        x.Sale.SalesCustomers.Any(sc => sc.CustomerId == loggedUser.CustomerId));
            }

            // Get only the thickness values from the sales
            var thicknesses = await salesArticlesQuery
                .Select(x => x.Sale.Thick)
                .Distinct()
                .ProjectTo<GetAllValuesForOptionResponse>(mapper.ConfigurationProvider)
                .ToListAsync(cancellationToken);

            return await Result<List<GetAllValuesForOptionResponse>>.SuccessAsync(thicknesses);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while getting sale thicknesses for ArticleId: {ArticleId}",
                request.ArticleId);
            return await Result<List<GetAllValuesForOptionResponse>>.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}
