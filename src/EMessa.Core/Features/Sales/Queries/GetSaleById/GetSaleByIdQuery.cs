using AutoMapper;
using EMessa.Base.Constants;
using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Sales;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Queries.GetSaleById;

public record GetSaleByIdQuery(int Id) : IRequest<Result<GetSaleByIdResponse>>;

public class GetSaleByIdQueryHandler(
    ILogger<GetSaleByIdQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbFactory,
    IMapper mapper,
    IRollDataService rollDataService,
    IMultiTranslationService multiTranslationService)
    : IRequestHandler<GetSaleByIdQuery, Result<GetSaleByIdResponse>>
{
    public async Task<Result<GetSaleByIdResponse>> Handle(GetSaleByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);
            var sale = await dbContext.Sales
                .Include(x => x.SalesArticles)
                    .ThenInclude(x => x.Article)
                .Include(x => x.SalesCustomers)
                    .ThenInclude(x => x.Customer)
                .Include(x => x.OrderItems.Where(oi => !oi.Order.IsDeleted))
                    .ThenInclude(x => x.Order)
                .Include(x => x.Rolls)
                .Include(x => x.Prices)
                .Include(x => x.Coat)
                .Include(x => x.Color)
                .Include(x => x.Thick)
                .Include(x => x.Translations)
                .Include(x => x.CreatedBy)
                .AsTracking()
                .SingleOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

            if (sale is null)
            {
                logger.LogWarning("GetSaleByIdQueryHandler | Sale with ID {SaleId} not found.", request.Id);
                return await Result<GetSaleByIdResponse>.FailAsync(ResultErrorMessages.NotFound);
            }

            await AddMissingTranslationsAsync(sale, cancellationToken);

            await dbContext.SaveChangesAsync(cancellationToken);

            var result = mapper.Map<GetSaleByIdResponse>(sale);

            try
            {
                await rollDataService.UpdateRollStatusAsync(result.Rolls, cancellationToken);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to update roll statuses for sale with ID {SaleId}.", request.Id);
            }

            return await Result<GetSaleByIdResponse>.SuccessAsync(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result<GetSaleByIdResponse>.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }

    private async Task AddMissingTranslationsAsync(Sale sale, CancellationToken cancellationToken)
    {
        foreach (var culture in SystemConstants.SupportedCultures
            .Where(x => !x.Code.Equals(SystemConstants.DefaultLanguageCode, StringComparison.OrdinalIgnoreCase)))
        {
            if (!sale.Translations.Any(x => x.LanguageCode == culture.Code))
            {
                var translatedNameTask = multiTranslationService.Translate(sale.Name, [culture.Code]);
                var translatedDescriptionTask = multiTranslationService.Translate(sale.Description ?? string.Empty, [culture.Code]);

                await Task.WhenAll(translatedNameTask, translatedDescriptionTask);

                var translatedNameResult = await translatedNameTask;
                var translatedDescriptionResult = await translatedDescriptionTask;

                sale.Translations.Add(new SaleTranslation
                {
                    SaleId = sale.Id,
                    LanguageCode = culture.Code,
                    Name = translatedNameResult.Data?.FirstOrDefault().Value ?? sale.Name,
                    Description = translatedDescriptionResult.Data?.FirstOrDefault().Value ?? sale.Description
                });
            }
        }
    }
}
