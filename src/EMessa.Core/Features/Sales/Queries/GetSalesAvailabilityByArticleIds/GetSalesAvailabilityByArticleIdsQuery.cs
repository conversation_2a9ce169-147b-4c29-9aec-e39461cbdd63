using MediatR;
using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.Base.Constants;
using EMessa.DAL.Data;
using Messa.Core.BL.eMessa.Sales.ViewModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Queries.GetSalesAvailabilityByArticleIds;

public sealed record GetSalesAvailabilityByArticleIdsQuery(List<int> ArticleIds)
    : IRequest<Result<HashSet<int>>>;

public class GetSalesAvailabilityByArticleIdsQueryHandler(
    IAppStateService appStateService,
    ILogger<GetSalesAvailabilityByArticleIdsQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbFactory)
    : IRequestHandler<GetSalesAvailabilityByArticleIdsQuery, Result<HashSet<int>>>
{
    public async Task<Result<HashSet<int>>> Handle(GetSalesAvailabilityByArticleIdsQuery request,
        CancellationToken cancellationToken)
    {
        var loggedUser = appStateService.UserData;

        if (!loggedUser.HasAnyRole(Role.Administrator, Role.Client))
        {
            return await Result<HashSet<int>>.SuccessAsync(new HashSet<int>());
        }

        try
        {
            await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

            var articleIdsSet = request.ArticleIds.ToHashSet();

            var dateNow = DateTime.UtcNow;

            var salesArticlesQuery = dbContext.SalesArticles
                .AsNoTracking()
                .AsQueryable()
                .Include(x => x.Sale)
                .ThenInclude(s => s.SalesCustomers)
                .Where(x => articleIdsSet.Contains(x.ArticleId)
                            && x.Sale.Status == SaleStatus.Active
                            && x.Sale.DateFrom <= dateNow
                            && x.Sale.DateTo >= dateNow);

            if (!loggedUser.HasAdminRole)
            {
                salesArticlesQuery = salesArticlesQuery
                    .Where(x => x.Sale.SalesCustomers.Any(sc => sc.CustomerId == loggedUser.CustomerId));
            }

            // Pobierz tylko potrzebne ArticleId zamiast całych obiektów
            var availableArticleIds = await salesArticlesQuery
                .Select(sa => sa.ArticleId)
                .Distinct()
                .ToListAsync(cancellationToken);

            // Zwróć HashSet dostępnych Promocji dla klienta/admina
            return await Result<HashSet<int>>.SuccessAsync(availableArticleIds.ToHashSet());
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result<HashSet<int>>.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}
