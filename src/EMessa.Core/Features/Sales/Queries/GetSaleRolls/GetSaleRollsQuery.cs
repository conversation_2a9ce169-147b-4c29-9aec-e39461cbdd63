using AutoMapper;
using AutoMapper.QueryableExtensions;
using EMessa.Core.Common;
using EMessa.Core.Common.Results;
using EMessa.Core.Extensions;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Sieve.Models;

namespace EMessa.Core.Features.Sales.Queries.GetSaleRolls;

public class GetSaleRollsQuery : SieveGetAllQueryBase<SaleRollResponse>
{
    public int SaleId { get; set; }
    public GetSaleRollsQuery(int saleId, SieveModel sieveModel) : base(sieveModel)
    {
        SaleId = saleId;
    }

    public GetSaleRollsQuery(int saleId) : base()
    {
        SaleId = saleId;
    }
}

public class GetArticlesSaleQueryHandler(
    ILogger<GetArticlesSaleQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbFactory,
    IRollDataService rollDataService,
    ISieveService sieveService,
    IMapper mapper)
        : IRequestHandler<GetSaleRollsQuery, ListResult<SaleRollResponse>>
{
    public async Task<ListResult<SaleRollResponse>> Handle(GetSaleRollsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            request.SieveModel.EnsureOrderByIfNoSorts(nameof(SaleRollResponse.Id), true);

            await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

            var saleRollIds = await dbContext.SaleRolls
                .Where(x => x.SaleId == request.SaleId)
                .Select(x => x.RollId)
                .ToListAsync(cancellationToken);

            var rollsFromApi = await rollDataService.GetRollsByIdsAsync(saleRollIds, cancellationToken);

            var saleRolls = await dbContext.SaleRolls
                .Where(x => x.SaleId == request.SaleId)
                .AsNoTracking()
                .ProjectTo<SaleRollResponse>(mapper.ConfigurationProvider)
                .ToListAsync(cancellationToken);

            foreach (var saleRoll in saleRolls)
            {
                var rollFromApi = rollsFromApi!.FirstOrDefault(x => x.RollId == saleRoll.RollId);
                if (rollFromApi is not null)
                {
                    saleRoll.Status = rollFromApi.Status;
                    saleRoll.CurrentWeight = rollFromApi.CurrentWeight;
                }
            }

            var response = sieveService
                .ExecuteSieve<SaleRollResponse, SaleRollResponse>(
                    request.SieveModel,
                    saleRolls.AsQueryable(),
                    cancellationToken);

            return response;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while getting sale rolls for SaleId: {SaleId}", request.SaleId);
            return ListResult<SaleRollResponse>.Failure(ResultErrorMessages.UnexpectedError);
        }
    }
}