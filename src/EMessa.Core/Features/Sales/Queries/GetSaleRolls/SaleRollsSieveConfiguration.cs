using EMessa.Core.Features.Rolls.Queries;
using Microsoft.IdentityModel.Tokens;
using Sieve.Services;

namespace EMessa.Core.Features.Sales.Queries.GetSaleRolls;

public class SaleRollsSieveConfiguration : ISieveConfiguration
{
    public void Configure(SievePropertyMapper mapper)
    {
        mapper.Property<SaleRollResponse>(x => x.RollId).CanFilter().CanSort();
        mapper.Property<SaleRollResponse>(x => x.SaleId).CanFilter().CanSort();
        mapper.Property<SaleRollResponse>(x => x.RollRegisteredDate).CanFilter().CanSort();
        mapper.Property<SaleRollResponse>(x => x.Supplier).CanFilter().CanSort();
        mapper.Property<SaleRollResponse>(x => x.RollNo).CanFilter().CanSort();
        mapper.Property<SaleRollResponse>(x => x.OrigNo).CanFilter().CanSort();
        mapper.Property<SaleRollResponse>(x => x.Color).CanFilter().CanSort();
        mapper.Property<SaleRollResponse>(x => x.ColorCode).CanFilter().CanSort();
        mapper.Property<SaleRollResponse>(x => x.Coat).CanFilter().CanSort();
        mapper.Property<SaleRollResponse>(x => x.CoatCode).CanFilter().CanSort();
        mapper.Property<SaleRollResponse>(x => x.Thick).CanFilter().CanSort();
        mapper.Property<SaleRollResponse>(x => x.SaleWeight).CanFilter().CanSort();
        mapper.Property<SaleRollResponse>(x => x.Weight).CanFilter().CanSort();
        mapper.Property<SaleRollResponse>(x => x.CreatedDate).CanFilter().CanSort();
        mapper.Property<SaleRollResponse>(x => x.CurrentWeight).CanFilter().CanSort();
    }

    public static IQueryable<SaleRollResponse> GlobalSearchFilter(
        IQueryable<SaleRollResponse> source, string op, string[] values)
    {
        var value = values.First();

        if (value.IsNullOrEmpty())
            return source;

        return op switch
        {
            "@=" => source.Where(x =>
                x.RollNo.ToString().Contains(value) ||
                x.OrigNo.Contains(value) ||
                x.Supplier.Contains(value) ||
                x.Color.Contains(value) ||
                x.ColorCode.Contains(value) ||
                x.Coat.Contains(value) ||
                x.CoatCode.Contains(value)
            ),
            _ => source
        };
    }
}