using AutoMapper;
using AutoMapper.QueryableExtensions;
using EMessa.Core.Common;
using EMessa.Core.Common.Results;
using EMessa.Core.Extensions;
using EMessa.Core.Features.Sales.Queries.CommonResponses;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Sieve.Models;

namespace EMessa.Core.Features.Sales.Queries.GetSaleArticles;

public class GetSaleArticlesQuery : SieveGetAllQueryBase<ArticleResponseForSale>
{
    public int SaleId { get; set; }
    public GetSaleArticlesQuery(int saleId, SieveModel sieveModel) : base(sieveModel)
    {
        SaleId = saleId;
    }

    public GetSaleArticlesQuery(int saleId) : base()
    {
        SaleId = saleId;
    }
}

public class GetArticlesSaleQueryHandler(
    ILogger<GetArticlesSaleQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ISieveService sieveService,
    IMapper mapper)
        : IRequestHandler<GetSaleArticlesQuery, ListResult<ArticleResponseForSale>>
{
    public async Task<ListResult<ArticleResponseForSale>> Handle(GetSaleArticlesQuery request, CancellationToken cancellationToken)
    {
        try
        {
            request.SieveModel.EnsureOrderByIfNoSorts(nameof(ArticleResponseForSale.Id), true);

            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var articleQuery = dbContext.SalesArticles
                .Where(x => x.SaleId == request.SaleId)
                .Include(x => x.Article)
                    .ThenInclude(x => x!.Categories)
                .Include(x => x.Article)
                    .ThenInclude(x => x!.Unit)
                .Include(x => x.Article)
                    .ThenInclude(x => x!.Translations)
                .AsNoTracking()
                .ProjectTo<ArticleResponseForSale>(mapper.ConfigurationProvider)
                .AsQueryable();

            var response = await sieveService
                .ExecuteSieveAsync<ArticleResponseForSale, ArticleResponseForSale>(
                    request.SieveModel,
                    articleQuery,
                    cancellationToken);

            return response;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error retrieving articles for sale with ID {SaleId}.", request.SaleId);
            return ListResult<ArticleResponseForSale>.Failure(ResultErrorMessages.UnexpectedError);
        }

    }
}