using EMessa.Core.Features.Sales.Queries.CommonResponses;
using Microsoft.IdentityModel.Tokens;
using Sieve.Services;

namespace EMessa.Core.Features.Sales.Queries.GetSaleArticles;

public class SaleArticleSieveConfiguration : ISieveConfiguration
{
    public void Configure(SievePropertyMapper mapper)
    {
        mapper.Property<ArticleResponseForSale>(x => x.Id).CanFilter().CanSort();
        mapper.Property<ArticleResponseForSale>(x => x.Name).CanFilter().CanSort();
        mapper.Property<ArticleResponseForSale>(x => x.Code).CanFilter().CanSort();
        mapper.Property<ArticleResponseForSale>(x => x.IsActive).CanFilter().CanSort();
        mapper.Property<ArticleResponseForSale>(x => x.Type).CanFilter().CanSort();
        mapper.Property<ArticleResponseForSale>(x => x.IsDeleted).CanFilter().CanSort();
        mapper.Property<ArticleResponseForSale>(x => x.SaleWidth).CanFilter().CanSort();
        mapper.Property<ArticleResponseForSale>(x => x.DefaultWidth).CanFilter().CanSort();
    }

    public static IQueryable<ArticleResponseForSale> GlobalSearchFilter(
        IQueryable<ArticleResponseForSale> source, string op, string[] values)
    {
        var value = values.First();

        if (value.IsNullOrEmpty())
            return source;

        return op switch
        {
            "@=" => source.Where(x =>
                x.Id.ToString().Contains(value) ||
                x.Id.ToString().Contains(value) ||
                x.Name.Contains(value) ||
                x.Code.Contains(value) ||
                x.Type.ToString().Contains(value) ||
                x.SaleWidth.ToString().Contains(value) ||
                x.DefaultWidth.ToString().Contains(value)
            ),
            _ => source
        };
    }
}