using AutoMapper;
using AutoMapper.QueryableExtensions;
using EMessa.Base.Constants;
using EMessa.Core.Common.Results;
using EMessa.Core.Features.Sales.Queries.CommonResponses;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using MediatR;
using Messa.Core.BL.eMessa.Sales.ViewModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Queries.GetSaleArticlesByArticleIdSaleId;

public record GetSaleArticlesByArticleIdSaleIdQuery(int ArticleId, int SaleId) : IRequest<Result<SaleArticleResponse>>;

public class GetSaleArticlesByArticleIdSaleIdQueryHandler(
    IAppStateService appStateService,
    ILogger<GetSaleArticlesByArticleIdSaleIdQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IRollDataService rollDataService,
    IMapper mapper)
    : IRequestHandler<GetSaleArticlesByArticleIdSaleIdQuery, Result<SaleArticleResponse>>
{
    public async Task<Result<SaleArticleResponse>> Handle(
        GetSaleArticlesByArticleIdSaleIdQuery request,
        CancellationToken cancellationToken)
    {
        var loggedUser = appStateService.UserData;
        if (!loggedUser.HasAnyRole(Role.Administrator, Role.Client))
        {
            return await Result<SaleArticleResponse>.FailAsync("Brak uprawnień.");
        }

        try
        {
            //todo czy mamy uwzględniać date oraz SaleStatus.Active? Co robimy jak przeminie?
            var dateNow = DateTime.UtcNow;
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);
            var query = dbContext.SalesArticles
                .Include(x => x.Sale)
                .Include(x => x.Sale).ThenInclude(s => s.SalesCustomers)
                .Include(x => x.Sale).ThenInclude(s => s.Rolls)
                .Include(x => x.Sale).ThenInclude(s => s.Prices)
                .Include(x => x.Sale).ThenInclude(s => s.Color)
                .Include(x => x.Sale).ThenInclude(s => s.Coat)
                .Include(x => x.Sale).ThenInclude(s => s.Thick)
                .Include(x => x.Sale).ThenInclude(s => s.Translations)
                .Include(x => x.Sale).ThenInclude(s => s.OrderItems)
                .Where(x => x.ArticleId == request.ArticleId
                            && x.SaleId == request.SaleId
                            && x.Sale.Status == SaleStatus.Active
                            && x.Sale.DateFrom <= dateNow
                            && x.Sale.DateTo >= dateNow);

            if (!loggedUser.HasAdminRole)
            {
                query = query.Where(x => x.Sale.SalesCustomers.Any(sc => sc.CustomerId == loggedUser.CustomerId));
            }

            var saleArticle = await query
                .AsNoTracking()
                .ProjectTo<SaleArticleResponse>(mapper.ConfigurationProvider)
                .FirstOrDefaultAsync(cancellationToken);

            if (saleArticle == null)
            {
                return await Result<SaleArticleResponse>.FailAsync("Nie znaleziono artykułu w promocji.");
            }
            
            try
            {
                await rollDataService.UpdateRollStatusAsync(saleArticle.Rolls, cancellationToken);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error occurred while updating roll statuses for ArticleId: {ArticleId}, SaleId: {SaleId}", request.ArticleId, request.SaleId);
            }

            return await Result<SaleArticleResponse>.SuccessAsync(saleArticle);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Błąd podczas pobierania artykułu promocyjnego po ArticleId i SaleId: {Message}", ex.Message);
            return await Result<SaleArticleResponse>.FailAsync("Wystąpił błąd podczas pobierania artykułu promocyjnego.");
        }
    }
} 