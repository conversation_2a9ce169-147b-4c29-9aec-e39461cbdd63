using EMessa.Base.Enums;

namespace EMessa.Core.Features.Sales.Queries.CommonResponses;

public class ArticleResponseForSale
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public ArticleType Type { get; set; }
    public bool IsDeleted { get; set; }
    public decimal DefaultWidth { get; set; }
    public decimal SaleWidth { get; set; }
}
