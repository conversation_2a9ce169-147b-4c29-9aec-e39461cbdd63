using EMessa.Core.Features.Customers.Queries;
using EMessa.Core.Features.OptionValues.Queries.GetAllValueForOption;
using EMessa.Core.Features.SaleTranslations.Queries.GetSaleTranslations;
using Messa.Core.BL.eMessa.Sales.ViewModels;

namespace EMessa.Core.Features.Sales.Queries.CommonResponses;

public class SaleResponse : SaleWeightCalculatorBase
{
    public int Id { get; set; }
    public string Name { get; set; } = null!;
    public string Description { get; set; } = "";
    public SaleStatus Status { get; set; }
    public DateTime DateFrom { get; set; }
    public DateTime DateTo { get; set; }
    public bool IsDeleted { get; set; }

    public int CoatId { get; set; }
    public int ColorId { get; set; }
    public int ThickId { get; set; }

    public GetAllValuesForOptionResponse Coat { get; set; } = null!;
    public GetAllValuesForOptionResponse Color { get; set; } = null!;
    public GetAllValuesForOptionResponse Thick { get; set; } = null!;

    public List<ArticleResponseForSale> Articles { get; set; } = [];
    public List<GetCustomersResponseBase> Customers { get; set; } = [];
    public List<SalePriceResponse> Prices { get; set; } = [];
    public List<GetSaleTranslationsResponse> Translations { get; set; } = [];

    public bool DateFromToActive =>
        DateTime.Now > DateFrom &&
        DateTime.Now < DateTo;

    public int RollsCount => Rolls.Count;
    public int OrderItemsCount => OrderItems.Count;
    public int CustomersCount => Customers.Count;
    public int ArticlesCount => Articles.Count;
}
