using EMessa.Base.Enums;
using EMessa.Core.Features.Sales.Queries.GetSaleRolls;

namespace EMessa.Core.Features.Sales.Queries.CommonResponses;

public abstract class SaleWeightCalculatorBase
{
    public List<OrderItemResponseForSale> OrderItems { get; set; } = [];
    public List<SaleRollResponse> Rolls { get; set; } = [];
    public decimal DefinedWeight { get; set; }

    // Dostępna waga (kg)
    public decimal AvailableWeight
    {
        get
        {
            decimal totalOrderItemWeight = OrderItems
                .Where(x => x.OrderStatus != OrderStatus.Rejected && x.OrderStatus != OrderStatus.Unknown)
                .Sum(x => x.OrderItemWeight);
            decimal totalCurrentRollWeight = Rolls.Sum(x => x.CurrentWeight);
            decimal totalRollSaleWeight = Rolls.Sum(x => x.SaleWeight);
            decimal availableRollWeight = Math.Min(totalCurrentRollWeight, totalRollSaleWeight);

            decimal availableWeightFromEMessa = DefinedWeight > 0
                ? DefinedWeight - totalOrderItemWeight
                : totalRollSaleWeight - totalOrderItemWeight;
            decimal availableWeight = Math.Min(availableWeightFromEMessa, availableRollWeight);
            return availableWeight;
        }
    }

    // Dostępna długość (mb)
    public decimal AvailableLength => CalculateAvailableLength(AvailableWeight, (this as IHasEfficiency)?.Efficiency ?? 0m, Rolls);

    public static decimal CalculateAvailableLength(decimal availableWeight, decimal efficiency, List<SaleRollResponse> rolls)
    {
        if (availableWeight <= 0)
            return 0m;

        if (rolls == null || rolls.Count == 0)
        {
            if (efficiency <= 0)
                return 0m;
            var lengthFromEfficiency = Math.Round(availableWeight / efficiency, 0, MidpointRounding.AwayFromZero);
            return lengthFromEfficiency;
        }

        // Średnia ważona efektywności
        var rollStats = rolls
            .Where(roll => roll is { Weight: > 0, CurrentWeight: > 0, Efficiency: > 0 })
            .Aggregate(
                (count: 0, totalWeightedEfficiency: 0m, totalWeight: 0m),
                (acc, roll) => (
                    acc.count + 1,
                    acc.totalWeightedEfficiency + roll.Efficiency * roll.Weight,
                    acc.totalWeight + roll.Weight
                ));

        if (rollStats.count == 0)
            return 0m;

        var weightedAverageEfficiency = rollStats.totalWeightedEfficiency / rollStats.totalWeight;
        if (weightedAverageEfficiency <= 0)
            return 0m;

        var lengthFromWeightedAverage = Math.Round(availableWeight / weightedAverageEfficiency, 0, MidpointRounding.AwayFromZero);
        return lengthFromWeightedAverage;
    }
}

public interface IHasEfficiency
{
    decimal Efficiency { get; }
}