using EMessa.Base.Constants;
using EMessa.Core.Common.Results;
using EMessa.Core.Features.AppState;
using EMessa.Core.Features.Sales.Commands.Common;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Sales;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Commands.DuplicateSale;

public record DuplicateSaleCommand(int SaleId) : IRequest<IResult<int>>, ISaleIdCommand;

public class DuplicateSaleCommandHandler(
    ILogger<DuplicateSaleCommandHandler> logger,
    IAppStateService appStateService,
    IDbContextFactory<ApplicationDbContext> dbFactory)
    : IRequestHandler<DuplicateSaleCommand, IResult<int>>
{
    public async Task<IResult<int>> Handle(DuplicateSaleCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var loggedUser = appStateService.UserData;

            if (!CheckPermissionToSale.CanAddOrEditSale(loggedUser))
                return await Result<int>.FailAsync(ResultErrorMessages.Unauthorized);

            var validationResult = await new DuplicateSaleValidator(dbFactory, logger)
                .ValidateAsync(request, cancellationToken);

            if (!validationResult.IsValid)
                return await Result<int>.FailAsync(validationResult);


            await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

            var sale = await dbContext.Sales
                    .Include(x => x.Prices)
                    .Include(x => x.Translations)
                    .Include(x => x.SalesArticles)
                    .Include(x => x.SalesCustomers)
                    .AsNoTracking()
                    .SingleAsync(x => x.Id == request.SaleId, cancellationToken);

            var newSale = DuplicateSale(sale, loggedUser);

            AddMissingTranslationsAsync(sale, newSale);

            await dbContext.Sales.AddAsync(newSale, cancellationToken);

            await dbContext.SaveChangesAsync(cancellationToken);
            return await Result<int>.SuccessAsync(newSale.Id);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result<int>.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }

    private static Sale DuplicateSale(Sale sale, UserData userData)
    {
        var newSale = new Sale
        {
            Name = sale.Name,
            Description = sale.Description,
            Status = Messa.Core.BL.eMessa.Sales.ViewModels.SaleStatus.New,
            DateFrom = sale.DateFrom,
            DateTo = sale.DateTo,
            IsDeleted = false,
            DefinedWeight = sale.DefinedWeight,
            CoatId = sale.CoatId,
            ColorId = sale.ColorId,
            ThickId = sale.ThickId,
            Efficiency = sale.Efficiency,
            CreatedDate = DateTime.UtcNow,
            CreatedById = userData.UserProfileId,
            Prices = [.. sale.Prices.Select(p => new DAL.Entities.Sales.SalePrice
            {
                Price = p.Price,
                CurrencyCode = p.CurrencyCode
            })],
            Translations = [.. sale.Translations.Select(t => new SaleTranslation
            {
                Name = t.Name,
                Description = t.Description,
                LanguageCode = t.LanguageCode
            })],
            SalesArticles = [.. sale.SalesArticles.Select(sa => new SaleArticle
            {
                ArticleId = sa.ArticleId,
                Width = sa.Width
            })],
            SalesCustomers = [.. sale.SalesCustomers.Select(sc => new SaleCustomer
            {
                CustomerId = sc.CustomerId
            })]
        };

        return newSale;
    }

    private static void AddMissingTranslationsAsync(Sale sale, Sale newSale)
    {
        foreach (var culture in SystemConstants.SupportedCultures
            .Where(x => !x.Code.Equals(SystemConstants.DefaultLanguageCode, StringComparison.OrdinalIgnoreCase)).ToList())
        {
            if (!newSale.Translations.Any(x => x.LanguageCode == culture.Code))
            {
                newSale.Translations.Add(new SaleTranslation
                {
                    LanguageCode = culture.Code,
                    Name = sale.Name,
                    Description = sale.Description
                });
            }
        }
    }
}