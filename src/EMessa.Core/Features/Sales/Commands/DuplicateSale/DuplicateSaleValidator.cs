using EMessa.Core.Features.Sales.Commands.AddSaleCustomer;
using EMessa.Core.Features.Sales.Commands.Common;
using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Commands.DuplicateSale;

public class DuplicateSaleValidatorBase : AbstractValidator<DuplicateSaleCommand>
{
    public DuplicateSaleValidatorBase()
    {
        RuleFor(x => x.SaleId)
            .GreaterThan(0).WithMessage("Nie wybrano żadnej {0}.".Tr("promocji"))
            .CustomAsync(ValidateSaleIdAsync);
    }

    protected virtual Task ValidateSaleIdAsync(
        int saleId, ValidationContext<DuplicateSaleCommand> context, CancellationToken cancellationToken)
            => Task.CompletedTask;
}

public class DuplicateSaleValidator(
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ILogger logger)
    : DuplicateSaleValidatorBase
{
    protected override async Task ValidateSaleIdAsync(
        int saleId, ValidationContext<DuplicateSaleCommand> context, CancellationToken cancellationToken) =>
            await CommonSaleValidator.ValidateSaleIdAsync(
                logger, dbContextFactory, saleId, context, cancellationToken);
}