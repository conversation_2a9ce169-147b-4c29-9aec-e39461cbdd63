using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using MediatR;
using Messa.Core.BL.eMessa.Sales.ViewModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Commands.CloseSale;

public record class CloseSaleCommand(int SaleId) : IRequest<IResult>;

public class CloseSaleCommandHandler(
    ILogger<CloseSaleCommandHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbFactory,
    IAppStateService appStateService)
    : IRequestHandler<CloseSaleCommand, IResult>
{
    public async Task<IResult> Handle(CloseSaleCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var loggedUser = appStateService.UserData;

            await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

            var sale = await dbContext.Sales
                .SingleOrDefaultAsync(s => s.Id == request.SaleId, cancellationToken);

            if (sale == null)
                return await Result.FailAsync(ResultErrorMessages.NotFound);

            sale.Status = SaleStatus.Closed;
            sale.ClosedById = loggedUser.UserProfileId;
            sale.ClosedDate = DateTime.UtcNow;

            dbContext.Sales.Update(sale);
            await dbContext.SaveChangesAsync(cancellationToken);
            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}