using EMessa.Core.Common.Results;
using EMessa.Core.Features.Sales.Commands.Common;
using EMessa.Core.Interfaces;
using EMessa.Core.Localizer;
using EMessa.Core.Models;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Sales;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Commands.AddSaleImages;
public record AddSaleImagesCommand(int SaleId, IEnumerable<ImageModel> SaleImages) : IRequest<IResult>, ISaleIdCommand;

public class AddSaleImagesCommandHandler(
    ILogger<AddSaleImagesCommandHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IFileStorageService fileStorage,
    IConfiguration configuration)
    : IRequestHandler<AddSaleImagesCommand, IResult>
{
    public async Task<IResult> Handle(AddSaleImagesCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var validationResult = await new AddSaleImgaesValidator(dbContextFactory, logger, configuration)
                    .ValidateAsync(request, cancellationToken);

            if (!validationResult.IsValid)
                return await Result.FailAsync(validationResult);

            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            if (request.SaleId <= 0)
            {
                return await Result.FailAsync("Nie wybrano żadnej promocji.".Tr());
            }
            var sale = await dbContext.Sales.FindAsync([request.SaleId], cancellationToken);
            if (sale is null)
            {
                return await Result.FailAsync("Promocja o podanym ID nie istnieje.".Tr());
            }

            foreach (var image in request.SaleImages)
            {
                var fileExtension = image.FileExtension.TrimStart('.').ToLowerInvariant() ?? "";

                if (image.FileContent == null || image.FileContent.Length == 0)
                {
                    continue; // Skip empty images
                }
                var result = await fileStorage.SaveFileAsync(image.FileContent, image.FileName, fileExtension, FileStorageType.SaleImages, cancellationToken);

                var saleImage = new SaleImage
                {
                    SaleId = request.SaleId,
                    OriginalName = $"{image.FileName}.{fileExtension}",
                    FilePath = result,
                    CreatedDate = DateTime.UtcNow
                };

                dbContext.SaleImages.Add(saleImage);
            }

            await dbContext.SaveChangesAsync(cancellationToken);

            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error while adding sale images.");
            return await Result.FailAsync("Wystąpił błąd podczas dodawania zdjęć do promocji.".Tr());
        }
    }
}
