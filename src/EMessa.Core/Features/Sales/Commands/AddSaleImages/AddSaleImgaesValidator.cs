using EMessa.Core.Features.Sales.Commands.Common;
using EMessa.Core.Localizer;
using EMessa.Core.Models;
using EMessa.DAL.Data;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Commands.AddSaleImages;

public class AddSaleImgaesValidatorBase : AbstractValidator<AddSaleImagesCommand>
{
    public AddSaleImgaesValidatorBase()
    {
        RuleFor(x => x.SaleId)
            .GreaterThan(0).WithMessage("Nie wybrano żadnej {0}.".Tr("promocji"))
            .CustomAsync(ValidateSaleIdAsync);

        RuleFor(x => x.SaleImages)
            .NotEmpty().WithMessage("Nie wybrano żadnego {0}.".Tr("zdjęcia promocji"))
            .Custom(ValidateSaleImages);
    }

    protected virtual Task ValidateSaleIdAsync(
        int saleId, ValidationContext<AddSaleImagesCommand> context, CancellationToken cancellationToken)
            => Task.CompletedTask;

    protected virtual void ValidateSaleImages(
        IEnumerable<ImageModel> saleImages, ValidationContext<AddSaleImagesCommand> context)
    {
        return;
    }
}

public class AddSaleImgaesValidator(
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ILogger logger,
    IConfiguration configuration)
    : AddSaleImgaesValidatorBase
{
    protected override async Task ValidateSaleIdAsync(
        int saleId, ValidationContext<AddSaleImagesCommand> context, CancellationToken cancellationToken) =>
            await CommonSaleValidator.ValidateSaleIdAsync(
                logger, dbContextFactory, saleId, context, cancellationToken);

    protected override void ValidateSaleImages(
        IEnumerable<ImageModel> saleImages, ValidationContext<AddSaleImagesCommand> context)
    {
        try
        {
            var allowedExtensions = configuration.GetSection("FileUploadSettings:AllowedSaleImageExtensions")
                                   .Get<string[]>() ?? throw new InvalidOperationException("Nie znaleziono konfiguracji dozwolonych rozszerzeń plików.");
            var maxSizeInMbConfig = configuration["FileUploadSettings:MaxSizeOfSaleImagesInMb"]
                ?? throw new InvalidOperationException("Nie znaleziono konfiguracji maksymalnego rozmiaru plików (FileUploadSettings:MaxSizeOfSaleImagesInMb).");

            var allowedExtensionsSet = allowedExtensions
                .Select(e => e.ToLowerInvariant())
                .ToHashSet();

            if (!int.TryParse(maxSizeInMbConfig, out var maxSizeInMb))
                throw new InvalidOperationException("Nieprawidłowy format MaxSizeOfSaleImagesInMb.");

            var maxSizeInBytes = maxSizeInMb * 1024L * 1024L;

            foreach (var image in saleImages)
            {
                var ext = image.FileExtension.TrimStart('.').ToLowerInvariant() ?? "";
                if (!allowedExtensions.Contains(ext))
                {
                    var message = string.Format("Plik '{0}' ma niedozwolone rozszerzenie '{1}'. Dozwolone: {2}".Tr(),
                        image.FileName, ext, string.Join(", ", allowedExtensions));
                    context.AddFailure(nameof(AddSaleImagesCommand.SaleImages), message);
                }

                if (image.FileContent == null || image.FileContent.Length == 0)
                {
                    var message = string.Format("Plik '{0}' jest pusty.".Tr(), image.FileName);
                    context.AddFailure(nameof(AddSaleImagesCommand.SaleImages), message);
                    continue;
                }

                if (image.FileContent.Length > maxSizeInBytes)
                {
                    var message = string.Format("Plik '{0}' przekracza maksymalny rozmiar {1} MB.".Tr(), image.FileName, maxSizeInMb);
                    context.AddFailure(nameof(AddSaleImagesCommand.SaleImages), message);
                }
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Błąd podczas walidacji zdjęć promocji.");
            context.AddFailure(nameof(AddSaleImagesCommand.SaleImages), "Wystąpił błąd podczas walidacji zdjęć promocji.".Tr());
        }
    }
}