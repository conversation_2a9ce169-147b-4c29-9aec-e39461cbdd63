using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Commands.RemoveSaleArticle;

public record RemoveSaleArticleCommand(int SaleId, IEnumerable<int> ArticleIds) : IRequest<IResult>;

public class RemoveSaleArticleCommandHandler(
    ILogger<RemoveSaleArticleCommandHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbFactory)
    : IRequestHandler<RemoveSaleArticleCommand, IResult>
{
    public async Task<IResult> Handle(RemoveSaleArticleCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var validationResult = await new RemoveSaleArticleValidator(dbFactory, logger)
                    .ValidateAsync(request, cancellationToken);

            if (!validationResult.IsValid)
                return await Result.FailAsync(validationResult);

            await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

            var saleArticles = await dbContext.SalesArticles
                .AsTracking()
                .Where(x => x.SaleId == request.SaleId && request.ArticleIds.Contains(x.ArticleId))
                .ToListAsync(cancellationToken);

            if (!saleArticles.Any())
            {
                logger.LogInformation("Żaden z podanych produktów ({ArticleIds}) nie był powiązany z promocją {SaleId}.", string.Join(", ", request.ArticleIds), request.SaleId);
                return await Result.SuccessAsync();
            }

            dbContext.SalesArticles.RemoveRange(saleArticles);

            await dbContext.SaveChangesAsync(cancellationToken);
            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}