using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Commands.RemoveSaleArticle;

public class RemoveSaleArticleValidatorBase : AbstractValidator<RemoveSaleArticleCommand>
{
    public RemoveSaleArticleValidatorBase()
    {
        RuleFor(x => x.SaleId)
            .GreaterThan(0).WithMessage("Nie wybrano żadnej {0}.".Tr("promocji"))
            .CustomAsync(ValidateSaleIdAsync);

        RuleFor(x => x.ArticleIds)
            .NotEmpty().WithMessage("Nie wybrano żadnego {0}.".Tr("artykułu"))
            .CustomAsync(ValidateArticleIdsAsync);
    }

    protected virtual Task ValidateSaleIdAsync(
        int saleId, ValidationContext<RemoveSaleArticleCommand> context, CancellationToken cancellationToken)
            => Task.CompletedTask;

    protected virtual Task ValidateArticleIdsAsync(
        IEnumerable<int> articleIds, ValidationContext<RemoveSaleArticleCommand> context, CancellationToken cancellationToken)
            => Task.CompletedTask;
}

public class RemoveSaleArticleValidator(
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ILogger logger)
    : RemoveSaleArticleValidatorBase
{
    protected override async Task ValidateSaleIdAsync(
        int saleId, ValidationContext<RemoveSaleArticleCommand> context, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var sale = await dbContext.Sales
                .AsNoTracking()
                .SingleOrDefaultAsync(x => x.Id == saleId, cancellationToken);

            if (sale is null)
            {
                context.AddFailure(nameof(RemoveSaleArticleCommand.SaleId), "Promocja o podanym ID nie istnieje.".Tr());
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            throw;
        }
    }

    protected override async Task ValidateArticleIdsAsync(
        IEnumerable<int> articleIds, ValidationContext<RemoveSaleArticleCommand> context, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var sale = await dbContext.Sales
            .Include(x => x.SalesArticles)
            .AsNoTracking()
            .SingleAsync(x => x.Id == context.InstanceToValidate.SaleId, cancellationToken);

            var actualArticleIdsInSale = sale.SalesArticles.Select(a => a.ArticleId).ToHashSet();

            var articlesRequestedForRemovalButNotInSale = articleIds
                .Where(idToRemove => !actualArticleIdsInSale.Contains(idToRemove))
                .ToList();

            if (articlesRequestedForRemovalButNotInSale.Any())
            {
                context.AddFailure(
                    nameof(RemoveSaleArticleCommand.ArticleIds),
                    "Następujące artykuły nie znajdują się w tej promocji i nie mogą zostać z niej usunięte:".Tr() +
                    " " + string.Join(", ", articlesRequestedForRemovalButNotInSale)
                );
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            throw;
        }
    }
}