using EMessa.Base.Constants;
using EMessa.Core.Features.AppState;

namespace EMessa.Core.Features.Sales.Commands.Common;

internal static class CheckPermissionToSale
{
    private static readonly string[] AllowedRoles = { Role.SaleManager, Role.Administrator };

    public static bool CanAddOrEditSale(UserData userData)
    {
        return userData.Roles.Any(role => AllowedRoles.Contains(role));
    }

    public static bool CanCancelSale(UserData userData)
    {
        return userData.Roles.Any(role => AllowedRoles.Contains(role));
    }

    public static bool CanDeleteSale(UserData userData)
    {
        return userData.Roles.Any(role => role.Contains(Role.Administrator));
    }
}
