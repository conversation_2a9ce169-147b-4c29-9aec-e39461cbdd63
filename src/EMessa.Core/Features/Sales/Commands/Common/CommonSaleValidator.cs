using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Commands.Common;

public static class CommonSaleValidator
{
    public static async Task ValidateSaleIdAsync<T>(
        ILogger logger,
        IDbContextFactory<ApplicationDbContext> dbContextFactory,
        int saleId,
        ValidationContext<T> context,
        CancellationToken cancellationToken) where T : ISaleIdCommand
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var sale = await dbContext.Sales
                .AsNoTracking()
                .SingleOrDefaultAsync(x => x.Id == saleId, cancellationToken);

            if (sale is null)
            {
                context.AddFailure(nameof(ISaleIdCommand.SaleId), "Promocja o podanym ID nie istnieje.".Tr());
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            throw;
        }
    }
}

public interface ISaleIdCommand
{
    int SaleId { get; }
}
