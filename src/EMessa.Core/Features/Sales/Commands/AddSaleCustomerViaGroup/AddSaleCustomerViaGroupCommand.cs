using EMessa.Core.Common.Results;
using EMessa.Core.Features.Sales.Commands.Common;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Sales;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Commands.AddSaleCustomerViaGroup;

public record AddSaleCustomerViaGroupCommand(int SaleId, IEnumerable<int> CustomerGroupIds) : IRequest<IResult>, ISaleIdCommand;

public class AddSaleCustomerViaGroupCommandHandler(
    ILogger<AddSaleCustomerViaGroupCommandHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbFactory)
    : IRequestHandler<AddSaleCustomerViaGroupCommand, IResult>
{
    public async Task<IResult> Handle(AddSaleCustomerViaGroupCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var validationResult = await new AddSaleCustomerViaGroupValidator(dbFactory, logger)
                           .ValidateAsync(request, cancellationToken);

            if (!validationResult.IsValid)
                return await Result.FailAsync(validationResult);

            await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

            var customerIds = await dbContext.CustomersGroupCustomers
                .AsNoTracking()
                .Where(x => request.CustomerGroupIds.Contains(x.CustomerGroupId))
                .Select(x => x.CustomerId)
                .Distinct()
                .ToListAsync(cancellationToken);

            var existingCustomerIds = dbContext.SalesCustomers
                .AsNoTracking()
                .Where(x => x.SaleId == request.SaleId)
                .Select(x => x.CustomerId)
                .ToHashSet();

            foreach (var customerId in customerIds)
            {
                if (!existingCustomerIds.Contains(customerId))
                {
                    dbContext.SalesCustomers.Add(new SaleCustomer
                    {
                        SaleId = request.SaleId,
                        CustomerId = customerId
                    });
                }
            }

            await dbContext.SaveChangesAsync(cancellationToken);
            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}