using EMessa.Core.Features.Sales.Commands.AddSaleCustomer;
using EMessa.Core.Features.Sales.Commands.Common;
using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Commands.AddSaleCustomerViaGroup;

public class AddSaleCustomerViaGroupValidatorBase : AbstractValidator<AddSaleCustomerViaGroupCommand>
{
    public AddSaleCustomerViaGroupValidatorBase()
    {
        RuleFor(x => x.SaleId)
            .GreaterThan(0).WithMessage("Nie wybrano żadnej {0}.".Tr("promocji"))
            .CustomAsync(ValidateSaleIdAsync);

        RuleFor(x => x.CustomerGroupIds)
            .NotEmpty().WithMessage("Nie wybrano żadnej {0}.".Tr("grupy klientów"))
            .CustomAsync(ValidateCustomerGroupIdsAsync);
    }

    protected virtual Task ValidateSaleIdAsync(
        int saleId, ValidationContext<AddSaleCustomerViaGroupCommand> context, CancellationToken cancellationToken)
            => Task.CompletedTask;

    protected virtual Task ValidateCustomerGroupIdsAsync(
        IEnumerable<int> customerGroupIds, ValidationContext<AddSaleCustomerViaGroupCommand> context, CancellationToken cancellationToken)
            => Task.CompletedTask;
}

public class AddSaleCustomerViaGroupValidator(
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ILogger logger)
    : AddSaleCustomerViaGroupValidatorBase
{
    protected override async Task ValidateSaleIdAsync(
        int saleId, ValidationContext<AddSaleCustomerViaGroupCommand> context, CancellationToken cancellationToken) =>
            await CommonSaleValidator.ValidateSaleIdAsync(
                logger, dbContextFactory, saleId, context, cancellationToken);

    protected override async Task ValidateCustomerGroupIdsAsync(
        IEnumerable<int> customerGroupIds, ValidationContext<AddSaleCustomerViaGroupCommand> context, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var customersFromDb = await dbContext.CustomerGroups
                .AsNoTracking()
                .Where(x => customerGroupIds.Contains(x.Id))
                .ToListAsync(cancellationToken);

            if (customersFromDb.Count != customerGroupIds.Count())
            {
                var notFoundIds = customerGroupIds.Except(customersFromDb.Select(x => x.Id)).ToList();
                context.AddFailure(
                    nameof(AddSaleCustomerCommand.CustomerIds),
                    "Grupy klientów o podanych ID nie istnieją:".Tr()
                    + " " + string.Join(", ", notFoundIds));
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            throw;
        }
    }
}