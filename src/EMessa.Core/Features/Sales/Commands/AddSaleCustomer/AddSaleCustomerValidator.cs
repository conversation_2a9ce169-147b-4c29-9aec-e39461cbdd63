using EMessa.Core.Features.Sales.Commands.Common;
using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Commands.AddSaleCustomer;

public class AddSaleCustomerValidatorBase : AbstractValidator<AddSaleCustomerCommand>
{
    public AddSaleCustomerValidatorBase()
    {
        RuleFor(x => x.SaleId)
            .GreaterThan(0).WithMessage("Nie wybrano żadnej {0}.".Tr("promocji"))
            .CustomAsync(ValidateSaleIdAsync);

        RuleFor(x => x.CustomerIds)
            .NotEmpty().WithMessage("Nie wybrano żadnego {0}.".Tr("klienta"))
            .CustomAsync(ValidateCustomerIdsAsync);
    }

    protected virtual Task ValidateSaleIdAsync(
        int saleId, ValidationContext<AddSaleCustomerCommand> context, CancellationToken cancellationToken)
            => Task.CompletedTask;

    protected virtual Task ValidateCustomerIdsAsync(
        IEnumerable<int> customerIds, ValidationContext<AddSaleCustomerCommand> context, CancellationToken cancellationToken)
            => Task.CompletedTask;
}

public class AddSaleCustomerValidator(
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ILogger logger)
    : AddSaleCustomerValidatorBase
{
    protected override async Task ValidateSaleIdAsync(
        int saleId, ValidationContext<AddSaleCustomerCommand> context, CancellationToken cancellationToken) => 
            await CommonSaleValidator.ValidateSaleIdAsync(
                logger, dbContextFactory, saleId, context, cancellationToken);

    protected override async Task ValidateCustomerIdsAsync(
        IEnumerable<int> customerIds, ValidationContext<AddSaleCustomerCommand> context, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var customersFromDb = await dbContext.Customers
                .AsNoTracking()
                .Where(x => customerIds.Contains(x.Id))
                .ToListAsync(cancellationToken);

            if (customersFromDb.DistinctBy(x => x.Id).Count() != customerIds.Distinct().Count())
            {
                var notFoundIds = customerIds.Except(customersFromDb.Select(x => x.Id)).ToList();
                context.AddFailure(
                    nameof(AddSaleCustomerCommand.CustomerIds),
                    "Klienci o podanych ID nie istnieją:".Tr()
                    + " " + string.Join(", ", notFoundIds));
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            throw;
        }
    }
}