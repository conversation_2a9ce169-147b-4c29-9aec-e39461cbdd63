using EMessa.Core.Common.Results;
using EMessa.Core.Features.Sales.Commands.Common;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Sales;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Commands.AddSaleCustomer;

public record AddSaleCustomerCommand(int SaleId, IEnumerable<int> CustomerIds) : IRequest<IResult>, ISaleIdCommand;

public class AddSaleCustomerCommandHandler(
    ILogger<AddSaleCustomerCommandHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbFactory)
    : IRequestHandler<AddSaleCustomerCommand, IResult>
{
    public async Task<IResult> Handle(AddSaleCustomerCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var validationResult = await new AddSaleCustomerValidator(dbFactory, logger)
                    .ValidateAsync(request, cancellationToken);

            if (!validationResult.IsValid)
                return await Result.FailAsync(validationResult);

            await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

            var saleCustomerIds = request.CustomerIds.Distinct().ToList();

            var saleCustomers = await dbContext.SalesCustomers
                .AsNoTracking()
                .Where(x => x.SaleId == request.SaleId)
                .ToListAsync(cancellationToken);

            foreach (var customerId in saleCustomerIds)
            {
                if (saleCustomers.All(x => x.CustomerId != customerId))
                {
                    dbContext.SalesCustomers.Add(new SaleCustomer
                    {
                        SaleId = request.SaleId,
                        CustomerId = customerId
                    });
                }
            }

            await dbContext.SaveChangesAsync(cancellationToken);
            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}