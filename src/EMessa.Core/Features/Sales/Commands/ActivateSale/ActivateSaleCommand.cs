using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using MediatR;
using Messa.Core.BL.eMessa.Sales.ViewModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Commands.ActivateSale;

public record class ActivateSaleCommand(int SaleId) : IRequest<IResult>;

public class ActivateSaleCommandHandler(
    ILogger<ActivateSaleCommandHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbFactory,
    IAppStateService appStateService)
    : IRequestHandler<ActivateSaleCommand, IResult>
{
    public async Task<IResult> Handle(ActivateSaleCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var loggedUser = appStateService.UserData;

            await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

            var sale = await dbContext.Sales
                .SingleOrDefaultAsync(s => s.Id == request.SaleId, cancellationToken);

            if (sale == null)
                return await Result.FailAsync("Promocja o podanym ID nie istnieje.".Tr());

            sale.Status = SaleStatus.Active;
            sale.ActivatedById = loggedUser.UserProfileId;
            sale.ActivatedDate = DateTime.UtcNow;
            sale.ClosedById = null;
            sale.ClosedDate = null;

            dbContext.Sales.Update(sale);
            await dbContext.SaveChangesAsync(cancellationToken);
            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}