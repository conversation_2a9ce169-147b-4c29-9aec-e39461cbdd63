using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Commands.RemoveSaleRoll;

public class RemoveSaleRollValidatorBase : AbstractValidator<RemoveSaleRollCommand>
{
    public RemoveSaleRollValidatorBase()
    {
        RuleFor(x => x.SaleRollIds)
            .NotEmpty().WithMessage("Nie wybrano żadnego {0}.".Tr("kręgu"))
            .CustomAsync(ValidateSaleRollIdsAsync);
    }

    protected virtual Task ValidateSaleRollIdsAsync(
        IEnumerable<int> saleRollIds, ValidationContext<RemoveSaleRollCommand> context, CancellationToken cancellationToken)
            => Task.CompletedTask;
}

public class RemoveSaleRollValidator(
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ILogger logger)
    : RemoveSaleRollValidatorBase
{
    protected override async Task ValidateSaleRollIdsAsync(
    IEnumerable<int> saleRollIds, ValidationContext<RemoveSaleRollCommand> context, CancellationToken cancellationToken)
    {
        await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

        try
        {
            // Pobranie kręgów z bazy na podstawie przekazanych ID
            var existingSaleRolls = await dbContext.SaleRolls
                .Where(x => saleRollIds.Contains(x.Id))
                .AsNoTracking()
                .Select(x => x.Id)
                .ToListAsync(cancellationToken);

            // Obliczenie różnicy – które ID nie zostały znalezione w bazie
            var invalidSaleRollIds = saleRollIds
                .Where(id => !existingSaleRolls.Contains(id))
                .ToList();

            if (invalidSaleRollIds.Count != 0)
            {
                context.AddFailure(
                    nameof(RemoveSaleRollCommand.SaleRollIds),
                    "Następujące kręgi (ID) nie zostały znalezione jako kręgi promocji:".Tr() +
                    " " + string.Join(", ", invalidSaleRollIds)
                );
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            throw;
        }
    }
}