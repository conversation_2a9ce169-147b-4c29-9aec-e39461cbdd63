using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Commands.RemoveSaleRoll;

public record class RemoveSaleRollCommand(IEnumerable<int> SaleRollIds) : IRequest<IResult>;

public class RemoveSaleRollCommandHandler(
    ILogger<RemoveSaleRollCommandHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbFactory)
    : IRequestHandler<RemoveSaleRollCommand, IResult>
{
    public async Task<IResult> Handle(RemoveSaleRollCommand request, CancellationToken cancellationToken)
    {
        await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

        try
        {
            var validationResult = await new RemoveSaleRollValidator(dbFactory, logger)
                    .ValidateAsync(request, cancellationToken);

            if (!validationResult.IsValid)
                return await Result.FailAsync(validationResult);

            var saleRollsToDelete = await dbContext.SaleRolls
                .Where(x => request.SaleRollIds.Contains(x.Id))
                .AsTracking()
                .ToListAsync(cancellationToken);

            dbContext.SaleRolls.RemoveRange(saleRollsToDelete);

            await dbContext.SaveChangesAsync(cancellationToken);
            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}
