using EMessa.Core.Common.Results;
using EMessa.Core.Features.Sales.Commands.Common;
using EMessa.Core.Interfaces;
using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using MediatR;
using Messa.Core.BL.eMessa.Sales.ViewModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Commands.DeleteSale;

public record class DeleteSaleCommand(int SaleId) : IRequest<IResult>;

public class DeleteSaleCommandHandler(
    ILogger<DeleteSaleCommandHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbFactory,
    IAppStateService appStateService)
    : IRequestHandler<DeleteSaleCommand, IResult>
{
    public async Task<IResult> Handle(DeleteSaleCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var loggedUser = appStateService.UserData;

            if (!CheckPermissionToSale.CanDeleteSale(loggedUser))
                return await Result<int>.FailAsync(ResultErrorMessages.Unauthorized);

            await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

            var sale = await dbContext.Sales
                .SingleOrDefaultAsync(s => s.Id == request.SaleId, cancellationToken);

            if (sale == null)
                return await Result.FailAsync(ResultErrorMessages.NotFound);

            bool canBeDeleted = sale.Status == SaleStatus.Closed || sale.Status == SaleStatus.Canceled;
            if (!canBeDeleted)
                return await Result.FailAsync("Tylko promocje o statusie 'Zamknięte' lub 'Anulowane' mogą zostać usunięte.".Tr());

            sale.IsDeleted = true;

            dbContext.Sales.Update(sale);
            await dbContext.SaveChangesAsync(cancellationToken);
            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}