using EMessa.Base.Enums;
using EMessa.Core.Features.Sales.Commands.Common;
using EMessa.Core.Interfaces;
using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Commands.AddEditSaleRoll;

public class AddSaleRollValidatorBase : AbstractValidator<AddEditSaleRollCommand>
{
    public AddSaleRollValidatorBase()
    {
        RuleFor(x => x.SaleId)
            .GreaterThan(0).WithMessage("Nie wybrano żadnej {0}.".Tr("promocji"))
            .CustomAsync(ValidateSaleIdAsync);

        RuleFor(x => x.SaleRolls)
            .NotEmpty().WithMessage("Nie wybrano żadnego {0}.".Tr("kręgu"))
            .CustomAsync(ValidateSaleRollsAsync);
    }

    protected virtual Task ValidateSaleIdAsync(
        int saleId, ValidationContext<AddEditSaleRollCommand> context, CancellationToken cancellationToken)
            => Task.CompletedTask;

    protected virtual Task ValidateSaleRollsAsync(
        IEnumerable<AddEditSaleRollRequest> saleRollsData, ValidationContext<AddEditSaleRollCommand> context, CancellationToken cancellationToken)
            => Task.CompletedTask;
}

public class AddEditSaleRollValidator(
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ILogger logger,
    IRollDataService rollDataService)
    : AddSaleRollValidatorBase
{
    protected override async Task ValidateSaleIdAsync(
        int saleId, ValidationContext<AddEditSaleRollCommand> context, CancellationToken cancellationToken) =>
            await CommonSaleValidator.ValidateSaleIdAsync(
                logger, dbContextFactory, saleId, context, cancellationToken);

    protected override async Task ValidateSaleRollsAsync(
        IEnumerable<AddEditSaleRollRequest> saleRollsData, ValidationContext<AddEditSaleRollCommand> context, CancellationToken cancellationToken)
    {
        try
        {
            var rolls = await rollDataService.GetRollsByIdsAsync(saleRollsData.Select(x => x.RollId), cancellationToken);

            if (!rolls.Any())
            {
                context.AddFailure("Nie udało się pobrać kręgów.".Tr());
                return;
            }

            if (rolls.Count() != saleRollsData.Count())
                context.AddFailure("Nie wszystkie kręgi zostały znalezione.".Tr());


            if (rolls.Any(r => r.Status != RollStatus.Active))
                context.AddFailure("Nie można dodać kręgów, które nie są aktywne.".Tr());


            if (saleRollsData.Any(roll => roll.SaleWeight > rolls.First(r => r.RollId == roll.RollId).CurrentWeight))
                context.AddFailure("Waga promocji nie może być większa niż aktualna waga kręgu.".Tr());
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            throw;
        }
    }
}