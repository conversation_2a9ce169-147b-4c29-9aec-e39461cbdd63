using AutoMapper;
using EMessa.Core.Common.Results;
using EMessa.Core.Features.Sales.Commands.Common;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Sales;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Commands.AddEditSaleRoll;

public record AddEditSaleRollCommand(int SaleId, IEnumerable<AddEditSaleRollRequest> SaleRolls)
    : IRequest<IResult>, ISaleIdCommand;

public class AddSaleRollCommandHandler(
    ILogger<AddSaleRollCommandHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbFactory,
    IMapper mapper,
    IRollDataService rollDataService)
    : IRequestHandler<AddEditSaleRollCommand, IResult>
{
    public async Task<IResult> Handle(AddEditSaleRollCommand request, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

            var validationResult = await new AddEditSaleRollValidator(dbFactory, logger, rollDataService)
                .ValidateAsync(request, cancellationToken);

            if (!validationResult.IsValid)
                return await Result.FailAsync(validationResult);

            var existSaleRolls = await dbContext.SaleRolls
                .AsTracking()
                .Where(x => x.SaleId == request.SaleId && request.SaleRolls.Select(r => r.RollId).Contains(x.RollId))
                .ToListAsync(cancellationToken);

            var rolls = await rollDataService.GetRollsByIdsAsync(request.SaleRolls.Select(x => x.RollId), cancellationToken);

            var mappedRollsFromApi = mapper.Map<List<SaleRoll>>(rolls);

            List<SaleRoll> rollsToAdd = [];

            foreach (var roll in request.SaleRolls)
            {
                if (existSaleRolls.Any(x => x.RollId == roll.RollId))
                {
                    existSaleRolls.First(existSaleRoll => existSaleRoll.RollId == roll.RollId).SaleWeight = roll.SaleWeight;
                }
                else
                {
                    var newSaleRoll = mappedRollsFromApi.First(x => x.RollId == roll.RollId);
                    newSaleRoll.SaleId = request.SaleId;

                    newSaleRoll.SaleWeight = roll.SaleWeight;
                    rollsToAdd.Add(newSaleRoll);
                }
            }

            await dbContext.SaleRolls.AddRangeAsync(rollsToAdd, cancellationToken);

            await dbContext.SaveChangesAsync(cancellationToken);
            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}