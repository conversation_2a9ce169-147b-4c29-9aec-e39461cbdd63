using AutoMapper;
using EMessa.Core.Common.Results;
using EMessa.Core.Features.Sales.Commands.Common;
using EMessa.Core.Features.Sales.Commands.SalePrice;
using EMessa.Core.Features.SaleTranslations;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Sales;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Commands.AddEditSale;

public class AddEditSaleCommand : IRequest<IResult<int>>
{
    public int? Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime DateFrom { get; set; }
    public DateTime DateTo { get; set; }
    public decimal DefinedWeight { get; set; }

    public int CoatId { get; set; }
    public int ColorId { get; set; }
    public int ThickId { get; set; }

    public IList<SalePriceCommand> Prices { get; set; } = [];
}

public class AddEditSaleCommandHandler(
    ILogger<AddEditSaleCommandHandler> logger,
    IMapper mapper,
    IAppStateService appStateService,
    IMultiTranslationService multiTranslationService,
    IDbContextFactory<ApplicationDbContext> dbFactory)
    : IRequestHandler<AddEditSaleCommand, IResult<int>>
{
    public async Task<IResult<int>> Handle(AddEditSaleCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var loggedUser = appStateService.UserData;

            if (!CheckPermissionToSale.CanAddOrEditSale(loggedUser))
                return await Result<int>.FailAsync(ResultErrorMessages.Unauthorized);

            var validationResult = await new AddEditSaleValidator(dbFactory, logger)
                .ValidateAsync(request, cancellationToken);

            if (!validationResult.IsValid)
                return await Result<int>.FailAsync(validationResult);


            await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

            Sale sale;

            if (request.Id > 0) // edit
            {
                sale = await dbContext.Sales
                    .Include(x => x.Prices)
                    .Include(x => x.Translations)
                    .AsTracking()
                    .SingleAsync(x => x.Id == request.Id, cancellationToken);

                sale.Prices.Clear();

                mapper.Map(request, sale);
            }
            else // add
            {
                sale = mapper.Map<Sale>(request);

                sale.CreatedById = loggedUser.UserProfileId;
                sale.CreatedDate = DateTime.UtcNow;

                await dbContext.Sales.AddAsync(sale, cancellationToken);
            }

            await SaleTranslationHelper.AddMissingTranslationsAsync(sale, multiTranslationService);

            await dbContext.SaveChangesAsync(cancellationToken);

            return await Result<int>.SuccessAsync(sale.Id);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result<int>.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}
