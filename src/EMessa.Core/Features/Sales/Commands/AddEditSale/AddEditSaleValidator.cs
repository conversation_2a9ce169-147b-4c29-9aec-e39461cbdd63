using EMessa.Core.Features.Countries.Commands.AddEdit;
using EMessa.Core.Features.Sales.Commands.SalePrice;
using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Commands.AddEditSale;

public class AddEditSaleValidatorBase : AbstractValidator<AddEditSaleCommand>
{
    public AddEditSaleValidatorBase()
    {
        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Pole {0} jest wymagane.".Tr("Nazwa"))
            .MaximumLength(220).WithMessage("Pole {0} może mieć maks. długość {1} znaków.".Tr("Nazwa", "220"));

        RuleFor(x => x.DefinedWeight)
            .GreaterThanOrEqualTo(0).WithMessage("Pole {0} musi być większe lub równe {1}.".Tr("Waga", "0"));

        RuleFor(x => x.DateFrom)
            .NotNull().WithMessage("Pole {0} jest wymagane.".Tr("Data od"));

        RuleFor(x => x.DateTo)
            .NotNull().WithMessage("Pole {0} jest wymagane.".Tr("Data do"))
            .GreaterThanOrEqualTo(x => x.DateFrom).WithMessage("Data {0} musi być większa lub równa dacie {1}.".Tr("do", "od"));

        RuleFor(x => x.CoatId)
            .GreaterThan(0).WithMessage("Nie wybrano żadnej {0}.".Tr("powłoki"))
            .CustomAsync(ValidateCoatIdAsync);

        RuleFor(x => x.ColorId)
            .GreaterThan(0).WithMessage("Nie wybrano żadnego {0}.".Tr("koloru"))
            .CustomAsync(ValidateColorIdAsync);

        RuleFor(x => x.ThickId)
            .GreaterThan(0).WithMessage("Nie wybrano żadnej {0}.".Tr("grubości"))
            .CustomAsync(ValidateThickIdAsync);

        RuleFor(x => x.Prices)
            .Custom((prices, context) =>
            {
                var validator = new SalePriceCommandValidator();

                foreach (var (price, index) in prices.Select((p, i) => (p, i)))
                {
                    var result = validator.Validate(price);

                    if (!result.IsValid)
                    {
                        foreach (var failure in result.Errors)
                        {
                            context.AddFailure($"'{price.Price} {price.CurrencyCode}' " + failure.ErrorMessage.Tr());
                        }
                    }
                }
            });

        RuleFor(x => x.Id)
            .CustomAsync(ValidateSaleIdAsync);
    }

    protected virtual Task ValidateCoatIdAsync(
        int coatId, ValidationContext<AddEditSaleCommand> context, CancellationToken cancellationToken)
            => Task.CompletedTask;

    protected virtual Task ValidateColorIdAsync(
        int colorId, ValidationContext<AddEditSaleCommand> context, CancellationToken cancellationToken)
            => Task.CompletedTask;

    protected virtual Task ValidateThickIdAsync(
        int thickId, ValidationContext<AddEditSaleCommand> context, CancellationToken cancellationToken)
            => Task.CompletedTask;

    protected virtual Task ValidateSaleIdAsync(
        int? saleId, ValidationContext<AddEditSaleCommand> context, CancellationToken cancellationToken)
            => Task.CompletedTask;
}

public class AddEditSaleValidator(
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ILogger logger)
    : AddEditSaleValidatorBase
{
    protected override async Task ValidateSaleIdAsync(
        int? saleId, ValidationContext<AddEditSaleCommand> context, CancellationToken cancellationToken)
    {
        if (saleId is null)
            return;

        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var sale = await dbContext.Sales
                .AsNoTracking()
                .SingleOrDefaultAsync(x => x.Id == saleId, cancellationToken);

            if (sale is null)
            {
                context.AddFailure(nameof(AddEditSaleCommand.Id), "Promocja o podanym ID nie istnieje.".Tr());
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            throw;
        }
    }
}
