using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Commands.DeleteSaleImages;

public record DeleteSaleImagesCommand(IEnumerable<int> SaleImageIds) : IRequest<IResult>;

public class DeleteSaleImagesCommandHandler(
    ILogger<DeleteSaleImagesCommandHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IFileStorageService fileStorage)
    : IRequestHandler<DeleteSaleImagesCommand, IResult>
{
    public async Task<IResult> Handle(DeleteSaleImagesCommand request, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var saleImages = await dbContext.SaleImages
                .Where(x => request.SaleImageIds.Contains(x.Id))
                .AsTracking()
                .ToListAsync(cancellationToken);

            foreach (var saleImage in saleImages)
            {
                fileStorage.DeleteFile(saleImage.FilePath, FileStorageType.SaleImages);

                dbContext.SaleImages.Remove(saleImage);
            }

            await dbContext.SaveChangesAsync(cancellationToken);

            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error while deleting sale images.");
            return await Result.FailAsync("Wystąpił błąd podczas usuwania zdjęć do promocji.".Tr());
        }
    }
}