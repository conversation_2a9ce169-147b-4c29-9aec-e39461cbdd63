using EMessa.Core.Localizer;
using EMessa.Core.Validators;
using FluentValidation;

namespace EMessa.Core.Features.Sales.Commands.SalePrice;

public class SalePriceCommandValidator : AbstractValidator<SalePriceCommand>
{
    public SalePriceCommandValidator()
    {
        RuleFor(x => x.Price)
            .NotEmpty().WithMessage("Pole {0} jest wymagane.".Tr("Cena"))
            .GreaterThan(0).WithMessage("Cena musi być większa od zera.".Tr());

        RuleFor(x => x.CurrencyCode)
            .ValidCurrencyCode("Kod waluty");
    }
}
