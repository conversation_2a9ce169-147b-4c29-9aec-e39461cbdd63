using EMessa.Core.Common.Results;
using EMessa.Core.Features.Sales.Commands.Common;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Sales;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Commands.AddSaleArticle;

public record SaleArticleRequest(int ArticleId, decimal Widht);

public record AddSaleArticleCommand(int SaleId, IEnumerable<SaleArticleRequest> SaleArticles) : IRequest<IResult>, ISaleIdCommand;

public class AddSaleArticleCommandHandler(
    ILogger<AddSaleArticleCommandHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbFactory)
    : IRequestHandler<AddSaleArticleCommand, IResult>
{
    public async Task<IResult> Handle(AddSaleArticleCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var validationResult = await new AddSaleArticleValidator(dbFactory, logger)
                    .ValidateAsync(request, cancellationToken);

            if (!validationResult.IsValid)
                return await Result.FailAsync(validationResult);

            await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

            var salesArticles = await dbContext.SalesArticles
                .AsTracking()
                .Where(x => x.SaleId == request.SaleId)
                .ToListAsync(cancellationToken);

            foreach (var article in request.SaleArticles)
            {
                if (!salesArticles.Any(x => x.ArticleId == article.ArticleId))
                {
                    dbContext.SalesArticles.Add(new SaleArticle
                    {
                        SaleId = request.SaleId,
                        ArticleId = article.ArticleId,
                        Width = article.Widht
                    });
                }
                else
                {
                    salesArticles.First(x => x.ArticleId == article.ArticleId).Width = article.Widht;
                }
            }

            await dbContext.SaveChangesAsync(cancellationToken);
            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}