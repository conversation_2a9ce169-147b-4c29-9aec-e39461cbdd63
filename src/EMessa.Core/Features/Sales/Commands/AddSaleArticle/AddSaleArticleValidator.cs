using EMessa.Core.Features.Sales.Commands.Common;
using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Commands.AddSaleArticle;

public class AddSaleArticleValidatorBase : AbstractValidator<AddSaleArticleCommand>
{
    public AddSaleArticleValidatorBase()
    {
        RuleFor(x => x.SaleId)
            .GreaterThan(0).WithMessage("Nie wybrano żadnej {0}.".Tr("promocji"))
            .CustomAsync(ValidateSaleIdAsync);

        RuleFor(x => x.SaleArticles)
            .NotEmpty().WithMessage("Nie wybrano żadnego {0}.".Tr("artykułu"))
            .CustomAsync(ValidateSaleArticlesAsync);

        RuleForEach(x => x.SaleArticles)
            .ChildRules(article =>
            {
                article.RuleFor(x => x.Widht)
                    .GreaterThanOrEqualTo(0).WithMessage("Szerokość musi być większa lub równa {0}.".Tr("0"));
            });
    }

    protected virtual Task ValidateSaleIdAsync(
        int saleId, ValidationContext<AddSaleArticleCommand> context, CancellationToken cancellationToken)
            => Task.CompletedTask;

    protected virtual Task ValidateSaleArticlesAsync(
        IEnumerable<SaleArticleRequest> saleArticles, ValidationContext<AddSaleArticleCommand> context, CancellationToken cancellationToken)
            => Task.CompletedTask;
}

public class AddSaleArticleValidator(
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ILogger logger)
    : AddSaleArticleValidatorBase
{
    protected override async Task ValidateSaleIdAsync(
        int saleId, ValidationContext<AddSaleArticleCommand> context, CancellationToken cancellationToken) =>
            await CommonSaleValidator.ValidateSaleIdAsync(
                logger, dbContextFactory, saleId, context, cancellationToken);

    protected override async Task ValidateSaleArticlesAsync(
        IEnumerable<SaleArticleRequest> saleArticles, ValidationContext<AddSaleArticleCommand> context, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var articlesFromDb = await dbContext.Articles
                .AsNoTracking()
                .Where(x => saleArticles.Select(y => y.ArticleId).Contains(x.Id))
                .ToListAsync(cancellationToken);

            if (articlesFromDb.Count != saleArticles.Count())
            {
                var notFoundIds = saleArticles.Select(x => x.ArticleId).Except(articlesFromDb.Select(x => x.Id)).ToList();
                context.AddFailure(nameof(AddSaleArticleCommand.SaleArticles),
                    "Artykuły o podanych ID nie istnieją:".Tr()
                    + " " + string.Join(", ", notFoundIds));
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            throw;
        }
    }
}