using EMessa.Core.Features.Sales.Commands.AddSaleCustomer;
using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Commands.RemoveSaleCustomer;

public class RemoveSaleCustomerValidatorBase : AbstractValidator<RemoveSaleCustomerCommand>
{
    public RemoveSaleCustomerValidatorBase()
    {
        RuleFor(x => x.SaleId)
            .GreaterThan(0).WithMessage("Nie wybrano żadnej {0}.".Tr("promocji"))
            .CustomAsync(ValidateSaleIdAsync);

        RuleFor(x => x.CustomerIds)
            .NotEmpty().WithMessage("Nie wybrano żadnego {0}.".Tr("klienta"))
            .CustomAsync(ValidateCustomerIdsAsync);
    }

    protected virtual Task ValidateSaleIdAsync(
        int saleId, ValidationContext<RemoveSaleCustomerCommand> context, CancellationToken cancellationToken)
            => Task.CompletedTask;

    protected virtual Task ValidateCustomerIdsAsync(
        IEnumerable<int> customerIds, ValidationContext<RemoveSaleCustomerCommand> context, CancellationToken cancellationToken)
            => Task.CompletedTask;
}

public class RemoveSaleCustomerValidator(
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ILogger logger)
    : RemoveSaleCustomerValidatorBase
{
    protected override async Task ValidateSaleIdAsync(
        int saleId, ValidationContext<RemoveSaleCustomerCommand> context, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var sale = await dbContext.Sales
                .AsNoTracking()
                .SingleOrDefaultAsync(x => x.Id == saleId, cancellationToken);

            if (sale is null)
            {
                context.AddFailure("Promocja", "Promocja o podanym ID nie istnieje.".Tr());
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            throw;
        }
    }

    protected override async Task ValidateCustomerIdsAsync(
        IEnumerable<int> customerIds, ValidationContext<RemoveSaleCustomerCommand> context, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var sale = await dbContext.Sales
            .Include(x => x.SalesCustomers)
            .AsNoTracking()
            .SingleAsync(x => x.Id == context.InstanceToValidate.SaleId, cancellationToken);

            var actualCustomerIdsInSale = sale.SalesCustomers.Select(c => c.CustomerId).ToHashSet();

            var customersRequestedForRemovalButNotInSaleIds = customerIds
                .Where(idToRemove => !actualCustomerIdsInSale.Contains(idToRemove))
                .ToList();

            if (customersRequestedForRemovalButNotInSaleIds.Any())
            {
                context.AddFailure(
                    nameof(RemoveSaleCustomerCommand.CustomerIds),
                    "Następujący klienci (ID) nie są przypisani do tej promocji i nie mogą zostać z niej usunięci:".Tr() +
                    " " + string.Join(", ", customersRequestedForRemovalButNotInSaleIds)
                );
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            throw;
        }
    }
}