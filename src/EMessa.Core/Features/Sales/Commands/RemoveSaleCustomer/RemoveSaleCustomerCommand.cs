using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Sales;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Commands.RemoveSaleCustomer;

public record RemoveSaleCustomerCommand(int SaleId, IEnumerable<int> CustomerIds) : IRequest<IResult>;

public class RemoveSaleCustomerCommandHandler(
    ILogger<RemoveSaleCustomerCommandHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbFactory)
    : IRequestHandler<RemoveSaleCustomerCommand, IResult>
{
    public async Task<IResult> Handle(RemoveSaleCustomerCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var validationResult = await new RemoveSaleCustomerValidator(dbFactory, logger)
                    .ValidateAsync(request, cancellationToken);

            if (!validationResult.IsValid)
                return await Result.FailAsync(validationResult);

            await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

            var saleCustomers = await dbContext.SalesCustomers
                .AsTracking()
                .Where(x => x.SaleId == request.SaleId && request.CustomerIds.Contains(x.CustomerId))
                .ToListAsync(cancellationToken);

            if (!saleCustomers.Any())
            {
                logger.LogInformation("Żaden z podanych klientów ({CustomerIds}) nie był powiązany z promocją {SaleId}.", string.Join(", ", request.CustomerIds), request.SaleId);
                return await Result.SuccessAsync();
            }

            dbContext.SalesCustomers.RemoveRange(saleCustomers);

            await dbContext.SaveChangesAsync(cancellationToken);
            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}