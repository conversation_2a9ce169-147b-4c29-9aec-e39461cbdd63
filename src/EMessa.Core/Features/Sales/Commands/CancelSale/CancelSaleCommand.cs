using EMessa.Core.Common.Results;
using EMessa.Core.Features.Sales.Commands.Common;
using EMessa.Core.Interfaces;
using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using MediatR;
using Messa.Core.BL.eMessa.Sales.ViewModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Sales.Commands.CancelSale;

public record class CancelSaleCommand(int SaleId) : IRequest<IResult>;

public class CancelSaleCommandHandler(
    ILogger<CancelSaleCommandHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbFactory,
    IAppStateService appStateService)
    : IRequestHandler<CancelSaleCommand, IResult>
{
    public async Task<IResult> Handle(CancelSaleCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var loggedUser = appStateService.UserData;

            if (!CheckPermissionToSale.CanCancelSale(loggedUser))
                return await Result<int>.FailAsync(ResultErrorMessages.Unauthorized);

            await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

            var sale = await dbContext.Sales
                .SingleOrDefaultAsync(s => s.Id == request.SaleId, cancellationToken);

            if (sale == null)
                return await Result.FailAsync(ResultErrorMessages.NotFound);

            if (sale.Status != SaleStatus.New)
                return await Result.FailAsync("Tylko promocje o statusie 'Nowe' mogą zostać anulowane.".Tr());

            sale.Status = SaleStatus.Canceled;
            sale.ClosedById = loggedUser.UserProfileId;
            sale.ClosedDate = DateTime.UtcNow;

            dbContext.Sales.Update(sale);
            await dbContext.SaveChangesAsync(cancellationToken);
            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}