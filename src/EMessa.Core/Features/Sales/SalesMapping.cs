using AutoMapper;
using EMessa.Core.Features.Articles.Queries.GetAllBySieve;
using EMessa.Core.Features.Rolls.Queries;
using EMessa.Core.Features.Sales.Commands.AddEditSale;
using EMessa.Core.Features.Sales.Commands.SalePrice;
using EMessa.Core.Features.Sales.Queries.CommonResponses;
using EMessa.Core.Features.Sales.Queries.GetAllSales;
using EMessa.Core.Features.Sales.Queries.GetSaleById;
using EMessa.Core.Features.Sales.Queries.GetSaleRolls;
using EMessa.Core.Features.Sales.Queries.GetSalesForCustomer;
using EMessa.Core.Models;
using EMessa.DAL.Entities.Articles;
using EMessa.DAL.Entities.Orders;
using EMessa.DAL.Entities.Sales;
using System.Globalization;

namespace EMessa.Core.Features.Sales;

public class SalesMapping : Profile
{
    public SalesMapping()
    {
        CreateMap<AddEditSaleCommand, Sale>();

        CreateMap<Sale, SaleResponse>()
            .ForMember(desc => desc.Customers, opt => opt.MapFrom(src => src.SalesCustomers.Select(x => x.Customer)))
            .ForMember(desc => desc.Articles, opt => opt.MapFrom(src => src.SalesArticles.Select(x => x.Article)));
        CreateMap<Sale, GetAllSalesResponse>()
            .IncludeBase<Sale, SaleResponse>();
        CreateMap<Sale, GetSaleByIdResponse>()
            .ForMember(desc => desc.Customers, opt => opt.MapFrom(src => src.SalesCustomers.Select(x => x.Customer)))
            .ForMember(desc => desc.Articles, opt => opt.MapFrom(src => src.SalesArticles.Select(x => x.Article)))
            .ForMember(desc => desc.CreatedByFullName,
                opt => opt.MapFrom(src => $"{src.CreatedBy.FirstName} {src.CreatedBy.LastName}"));
        CreateMap<Sale, GetSalesForCustomerResponse>();

        CreateMap<GetSaleByIdResponse, AddEditSaleCommand>();

        CreateMap<SalePrice, SalePriceResponse>();
        CreateMap<SalePriceResponse, SalePriceCommand>();
        CreateMap<SalePriceCommand, SalePrice>()
            .ForMember(desc => desc.Price, opt => opt.MapFrom(src => src.Price.HasValue ? src.Price.Value : 0m))
            .ForMember(dest => dest.CurrencyCode, opt => opt.MapFrom(src => src.CurrencyCode.ToUpper()))
            .ReverseMap();

        CreateMap<Roll, SaleRoll>()
            .ReverseMap();
        CreateMap<SaleRoll, SaleRollResponse>();
        CreateMap<SaleRollResponse, SaleRollResponse>();
        CreateMap<RollResponse, SaleRollResponse>();

        CreateMap<OrderItem, OrderItemResponseForSale>()
            .ForMember(desc => desc.OrderItemId, opt => opt.MapFrom(src => src.Id))
            .ForMember(desc => desc.OrderItemWeight, opt => opt.MapFrom(src => src.Weight))
            .ForMember(desc => desc.OrderItemIndex, opt => opt.MapFrom(src => src.Index))
            .ForMember(desc => desc.OrderItemQuantity, opt => opt.MapFrom(src => src.Quantity))
            .ForMember(desc => desc.OrderItemArticleName, opt => opt.MapFrom(src => src.Article.Name))
            .ForMember(desc => desc.OrderId, opt => opt.MapFrom(src => src.Order.Id))
            .ForMember(desc => desc.OrderNo, opt => opt.MapFrom(src => src.Order.No))
            .ForMember(desc => desc.OrderMessaNo, opt => opt.MapFrom(src => src.Order.MessaNo))
            .ForMember(desc => desc.OrderType, opt => opt.MapFrom(src => src.Order.Type))
            .ForMember(desc => desc.OrderStatus, opt => opt.MapFrom(src => src.Order.Status))
            .ForMember(desc => desc.OrderCustomerShortName, opt => opt.MapFrom(src => src.Order.Customer.ShortName))
            .ForMember(desc => desc.OrderCustomerLocalizationName,
                opt => opt.MapFrom(src => src.Order.CustomerLocalization.Name));
        //CreateMap<OrderItemResponseForSale, OrderItemResponseForSale>();

        CreateMap<Article, ArticleResponseForSale>();
        CreateMap<SaleArticle, ArticleResponseForSale>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.ArticleId))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Article!.Name))
            .ForMember(dest => dest.Code, opt => opt.MapFrom(src => src.Article!.Code))
            .ForMember(dest => dest.Type, opt => opt.MapFrom(src => src.Article!.Type))
            .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src => src.Article!.IsActive))
            .ForMember(dest => dest.IsDeleted, opt => opt.MapFrom(src => src.Article!.IsDeleted))
            .ForMember(dest => dest.DefaultWidth, opt => opt.MapFrom(src => src.Article!.DefaultWidth))
            .ForMember(dest => dest.SaleWidth, opt => opt.MapFrom(src => src.Width))
            .ReverseMap();
        CreateMap<GetAllArticlesBySieveResponse, ArticleResponseForSale>();

        CreateMap<SaleArticle, SaleArticleResponse>()
            .ForMember(dest => dest.Width, opt => opt.MapFrom(src => src.Width))
            .ForMember(dest => dest.DefinedWeight, opt => opt.MapFrom(src => src.Sale.DefinedWeight))
            .ForMember(dest => dest.SaleId, opt => opt.MapFrom(src => src.Sale.Id))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Sale.Status))
            .ForMember(dest => dest.DateFrom, opt => opt.MapFrom(src => src.Sale.DateFrom))
            .ForMember(dest => dest.DateTo, opt => opt.MapFrom(src => src.Sale.DateTo))
            .ForMember(dest => dest.ColorId, opt => opt.MapFrom(src => src.Sale.ColorId))
            .ForMember(dest => dest.CoatId, opt => opt.MapFrom(src => src.Sale.CoatId))
            .ForMember(dest => dest.ThickId, opt => opt.MapFrom(src => src.Sale.ThickId))
            .ForMember(dest => dest.Color, opt => opt.MapFrom(src => src.Sale.Color))
            .ForMember(dest => dest.Coat, opt => opt.MapFrom(src => src.Sale.Coat))
            .ForMember(dest => dest.Thick, opt => opt.MapFrom(src => src.Sale.Thick))
            .ForMember(dest => dest.Prices, opt => opt.MapFrom(src => src.Sale.Prices))
            .ForMember(dest => dest.Rolls, opt => opt.MapFrom(src => src.Sale.Rolls))
            .ForMember(dest => dest.OrderItems, opt => opt.MapFrom(src => src.Sale.OrderItems))
            .ForMember(dest => dest.Name, opt =>
                opt.MapFrom(src =>
                    src.Sale.Translations.Any(t => t.LanguageCode == CultureInfo.CurrentCulture.Name)
                        ? src.Sale.Translations.First(t => t.LanguageCode == CultureInfo.CurrentCulture.Name).Name
                        : src.Sale.Name))
            .ForMember(dest => dest.Description, opt =>
                opt.MapFrom(src =>
                    src.Sale.Translations.Any(t => t.LanguageCode == CultureInfo.CurrentCulture.Name)
                        ? src.Sale.Translations.First(t => t.LanguageCode == CultureInfo.CurrentCulture.Name)
                            .Description
                        : src.Sale.Description));
    }
}
