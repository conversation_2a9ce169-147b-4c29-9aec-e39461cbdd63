using EMessa.Base.Enums;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Orders;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Orders;

public interface IFakeOrdersService
{
    Task<List<Order>> GetOrdersAsync();
    Task SetOrders(int ordersCount = 20);
}

public class FakeOrdersService(
    ILogger<FakeOrdersService> logger,
    IDbContextFactory<ApplicationDbContext> dbFactory) : IFakeOrdersService
{
    private static List<Order> _orders = [];

    public async Task<List<Order>> GetOrdersAsync()
    {
        try
        {
            if (!_orders.Any())
            {
                await SetOrders();
            }
            return _orders;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while getting fake orders: {Message}", ex.Message);
            throw;
        }
    }

    public async Task SetOrders(int ordersCount = 50)
    {
        try
        {
            _orders.Clear();

            var orders = await GetOrdersAsync(ordersCount);

            _orders = orders.ToList();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while setting fake orders: {Message}", ex.Message);
            throw;
        }
    }

    private async Task<List<Order>> GetOrdersAsync(int ordersCount = 20)
    {
        try
        {
            var random = new Random();
            logger.LogInformation("Rozpoczynam generowanie zamówień. Liczba zamówień: {Count}", ordersCount);

            await using var dbContext = await dbFactory.CreateDbContextAsync();

            List<string> roleNames = ["TRADEMANAGER", "TRADE", "CLIENTMANAGER", "CLIENT"];
            logger.LogInformation("Pobieram role użytkowników: {Roles}", string.Join(", ", roleNames));

            var roleIds = await dbContext.Roles
                .Where(r => roleNames.Contains(r.NormalizedName!))
                .Select(r => r.Id)
                .ToListAsync();

            logger.LogInformation("Znaleziono {Count} ról pasujących do filtrów", roleIds.Count);

            var users = await dbContext.Users
                .Where(u => u.UserRoles.Any(ur => roleIds.Contains(ur.RoleId)))
                .Select(u => u.UserName)
                .ToListAsync();

            logger.LogInformation("Znaleziono {Count} użytkowników z pasującymi rolami", users.Count);

            var userProfiles = await dbContext.UserProfiles
                .Where(x => x.Active)
                .Include(x => x.UserBranches)
                .Include(x => x.UserLocalizations)
                .Include(x => x.CustomerUsers)
                .Include(x => x.Customer)
                    .ThenInclude(c => c.Localisations)
                .Include(x => x.Customer)
                    .ThenInclude(c => c.Branch)
                .Where(x => x.Customer.Localisations.Any())
                .Where(x => users.Contains(x.UserName))
                .ToListAsync();

            logger.LogInformation("Znaleziono {Count} aktywnych profili użytkowników", userProfiles.Count);

            int firstId = 111;
            var orders = new List<Order>();

            for (int i = 0; i < ordersCount; i++)
            {
                var orderer = userProfiles[random.Next(userProfiles.Count)];
                var customer = orderer.Customer;

                logger.LogDebug("Tworzę zamówienie #{Index} dla użytkownika {UserName} (UserProfileId: {UserId})", i + 1, orderer.UserName, orderer.Id);

                if (orderer.Customer.IsAppOwner)
                {
                    var customers = userProfiles
                        .Select(x => x.Customer)
                        .Where(c => !c.IsAppOwner)
                        .Where(c => orderer.UserBranches.Select(x => x.BranchId).Contains(c.BranchId))
                        .ToList();

                    if (!customers.Any())
                    {
                        logger.LogWarning("Brak klientów dla właściciela aplikacji. UserProfileId: {UserId}", orderer.Id);
                        continue;
                    }

                    customer = customers[random.Next(customers.Count)];
                    logger.LogDebug("Właściciel aplikacji. Wybrano klienta zastępczego: {CustomerId}", customer.Id);
                }

                var statuses = Enum.GetValues<OrderStatus>().Where(x => x != OrderStatus.Unknown).ToList();
                var status = statuses[random.Next(statuses.Count)];

                var localization = customer.Localisations[random.Next(customer.Localisations.Count)];

                var order = new Order
                {
                    Id = firstId++,
                    No = $"{DateTime.UtcNow.Year}/{firstId + 33}",
                    MessaNo = firstId + 2000,
                    CustomerNo = $"CUST0{random.Next(1, 999)}",
                    CreatedDate = DateTime.UtcNow.AddDays(-random.Next(1, 60)),
                    CreatedById = orderer.Id,
                    CustomerId = customer.Id,
                    Customer = customer,
                    CustomerLocalizationId = localization.Id,
                    CustomerLocalization = localization,
                    BranchId = customer.BranchId,
                    Branch = customer.Branch,
                    Type = OrderType.Order,
                    Status = status,
                    Comments = $"Test order {i + 1}",
                    OrderItems = []
                };

                var orderItemsCount = random.Next(1, 8);
                logger.LogDebug("Dodaję {Count} pozycji do zamówienia #{OrderId}", orderItemsCount, order.Id);

                for (int j = 0; j < orderItemsCount; j++)
                {
                    var articles = await dbContext.Articles
                        .Where(a => a.IsDeleted == false)
                        .Where(a => a.IsActive)
                        .Include(a => a.Options)
                            .ThenInclude(o => o.Values)
                                .ThenInclude(v => v.OptionValue)
                        .Include(a => a.Options)
                            .ThenInclude(o => o.Option)
                                .ThenInclude(o => o.Values)
                        .Include(a => a.Unit)
                        .ToListAsync();

                    if (!articles.Any())
                    {
                        logger.LogWarning("Brak artykułów do dodania do zamówienia");
                        continue;
                    }

                    var article = articles[random.Next(articles.Count)];
                    var orderItemId = j + 1;

                    var item = new OrderItem
                    {
                        Id = orderItemId,
                        OrderId = order.Id,
                        Order = order,
                        ArticleId = article.Id,
                        Article = article,
                        Weight = (decimal)Math.Round(random.NextDouble() * 100, 2),
                        Quantity = random.Next(1, 20),
                        Comments = $"Item {orderItemId} for order {orderItemId}",
                        DraftHashedFileName = null,
                        SaleId = null,
                        OptionValues = [],
                        RequestedSheets = []
                    };

                    if (article.Options.Any())
                    {
                        logger.LogDebug("Dodaję opcje do pozycji zamówienia #{ItemId}", item.Id);
                        int ovCount = 1;

                        foreach (var option in article.Options)
                        {
                            item.OptionValues.Add(new OrderItemOptionValue
                            {
                                Id = ovCount++,
                                OrderItemId = item.Id,
                                OrderItem = item,
                                OptionId = option.OptionId,
                                Option = option.Option,
                                OptionValueId = option.Id,
                                OptionValue = option.Values[random.Next(option.Values.Count)].OptionValue,
                            });
                        }
                    }

                    if (article.Type == ArticleType.Complex)
                    {
                        var sheetCount = random.Next(1, 8);
                        logger.LogDebug("Dodaję {Count} arkuszy do pozycji #{ItemId}", sheetCount, item.Id);

                        for (int k = 0; k < sheetCount; k++)
                        {
                            var width = Math.Round((decimal)random.NextDouble() + (random.Next(2, 6) / 10), 2);
                            var length = Math.Round((decimal)random.NextDouble() + (random.Next(2, 20) / 10), 2);
                            var quntity = random.Next(1, 50);

                            item.RequestedSheets.Add(new RequestedOrderItemSheet
                            {
                                Id = k + 1,
                                OrderItemId = item.Id,
                                OrderItem = item,
                                Width = width,
                                Length = length,
                                Quantity = quntity,
                                OrderItemSheets =
                                {
                                    new OrderItemSheet
                                    {
                                        Id = k + 1,
                                        Width = width,
                                        Length = length,
                                        Quantity = quntity
                                    }
                                }
                            });
                        }
                    }

                    order.OrderItems.Add(item);
                }

                orders.Add(order);
            }

            logger.LogInformation("Wygenerowano {Count} zamówień", orders.Count);
            return orders;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Wystąpił błąd podczas generowania zamówień: {Message}", ex.Message);
            throw;
        }
    }

}
