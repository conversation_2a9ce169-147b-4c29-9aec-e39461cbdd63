using EMessa.Base.Constants;
using EMessa.Base.Enums;
using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Orders.Commands.SendOrder;

public record SendOrderCommand(int OrderId) : IRequest<IResult>;

public class SendOrderCommandHandler(
    ILogger<SendOrderCommandHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IAppStateService appStateService,
    IOrderAccessService orderAccessService)
    : IRequestHandler<SendOrderCommand, IResult>
{
    public async Task<IResult> Handle(SendOrderCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var loggedUser = appStateService.UserData;

            if (!loggedUser.HasAnyRole(Role.Administrator, Role.Trade, Role.TradeManager, Role.Client, Role.ClientManager))
            {
                logger.LogWarning("User {UserId} attempted to send order {OrderId} without sufficient permissions.", loggedUser.UserProfileId, request.OrderId);
                return await Result.FailAsync(ResultErrorMessages.Unauthorized);
            }

            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var ordersQuery = await orderAccessService.GetAccessibleOrdersAsync(dbContext, cancellationToken);

            var order = await ordersQuery
                .Include(o => o.OrderItems)
                .AsTracking()
                .FirstOrDefaultAsync(o => o.Id == request.OrderId, cancellationToken);

            if (order is null)
            {
                logger.LogWarning("Order with ID {OrderId} not found for user {UserId}.", request.OrderId, loggedUser.UserProfileId);
                return await Result.FailAsync(ResultErrorMessages.NotFound);
            }

            if (order.IsEdited)
            {
                logger.LogWarning("Order {OrderId} has been edited and cannot be sent.", request.OrderId);
                return await Result.FailAsync("Zamówienie jest edytowane i nie może być wysłane.".Tr());
            }

            if (order.IsDeleted)
            {
                logger.LogWarning("Order {OrderId} has been deleted and cannot be sent.", request.OrderId);
                return await Result.FailAsync(ResultErrorMessages.OperationFailed);
            }

            if (!OrderAccessConstants.AllowedStatusesForSend.Contains(order.Status))
            {
                logger.LogWarning("Order {OrderId} is not in a state that allows sending. Current status: {Status}", request.OrderId, order.Status);
                return await Result.FailAsync("Zamówienie posiada status, który nie pozwala na wysłanie do realizacji.".Tr());
            }

            var customer = await dbContext.Customers
                .AsNoTracking()
                .FirstOrDefaultAsync(c => c.Id == order.CustomerId, cancellationToken);

            if (customer is null)
            {
                logger.LogError("Customer with ID {CustomerId} not found for order {OrderId}.", order.CustomerId, request.OrderId);
                return await Result.FailAsync(ResultErrorMessages.UnexpectedErrorWithMessage("Brak przypisanego klienta do zamówienia."));
            }

            if (customer.FastProductionTrack &&
                customer.FactoryId.HasValue)
            {
                order.FactoryId = customer.FactoryId.Value;
                order.Status = OrderStatus.Accepted;
                foreach (var orderItem in order.OrderItems)
                    orderItem.Status = OrderStatus.Accepted;
            }
            else
            {
                order.Status = OrderStatus.Sent;
                foreach (var orderItem in order.OrderItems)
                    orderItem.Status = OrderStatus.Sent;
            }

            order.EditedById = loggedUser.UserProfileId;
            order.LastEditDate = DateTime.UtcNow;

            await dbContext.SaveChangesAsync(cancellationToken);

            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while sending order {OrderId} for user {UserId}.", request.OrderId, appStateService.UserData.UserProfileId);
            return await Result.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}