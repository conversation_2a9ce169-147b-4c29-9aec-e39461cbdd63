namespace EMessa.Core.Features.Orders.Commands.CreateOrder;

public record CreateOrderRequest
{
    public int UserProfileId { get; init; }
    public int CustomerId { get; init; }
    public int CustomerLocalizationId { get; init; }
    public int BranchId { get; init; }
    public string CustomerNo { get; init; } = string.Empty;
    public string Comments { get; init; } = string.Empty;
    public bool DifferentDeliveryLocalization { get; init; }
    // public DateTime? PlanedRealizationDate { get; init; } //ask Da<PERSON>my przy tworzeniu zamówienia?
    public List<int> OrderItemIds { get; init; } = [];
    public List<CreateLocalizationRequest> Localizations { get; init; } = [];
}