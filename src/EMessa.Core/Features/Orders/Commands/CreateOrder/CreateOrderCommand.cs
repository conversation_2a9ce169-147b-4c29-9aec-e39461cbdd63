using System.Data;
using EMessa.Base.Enums;
using EMessa.Core.Common.Results;
using EMessa.Core.Features.ShoppingCart.Notifications.ShoppingCartChanged;
using EMessa.Core.Interfaces;
using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Orders;
using EMessa.DAL.Entities.Localizations;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using AutoMapper;

namespace EMessa.Core.Features.Orders.Commands.CreateOrder;

public record CreateOrderCommand(CreateOrderRequest Request) : IRequest<IResult<CreateOrderResponse>>;

public class CreateOrderCommandHandler(
    ILogger<CreateOrderCommandHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IMediator mediator,
    IMapper mapper)
    : IRequestHandler<CreateOrderCommand, IResult<CreateOrderResponse>>
{
    public async Task<IResult<CreateOrderResponse>> Handle(CreateOrderCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);
            // IsolationLevel.Serializable - pełne zabezpieczenie przed konkurencyjnością
            // Generowanie numeru w transakcji - żadnych duplikatów
            await using var transaction = await dbContext.Database.BeginTransactionAsync(
                IsolationLevel.Serializable, cancellationToken);

            // Pobierz koszyk użytkownika
            var shoppingCart = await GetShoppingCart(dbContext, request.Request.UserProfileId, cancellationToken);
            if (shoppingCart == null)
            {
                return await Result<CreateOrderResponse>.FailAsync("Nie znaleziono koszyka".Tr());
            }

            // Sprawdź czy są produkty do zamówienia
            if (request.Request.OrderItemIds.Count == 0)
            {
                return await Result<CreateOrderResponse>.FailAsync("Brak produktów do zamówienia".Tr());
            }

            // Sprawdź czy wszystkie elementy są dostępne
            var orderItems = await GetOrderItems(dbContext, request.Request.OrderItemIds, cancellationToken);
            var foundItemIds = orderItems.Select(x => x.Id).ToHashSet();
            if (!foundItemIds.SetEquals(request.Request.OrderItemIds))
            {
                return await Result<CreateOrderResponse>.FailAsync(
                    "Brak niektórych elementów do zamówienia".Tr(), new CreateOrderResponse
                    {
                        OrderItemIds = foundItemIds.ToList()
                    });
            }

            // Wygeneruj numer zamówienia (w tej samej transakcji!)
            var currentYear = DateTime.Now.Year;
            var startOfYear = new DateTime(currentYear, 1, 1);
            var startOfNextYear = new DateTime(currentYear + 1, 1, 1);

            var ordersCount = await dbContext.Orders
                .Where(x =>
                    x.Type == OrderType.Order && //ask czy ograniczamy tu wg typu zamowienia?
                    x.CreatedDate >= startOfYear &&
                    x.CreatedDate < startOfNextYear)
                .CountAsync(cancellationToken);

            var nextOrderNumber = ordersCount + 1;
            var orderNo = $"{nextOrderNumber}/{currentYear}";

            // Utwórz nowe zamówienie
            var newOrder = new Order
            {
                No = orderNo,
                CustomerId = request.Request.CustomerId,
                CustomerLocalizationId = request.Request.CustomerLocalizationId,
                BranchId = request.Request.BranchId,
                CustomerNo = request.Request.CustomerNo,
                Comments = request.Request.Comments,
                Type = OrderType.Order,
                Status = OrderStatus.New,
                CreatedById = request.Request.UserProfileId,
                CreatedDate = DateTime.UtcNow,
                DifferentDeliveryLocalization = request.Request.DifferentDeliveryLocalization,
                IsDeleted = false
            };

            // Dodaj lokalizacje
            foreach (var localizationRequest in request.Request.Localizations)
            {
                var localization = mapper.Map<Localization>(localizationRequest);
                newOrder.Localizations.Add(localization);
            }

            // Dodaj zamówienie do bazy danych (aby uzyskać Order.Id)
            dbContext.Orders.Add(newOrder);
            await dbContext.SaveChangesAsync(cancellationToken);

            // Ustaw OrderId dla elementów zamówienia z koszyka i zmień ich status
            foreach (var orderItem in orderItems)
            {
                orderItem.OrderId = newOrder.Id;
                orderItem.Status = OrderStatus.New;
                orderItem.SelectedForOrder = true;
                dbContext.Entry(orderItem).State = EntityState.Modified;
            }

            // Zapisz wszystkie pozostałe zmiany i commit transakcji
            await dbContext.SaveChangesAsync(cancellationToken);
            await transaction.CommitAsync(cancellationToken);

            await mediator.Publish(new ShoppingCartChangedNotification(), cancellationToken);

            var response = new CreateOrderResponse
            {
                OrderId = newOrder.Id,
                OrderNo = newOrder.No,
                OrderItemIds = orderItems.Select(oi => oi.Id).ToList()
            };
            return await Result<CreateOrderResponse>.SuccessAsync(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Błąd podczas tworzenia zamówienia");
            return await Result<CreateOrderResponse>.FailAsync("Błąd podczas tworzenia zamówienia".Tr());
        }
    }

    private async Task<Order?> GetShoppingCart(
        ApplicationDbContext dbContext,
        int userProfileId,
        CancellationToken cancellationToken)
    {
        return await dbContext.Orders
            .Include(x => x.OrderItems)
            .FirstOrDefaultAsync(x =>
                    x.Type == OrderType.ShoppingCart &&
                    x.CreatedById == userProfileId,
                cancellationToken);
    }

    private async Task<List<OrderItem>> GetOrderItems(
        ApplicationDbContext dbContext,
        List<int> orderItemIds,
        CancellationToken cancellationToken)
    {
        return await dbContext.OrderItems
            .Include(x => x.OptionValues)
            .Include(x => x.RequestedSheets)
            .Where(x => orderItemIds.Contains(x.Id))
            .ToListAsync(cancellationToken);
    }
}