using EMessa.Base.Constants;
using EMessa.Base.Enums;
using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Orders.Commands.AcceptOrder;

public record AcceptOrderCommand(int OrderId, int FactoryId) : IRequest<IResult>;

public class AcceptOrderCommandHandler(
    ILogger<AcceptOrderCommandHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IAppStateService appStateService,
    IOrderAccessService orderAccessService)
    : IRequestHandler<AcceptOrderCommand, IResult>
{
    public async Task<IResult> Handle(AcceptOrderCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var loggedUser = appStateService.UserData;

            if (!loggedUser.HasAnyRole(Role.Administrator, Role.Trade, Role.TradeManager))
            {
                logger.LogWarning("User {UserId} attempted to accept order {OrderId} without sufficient permissions.", loggedUser.UserProfileId, request.OrderId);
                return await Result.FailAsync(ResultErrorMessages.Unauthorized);
            }

            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);


            var factory = await dbContext.Factories
                .AsNoTracking()
                .FirstOrDefaultAsync(f => f.Id == request.FactoryId, cancellationToken);

            if (factory is null)
            {
                logger.LogError("Factory with ID {FactoryId} not found.", request.FactoryId);
                return await Result.FailAsync(ResultErrorMessages.NotFound);
            }

            var ordersQuery = await orderAccessService.GetAccessibleOrdersAsync(dbContext, cancellationToken);

            var order = await ordersQuery
                .Include(o => o.OrderItems)
                .AsTracking()
                .FirstOrDefaultAsync(o => o.Id == request.OrderId, cancellationToken);

            if (order is null)
            {
                logger.LogWarning("Order with ID {OrderId} not found for user {UserId}.", request.OrderId, loggedUser.UserProfileId);
                return await Result.FailAsync(ResultErrorMessages.NotFound);
            }

            if (order.IsEdited)
            {
                logger.LogWarning("Order {OrderId} has been edited and cannot be accept.", request.OrderId);
                return await Result.FailAsync("Zamówienie jest edytowane i nie może być wysłane.".Tr());
            }

            if (order.IsDeleted)
            {
                logger.LogWarning("Order {OrderId} has been deleted and cannot be accept.", request.OrderId);
                return await Result.FailAsync(ResultErrorMessages.OperationFailed);
            }

            if (!OrderAccessConstants.AllowedStatusesForAccept.Contains(order.Status))
            {
                logger.LogWarning("Order {OrderId} is not in a state that allows accepting. Current status: {Status}", request.OrderId, order.Status);
                return await Result.FailAsync("Zamówienie posiada status, który nie pozwala na akceptacje.".Tr());
            }

            order.FactoryId = request.FactoryId;
            order.Status = OrderStatus.Accepted;
            foreach (var orderItem in order.OrderItems)
                orderItem.Status = OrderStatus.Accepted;
            order.EditedById = loggedUser.UserProfileId;
            order.LastEditDate = DateTime.UtcNow;

            await dbContext.SaveChangesAsync(cancellationToken);

            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while accepting order {OrderId} for user {UserId}.", request.OrderId, appStateService.UserData.UserProfileId);
            return await Result.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}