using EMessa.Base.Constants;
using EMessa.Base.Enums;
using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using MediatR;
using MessaApi;
using MessaApi.OrderStatus;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Orders.Commands.RejectOrder;

public record RejectOrderCommand(int OrderId) : IRequest<IResult>;

public class RejectOrderCommandHandler(
    ILogger<RejectOrderCommandHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IAppStateService appStateService,
    IOrderAccessService orderAccessService,
    IMessaApiClient messaApiClient)
    : IRequestHandler<RejectOrderCommand, IResult>
{
    public async Task<IResult> Handle(RejectOrderCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var loggedUser = appStateService.UserData;

            if (!loggedUser.HasAnyRole(Role.Administrator, Role.TradeManager, Role.Client, Role.ClientManager))
            {
                logger.LogWarning("User {UserId} attempted to reject order {OrderId} without sufficient permissions.", loggedUser.UserProfileId, request.OrderId);
                return await Result.FailAsync(ResultErrorMessages.Unauthorized);
            }

            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var ordersQuery = await orderAccessService.GetAccessibleOrdersAsync(dbContext, cancellationToken);

            var order = await ordersQuery
                .Include(o => o.OrderItems)
                .AsTracking()
                .FirstOrDefaultAsync(o => o.Id == request.OrderId, cancellationToken);

            if (order is null)
            {
                logger.LogWarning("Order with ID {OrderId} not found for user {UserId}.", request.OrderId, loggedUser.UserProfileId);
                return await Result.FailAsync(ResultErrorMessages.NotFound);
            }

            if (!OrderAccessConstants.AllowedStatusesForReject.Contains(order.Status))
            {
                logger.LogWarning("Order {OrderId} is not in a state that allows rejecting. Current status: {Status}", request.OrderId, order.Status);
                return await Result.FailAsync("Zamówienie posiada status, który nie pozwala na odrzucenie.".Tr());
            }

            if (order.IsEdited)
            {
                logger.LogWarning("Order {OrderId} has been edited and cannot be reject.", request.OrderId);
                return await Result.FailAsync("Zamówienie jest edytowane i nie może być odrzucone.".Tr());
            }

            if (order.IsDeleted)
            {
                logger.LogWarning("Order {OrderId} has been deleted and cannot be reject.", request.OrderId);
                return await Result.FailAsync(ResultErrorMessages.OperationFailed);
            }

            if ((order.Status == OrderStatus.Accepted || order.Status == OrderStatus.Preparation) && order.MessaNo.HasValue)
            {
                var result = await MessaApiCheck(order.MessaNo.Value, $"{loggedUser.UserFirstName} {loggedUser.UserLastName}");
                if (!result)
                {
                    logger.LogWarning("Order {OrderId} is not in a state that allows rejecting. Current status: {Status}", request.OrderId, order.Status);
                    return await Result.FailAsync("Zamówienie posiada status, który nie pozwala na odrzucenie.".Tr());
                }
            }

            order.Status = OrderStatus.Rejected;
            foreach (var orderItem in order.OrderItems)
                orderItem.Status = OrderStatus.Rejected;
            order.EditedById = loggedUser.UserProfileId;
            order.LastEditDate = DateTime.UtcNow;

            await dbContext.SaveChangesAsync(cancellationToken);

            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while rejecting order {OrderId} for user {UserId}.", request.OrderId, appStateService.UserData.UserProfileId);
            return await Result.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }

    private async Task<bool> MessaApiCheck(int messaNo, string userName)
    {
        MessaOrderStatusDTO result;

        try
        {
            result = await messaApiClient.OrderStatus.GetByMessaNo(messaNo);

        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to retrieve Messa order status for MessaNo {MessaNo} for user {UserName}.", messaNo, userName);
            throw;
        }

        if (result.OrderStatus != MessaOrderStatus.New)
        {
            return false;
        }

        try
        {
            await messaApiClient.OrdersApi.CancelOrder(new((int)result.MessaId, userName));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to cancel Messa order with MessaNo {MessaNo} for user {UserName}.", messaNo, userName);
            throw;
        }

        return true;
    }
}