using AutoMapper;
using EMessa.Core.Features.CustomerLocalizations.Queries.Get;
using EMessa.Core.Features.Orders.ApiQueries.Common;
using EMessa.Core.Features.Orders.Commands.CreateOrder;
using EMessa.Core.Features.Orders.Commands.EditOrder;
using EMessa.Core.Features.Orders.Common;
using EMessa.Core.Features.Orders.Queries.Common;
using EMessa.Core.Features.Orders.Queries.GetAllOrders;
using EMessa.Core.Features.Orders.Queries.GetOrderById;
using EMessa.DAL.Entities.Localizations;
using EMessa.DAL.Entities.Orders;

namespace EMessa.Core.Features.Orders;

public class OrdersMapping : Profile
{
    public OrdersMapping()
    {
        CreateMap<Order, OrderResponse>()
            .ForMember(desc => desc.CustomerShortName, opt => opt.MapFrom(src => src.Customer.ShortName))
            .ForMember(desc => desc.CreatedByFullName, opt => opt.MapFrom(src => $"{src.CreatedBy.FirstName} {src.CreatedBy.LastName}"))
            .ForMember(desc => desc.BranchName, opt => opt.MapFrom(src => src.Branch.Name))
            .ForMember(desc => desc.FactoryName, opt => opt.MapFrom(src => src.Factory != null ? src.Factory.Name : ""));
        CreateMap<LockOrder, LockOrderResponse>()
            .ForMember(desc => desc.LockedBy,
                opt => opt.MapFrom(src => src.LockedBy.Email))
            .ForMember(desc => desc.OrderNo, opt => opt.MapFrom(src => src.Order.No));
        CreateMap<OrderItem, OrderItemResponse>()
            .ForMember(desc => desc.ArticleName, opt => opt.MapFrom(src => src.Article.Name))
            .ForMember(desc => desc.ArticleType, opt => opt.MapFrom(src => src.Article.Type))
            .ForMember(desc => desc.ArticleUnitName, opt => opt.MapFrom(src => src.Article.Unit!.Name));

        CreateMap<Order, GetOrderByIdResponse>()
            .ForMember(desc => desc.CustomerShortName, opt => opt.MapFrom(src => src.Customer.ShortName))
            .ForMember(desc => desc.CreatedByFullName, opt => opt.MapFrom(src => $"{src.CreatedBy.FirstName} {src.CreatedBy.LastName}"))
            .ForMember(desc => desc.BranchName, opt => opt.MapFrom(src => src.Branch.Name))
            .ForMember(desc => desc.FactoryName, opt => opt.MapFrom(src => src.Factory != null ? src.Factory.Name : ""));
        CreateMap<Order, GetAllOrdersResponse>()
            .ForMember(desc => desc.CustomerShortName, opt => opt.MapFrom(src => src.Customer.ShortName))
            .ForMember(desc => desc.CreatedByFullName, opt => opt.MapFrom(src => $"{src.CreatedBy.FirstName} {src.CreatedBy.LastName}"))
            .ForMember(desc => desc.BranchName, opt => opt.MapFrom(src => src.Branch.Name))
            .ForMember(desc => desc.FactoryName, opt => opt.MapFrom(src => src.Factory != null ? src.Factory.Name : ""));
        CreateMap<GetAllOrdersResponse, GetOrderByIdResponse>();

        CreateMap<OrderItemOptionValue, OrderItemProductOptionResponse>()
            .ForMember(desc => desc.OptionName, opt => opt.MapFrom(src => src.Option.Name))
            .ForMember(desc => desc.OptionValue, opt => opt.MapFrom(src => src.OptionValue!.Value));

        CreateMap<RequestedOrderItemSheet, OrderItemRequestedSheetResponse>();
        CreateMap<OrderItemSheet, OrderItemSheetResponse>();

        CreateMap<RequestedOrderItemSheet, OrderItemSheet>();
        CreateMap<CreateLocalizationRequest, Localization>();
        CreateMap<Localization, GetCustomerLocalizationsResponse>();
        CreateMap<GetCustomerLocalizationsResponse, CreateLocalizationRequest>();
        
        CreateMap<GetOrderByIdResponse, EditOrderRequest>();
        CreateMap<LocalizationResponse, Localization>();//ask

        // API DTOs
        CreateMap<Order, OrderDTO>()
            .ForMember(desc => desc.CustomerShortName, opt => opt.MapFrom(src => src.Customer.ShortName))
            .ForMember(desc => desc.CreatedByFullName, opt => opt.MapFrom(src => $"{src.CreatedBy.FirstName} {src.CreatedBy.LastName}"))
            .ForMember(desc => desc.BranchName, opt => opt.MapFrom(src => src.Branch.Name))
            .ForMember(desc => desc.FactoryName, opt => opt.MapFrom(src => src.Factory != null ? src.Factory.Name : ""));
        CreateMap<OrderItem, OrderItemDTO>()
            .ForMember(desc => desc.ArticleName, opt => opt.MapFrom(src => src.Article.Name))
            .ForMember(desc => desc.ArticleType, opt => opt.MapFrom(src => src.Article.Type))
            .ForMember(desc => desc.ArticleUnitName, opt => opt.MapFrom(src => src.Article.Unit!.Name));
        CreateMap<OrderItemOptionValue, OrderItemProductOptionDTO>()
            .ForMember(desc => desc.OptionCode, opt => opt.MapFrom(src => src.Option.Code))
            .ForMember(desc => desc.OptionName, opt => opt.MapFrom(src => src.Option.Name))
            .ForMember(desc => desc.OptionValueCode, opt => opt.MapFrom(src => src.OptionValue!.Code))
            .ForMember(desc => desc.OptionValue, opt => opt.MapFrom(src => src.OptionValue!.Value));
        CreateMap<RequestedOrderItemSheet, OrderItemRequestedSheetDTO>();
        CreateMap<OrderItemSheet, OrderItemSheetDTO>();
        CreateMap<Localization, CustomerLocalizationsDTO>();
    }
}