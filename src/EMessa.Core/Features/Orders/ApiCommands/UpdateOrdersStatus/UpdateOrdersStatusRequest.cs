using EMessa.Base.Enums;

namespace EMessa.Core.Features.Orders.ApiCommands.UpdateOrdersStatus;

public class UpdateOrdersStatusRequest
{
    public List<UpdateOrderStatusRequest> Orders { get; set; } = [];
}

public class UpdateOrderStatusRequest : UpdateOrderStatusRequestBase
{
    public int OrderId { get; set; }
}

public class UpdateOrderStatusRequestBase
{
    public OrderStatus OrderStatus { get; set; }
    public bool ApplyStatusToOrderItems { get; set; }
    public int MessaNo { get; set; }
}