using EMessa.Base.Constants;
using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Orders.ApiCommands.UpdateOrdersStatus;

public record ApiUpdateOrdersStatusCommand(UpdateOrdersStatusRequest Request) : IRequest<IResult>;

public class ApiUpdateOrdersStatusCommandHandler(
    ILogger<ApiUpdateOrdersStatusCommandHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory)
    : IRequestHandler<ApiUpdateOrdersStatusCommand, IResult>
{
    public async Task<IResult> Handle(ApiUpdateOrdersStatusCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var orderMessaNos = request.Request.Orders.Select(o => o.MessaNo).ToList();

            if (orderMessaNos.Count != orderMessaNos.Distinct().Count())
            {
                logger.LogWarning("Duplicate MessaNo values found in the request: {MessaNos}.", string.Join(", ", orderMessaNos));
                return await Result.FailAsync("Duplicate MessaNo values are not allowed.");
            }

            foreach (var order in request.Request.Orders)
            {
                if (!OrderAccessConstants.AllowedTargetStatusesForAPI.Contains(order.OrderStatus))
                {
                    logger.LogWarning("Invalid order status {OrderItemStatus} for OrderId {OrderId}.", order.OrderStatus, order.OrderId);
                    return await Result.FailAsync("The status to be set is not allowed to be set via the API.");
                }
            }

            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var orderIds = request.Request.Orders.Select(o => o.OrderId).ToList();
            var orders = await dbContext.Orders
                .Include(o => o.OrderItems)
                .AsTracking()
                .Where(o => orderIds.Contains(o.Id))
                .ToListAsync(cancellationToken);

            var ordersByMessaNo = await dbContext.Orders
                .AsNoTracking()
                .Where(o => o.MessaNo.HasValue && orderMessaNos.Contains(o.MessaNo.Value))
                .ToListAsync(cancellationToken);

            if (orders.Count == 0)
            {
                logger.LogWarning("No Orders found for the provided OrderIds: {OrderIds}.", string.Join(", ", orderIds));
                return await Result.FailAsync(ResultErrorMessages.NotFound);
            }

            foreach (var order in orders)
            {
                var orderRequest = request.Request.Orders
                     .First(o => o.OrderId == order.Id);

                if(ordersByMessaNo.Any(o => o.MessaNo == orderRequest.MessaNo && o.Id != order.Id))
                {
                    logger.LogWarning("Order with MessaNo {MessaNo} already exists for another OrderId {OrderId}.", orderRequest.MessaNo, orderRequest.OrderId);
                    return await Result.FailAsync(
                            $"An order with the same MessaNo already exists. Tried to set MessaNo {orderRequest.MessaNo} for OrderId {orderRequest.OrderId}, but it already exists for OrderId {ordersByMessaNo.FirstOrDefault(o => o.MessaNo == orderRequest.MessaNo)?.Id}.");

                }

                if (orderRequest.MessaNo <= 0)
                {
                    logger.LogWarning("Invalid MessaNo {MessaNo} for OrderId {OrderId}.", orderRequest.MessaNo, orderRequest.OrderId);
                    return await Result.FailAsync($"MessaNo must be a positive integer for OrderId {orderRequest.OrderId}.");
                }

                if (!OrderAccessConstants.AllowedSourceStatusesForAPI.Contains(order.Status))
                {
                    logger.LogWarning("Invalid order status {OrderItemStatus} for OrderId {OrderId}.", orderRequest.OrderStatus, orderRequest.OrderId);
                    return await Result.FailAsync("Order status does not allow status change via API.");
                }

                order.MessaNo = orderRequest.MessaNo;
                order.Status = orderRequest.OrderStatus;
                order.LastEditDate = DateTime.UtcNow;

                if (orderRequest.ApplyStatusToOrderItems)
                {
                    foreach (var orderItem in order.OrderItems)
                    {
                        orderItem.Status = orderRequest.OrderStatus;
                    }
                }
            }

            await dbContext.SaveChangesAsync(cancellationToken);

            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while updating orders statuses.");
            return await Result.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}
