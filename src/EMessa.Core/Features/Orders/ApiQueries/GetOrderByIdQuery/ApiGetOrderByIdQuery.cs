using AutoMapper;
using AutoMapper.QueryableExtensions;
using EMessa.Core.Common.Results;
using EMessa.Core.Features.Orders.ApiQueries.Common;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Orders.ApiQueries.GetOrderByIdQuery;

public record ApiGetOrderByIdQuery(int OrderId) : IRequest<IResult<OrderDTO>>;

public class GetOrderByIdQueryHandler(
    ILogger<GetOrderByIdQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IMapper mapper)
    : IRequestHandler<ApiGetOrderByIdQuery, IResult<OrderDTO>>
{
    public async Task<IResult<OrderDTO>> Handle(ApiGetOrderByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var order = await dbContext.Orders
                .AsNoTracking()
                .ProjectTo<OrderDTO>(mapper.ConfigurationProvider)
                .FirstOrDefaultAsync(x => x.Id == request.OrderId, cancellationToken);

            if (order is null)
            {
                logger.LogWarning("Order with ID {OrderId} not found.", request.OrderId);
                return await Result<OrderDTO>.FailAsync(ResultErrorMessages.NotFound);
            }

            return await Result<OrderDTO>.SuccessAsync(order);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result<OrderDTO>.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}