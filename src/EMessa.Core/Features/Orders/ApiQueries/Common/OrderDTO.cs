using EMessa.Base.Enums;

namespace EMessa.Core.Features.Orders.ApiQueries.Common;

public class OrderDTO
{
    public int Id { get; set; }
    public string No { get; set; }
    public int? MessaNo { get; set; }
    public string CustomerNo { get; set; } = string.Empty;
    public OrderStatus Status { get; set; } = OrderStatus.New;
    public string CreatedByFullName { get; set; } = string.Empty;
    public DateTime CreatedDate { get; set; }
    public DateTime? PlanedRealizationDate { get; set; }
    public DateTime? RealizationDate { get; set; }
    public string Comments { get; set; } = string.Empty;
    public IList<OrderItemDTO> OrderItems { get; set; } = [];
    public decimal Weight { get; set; }
    public bool IsEdited { get; set; }

    public int FactoryId { get; set; }
    public string FactoryName { get; set; } = string.Empty;
    public int CustomerId { get; init; }
    public string CustomerShortName { get; set; } = string.Empty;
    public int CustomerLocalizationId { get; init; }
    public string CustomerLocationName { get; set; } = string.Empty;
    public int BranchId { get; init; }
    public string BranchName { get; set; } = string.Empty;
    public bool DifferentDeliveryLocalization { get; set; }
    public ICollection<CustomerLocalizationsDTO> Localizations { get; set; } = [];
}
