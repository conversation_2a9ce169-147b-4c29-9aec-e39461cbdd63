using EMessa.Base.Constants;

namespace EMessa.Core.Features.Orders.ApiQueries.Common;

public class OrderItemSheetDTO
{
    public int Id { get; set; }
    public int Quantity { get; set; }
    public decimal Width { get; set; }
    public decimal Length { get; set; }

    public decimal Mb => Math.Round(Quantity * Length, SystemConstants.RoundingSumsDecimals, MidpointRounding.AwayFromZero);
    public decimal M2 => Math.Round(Quantity * Length * Width, SystemConstants.RoundingSumsDecimals, MidpointRounding.AwayFromZero);
}
