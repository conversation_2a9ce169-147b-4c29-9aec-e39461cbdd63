using EMessa.Base.Constants;

namespace EMessa.Core.Features.Orders.ApiQueries.Common;

public class OrderItemRequestedSheetDTO
{
    public int Id { get; set; }
    public int Quantity { get; set; }
    public decimal Width { get; set; }
    public decimal Length { get; set; }
    public int? SplitN { get; set; }

    public List<OrderItemSheetDTO> OrderItemSheets { get; set; } = [];

    public decimal SumMb => Math.Round(OrderItemSheets.Sum(x => x.Mb),
        SystemConstants.RoundingSumsDecimals, MidpointRounding.AwayFromZero);
    public decimal SumM2 => Math.Round(OrderItemSheets.Sum(x => x.M2),
        SystemConstants.RoundingSumsDecimals, MidpointRounding.AwayFromZero);
}
