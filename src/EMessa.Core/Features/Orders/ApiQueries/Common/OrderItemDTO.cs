using EMessa.Base.Enums;

namespace EMessa.Core.Features.Orders.ApiQueries.Common;

public class OrderItemDTO
{
    public int Id { get; set; }
    public int OrderId { get; set; }
    public int ArticleId { get; set; }
    public string ArticleName { get; set; } = string.Empty;
    public ArticleType ArticleType { get; set; }
    public string ArticleUnitName { get; set; } = string.Empty;
    public string Index { get; set; } = string.Empty;

    public decimal Quantity { get; set; }
    public int SumQuantity { get; set; }
    public decimal SumM2 { get; set; }
    public decimal SumMb { get; set; }
    public decimal Weight { get; set; }
    public bool DraftEditable { get; set; }
    public string Comments { get; set; } = string.Empty;

    public List<OrderItemRequestedSheetDTO> RequestedSheets { get; set; } = [];
    public List<OrderItemProductOptionDTO> OptionValues { get; set; } = [];

    public decimal BasePrice { get; set; }
    public decimal FinalPrice { get; set; }
    public string CurrencyCode { get; set; } = null!;

    public OrderStatus Status { get; set; }
    public DateTime? ProductionDate { get; set; }
    public DateTime? TransportDate { get; set; }

    public int? SaleId { get; set; }
}
