using AutoMapper;
using AutoMapper.QueryableExtensions;
using EMessa.Base.Enums;
using EMessa.Core.Common.Results;
using EMessa.Core.Features.Orders.ApiQueries.Common;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Orders.ApiQueries.GetAcceptedOrdersQuery;

public record ApiGetAcceptedOrdersQuery() : IRequest<ListResult<OrderDTO>>;

public class ApiGetAcceptedOrdersQueryHandler(
    ILogger<ApiGetAcceptedOrdersQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IMapper mapper)
    : IRequestHandler<ApiGetAcceptedOrdersQuery, ListResult<OrderDTO>>
{
    public async Task<ListResult<OrderDTO>> Handle(ApiGetAcceptedOrdersQuery request, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var orders = await dbContext.Orders
                .AsNoTracking()
                .ProjectTo<OrderDTO>(mapper.ConfigurationProvider)
                .Where(o => o.Status == OrderStatus.Accepted)
                .ToListAsync(cancellationToken);

            return ListResult<OrderDTO>.Success(orders, orders.Count);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return ListResult<OrderDTO>.Failure(ResultErrorMessages.UnexpectedError);
        }
    }
}