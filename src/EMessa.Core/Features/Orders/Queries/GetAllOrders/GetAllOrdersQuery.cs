using AutoMapper;
using AutoMapper.QueryableExtensions;
using EMessa.Core.Common;
using EMessa.Core.Common.Results;
using EMessa.Core.Extensions;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Sieve.Models;

namespace EMessa.Core.Features.Orders.Queries.GetAllOrders;

public class GetAllOrdersQuery : SieveGetAllQueryBase<GetAllOrdersResponse>
{
    public GetAllOrdersQuery(SieveModel sieveModel) : base(sieveModel)
    { }

    public GetAllOrdersQuery() : base()
    { }
}

public class GetAllOrdersQueryHandler(
    ILogger<GetAllOrdersQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IOrderAccessService orderAccessService,
    IOrderResponseTranslationService orderResponseTranslationService,
    ISieveService sieveService,
    IMapper mapper)
    : IRequestHandler<GetAllOrdersQuery, ListResult<GetAllOrdersResponse>>
{
    public async Task<ListResult<GetAllOrdersResponse>> Handle(GetAllOrdersQuery request, CancellationToken cancellationToken)
    {
        try
        {
            request.SieveModel.EnsureOrderByIfNoSorts(nameof(GetAllOrdersResponse.Id), true);

            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var query = await orderAccessService.GetAccessibleOrdersAsync(dbContext, cancellationToken);

            var response = await sieveService
                .ExecuteSieveAsync<GetAllOrdersResponse, GetAllOrdersResponse>(
                    request.SieveModel,
                    query.AsNoTracking().ProjectTo<GetAllOrdersResponse>(mapper.ConfigurationProvider),
                    cancellationToken);

            response.Data = [.. await orderResponseTranslationService.TranslateArticleNamesAsync(dbContext, response.Data, cancellationToken)];

            return response;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while handling GetAllOrdersQuery: {Message}", ex.Message);
            return ListResult<GetAllOrdersResponse>.Failure(ResultErrorMessages.UnexpectedError);
        }
    }
}