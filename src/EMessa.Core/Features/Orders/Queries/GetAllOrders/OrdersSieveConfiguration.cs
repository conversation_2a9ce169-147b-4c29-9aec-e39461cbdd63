using EMessa.Base.Enums;
using EMessa.Core.Common;
using EMessa.Core.Helpers;
using Sieve.Services;

namespace EMessa.Core.Features.Orders.Queries.GetAllOrders;

public class OrdersSieveConfiguration : ISieveConfiguration
{
    public void Configure(SievePropertyMapper mapper)
    {
        mapper.Property<GetAllOrdersResponse>(x => x.Id).CanFilter().CanSort();
        mapper.Property<GetAllOrdersResponse>(x => x.No).CanFilter().CanSort();
        mapper.Property<GetAllOrdersResponse>(x => x.MessaNo).CanFilter().CanSort();
        mapper.Property<GetAllOrdersResponse>(x => x.CustomerNo).CanFilter().CanSort();
        mapper.Property<GetAllOrdersResponse>(x => x.Status).CanFilter().CanSort();
        mapper.Property<GetAllOrdersResponse>(x => x.CustomerShortName).CanFilter().CanSort();
        mapper.Property<GetAllOrdersResponse>(x => x.CustomerLocationName).CanFilter().CanSort();
        mapper.Property<GetAllOrdersResponse>(x => x.BranchName).CanFilter().CanSort();
        mapper.Property<GetAllOrdersResponse>(x => x.Comments).CanFilter().CanSort();
        mapper.Property<GetAllOrdersResponse>(x => x.Weight).CanFilter().CanSort();
        mapper.Property<GetAllOrdersResponse>(x => x.CreatedDate).CanSort();
    }

    public static IQueryable<GetAllOrdersResponse> GlobalSearchFilter(
        IQueryable<GetAllOrdersResponse> source, string op, string[] values)
    {
        var value = values.FirstOrDefault();
        if (string.IsNullOrEmpty(value))
            return source;

        // Szukaj statusów, których DisplayName zawiera wartość
        var matchingStatusIds = Enum.GetValues(typeof(OrderStatus))
            .Cast<OrderStatus>()
            .Where(status => status.GetDisplayName().Contains(value, StringComparison.OrdinalIgnoreCase))
            .Select(status => (int)status)
            .ToList();

        return op switch
        {
            "@=" => source.Where(x =>
                   (x.MessaNo.HasValue && x.MessaNo.Value.ToString().Contains(value)) ||
                   x.Id.ToString().Contains(value) ||
                   x.No.Contains(value) ||
                   x.CustomerNo.ToString().Contains(value) ||
                   matchingStatusIds.Contains((int)x.Status) ||
                   x.Comments.Contains(value) ||
                   x.CustomerNo.Contains(value) ||
                   x.CustomerShortName.Contains(value) ||
                   x.CustomerLocationName.Contains(value) ||
                   x.BranchName.Contains(value)),
            _ => source
        };
    }

    public static IQueryable<GetAllOrdersResponse> CreatedDateFilter(
        IQueryable<GetAllOrdersResponse> source, string op, string[] values) =>
            SieveDateFiltersHelper.DateOnlyFilter(source, x => x.CreatedDate, op, values);
}
