using EMessa.Core.Common.Results;
using EMessa.Core.Helpers;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Internal;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Orders.Queries.GetOrderCountsByStatus;

public record GetOrderCountsByStatusQuery(DateTime? CreatedFrom, DateTime? CreatedTo) : IRequest<ListResult<GetOrderCountsByStatusResponse>>;

public class GetOrderCountsByStatusQueryHandler(
    ILogger<GetOrderCountsByStatusQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IOrderAccessService orderAccessService)
    : IRequestHandler<GetOrderCountsByStatusQuery, ListResult<GetOrderCountsByStatusResponse>>
{
    public async Task<ListResult<GetOrderCountsByStatusResponse>> Handle(GetOrderCountsByStatusQuery request, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var data = await orderAccessService.GetAccessibleOrdersAsync(dbContext, cancellationToken);

            if (request.CreatedFrom.HasValue)
                data = SieveDateFiltersHelper.DateOnlyFilter(data, x => x.CreatedDate, ">=", [request.CreatedFrom.Value.ToString("yyyy-MM-ddTHH:mm:ss.fffffffZ")]);

            if (request.CreatedTo.HasValue)
                data = SieveDateFiltersHelper.DateOnlyFilter(data, x => x.CreatedDate, "<=", [request.CreatedTo.Value.ToString("yyyy-MM-ddTHH:mm:ss.fffffffZ")]);

            var response = await data
                .AsNoTracking()
                .GroupBy(x => x.Status)
                .Select(g => new GetOrderCountsByStatusResponse
                {
                    Status = g.Key,
                    Count = g.Count()
                }).ToListAsync(cancellationToken);

            return ListResult<GetOrderCountsByStatusResponse>.Success(response, response.Count);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while handling GetAllOrdersQuery: {Message}", ex.Message);
            return ListResult<GetOrderCountsByStatusResponse>.Failure(ResultErrorMessages.UnexpectedError);
        }
    }
}