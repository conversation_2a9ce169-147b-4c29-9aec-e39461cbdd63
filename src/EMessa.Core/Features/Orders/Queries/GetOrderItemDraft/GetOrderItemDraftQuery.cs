using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Orders.Queries.GetOrderItemDraft;

public record GetOrderItemDraftQuery(int OrderItemId) : IRequest<Result<GetOrderItemDraftResponse>>;

public class GetOrderItemDraftQueryHandler(
    ILogger<GetOrderItemDraftQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IFileStorageService fileStorage) : IRequestHandler<GetOrderItemDraftQuery, Result<GetOrderItemDraftResponse>>
{
    public async Task<Result<GetOrderItemDraftResponse>> Handle(GetOrderItemDraftQuery request, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var orderItem = await dbContext.OrderItems
                .Where(x => x.Id == request.OrderItemId)
                .FirstOrDefaultAsync(cancellationToken);

            if (orderItem is null || string.IsNullOrWhiteSpace(orderItem.DraftHashedFileName))
            {
                logger.LogWarning("GetOrderItemDraftQueryHandler | No draft found for OrderItemId {OrderItemId}.", request.OrderItemId);
                return Result<GetOrderItemDraftResponse>.Fail("Nie znaleziono szkicu dla tej pozycji zamówienia.".Tr());
            }

            GetOrderItemDraftResponse? orderItemDraft = null;

            try
            {
                var draftImage = fileStorage.GetFileAsync(orderItem.DraftHashedFileName, FileStorageType.Drafts, cancellationToken);

                orderItemDraft = new GetOrderItemDraftResponse(
                    FileContent: await draftImage,
                    FileName: orderItem.DraftOriginalFileName ?? "",
                    FileExtension: Path.GetExtension(orderItem.DraftOriginalFileName)?.TrimStart('.') ?? ""
                );
            }
            catch (FileNotFoundException ex)
            {
                logger.LogWarning(ex, "File not found for draft image with OrderItemId {OrderItemId}.", request.OrderItemId);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error retrieving file for draft image with OrderItemId {OrderItemId}.", request.OrderItemId);
                throw;
            }

            if (orderItemDraft is null)
            {
                logger.LogWarning("GetOrderItemDraftQueryHandler | No draft images found for OrderItemId {OrderItemId}.", request.OrderItemId);
                return Result<GetOrderItemDraftResponse>.Fail("Nie znaleziono szkicu dla tej pozycji zamówienia.".Tr());
            }

            return Result<GetOrderItemDraftResponse>.Success(orderItemDraft);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while getting order item draft for OrderItemId: {OrderItemId}", request.OrderItemId);
            return Result<GetOrderItemDraftResponse>.Fail(ResultErrorMessages.UnexpectedError);
        }
    }
}
