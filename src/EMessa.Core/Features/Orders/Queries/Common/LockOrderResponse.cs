namespace EMessa.Core.Features.Orders.Queries.Common;

public class LockOrderResponse
{
    public int Id { get; set; }
    public int OrderId { get; set; }
    public string OrderNo { get; set; } = null!;
    public int LockedByUserProfileId { get; set; }
    public string LockedBy { get; set; } = null!;
    public bool IsLockedByOtherUser { get; set; }
    public bool IsLockedByCurrentUser => !IsLockedByOtherUser;
    // public DateTime ExpiresAt { get; set; } // not used yet
    // public bool IsExpired => DateTime.Now > ExpiresAt;
    // public TimeSpan TimeToExpiry => ExpiresAt - DateTime.Now;
}
