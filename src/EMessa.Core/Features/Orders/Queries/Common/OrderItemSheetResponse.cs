using EMessa.Base.Constants;

namespace EMessa.Core.Features.Orders.Queries.Common;

public class OrderItemSheetResponse
{
    public int Id { get; set; }
    public int Quantity { get; set; }
    public decimal Width { get; set; }
    public decimal Length { get; set; }

    public decimal Mb => Quantity * Length;
    public decimal M2 => Quantity * Length * Width;
    public decimal RoundMb => Math.Round(Mb, SystemConstants.RoundingSumsDecimals, MidpointRounding.AwayFromZero);
    public decimal RoundM2 => Math.Round(M2, SystemConstants.RoundingSumsDecimals, MidpointRounding.AwayFromZero);
}
