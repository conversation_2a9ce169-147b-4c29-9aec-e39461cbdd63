using EMessa.Base.Constants;
using EMessa.Base.Enums;

namespace EMessa.Core.Features.Orders.Queries.Common;

public class OrderItemResponse
{
    public int Id { get; set; }
    public int OrderId { get; set; }
    public int ArticleId { get; set; }
    public string ArticleName { get; set; } = string.Empty;
    public string ArticleTranslatedName { get; set; } = string.Empty;
    public ArticleType ArticleType { get; set; }
    public string ArticleUnitName { get; set; } = string.Empty;
    public string Index { get; set; } = string.Empty;

    public decimal Quantity { get; set; }
    public int SumQuantity { get; set; }
    public decimal SumM2 { get; set; }
    public decimal SumMb { get; set; }
    public decimal Weight { get; set; }
    public bool DraftEditable { get; set; }
    public string Comments { get; set; } = string.Empty;

    public List<OrderItemRequestedSheetResponse> RequestedSheets { get; set; } = [];
    public List<OrderItemProductOptionResponse> OptionValues { get; set; } = [];

    public decimal BasePrice { get; set; }
    public decimal FinalPrice { get; set; }
    public string CurrencyCode { get; set; } = null!;

    public OrderStatus Status { get; set; }
    public DateTime? ProductionDate { get; set; }
    public DateTime? TransportDate { get; set; }

    public int? SaleId { get; set; }

    public string QuantityUnitDisplay
    {
        get
        {
            decimal displayQuantity;

            switch (ArticleType)
            {
                case ArticleType.Complex:
                    var unitNameLower = ArticleUnitName.ToLower();
                    displayQuantity = unitNameLower switch
                    {
                        _ when unitNameLower.Contains(UnitCode.Szt) => Quantity,
                        UnitCode.Mb => SumMb,
                        _ => SumM2
                    };
                    break;
                case ArticleType.Trade:
                case ArticleType.Service:
                default:
                    displayQuantity = Quantity;
                    break;
            }

            return $"{displayQuantity:n2} {ArticleUnitName}";
        }
    }
    public string PriceCurrencyDisplay => $"{FinalPrice:n2} {CurrencyCode}";
}
