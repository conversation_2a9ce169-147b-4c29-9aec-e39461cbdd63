using AutoMapper;
using AutoMapper.QueryableExtensions;
using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Orders.Queries.GetOrderById;

public record GetOrderByIdQuery(int Id) : IRequest<Result<GetOrderByIdResponse>>;

public class GetOrderByIdQueryHandler(
    ILogger<GetOrderByIdQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IOrderResponseTranslationService orderResponseTranslationService,
    IOrderAccessService orderAccessService,
    IMapper mapper)
    : IRequestHandler<GetOrderByIdQuery, Result<GetOrderByIdResponse>>
{
    public async Task<Result<GetOrderByIdResponse>> Handle(GetOrderByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var data = await orderAccessService.GetAccessibleOrdersAsync(dbContext, cancellationToken);

            var order = await data
                .AsNoTracking()
                .ProjectTo<GetOrderByIdResponse>(mapper.ConfigurationProvider)
                .FirstOrDefaultAsync(x => x.Id == request.Id, cancellationToken: cancellationToken);

            if (order is null)
            {
                logger.LogWarning("GetOrderByIdQueryHandler | Order with ID {OrderId} not found.", request.Id);
                return await Result<GetOrderByIdResponse>.FailAsync(ResultErrorMessages.NotFound);
            }

            order = await orderResponseTranslationService.TranslateArticleNamesAsync(dbContext, order, cancellationToken);

            return await Result<GetOrderByIdResponse>.SuccessAsync(order);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result<GetOrderByIdResponse>.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}