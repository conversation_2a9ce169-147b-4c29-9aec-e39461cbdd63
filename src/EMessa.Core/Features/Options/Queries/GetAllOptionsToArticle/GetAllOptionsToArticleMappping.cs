using AutoMapper;
using EMessa.DAL.Entities.Options;
using System.Globalization;

namespace EMessa.Core.Features.Options.Queries.GetAllOptionsToArticle
{
    public class GetAllOptionsArticleMapping : Profile
    {
        public GetAllOptionsArticleMapping()
        {
            CreateMap<Option, GetAllOptionsArticleResponse>()
                .ForMember(d => d.OriginalName, opt => opt.MapFrom(s => s.Name ?? ""))
                .ForMember(d => d.Name,
                    x => x.MapFrom(s => 
                        s.Translations.Any(t => t.LanguageCode == CultureInfo.CurrentCulture.Name)
                        ? s.Translations.First(t => t.LanguageCode == CultureInfo.CurrentCulture.Name).Name
                        : s.Name))
                ;
        }
    }
}