using EMessa.Core.Common;
using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Messages;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Sieve.Models;

namespace EMessa.Core.Features.Messages.Queries.GetAll;

public class GetAllMessagesQuery : SieveGetAllQueryBase<GetMessageResponse>
{
    public GetAllMessagesQuery(SieveModel sieveModel) : base(sieveModel)
    { }

    public GetAllMessagesQuery() : base()
    { }
}

public class GetAllMessagesQueryHandler(
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ISieveService sieveService)
    : IRequestHandler<GetAllMessagesQuery, ListResult<GetMessageResponse>>
{
    public async Task<ListResult<GetMessageResponse>> Handle(GetAllMessagesQuery request, CancellationToken cancellationToken)
    {
        await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

        var messages = dbContext
            .Messages
            .Include(x => x.Translations)
            .AsNoTracking()
            .AsQueryable();

        var response = await sieveService
            .ExecuteSieveAsync<Message, GetMessageResponse>(
                request.SieveModel,
                messages,
                cancellationToken);

        return response;
    }
}
