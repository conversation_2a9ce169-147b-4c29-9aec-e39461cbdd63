using System.Globalization;
using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.ArticleValidators;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Sieve.Models;

namespace EMessa.Core.Features.ArticleValidators.Queries.GetAllArticleValidatorCondition;

public record GetAllArticleValidatorConditionQuery(SieveModel SieveModel)
    : IRequest<ListResult<GetAllArticleValidatorConditionResponse>>
{
    public GetAllArticleValidatorConditionQuery()
        : this(new SieveModel { Page = 1, PageSize = int.MaxValue })
    {
    }
}

public class GetAllArticleValidatorConditionQueryHandler(
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ISieveService sieveService)
    : IRequestHandler<GetAllArticleValidatorConditionQuery, ListResult<GetAllArticleValidatorConditionResponse>>
{
    public async Task<ListResult<GetAllArticleValidatorConditionResponse>> Handle(
        GetAllArticleValidatorConditionQuery request,
        CancellationToken cancellationToken)
    {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var conditions = dbContext
                .ArticleValidatorConditions
                .Include(x => x.Translations)
                .OrderBy(x => x.Name)
                .AsNoTracking()
                .AsQueryable();
            

            var response = await sieveService
                .ExecuteSieveAsync<ArticleValidatorCondition, GetAllArticleValidatorConditionResponse>(
                    request.SieveModel,
                    conditions,
                    cancellationToken);
            
            return response;
    }
}