using AutoMapper;
using EMessa.Core.Common.Results;
using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Profiles.Queries.Get
{
    public record GetProfileQuery(string UserName) : IRequest<Result<GetProfileResponse>>;

    public class GetProfileQueryHandler(
        ILogger<GetProfileQueryHandler> logger,
        IMapper mapper,
        IDbContextFactory<ApplicationDbContext> dbContextFactory)
        : IRequestHandler<GetProfileQuery, Result<GetProfileResponse>>
    {
        public async Task<Result<GetProfileResponse>> Handle(GetProfileQuery request, CancellationToken cancellationToken)
        {
            try
            {
                await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);
                var userProfile = await dbContext.UserProfiles.SingleAsync(x => x.UserName == request.UserName, cancellationToken);
                
                var result = mapper.Map<GetProfileResponse>(userProfile);
                
                return await Result<GetProfileResponse>.SuccessAsync(result);
            }
            catch (Exception e)
            {
                logger.LogError(e, e.Message);
                return await Result<GetProfileResponse>.FailAsync("Operacja nie powiodła się.".Tr());
            }
        }
    }
}
