using EMessa.DAL.Entities.Categories;

namespace EMessa.Core.Features.Categories.Queries.Get
{
   public class GetCategoryResponse
    {
        public int Id { get; set; }
        public string Code { get; set; } = "";
        public string Name { get; set; } = "";
        public string TranslatedName { get; set; } = "";
        public string Description { get; set; } = "";
        public Category Parent { get; set; } = null!; // TODO Tutaj i poniżej zamiast Category powinno być GetCategoryResponse
        public int? ParentId { get; set; }
        public ICollection<Category> SubCategories { get; set; } = [];
        public int OrdinaryNumber { get; set; }
        public bool IsActive { get; set; } = true;
    }

}
