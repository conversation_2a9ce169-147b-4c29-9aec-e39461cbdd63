using EMessa.Core.Common;
using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Notifications;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Sieve.Models;

namespace EMessa.Core.Features.Notifications.Queries.GetAll;

public class GetAllNotificationsQuery : SieveGetAllQueryBase<GetAllNotificationsResponse>
{
    public GetAllNotificationsQuery(SieveModel sieveModel) : base(sieveModel)
    { }

    public GetAllNotificationsQuery() : base()
    { }
}

public class GetAllNotificationsQuerySieveHandler(
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ISieveService sieveService)
    : IRequestHandler<GetAllNotificationsQuery, ListResult<GetAllNotificationsResponse>>
{
    public async Task<ListResult<GetAllNotificationsResponse>> Handle(GetAllNotificationsQuery request, CancellationToken cancellationToken)
    {
        await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

        var notifications = dbContext
            .Notifications
            .Include(x => x.CreatedBy)
            .Include(x => x.EditedBy)
            .AsNoTracking()
            .AsQueryable();

        var response = await sieveService
            .ExecuteSieveAsync<Notification, GetAllNotificationsResponse>(
                request.SieveModel,
                notifications,
                cancellationToken);

        return response;
    }
}
