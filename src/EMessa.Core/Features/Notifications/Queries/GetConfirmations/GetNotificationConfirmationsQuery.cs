using EMessa.Core.Common;
using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Sieve.Models;

namespace EMessa.Core.Features.Notifications.Queries.GetConfirmations;

public class GetNotificationConfirmationsQuery : SieveGetAllQueryBase<GetNotificationConfirmationsResponse>
{
    public int NotificationId { get; private set; }
    public GetNotificationConfirmationsQuery(int notificationId, SieveModel sieveModel) : base(sieveModel)
    {
        NotificationId = notificationId;
    }

    public GetNotificationConfirmationsQuery(int notificationId) : base()
    {
        NotificationId = notificationId;
    }
}

public class GetNotificationConfirmationsQuerySieveHandler(
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ISieveService sieveService)
    : IRequestHandler<GetNotificationConfirmationsQuery, ListResult<GetNotificationConfirmationsResponse>>
{
    public async Task<ListResult<GetNotificationConfirmationsResponse>> Handle(GetNotificationConfirmationsQuery request, CancellationToken cancellationToken)
    {

        await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

        var all = from user in dbContext.UserProfiles
                  join nu in dbContext.NotificationUsers
                      on new { UserId = user.Id, request.NotificationId }
                      equals new { UserId = nu.UserProfileId, nu.NotificationId }
                      into joined
                  from confirmation in joined.DefaultIfEmpty()
                  where confirmation == null || confirmation.NotificationId == request.NotificationId
                  select new GetNotificationConfirmationsResponse
                  {
                      Id = confirmation != null ? confirmation.Id : 0, // Id = 0 dla pustych rekordów
                      ConfirmedDate = confirmation != null ? confirmation.ConfirmedDate : null,
                      NotificationId = request.NotificationId,
                      UserId = user.Id,
                      UserName = user.FirstName + " " + user.LastName
                  };

        var response = await sieveService
            .ExecuteSieveAsync<GetNotificationConfirmationsResponse, GetNotificationConfirmationsResponse>(
                request.SieveModel,
                all,
                cancellationToken);

        // Ustawienie ujemnego Id dla pustych rekordów w celu rozróżnienia ich
        int emptyId = -1;
        response.Data.ForEach(x => x.Id = x.Id == 0 ? emptyId-- : x.Id);

        return response;
    }
}