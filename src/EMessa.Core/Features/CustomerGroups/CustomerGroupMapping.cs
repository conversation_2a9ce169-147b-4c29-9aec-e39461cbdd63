using AutoMapper;
using EMessa.Core.Features.CustomerGroups.Commands.AddEditCustomerGroup;
using EMessa.Core.Features.CustomerGroups.Queries.CommonResponses;
using EMessa.Core.Features.CustomerGroups.Queries.GetAll;
using EMessa.Core.Features.CustomerGroups.Queries.GetById;
using EMessa.DAL.Entities.Customers;

namespace EMessa.Core.Features.CustomerGroups;

public class CustomerGroupMapping : Profile
{
    public CustomerGroupMapping()
    {
        CreateMap<AddEditCustomerGroupCommand, CustomerGroup>();
        CreateMap<GetCustomerGroupsByIdResponse, AddEditCustomerGroupCommand>();

        CreateMap<CustomerGroup, CustomerGroupResponse>();
        CreateMap<CustomerGroup, GetAllCustomerGroupsResponse>()
            .ForMember(dest => dest.CustomersCount, opt => opt.MapFrom(src => src.CustomersGroupCustomers.Count));
        CreateMap<CustomerGroup, GetCustomerGroupsByIdResponse>()
            .ForMember(dest => dest.Customers, opt => opt.MapFrom(src => src.CustomersGroupCustomers.Select(x => x.Customer)));
    }
}
