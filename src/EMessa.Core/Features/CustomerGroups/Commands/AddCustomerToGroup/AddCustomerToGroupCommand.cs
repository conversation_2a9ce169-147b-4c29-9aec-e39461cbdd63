using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Customers;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.CustomerGroups.Commands.AddCustomerToGroup;

public record AddCustomerToGroupCommand(int CustomerGroupId, IEnumerable<int> CustomerIds) : IRequest<IResult>;

public class AddCustomerToGroupCommandHandler(
    ILogger<AddCustomerToGroupCommandHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory)
    : IRequestHandler<AddCustomerToGroupCommand, IResult>
{
    public async Task<IResult> Handle(AddCustomerToGroupCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var validator = new AddCustomerToGroupValidator(dbContextFactory, logger);
            var validationResult = await validator.ValidateAsync(request, cancellationToken);

            if (!validationResult.IsValid)
                return await Result<int>.FailAsync(validationResult);

            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var customerGroupCustomers = await dbContext.CustomersGroupCustomers
                .AsNoTracking()
                .Where(x => x.CustomerGroupId == request.CustomerGroupId)
                .ToListAsync(cancellationToken);

            foreach (var customerID in request.CustomerIds)
            {
                if (!customerGroupCustomers.Any(x => x.CustomerId == customerID))
                {
                    dbContext.CustomersGroupCustomers.Add(new CustomerGroupCustomer
                    {
                        CustomerGroupId = request.CustomerGroupId,
                        CustomerId = customerID
                    });
                }
            }

            await dbContext.SaveChangesAsync(cancellationToken);
            //logger.LogInformation("Dodano {Count} klientów do grupy {CustomerGroupId}.", customerGroupCustomers.Count, request.CustomerGroupId);

            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}