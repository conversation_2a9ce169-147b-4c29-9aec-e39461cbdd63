using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.CustomerGroups.Commands.AddCustomerToGroup;

public class AddCustomerToGroupValidatorBase : AbstractValidator<AddCustomerToGroupCommand>
{
    public AddCustomerToGroupValidatorBase()
    {
        RuleFor(x => x.CustomerGroupId)
            .GreaterThan(0).WithMessage("Pole {0} jest wymagane.".Tr("Grupa klientów"))
            .CustomAsync(ValidateCustomerGroupIdAsync);

        RuleFor(x => x.CustomerIds)
            .NotEmpty().WithMessage("Pole {0} jest wymagane.".Tr("Klienci"))
            .CustomAsync(ValidateCustomerIdsAsync);
    }

    protected virtual Task ValidateCustomerGroupIdAsync(
        int customerGroupId, ValidationContext<AddCustomerToGroupCommand> context, CancellationToken cancellationToken)
            => Task.CompletedTask;

    protected virtual Task ValidateCustomerIdsAsync(
        IEnumerable<int> customerIds, ValidationContext<AddCustomerToGroupCommand> context, CancellationToken cancellationToken)
            => Task.CompletedTask;
}

public class AddCustomerToGroupValidator(
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ILogger logger)
    : AddCustomerToGroupValidatorBase
{
    protected override async Task ValidateCustomerGroupIdAsync(
     int customerGroupId, ValidationContext<AddCustomerToGroupCommand> context, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var customerGroup = await dbContext.CustomerGroups
                .AsNoTracking()
                .SingleOrDefaultAsync(x => x.Id == customerGroupId, cancellationToken);

            if (customerGroup is null)
            {
                context.AddFailure(nameof(AddCustomerToGroupCommand.CustomerGroupId), "Grupa użytkowników o podanym ID nie istnieje.".Tr());
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            throw;
        }
    }

    protected override async Task ValidateCustomerIdsAsync(
        IEnumerable<int> customerIds, ValidationContext<AddCustomerToGroupCommand> context, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var customersFromDb = await dbContext.Customers
                .AsNoTracking()
                .Where(x => customerIds.Contains(x.Id))
                .ToListAsync(cancellationToken);

            if (customersFromDb.Count != customerIds.Count())
            {
                var notFoundIds = customerIds.Except(customersFromDb.Select(x => x.Id)).ToList();
                context.AddFailure(
                    nameof(AddCustomerToGroupCommand.CustomerIds),
                    "Klienci o podanych ID nie istnieją:".Tr()
                    + " " + string.Join(", ", notFoundIds));
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            throw;
        }
    }
}