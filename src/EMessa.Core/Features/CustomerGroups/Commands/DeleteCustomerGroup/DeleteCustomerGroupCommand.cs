using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.CustomerGroups.Commands.DeleteCustomerGroup;
public record DeleteCustomerGroupCommand(int CustomerGroupId) : IRequest<IResult>;

public class DeleteCustomerGroupCommandHandler(
    ILogger<DeleteCustomerGroupCommandHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory)
    : IRequestHandler<DeleteCustomerGroupCommand, IResult>
{
    public async Task<IResult> Handle(DeleteCustomerGroupCommand request, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var customerGroup = await dbContext.CustomerGroups
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.Id == request.CustomerGroupId, cancellationToken);

            if (customerGroup is null)
            {
                logger.LogWarning("Attempted to delete customer group with id '{Id}', but the customer group was not found.", request.CustomerGroupId);
                return await Result.FailAsync(ResultErrorMessages.NotFound);
            }

            dbContext.CustomerGroups.Remove(customerGroup);
            await dbContext.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Deleted customer group with id '{Id}'.", request.CustomerGroupId);

            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}
