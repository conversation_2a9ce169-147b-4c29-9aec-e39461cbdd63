using AutoMapper;
using EMessa.Base.Enums;
using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Customers;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.CustomerGroups.Commands.AddEditCustomerGroup;

public class AddEditCustomerGroupCommand : IRequest<IResult<int>>
{
    public int? Id { get; set; }
    public string Name { get; set; } = null!;
    public string Code { get; set; } = null!;
    public string? Description { get; set; }
    public CustomerGroupType Type { get; set; }
}

public class AddEditCustomerGroupCommandHandler(
    ILogger<AddEditCustomerGroupCommandHandler> logger,
    IMapper mapper,
    IDbContextFactory<ApplicationDbContext> dbContextFactory)
    : IRequestHandler<AddEditCustomerGroupCommand, IResult<int>>
{
    public async Task<IResult<int>> Handle(AddEditCustomerGroupCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var validationResult = await new AddEditCustomerGroupValidator()
            .ValidateAsync(request, cancellationToken);

            if (!validationResult.IsValid)
                return await Result<int>.FailAsync(validationResult);

            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);


            var customerGroup = await dbContext.CustomerGroups
                .SingleOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

            if (customerGroup is null && request.Id > 0)
            {
                logger.LogWarning("Dokonano próby edycji nieistniejącej grupy klientów o ID: {Id}", request.Id);
                return await Result<int>.FailAsync(ResultErrorMessages.NotFound);
            }

            if (customerGroup is not null)
            {
                mapper.Map(request, customerGroup);
            }
            else
            {
                customerGroup = mapper.Map<CustomerGroup>(request);
            }

            dbContext.CustomerGroups.Entry(customerGroup).State = customerGroup.Id > 0 ? EntityState.Modified : EntityState.Added;

            await dbContext.SaveChangesAsync(cancellationToken);
            return await Result<int>.SuccessAsync(customerGroup.Id);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result<int>.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}
