using EMessa.Core.Localizer;
using FluentValidation;

namespace EMessa.Core.Features.CustomerGroups.Commands.AddEditCustomerGroup;

public class AddEditCustomerGroupValidator : AbstractValidator<AddEditCustomerGroupCommand>
{
    public AddEditCustomerGroupValidator()
    {
        RuleFor(x => x.Code)
            .NotEmpty().WithMessage("Kod grupy klientów jest wymagany.")
            .MaximumLength(100).WithMessage("Kod grupy klientów nie może przekraczać 50 znaków.");

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Pole {0} jest wymagane.".Tr("Nazwa pełna"))
            .MaximumLength(3000).WithMessage("Pole {0} może mieć maks. długość {1} znaków.".Tr("Nazwa pełna", "250"));

        RuleFor(x => x.Type)
            .IsInEnum().WithMessage("Pole {0} jest wymagane.".Tr("Typ grupy klientów"));
    }
}