using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.CustomerGroups.Commands.AddCustomerToGroup;

public class RemoveCustomerFromGroupValidatorBase : AbstractValidator<RemoveCustomerFromGroupCommand>
{
    public RemoveCustomerFromGroupValidatorBase()
    {
        RuleFor(x => x.CustomerGroupId)
            .GreaterThan(0).WithMessage("Pole {0} jest wymagane.".Tr("Grupa klientów"))
            .CustomAsync(ValidateCustomerGroupIdAsync);

        RuleFor(x => x.CustomerIds)
            .NotEmpty().WithMessage("Pole {0} jest wymagane.".Tr("<PERSON><PERSON>nci"))
            .CustomAsync(ValidateCustomerIdsAsync);
    }

    protected virtual Task ValidateCustomerGroupIdAsync(
        int customerGroupId, ValidationContext<RemoveCustomerFromGroupCommand> context, CancellationToken cancellationToken)
            => Task.CompletedTask;

    protected virtual Task ValidateCustomerIdsAsync(
        IEnumerable<int> customerIds, ValidationContext<RemoveCustomerFromGroupCommand> context, CancellationToken cancellationToken)
            => Task.CompletedTask;
}

public class RemoveCustomerFromGroupValidator(
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ILogger logger)
    : RemoveCustomerFromGroupValidatorBase
{
    protected override async Task ValidateCustomerGroupIdAsync(
     int customerGroupId, ValidationContext<RemoveCustomerFromGroupCommand> context, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var customerGroup = await dbContext.CustomerGroups
                .AsNoTracking()
                .SingleOrDefaultAsync(x => x.Id == customerGroupId, cancellationToken);

            if (customerGroup is null)
            {
                context.AddFailure(nameof(RemoveCustomerFromGroupCommand.CustomerGroupId), "Grupa użytkowników o podanym ID nie istnieje.".Tr());
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            throw;
        }
    }

    protected override async Task ValidateCustomerIdsAsync(
        IEnumerable<int> customerIds, ValidationContext<RemoveCustomerFromGroupCommand> context, CancellationToken cancellationToken)
    {
        var command = context.InstanceToValidate;
        var customerGroupId = command.CustomerGroupId;
        var distinctCustomerIdsToValidate = customerIds.Distinct().ToList();

        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var customersFoundInDb = await dbContext.Customers
                .AsNoTracking()
                .Where(x => distinctCustomerIdsToValidate.Contains(x.Id))
                .Select(x => x.Id)
                .ToListAsync(cancellationToken);

            var notExistingInDbIds = distinctCustomerIdsToValidate.Except(customersFoundInDb).ToList();
            if (notExistingInDbIds.Any())
            {
                context.AddFailure(nameof(RemoveCustomerFromGroupCommand.CustomerIds),
                    "Następujący klienci o podanych ID nie istnieją w bazie danych:".Tr()
                    + " " + string.Join(", ", notExistingInDbIds));
            }

            var customerGroup = await dbContext.CustomerGroups
            .AsNoTracking()
            .Include(cg => cg.CustomersGroupCustomers)
            .FirstAsync(cg => cg.Id == customerGroupId, cancellationToken);

            var customerIdsCurrentlyInGroup = customerGroup.CustomersGroupCustomers.Select(c => c.CustomerId).ToHashSet();

            var customersFoundInDbButNotMembersOfThisGroup = customersFoundInDb
                .Where(existingCustomerIdFromDb => !customerIdsCurrentlyInGroup.Contains(existingCustomerIdFromDb))
                .ToList();

            if (customersFoundInDbButNotMembersOfThisGroup.Any())
            {
                string groupNameForMessage = string.IsNullOrEmpty(customerGroup.Name)
                    ? $"ID: {customerGroupId}"
                    : $"'{customerGroup.Name}' (ID: {customerGroupId})";

                context.AddFailure(nameof(RemoveCustomerFromGroupCommand.CustomerIds),
                    "Następujący klienci nie należą do grupy {0} i nie mogą zostać z niej usunięci:".Tr(customerGroup.Name)
                    + " " + string.Join(", ", customersFoundInDbButNotMembersOfThisGroup));
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            throw;
        }
    }
}