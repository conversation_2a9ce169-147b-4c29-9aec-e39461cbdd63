using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using FluentValidation;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.CustomerGroups.Commands.AddCustomerToGroup;

public record RemoveCustomerFromGroupCommand(int CustomerGroupId, IEnumerable<int> CustomerIds) : IRequest<IResult>;

public class RemoveCustomerFromGroupCommandHandler(
    ILogger<RemoveCustomerFromGroupCommandHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory)
    : IRequestHandler<RemoveCustomerFromGroupCommand, IResult>
{
    public async Task<IResult> Handle(RemoveCustomerFromGroupCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var validationResult = await new RemoveCustomerFromGroupValidator(dbContextFactory, logger)
                .ValidateAsync(request, cancellationToken);

            if (!validationResult.IsValid)
                return await Result.FailAsync(validationResult);

            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var customersGroupCustomers = await dbContext.CustomersGroupCustomers
                .AsTracking()
                .Where(x => x.CustomerGroupId == request.CustomerGroupId&& request.CustomerIds.Contains(x.CustomerId))
                .ToListAsync(cancellationToken);

            if (!customersGroupCustomers.Any())
            {
                logger.LogInformation("Żaden z podanych klientów ({CustomerIds}) nie był powiązany z grupą {CustomerGroupId}.",
                    string.Join(", ", request.CustomerIds), request.CustomerGroupId);
                return await Result.SuccessAsync();
            }

            dbContext.RemoveRange(customersGroupCustomers);

            await dbContext.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Usunięto {Count} klientów z grupy {CustomerGroupId}.",
                customersGroupCustomers.Count, request.CustomerGroupId);

            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}