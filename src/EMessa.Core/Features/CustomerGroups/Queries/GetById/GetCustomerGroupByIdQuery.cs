using AutoMapper;
using EMessa.Core.Common.Results;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.CustomerGroups.Queries.GetById;

public record GetCustomerGroupByIdQuery(int CustomerGroupId) : IRequest<Result<GetCustomerGroupsByIdResponse>>;

public class GetCustomerGroupByIdQueryHandler(
    ILogger<GetCustomerGroupByIdQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IMapper mapper)
    : IRequestHandler<GetCustomerGroupByIdQuery, Result<GetCustomerGroupsByIdResponse>>
{
    public async Task<Result<GetCustomerGroupsByIdResponse>> Handle(GetCustomerGroupByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var customerGroup = await dbContext.CustomerGroups
                .Include(x => x.CustomersGroupCustomers)
                    .ThenInclude(x => x.Customer)
                        .ThenInclude(x => x!.Branch)
                .AsNoTracking()
                .SingleOrDefaultAsync(x => x.Id == request.CustomerGroupId, cancellationToken);

            if (customerGroup is null)
            {
                logger.LogWarning("Customer group with ID {CustomerGroupId} not found.", request.CustomerGroupId);
                return await Result<GetCustomerGroupsByIdResponse>.FailAsync(ResultErrorMessages.NotFound);
            }

            var result = mapper.Map<GetCustomerGroupsByIdResponse>(customerGroup);

            return await Result<GetCustomerGroupsByIdResponse>.SuccessAsync(result);

        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result<GetCustomerGroupsByIdResponse>.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}