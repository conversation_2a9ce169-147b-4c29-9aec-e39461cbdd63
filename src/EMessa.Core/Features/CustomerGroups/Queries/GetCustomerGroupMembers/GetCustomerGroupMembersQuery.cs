using EMessa.Core.Common;
using EMessa.Core.Common.Results;
using EMessa.Core.Features.Customers.Queries.GetAll;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Customers;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Sieve.Models;

namespace EMessa.Core.Features.CustomerGroups.Queries.GetCustomerGroupMembers;

public class GetCustomerGroupMembersQuery : SieveGetAllQueryBase<GetAllCustomersResponse>
{
    public int CustomerGroupId { get; set; }
    public GetCustomerGroupMembersQuery(int customerGroupId, SieveModel sieveModel) : base(sieveModel)
    {
        CustomerGroupId = customerGroupId;
    }

    public GetCustomerGroupMembersQuery(int customerGroupId) : base()
    {
        CustomerGroupId = customerGroupId;
    }
}

public class GetCustomerGroupMembersQueryHandler(
    ILogger<GetCustomerGroupMembersQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ISieveService sieveService)
        : IRequestHandler<GetCustomerGroupMembersQuery, ListResult<GetAllCustomersResponse>>
{
    public async Task<ListResult<GetAllCustomersResponse>> Handle(GetCustomerGroupMembersQuery request, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var customersQuery = dbContext.CustomersGroupCustomers
                .Include(x => x.Customer)
                    .ThenInclude(x => x!.Branch)
                .Where(x => x.CustomerGroupId == request.CustomerGroupId)
                .Select(x => x.Customer!)
                .AsNoTracking()
                .AsQueryable();

            var response = await sieveService
                .ExecuteSieveAsync<Customer, GetAllCustomersResponse>(
                    request.SieveModel,
                    customersQuery,
                    cancellationToken);

            return response;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return ListResult<GetAllCustomersResponse>.Failure(ResultErrorMessages.UnexpectedError);
        }
    }
}