using Microsoft.IdentityModel.Tokens;
using Sieve.Services;

namespace EMessa.Core.Features.CustomerGroups.Queries.GetAll;

public class CustomerGroupsSieveConfiguration : ISieveConfiguration
{
    public void Configure(SievePropertyMapper mapper)
    {
        mapper.Property<GetAllCustomerGroupsResponse>(x => x.Id).CanFilter().CanSort();
        mapper.Property<GetAllCustomerGroupsResponse>(x => x.Code).CanFilter().CanSort();
        mapper.Property<GetAllCustomerGroupsResponse>(x => x.Name).CanFilter().CanSort();
        mapper.Property<GetAllCustomerGroupsResponse>(x => x.Description).CanFilter().CanSort();
        mapper.Property<GetAllCustomerGroupsResponse>(x => x.Type).CanFilter().CanSort();
        mapper.Property<GetAllCustomerGroupsResponse>(x => x.CustomersCount).CanFilter().CanSort();
    }

    public static IQueryable<GetAllCustomerGroupsResponse> GlobalSearchFilter(
        IQueryable<GetAllCustomerGroupsResponse> source, string op, string[] values)
    {
        var value = values.First();

        if (value.IsNullOrEmpty())
            return source;

        return op switch
        {
            "@=" => source.Where(x =>
                   x.Id.ToString().Contains(value)
                || x.Code.Contains(value)
                || x.Name.Contains(value)
                || (x.Description != null && x.Description.Contains(value))
                || x.Type.ToString().Contains(value)
                || x.CustomersCount.ToString().Contains(value)),
            _ => source
        };
    }
}
