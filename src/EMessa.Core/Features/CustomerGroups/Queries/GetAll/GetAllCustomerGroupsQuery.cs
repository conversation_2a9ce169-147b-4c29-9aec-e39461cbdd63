using AutoMapper;
using AutoMapper.QueryableExtensions;
using EMessa.Core.Common;
using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Sieve.Models;

namespace EMessa.Core.Features.CustomerGroups.Queries.GetAll;

public class GetAllCustomerGroupsQuery : SieveGetAllQueryBase<GetAllCustomerGroupsResponse>
{
    public GetAllCustomerGroupsQuery(SieveModel sieveModel) : base(sieveModel)
    { }

    public GetAllCustomerGroupsQuery() : base()
    { }
}


public class GetAllCustomerGroupsQueryHandler(
    ILogger<GetAllCustomerGroupsQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ISieveService sieveService,
    IMapper mapper)
    : IRequestHandler<GetAllCustomerGroupsQuery, ListResult<GetAllCustomerGroupsResponse>>
{
    public async Task<ListResult<GetAllCustomerGroupsResponse>> Handle(GetAllCustomerGroupsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var customerGroupsQuery = dbContext.CustomerGroups
                .Include(x => x.CustomersGroupCustomers)
                .AsNoTracking()
                .AsQueryable()
                .ProjectTo<GetAllCustomerGroupsResponse>(mapper.ConfigurationProvider);

            var response = await sieveService
            .ExecuteSieveAsync<GetAllCustomerGroupsResponse, GetAllCustomerGroupsResponse>(
                request.SieveModel,
                customerGroupsQuery,
                cancellationToken);

            return response;

        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return ListResult<GetAllCustomerGroupsResponse>.Failure(ResultErrorMessages.UnexpectedError);
        }
    }
}