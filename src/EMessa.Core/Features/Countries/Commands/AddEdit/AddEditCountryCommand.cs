using AutoMapper;
using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Countries;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Countries.Commands.AddEdit;

public class AddEditCountryCommand : IRequest<IResult<int>>
{
    public int Id { get; set; }
    public string Code { get; set; } = null!;
    public string Name { get; set; } = null!;
    public string LangCode { get; set; } = null!;
    public string VatCode { get; set; } = null!;
    public string CurrencyCode { get; set; } = null!;
}

public class AddEditCountryCommandHandler(
    ILogger<AddEditCountryCommandHandler> logger,
    IMapper mapper,
    IDbContextFactory<ApplicationDbContext> dbFactory) : IRequestHandler<AddEditCountryCommand, IResult<int>>
{
    public async Task<IResult<int>> Handle(AddEditCountryCommand request, CancellationToken cancellationToken)
    {
        var validationResult = await new AddEditCountryValidator()
            .ValidateAsync(request, cancellationToken);

        if (!validationResult.IsValid)
            return await Result<int>.FailAsync(validationResult);

        try
        {
            await using var db = await dbFactory.CreateDbContextAsync(cancellationToken);

            var country = await db.Countries
                .SingleOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

            if (country is null && request.Id > 0)
            {
                logger.LogWarning("Dokonano próby edycji nieistniejącego państwa o Id {Id}", request.Id);
                return await Result<int>.FailAsync(ResultErrorMessages.NotFound);
            }

            country = mapper.Map<Country>(request);

            db.Countries.Entry(country).State = country.Id > 0 ? EntityState.Modified : EntityState.Added;
            await db.SaveChangesAsync(cancellationToken);

            return await Result<int>.SuccessAsync(country.Id);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result<int>.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}