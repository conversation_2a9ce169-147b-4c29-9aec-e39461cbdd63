using EMessa.Base.Constants;
using EMessa.Core.Features.Sales.Commands.AddEditSale;
using EMessa.Core.Localizer;
using EMessa.Core.Validators;
using FluentValidation;

namespace EMessa.Core.Features.Countries.Commands.AddEdit;

public class AddEditCountryValidator : AbstractValidator<AddEditCountryCommand>
{
    public AddEditCountryValidator()
    {
        RuleFor(x => x.Code)
            .NotEmpty().WithMessage("Pole {0} jest wymagane.".Tr("Kod"))
            .MaximumLength(60).WithMessage("Pole {0} może mieć maks. długość {1} znaków.".Tr("Kod", "60"));

        RuleFor(x => x.Name)
            .NotEmpty().WithMessage("Pole {0} jest wymagane.".Tr("Nazwa"))
            .MaximumLength(120).WithMessage("Pole {0} może mieć maks. długość {1} znaków.".Tr("Nazwa", "120"));

        RuleFor(x => x.<PERSON>)
            .NotEmpty().WithMessage("Pole {0} jest wymagane.".Tr("Kod języka"))
            .MaximumLength(20).WithMessage("Pole {0} może mieć maks. długość {1} znaków.".Tr("Kod języka", "20"));

        RuleFor(x => x.VatCode)
            .NotEmpty().WithMessage("Pole {0} jest wymagane.".Tr("Kod VAT"))
            .MaximumLength(20).WithMessage("Pole {0} może mieć maks. długość {1} znaków.".Tr("Kod VAT", "20"));

        RuleFor(x => x.CurrencyCode)
            .ValidCurrencyCode("Kod waluty");
    }
}

