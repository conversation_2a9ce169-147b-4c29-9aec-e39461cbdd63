using EMessa.Core.Common.Results;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Countries.Queries.GetAllCountryCurrencies;

public record GetAllCountryCurrenciesQuery : IRequest<ListResult<string>>;

public class GetAllCountryCurrenciesQueryHandler(
    ILogger<GetAllCountryCurrenciesQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbFactory)
    : IRequestHandler<GetAllCountryCurrenciesQuery, ListResult<string>>
{
    public async Task<ListResult<string>> Handle(GetAllCountryCurrenciesQuery request, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

            var countryCurrencies = await dbContext
                .Countries
                .Select(x => x.CurrencyCode)
                .Distinct()
                .AsNoTracking()
                .ToListAsync(cancellationToken);

            return ListResult<string>.Success(countryCurrencies, countryCurrencies.Count);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while fetching all currencies.");
            return ListResult<string>.Failure(ResultErrorMessages.UnexpectedError);
        }
    }
}
