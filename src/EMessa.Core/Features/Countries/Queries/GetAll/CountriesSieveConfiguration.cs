using EMessa.DAL.Entities.Countries;
using Microsoft.IdentityModel.Tokens;
using Sieve.Services;

namespace EMessa.Core.Features.Branches.Queries.GetAll;

public class CountriesSieveConfiguration : ISieveConfiguration
{
    public void Configure(SievePropertyMapper mapper)
    {
        mapper.Property<Country>(x => x.Id).CanFilter().CanSort();
        mapper.Property<Country>(x => x.Code).CanFilter().CanSort();
        mapper.Property<Country>(x => x.Name).CanFilter().CanSort();
        mapper.Property<Country>(x => x.LangCode).CanFilter().CanSort();
        mapper.Property<Country>(x => x.VatCode).CanFilter().CanSort();
        mapper.Property<Country>(x => x.CurrencyCode).CanFilter().CanSort();
    }

    public static IQueryable<Country> GlobalSearchFilter(
        IQueryable<Country> source, string op, string[] values)
    {
        var value = values.First();

        if (value.IsNullOrEmpty())
            return source;

        return op switch
        {
            "@=" => source.Where(x =>
                   x.Code.Contains(value)
                || x.Name.Contains(value)
                || x.LangCode.Contains(value)
                || x.VatCode.Contains(value)
                || x.CurrencyCode.Contains(value)),
            _ => source
        };
    }
}