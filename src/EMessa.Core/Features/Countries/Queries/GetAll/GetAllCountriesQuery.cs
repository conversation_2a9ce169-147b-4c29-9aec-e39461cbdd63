using EMessa.Core.Common;
using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Countries;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Sieve.Models;

namespace EMessa.Core.Features.Countries.Queries.GetAll;

public class GetAllCountriesQuery : SieveGetAllQueryBase<GetAllCountriesResponse>
{
    public GetAllCountriesQuery(SieveModel sieveModel) : base(sieveModel)
    { }

    public GetAllCountriesQuery() : base()
    { }
}

public class GetAllCountriesQueryHandler(
    ISieveService sieveService,
    IDbContextFactory<ApplicationDbContext> dbFactory)
    : IRequestHandler<GetAllCountriesQuery, ListResult<GetAllCountriesResponse>>
{
    public async Task<ListResult<GetAllCountriesResponse>> Handle(GetAllCountriesQuery request, CancellationToken cancellationToken)
    {
        await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

        var countries = dbContext
            .Countries
            .AsNoTracking()
            .AsQueryable();

        var response = await sieveService
            .ExecuteSieveAsync<Country, GetAllCountriesResponse>(
                request.SieveModel,
                countries,
                cancellationToken);

        return response;
    }
}
