namespace EMessa.Core.Features.ArticleOptionValues.Queries.GetValuesForArticleOption
{
    public class GetAllValuesForArticleOptionResponse
    {
        public int No { get; set; }
        public string Value { get; set; } = null!;
        public string OriginalValue { get; set; } = null!;
        public string Code { get; set; } = null!;
        public int OptionId { get; set; }
        public int ArticleOptionValueId { get; set; }
        public decimal ArticleOptionValueWeightFactor { get; set; } 
        public string ArticleOptionValueValueInfo { get; set; } = string.Empty;
        public bool ArticleOptionValueIsDefault { get; set; } 
        /// <summary>
        /// Dodatkowa strefa wyłączona w prawo (dotyczy np. filcu)
        /// </summary>
        public decimal? ArticleOptionValueEmbossZoneAddition { get; set; }
    }
}

