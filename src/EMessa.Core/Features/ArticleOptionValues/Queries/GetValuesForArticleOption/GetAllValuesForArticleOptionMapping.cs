using AutoMapper;
using EMessa.DAL.Entities.ArticleOptions;
using System.Globalization;
using EMessa.Core.Common.Results;

namespace EMessa.Core.Features.ArticleOptionValues.Queries.GetValuesForArticleOption
{
    public class GetAllValuesForArticleOptionMapping : Profile
    {
        public GetAllValuesForArticleOptionMapping()
        {
            CreateMap<ArticleOptionValue, GetAllValuesForArticleOptionResponse>()

          .ForMember(x => x.ArticleOptionValueId, opt => opt.MapFrom(s => s.Id))
          .ForMember(x => x.ArticleOptionValueWeightFactor, opt => opt.MapFrom(s => s.WeightFactor))
          .ForMember(x => x.ArticleOptionValueEmbossZoneAddition, opt => opt.MapFrom(s => s.EmbossZoneAddition))
          .ForMember(x => x.ArticleOptionValueIsDefault, opt => opt.MapFrom(s => s.IsDefault))
          .ForMember(x => x.Code, opt => opt.MapFrom(s => s.OptionValue == null ? "" : s.OptionValue.Code))
          .ForMember(x => x.OriginalValue, opt => opt.MapFrom(s => s.OptionValue.Value == null ? "" : s.OptionValue.Value))
          .ForMember(x => x.Value,
                        x => x.MapFrom(z =>
                            z.OptionValue.Translations.FirstOrDefault(x => x.LanguageCode == CultureInfo.CurrentCulture.Name) == null
                            ? z.OptionValue.Value
                            : z.OptionValue.Translations.FirstOrDefault(x => x.LanguageCode == CultureInfo.CurrentCulture.Name).Value));


                








            CreateMap<ListResult<ArticleOptionValue>, ListResult<GetAllValuesForArticleOptionResponse>>();

        }
    }
}
