using AutoMapper;
using EMessa.DAL.Entities.ArticleOptions;
using EMessa.Core.Interfaces;
using EMessa.Core.Localizer;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using EMessa.DAL.Data;
using EMessa.Core.Common.Results;

namespace EMessa.Core.Features.ArticleOptionValues.Commands.AddValueToArticleOptionCommand
{
    public class AddValueToArticleOptionCommand(int articleId, int optionId, int optionValueId) : IRequest<IResult>
    {
        public int ArticleId { get; init; } = articleId;
        public int OptionId { get; init; } = optionId;
        public int OptionValueId { get; init; } = optionValueId;
    }
    
    public class AddValueToArticleCommandHandler(
        IDbContextFactory<ApplicationDbContext> dbContextFactory,
        IMapper mapper,
        ILogger<AddValueToArticleCommandHandler> logger)
        : IRequestHandler<AddValueToArticleOptionCommand, IResult>
    {
        private readonly IMapper _mapper = mapper;

        public async Task<IResult> Handle(AddValueToArticleOptionCommand request, CancellationToken cancellationToken)
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);
            await using var transaction = await dbContext.Database.BeginTransactionAsync(cancellationToken);
            try
            {
                var article = dbContext.Articles.AsTracking().SingleOrDefault(x => x.Id == request.ArticleId);
                if (article == null)
                {
                    const string message = "Nie znaleziono produktu.";
                    logger.LogError(message);
                    return await Result.FailAsync(message.Tr());
                }

                var optionValue = dbContext.OptionValues
                    .Include(optionValue => optionValue.Option)
                    .SingleOrDefault(x => x.Id == request.OptionValueId);
                if (optionValue == null)
                {
                    const string message = "Nie znaleziono wartości opcji.";
                    logger.LogError(message);
                    return await Result.FailAsync(message.Tr());
                }

                var articleOption = await dbContext.ArticleOptions.SingleOrDefaultAsync(
                    x => x.Article.Id == article.Id && x.OptionId == request.OptionId,
                    cancellationToken: cancellationToken);
                if (articleOption == null)
                {
                    articleOption= new ArticleOption()
                    {
                        ArticleId = article.Id,
                        OptionId = request.OptionId,
                        UseDefaultValue = optionValue.Option.UseDefaultValue,
                    };

                    dbContext.ArticleOptions.Add(articleOption);
                    await dbContext.SaveChangesAsync(cancellationToken);
                }

                var articleOptionValue = dbContext.ArticleOptionValues
                    .SingleOrDefault(x => x.OptionValueId == optionValue.Id && x.ArticleOptionId == articleOption.Id);
                if (articleOptionValue != null)
                {
                    return await Result.FailAsync("Wybrana wartość opcji jest już dodana do produktu.".Tr());
                }

                var newArticleOptionValue = new ArticleOptionValue
                {
                    ArticleOptionId = articleOption.Id,
                    OptionValueId = optionValue.Id,
                    EmbossZoneAddition = optionValue.EmbossZoneAddition,
                    WeightFactor = optionValue.WeightFactor,
                    IsDefault = optionValue.IsDefault,
                };

                dbContext.ArticleOptionValues.Add(newArticleOptionValue);
                await dbContext.SaveChangesAsync(cancellationToken);

                await transaction.CommitAsync(cancellationToken);
                
                return await Result.SuccessAsync();
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync(cancellationToken);
                logger.LogError(ex, ex.Message);
                return await Result.FailAsync("Nieokreślony błąd aplikacji.".Tr());
            }
        }
    }
}
