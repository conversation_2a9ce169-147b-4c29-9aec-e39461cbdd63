using AutoMapper;
using EMessa.Core.Features.ArticleOptionValues.Queries.GetValuesForArticleOption;
using EMessa.Core.Localizer;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.Core.Common.Results;

namespace EMessa.Core.Features.ArticleOptionValues.Commands.EditArticleOptionValue;

public class EditArticleOptionValueCommand(GetAllValuesForArticleOptionResponse request) : IRequest<IResult>
{
    public GetAllValuesForArticleOptionResponse Request { get; set; } = request;
}

public class EditArticleOptionValueCommandHandler(
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ILogger<EditArticleOptionValueCommandHandler> logger)
    : IRequestHandler<EditArticleOptionValueCommand, IResult>
{
    public async Task<IResult> Handle(EditArticleOptionValueCommand request, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);
            await using var transaction = await dbContext.Database.BeginTransactionAsync(cancellationToken);

            var articleOptionValue = await dbContext.ArticleOptionValues
                .SingleOrDefaultAsync(x => x.Id == request.Request.ArticleOptionValueId,
                    cancellationToken: cancellationToken);
            if (articleOptionValue == null)
            {
                logger.LogError(
                    "Bład aktualizacji wartości opcji produktu, nie znaleziono wartości opcji dla produktu.");
                return await Result.FailAsync("Nie znaleziono wartości opcji dla produktu.".Tr());
            }

            articleOptionValue.WeightFactor = request.Request.ArticleOptionValueWeightFactor;
            articleOptionValue.EmbossZoneAddition = request.Request.ArticleOptionValueEmbossZoneAddition;
            articleOptionValue.IsDefault = request.Request.ArticleOptionValueIsDefault;
            dbContext.ArticleOptionValues.Update(articleOptionValue);
            await dbContext.SaveChangesAsync(cancellationToken);

            await transaction.CommitAsync(cancellationToken);

            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result.FailAsync("Nieokreślony błąd operacji.".Tr());
        }
    }
}

