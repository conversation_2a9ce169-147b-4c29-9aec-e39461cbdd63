using EMessa.Core.Common;
using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Configurations;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Sieve.Models;

namespace EMessa.Core.Features.Configurations.Queries.GetAll;

public class GetAllConfigurationsQuery : SieveGetAllQueryBase<GetAllConfigurationsResponse>
{
    public GetAllConfigurationsQuery(SieveModel sieveModel) : base(sieveModel)
    { }

    public GetAllConfigurationsQuery() : base()
    { }
}

public class GetAllConfigurationsQueryHandler(
    ISieveService sieveService,
    IDbContextFactory<ApplicationDbContext> dbFactory)
    : IRequestHandler<GetAllConfigurationsQuery, ListResult<GetAllConfigurationsResponse>>
{
    public async Task<ListResult<GetAllConfigurationsResponse>> Handle(GetAllConfigurationsQuery request, CancellationToken cancellationToken)
    {
        await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

        var configurations = dbContext
            .Configurations
            .AsNoTracking()
            .AsQueryable();

        var response = await sieveService
            .ExecuteSieveAsync<Configuration, GetAllConfigurationsResponse>(
                request.SieveModel,
                configurations,
                cancellationToken);

        return response;
    }
}