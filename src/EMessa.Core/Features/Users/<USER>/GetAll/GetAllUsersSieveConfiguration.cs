using EMessa.Core.Models;
using Microsoft.IdentityModel.Tokens;
using Sieve.Services;

namespace EMessa.Core.Features.Users.Queries.GetAllSieve;

public class GetAllUsersSieveConfiguration : ISieveConfiguration
{
    public void Configure(SievePropertyMapper mapper)
    {
        mapper.Property<ApplicationUserProfileWithRoles>(x => x.Id).CanFilter().CanSort();
        mapper.Property<ApplicationUserProfileWithRoles>(x => x.FirstName).CanFilter().CanSort();
        mapper.Property<ApplicationUserProfileWithRoles>(x => x.LastName).CanFilter().CanSort();
        mapper.Property<ApplicationUserProfileWithRoles>(x => x.Email).CanFilter().CanSort();
        mapper.Property<ApplicationUserProfileWithRoles>(x => x.Customer.ShortName).HasName("CustomerShortName").CanFilter().CanSort();
        mapper.Property<ApplicationUserProfileWithRoles>(x => x.Customer.Name).HasName("CustomerShortName").CanFilter().CanSort();
        mapper.Property<ApplicationUserProfileWithRoles>(x => x.Customer.Branch.Id).HasName("CustomerBranchId").CanFilter().CanSort();
        mapper.Property<ApplicationUserProfileWithRoles>(x => x.Customer.Branch.Name).HasName("CustomerBranchName").CanFilter().CanSort();
        mapper.Property<ApplicationUserProfileWithRoles>(x => x.UserBranches).CanFilter().CanSort();
        mapper.Property<ApplicationUserProfileWithRoles>(x => x.FactoryId).CanFilter().CanSort();
        mapper.Property<ApplicationUserProfileWithRoles>(x => x.Factory!.Name).HasName("FactoryName").CanFilter().CanSort();
        mapper.Property<ApplicationUserProfileWithRoles>(x => x.Active).CanFilter().CanSort();
        mapper.Property<ApplicationUserProfileWithRoles>(x => x.SmsNotifications).CanFilter().CanSort();
        mapper.Property<ApplicationUserProfileWithRoles>(x => x.EmailNotifications).CanFilter().CanSort();
        mapper.Property<ApplicationUserProfileWithRoles>(x => x.UserType).CanFilter().CanSort();
    }

    public static IQueryable<ApplicationUserProfileWithRoles> GlobalSearchFilter(
        IQueryable<ApplicationUserProfileWithRoles> source, string op, string[] values)
    {
        var value = values.First();

        if (value.IsNullOrEmpty())
            return source;

        return op switch
        {
            "@=" => source.Where(x =>
               x.Id.ToString().Contains(value)
            || x.FirstName.Contains(value)
            || x.LastName.Contains(value)
            || x.Email.Contains(value)
            || x.Customer.ShortName.Contains(value)
            || x.Customer.Name.Contains(value)
            || (x.Factory != null && x.Factory.Name.Contains(value))
            || x.UserBranches.Any(b =>
                   b.Branch.Name.Contains(value))
            || x.UserLocalizations.Any(l =>
                   l.CustomerLocalization.Name.Contains(value))
            || x.Roles.Any(r =>
                   (r.Role.Name != null && r.Role.Name.Contains(value)))),
            _ => source
        };
    }

    public static IQueryable<ApplicationUserProfileWithRoles> LocationsFilter(
        IQueryable<ApplicationUserProfileWithRoles> source, string op, string[] values)
    {
        var value = values.First();

        if (value.IsNullOrEmpty())
            return source;

        return op switch
        {
            "@=" => source.Where(x =>
                x.UserLocalizations.Any(l =>
                   (l.CustomerLocalization != null && l.CustomerLocalization.Name.Contains(value)))),
            _ => source
        };
    }

    public static IQueryable<ApplicationUserProfileWithRoles> BranchesFilter(
        IQueryable<ApplicationUserProfileWithRoles> source, string op, string[] values)
    {
        var value = values.First();

        if (value.IsNullOrEmpty())
            return source;

        return op switch
        {
            "@=" => source.Where(x =>
                x.UserBranches.Any(ub =>
                   (ub.Branch.Name != null && ub.Branch.Name.Contains(value)))),
            _ => source
        };
    }

    public static IQueryable<ApplicationUserProfileWithRoles> RolesFilter(
        IQueryable<ApplicationUserProfileWithRoles> source, string op, string[] values)
    {
        var value = values.First();

        if (value.IsNullOrEmpty())
            return source;

        return op switch
        {
            "==" => source.Where(x =>
                x.Roles.Any(r =>
                   (r.Role.Name != null && r.Role.Name == value))),
            _ => source
        };
    }
}