using EMessa.Base.Constants;
using EMessa.Base.Enums;
using EMessa.Core.Common;
using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.Core.Models;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Sieve.Models;

namespace EMessa.Core.Features.Users.Queries.GetAll;

public class GetAllUsersQuery : SieveGetAllQueryBase<GetAllUsersResponse>
{
    public GetAllUsersQuery(SieveModel sieveModel) : base(sieveModel)
    { }

    public GetAllUsersQuery() : base()
    { }
}

public class GetAllUsersQueryHandler(
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IAppStateService appStateService,
    ISieveService sieveService)
    : IRequestHandler<GetAllUsersQuery, ListResult<GetAllUsersResponse>>
{
    public async Task<ListResult<GetAllUsersResponse>> Handle(GetAllUsersQuery request,
        CancellationToken cancellationToken)
    {
        var loggedUser = appStateService.UserData;

        if (loggedUser.MaxRole == Role.Client)
        {
            return ListResult<GetAllUsersResponse>.Failure(ResultErrorMessages.Unauthorized);
        }

        await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

        var query = dbContext.UserProfiles
            .Include(p => p.Customer).ThenInclude(p => p.Branch)
            .Include(p => p.Factory)
            .Include(x => x.UserLocalizations).ThenInclude(x => x.CustomerLocalization)
            .Include(p => p.UserBranches).ThenInclude(p => p.Branch)
            .Join(dbContext.Users.Include(u => u.UserRoles).ThenInclude(ur => ur.Role),
                profile => profile.UserName,
                user => user.UserName,
                (profile, user) => new ApplicationUserProfileWithRoles
                {
                    Id = profile.Id,
                    UserName = profile.UserName,
                    Email = profile.Email,
                    CreatedDate = profile.CreatedDate,
                    ModifiedDate = profile.ModifiedDate,
                    Active = profile.Active,
                    FirstName = profile.FirstName,
                    LastName = profile.LastName,
                    FirstPhoneNumber = profile.FirstPhoneNumber,
                    SecondPhoneNumber = profile.SecondPhoneNumber,
                    UserType = profile.UserType,
                    XlLogin = profile.XlLogin,
                    XlPassword = profile.XlPassword,
                    VatPl = profile.VatPl,
                    VatOutsidePl = profile.VatOutsidePl,
                    RegulationsAccepted = profile.RegulationsAccepted,
                    GeneralSaleTermsAccepted = profile.GeneralSaleTermsAccepted,
                    FirstConfiguration = profile.FirstConfiguration,
                    EmailNotifications = profile.EmailNotifications,
                    SmsNotifications = profile.SmsNotifications,
                    NotificationsLang = profile.NotificationsLang,
                    LastLoginDate = profile.LastLoginDate,
                    LastLoginIp = profile.LastLoginIp,
                    FactoryId = profile.FactoryId,
                    Factory = profile.Factory,
                    CustomerId = profile.CustomerId,
                    Customer = profile.Customer,
                    CanRepresentCustomer = profile.CanRepresentCustomer,
                    CustomerUsers = profile.CustomerUsers,
                    UserBranches = profile.UserBranches,
                    UserLocalizations = profile.UserLocalizations,
                    Roles = user.UserRoles
                })
            .AsQueryable();

        if (loggedUser.MaxRole != Role.Administrator)
        {
            // nie-admin nie widzi adminów i samego siebie
            query = query.Where(x => !x.Roles.Any(ur => ur.Role.Name == Role.Administrator) && x.Id != loggedUser.UserProfileId);

            if (loggedUser.Roles.Contains(Role.Production) &&
                (loggedUser.Roles.Contains(Role.TradeManager) || loggedUser.Roles.Contains(Role.Trade)))
            {
                query = query.Where(x =>
                    x.UserType == UserTypes.Employee && x.FactoryId == loggedUser.FactoryId ||
                    x.UserType == UserTypes.Client && x.Customer.FactoryId == loggedUser.FactoryId ||
                    x.UserType == UserTypes.Employee && x.UserBranches.Any(y => loggedUser.BranchIds.Contains(y.BranchId)) ||
                    x.UserType == UserTypes.Client && loggedUser.BranchIds.Contains(x.Customer.BranchId));
            }
            else if (loggedUser.Roles.Contains(Role.Production))
            {
                query = query.Where(x =>
                    x.UserType == UserTypes.Employee && x.FactoryId == loggedUser.FactoryId ||
                    x.UserType == UserTypes.Client && x.Customer.FactoryId == loggedUser.FactoryId);
            }
            else if (loggedUser.Roles.Contains(Role.TradeManager) || loggedUser.Roles.Contains(Role.Trade))
            {
                query = query.Where(x =>
                    x.UserType == UserTypes.Employee && x.UserBranches.Any(y => loggedUser.BranchIds.Contains(y.BranchId)) ||
                    x.UserType == UserTypes.Client && loggedUser.BranchIds.Contains(x.Customer.BranchId));
            }
            else if (loggedUser.Roles.Contains(Role.ClientManager))
            {
                query = query.Where(x =>
                    x.UserType == UserTypes.Client && x.CustomerId == loggedUser.CustomerId);
            }
            else
            {
                return new([]);
            }
        }

        var response = await sieveService
            .ExecuteSieveAsync<ApplicationUserProfileWithRoles, GetAllUsersResponse>(
                request.SieveModel,
                query,
                cancellationToken);

        return response;
    }
}
