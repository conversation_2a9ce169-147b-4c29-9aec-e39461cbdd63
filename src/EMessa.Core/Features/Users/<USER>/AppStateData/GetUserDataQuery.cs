using EMessa.Base.Constants;
using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Users.Queries.AppStateData;

public record GetUserDataQuery(string UserName) : IRequest<IResult<GetUserDataResult>>;

public class GetUserDataQueryHandler(
    ILogger<GetUserDataQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory)
    : IRequestHandler<GetUserDataQuery, IResult<GetUserDataResult>>
{
    public async Task<IResult<GetUserDataResult>> Handle(GetUserDataQuery request, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var user = dbContext.Users
                .Where(x => x.UserName == request.UserName)
                .Include(x => x.UserRoles).ThenInclude(x => x.Role)
                .Single();

            var userProfile = dbContext.UserProfiles
                .Include(x => x.UserBranches)
                .Include(x => x.Customer)
                .Single(x => x.UserName == request.UserName);

            var userRoles = user.UserRoles
                .Where(ur => ur.Role.Name != null)
                .Select(ur => ur.Role.Name!)
                .ToList();
            
            // var branchIds = userProfile.UserBranches.Count > 0
            //     ? userProfile.UserBranches.Select(x => x.BranchId).ToList()
            //     : [userProfile.Customer.BranchId];
            var branchIds = IsOnlyClientOrClientManagerRole(userRoles)
                ? [userProfile.Customer.BranchId]
                : userProfile.UserBranches.Select(x => x.BranchId).ToList();
            
            var result = new GetUserDataResult(
                user.Id,
                userProfile.Id,
                userProfile.FirstName,
                userProfile.LastName,
                userProfile.CustomerId,
                userProfile.FirstConfiguration,
                userRoles,
                branchIds,
                userProfile.FactoryId,
                userProfile.Email
            );

            return await Result<GetUserDataResult>.SuccessAsync(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result<GetUserDataResult>.FailAsync("Błąd pobrania danych użytkownika.".Tr());
        }
    }

    private bool IsOnlyClientOrClientManagerRole(List<string> userRoles)
    {
        var maxRole = Role.MaxRole(userRoles);
        return maxRole is Role.Client or Role.ClientManager;
    }
}