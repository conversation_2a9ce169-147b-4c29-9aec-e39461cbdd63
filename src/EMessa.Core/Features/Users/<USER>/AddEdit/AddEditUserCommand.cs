using AutoMapper;
using EMessa.Base.Constants;
using EMessa.Base.Enums;
using EMessa.Core.Common.Results;
using EMessa.Core.Features.AppState;
using EMessa.Core.Features.Customers.Commands.AddEdit;
using EMessa.Core.Features.Users.Notifications.NewUserAdded;
using EMessa.Core.Helpers;
using EMessa.Core.Interfaces;
using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Identity;
using EMessa.DAL.Entities.Users;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Users.Commands.AddEdit;

public record AddEditUserCommand(AddEditUserRequest Data) : IRequest<IResult<int>>;

public class AddEditUserCommandHandler(
    ILogger<AddEditUserCommandHandler> logger,
    IMapper mapper,
    UserManager<ApplicationUser> userManager,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IMediator mediator,
    IAppStateService appStateService)
    : IRequestHandler<AddEditUserCommand, IResult<int>>
{
    public async Task<IResult<int>> Handle(AddEditUserCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var validationResult = await new AddEditUserValidator(dbContextFactory, logger)
            .ValidateAsync(request.Data, cancellationToken);

            if (!validationResult.IsValid)
                return await Result<int>.FailAsync(validationResult);

            var requestUser = request.Data;

            if (!await ValidateUserAddEditAccess(requestUser))
            {
                return await Result<int>.FailAsync(ResultErrorMessages.Unauthorized);
            }

            var loggedUser = appStateService.UserData;

            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var requestUserRoles = await dbContext.Roles
                .Where(x => requestUser.Roles.Contains(x.Id))
                .Select(x => x.Name)
                .ToListAsync(cancellationToken);

            var customer = await dbContext.Customers.SingleAsync(
                x => x.Id == requestUser.CustomerId, cancellationToken
            );
            var customerIsAppOwner = customer.IsAppOwner;

            var userProfile = await dbContext.UserProfiles
                .Include(x => x.UserBranches)
                .SingleOrDefaultAsync(p => p.Id == requestUser.Id, cancellationToken);

            var originalProfileUserName = userProfile?.UserName;

            var error = await ValidateEmailAndSetUserName(
                dbContext, loggedUser, userProfile, requestUser, cancellationToken);
            if (error != null)
            {
                return await Result<int>.FailAsync(error);
            }

            error = ValidateUserType(requestUser, requestUserRoles, customerIsAppOwner);
            if (error != null)
            {
                return await Result<int>.FailAsync(error);
            }

            if (userProfile == null)
            {
                userProfile = await AddApplicationUserProfile(
                    dbContext, cancellationToken, requestUser, requestUserRoles);
            }
            else
            {
                userProfile = await EditApplicationUserProfile(
                    dbContext, cancellationToken, userProfile, requestUser, requestUserRoles, loggedUser.HasAdminRole);
            }

            List<string>? errors;
            var user = originalProfileUserName != null
                ? await userManager.FindByNameAsync(originalProfileUserName) // Find by original UserName
                : null;
            if (user == null)
            {
                errors = await AddApplicationUser(requestUser, requestUserRoles!);
            }
            else
            {
                errors = await EditApplicationUser(user, userProfile, requestUserRoles);
            }

            if (errors != null)
            {
                return await Result<int>.FailAsync(errors);
            }

            return await Result<int>.SuccessAsync(userProfile.Id);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result<int>.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }

    private async Task<string?> ValidateEmailAndSetUserName(
        ApplicationDbContext dbContext,
        UserData loggedUser,
        ApplicationUserProfile? userProfile,
        AddEditUserRequest requestUser,
        CancellationToken cancellationToken)
    {
        if (userProfile == null)
        {
            var byUserNameExits = await dbContext.UserProfiles.AnyAsync(
                x => x.UserName == requestUser.Email, cancellationToken);

            return byUserNameExits
                ? "W systemie istnieje już użytkownik korzystający z e-mail '{0}'.".Tr(requestUser.Email)
                : null;
        }

        if (userProfile.Email == requestUser.Email) return null;

        if (!loggedUser.HasAdminRole) // NOTE customerIsAppOwner also?
        {
            logger.LogWarning(
                "Brak uprawnień do edycji e-maila użytkownika z {OldEmail} na {NewEmail} przez {LoggedUserSignature}",
                userProfile.Email,
                requestUser.Email,
                GetLoggedUserSignature());
            return "Brak uprawnień do edycji e-maila.".Tr();
        }

        var byUserNameExists = await dbContext.UserProfiles.AnyAsync(
            x => x.UserName == requestUser.Email && x.Id != requestUser.Id,
            cancellationToken);
        if (byUserNameExists)
        {
            return "W systemie istnieje już użytkownik korzystający z e-mail '{0}'.".Tr(requestUser.Email);
        }

        // Ok, zmiana e-maila
        requestUser.UserName = requestUser.Email;

        // Kto zmienił e-mail?
        logger.LogWarning(
            "Zmiana e-maila użytkownika z {OldEmail} na {NewEmail} przez {LoggedUserSignature}",
            userProfile.Email,
            requestUser.Email,
            GetLoggedUserSignature());

        return null;
    }

    private static string? ValidateUserType(
        AddEditUserRequest requestUser, List<string?> requestUserRoles, bool customerIsAppOwner)
    {
        switch (requestUser.UserType)
        {
            case UserTypes.Client:
                {
                    if (!requestUserRoles.All(x => new[] { Role.Client, Role.ClientManager }.Contains(x)))
                    {
                        return "Niewłaściwe przypisanie ról.".Tr();
                    }

                    if (customerIsAppOwner)
                    {
                        return "Klient nie może być przypisany do firmy będącej właścicielem aplikacji.".Tr();
                    }

                    requestUser.XlLogin = null;
                    requestUser.XlPassword = null;
                    break;
                }
            case UserTypes.Employee:
                {
                    if (!requestUserRoles.All(x => new[] { Role.Administrator, Role.TradeManager, Role.Trade, Role.Production, Role.SaleManager }.Contains(x)))
                    {
                        return "Niewłaściwe przypisanie ról.".Tr();
                    }

                    if (!customerIsAppOwner)
                    {
                        return "Pracownik musi być przypisany do firmy będącej właścicielem aplikacji.".Tr();
                    }

                    if (requestUserRoles.Any(x => x == Role.Trade))
                    {
                        if (requestUser.BranchIds.Count() > 1)
                        {
                            return "Pracownik w roli Handel może być przypisany tylko do jednego oddziału.".Tr();
                        }
                    }

                    break;
                }
        }

        return null;
    }

    private async Task<ApplicationUserProfile> AddApplicationUserProfile(
        ApplicationDbContext dbContext,
        CancellationToken cancellationToken,
        AddEditUserRequest requestUser,
        List<string?> requestUserRoles
    )
    {
        var userProfile = mapper.Map<ApplicationUserProfile>(requestUser);
        userProfile.UserName = userProfile.Email;

        // jeśli user nie ma roli produkcja to skasować id zakładu
        if (requestUserRoles.Any(x => x == Role.Production))
        {
            userProfile.FactoryId = requestUser.FactoryId;
        }
        else
        {
            userProfile.FactoryId = null;
        }

        await dbContext.UserProfiles.AddAsync(userProfile, cancellationToken);
        await dbContext.SaveChangesAsync(cancellationToken);

        AddUserBranches(dbContext, userProfile.Id, [.. requestUser.BranchIds]);
        AddUserLocations(dbContext, userProfile.Id, requestUser.LocationIds);
        await dbContext.SaveChangesAsync(cancellationToken);

        return userProfile;
    }

    private async Task<ApplicationUserProfile> EditApplicationUserProfile(
        ApplicationDbContext dbContext,
        CancellationToken cancellationToken,
        ApplicationUserProfile userProfile,
        AddEditUserRequest requestUser,
        List<string?> requestUserRoles,
        bool hasAdminRole)
    {
        // ValidateEmailAndSetUserName sprawdza czy e-mail jest unikalny oraz zmienia UserName na Email jeśli zmieniono e-mail i są uprawnienia

        var firstConfiguration = userProfile.FirstConfiguration;
        var userType = userProfile.UserType;

        userProfile = mapper.Map<ApplicationUserProfile>(requestUser);
        userProfile.FirstConfiguration = firstConfiguration;
        userProfile.UserType =
            hasAdminRole
                ? requestUser.UserType
                : userType; // blokada zmiany typu użytkownika dla użytkowników bez roli administratora

        // jeśli user nie ma roli produkcja to skasować id zakładu
        userProfile.FactoryId = requestUserRoles.Any(x => x == Role.Production)
            ? requestUser.FactoryId
            : null;

        var removeLocalizationsRange = await dbContext.UserCustomerLocalizations
            .Where(x => x.UserProfileId == userProfile.Id)
            .ToListAsync(cancellationToken: cancellationToken);
        dbContext.UserCustomerLocalizations.RemoveRange(removeLocalizationsRange);

        AddUserLocations(dbContext, userProfile.Id, requestUser.LocationIds);

        if (hasAdminRole)
        {
            var removeBranchesRange = await dbContext.UserBranches
                .Where(x => x.UserProfileId == userProfile.Id)
                .ToListAsync(cancellationToken: cancellationToken);
            dbContext.UserBranches.RemoveRange(removeBranchesRange);

            AddUserBranches(dbContext, userProfile.Id, [.. requestUser.BranchIds]);
        }

        dbContext.UserProfiles.Update(userProfile);
        await dbContext.SaveChangesAsync(cancellationToken);

        return userProfile;
    }

    private async Task<List<string>?> AddApplicationUser(
        AddEditUserRequest requestUser,
        List<string> requestUserRoles
    )
    {
        var user = new ApplicationUser()
        {
            UserName = requestUser.Email,
            Email = requestUser.Email
        };

        var password = PasswordHelper.GetRandomPassword();
        var result = await userManager.CreateAsync(user, password);
        if (!result.Succeeded)
        {
            var errors = result.Errors.Select(x => x.Description).ToList();
            return errors;
        }

        if (requestUserRoles.Count != 0)
        {
            await userManager.AddToRolesAsync(user, requestUserRoles);
        }

        // 
        // var code = await _userManager.GeneratePasswordResetTokenAsync(user);
        // code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));

        await mediator.Publish(new NewUserAddedNotification(user.UserName));

        return null;
    }

    private async Task<List<string>?> EditApplicationUser(
        ApplicationUser user,
        ApplicationUserProfile userProfile,
        List<string?> requestUserRoles
    )
    {
        // ValidateEmailAndSetUserName sprawdza czy e-mail jest unikalny oraz zmienia UserName na Email jeśli zmieniono e-mail i są uprawnienia

        var isEmailChanged = false;
        if (user.Email != userProfile.Email)
        {
            var password = PasswordHelper.GetRandomPassword();
            password = userManager.PasswordHasher.HashPassword(user, password);

            user.UserName = userProfile.Email;
            user.Email = userProfile.Email;
            user.EmailConfirmed = false;
            user.PasswordHash = password;

            // Wysłać powiadomienie o zmianie e-maila
            isEmailChanged = true;
        }

        var result = await userManager.UpdateAsync(user);

        if (!result.Succeeded)
        {
            var errors = result.Errors.Select(x => x.Description).ToList();
            return errors;
        }

        if (isEmailChanged)
        {
            await mediator.Publish(new NewUserAddedNotification(user.UserName!));
        }

        var userRoles = await userManager.GetRolesAsync(user);
        await userManager.RemoveFromRolesAsync(user, userRoles);

        if (requestUserRoles.Count != 0)
        {
            await userManager.AddToRolesAsync(user, requestUserRoles!);
        }

        return null;
    }

    /// <summary>
    /// Kontrola tworzenia i edycji użytkownika na podstawie maksymalnej roli zalogowanego użytkownika
    /// </summary>
    /// <param name="requestUser">Obiekt żądania zawierający dane użytkownika do edycji</param>
    /// <returns>True, jeśli edycja jest dozwolona; w przeciwnym razie false</returns>
    private async Task<bool> ValidateUserAddEditAccess(AddEditUserRequest requestUser)
    {
        var maxRole = appStateService.UserData.MaxRole;

        if (requestUser.UserType == UserTypes.Employee) // pracowników może tworzyć/edytować tylko administrator
            return maxRole == Role.Administrator;

        return maxRole switch
        {
            Role.Administrator => true,
            Role.TradeManager => await ValidateCustomerAccess(requestUser.CustomerId),
            Role.Trade => await ValidateCustomerAccess(requestUser.CustomerId),
            Role.Production => true,
            Role.ClientManager => false,
            Role.Client => false,
            _ => false
        };
    }

    /// <summary>
    /// Sprawdza, czy zalogowany użytkownik ma dostęp do edycji danych klienta
    /// </summary>
    /// <param name="customerId">Identyfikator klienta do sprawdzenia</param>
    /// <returns>
    /// True, jeśli użytkownik ma dostęp do edycji danych klienta;
    /// False, jeśli użytkownik nie ma dostępu lub klient nie istnieje
    /// </returns>
    /// <remarks>
    /// Metoda sprawdza, czy klient o podanym ID istnieje i czy zalogowany użytkownik
    /// ma dostęp do oddziału, do którego przypisany jest klient.
    /// </remarks>
    private async Task<bool> ValidateCustomerAccess(int? customerId)
    {
        if (!customerId.HasValue) return false;
        await using var db = await dbContextFactory.CreateDbContextAsync();
        var customer = await db.Customers.SingleOrDefaultAsync(x => x.Id == customerId);
        return customer != null && appStateService.UserData.BranchIds.Contains(customer.BranchId);
    }

    private string GetLoggedUserSignature()
    {
        var loggedUser = appStateService.UserData;
        return $"({loggedUser.UserProfileId} {loggedUser.UserFirstName} {loggedUser.UserLastName})";
    }

    private void AddUserBranches(ApplicationDbContext dbContext, int userProfileId, int[] branchIds)
    {
        if (branchIds != null && branchIds.Length > 0)
        {
            dbContext.UserBranches.AddRange(branchIds.Select(x => new UserBranch()
            {
                UserProfileId = userProfileId,
                BranchId = x
            }));
        }
    }
    private void AddUserLocations(ApplicationDbContext dbContext, int userProfileId, IEnumerable<int> locationIds)
    {
        if (locationIds != null && locationIds.Count() > 0)
        {
            dbContext.UserCustomerLocalizations.AddRange(locationIds.Select(x =>
                new UserCustomerLocalization()
                {
                    UserProfileId = userProfileId,
                    CustomerLocalizationId = x
                }));
        }
    }

}
