using EMessa.Core.Common;
using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Pages;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Sieve.Models;

namespace EMessa.Core.Features.Pages.Queries.GetAll;

public class GetAllPagesQuery : SieveGetAllQueryBase<GetAllPagesResponse>
{
    public GetAllPagesQuery(SieveModel sieveModel) : base(sieveModel)
    { }

    public GetAllPagesQuery() : base()
    { }
}

public class GetAllPagesQueryHandler(
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ISieveService sieveService)
    : IRequestHandler<GetAllPagesQuery, ListResult<GetAllPagesResponse>>
{
    public async Task<ListResult<GetAllPagesResponse>> Handle(GetAllPagesQuery request, CancellationToken cancellationToken)
    {
        await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

        var pages = dbContext
            .Pages
            .Include(x => x.CreatedBy)
            .Include(x => x.EditedBy)
            .Include(x => x.Translations)
            .AsNoTracking()
            .AsQueryable();

        var response = await sieveService
            .ExecuteSieveAsync<Page, GetAllPagesResponse>(
                request.SieveModel,
                pages,
                cancellationToken);

        return response;
    }
}
