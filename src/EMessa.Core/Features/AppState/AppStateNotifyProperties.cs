namespace EMessa.Core.Features.AppState;

public static class AppStateNotifyProperties
{
    public const string SetUserData = nameof(SetUserData);
    public const string UserLoggedIn = nameof(UserLoggedIn);
    public const string ScreenSizeChanged = nameof(ScreenSizeChanged);

    public const string OrdersStateChanged = nameof(OrdersStateChanged);
    public const string CategoryChanged = nameof(CategoryChanged);
    public const string CatalogVisibleChanged = nameof(CatalogVisibleChanged);

    public const string ShoppingCartChanged = nameof(ShoppingCartChanged);
    public const string OwnLockedOrderChanged = nameof(OwnLockedOrderChanged);
    public const string WatchedOrderChanged = nameof(WatchedOrderChanged);
}