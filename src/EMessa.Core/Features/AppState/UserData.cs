using EMessa.Base.Constants;

namespace EMessa.Core.Features.AppState;

public class UserData
{
    public UserData()
    {
        UserProfileId = 0;
        UserFirstName = "";
        UserLastName = "";
        Email = "";
    }

    public UserData(int userProfileId, string userFirstName, string userLastName, bool firstConfiguration, int? customerId, List<string> roles, List<int> branchIds, string email)
    {
        UserProfileId = userProfileId;
        FirstConfiguration = firstConfiguration;
        CustomerId = customerId;
        UserFirstName = userFirstName;
        UserLastName = userLastName;
        Roles = roles;
        Email = email;
        BranchIds = [.. branchIds];
    }
    
    public void SetUserData(
        int userProfileId, 
        string firstName, 
        string lastName, 
        bool firstConfiguration,
        int? customerId, 
        List<string> roles, 
        List<int> branchIds, 
        int? factoryId, 
        string email)
    {
        UserProfileId = userProfileId;
        FirstConfiguration = firstConfiguration;
        CustomerId = customerId;
        UserFirstName = firstName;
        UserLastName = lastName;
        Roles = roles;
        BranchIds = [.. branchIds];
        FactoryId = factoryId;
        Email = email;
    }

    public string Initials => $"{UserFirstName.FirstOrDefault()}{UserLastName.FirstOrDefault()}";
    public int UserProfileId { get; protected set; }
    public bool FirstConfiguration { get; protected set; }
    public int? CustomerId { get; protected set; }
    public string UserFirstName { get; protected set; }
    public string UserLastName { get; protected set; }
    public string Email { get; protected set; }
    public List<int> BranchIds { get; protected set; } = [];
    public int? FactoryId { get; set; }

    public List<string> Roles { get; set; } = [];
    public string MaxRole => Role.MaxRole(Roles);
    public bool HasAnyRole(params string[] roles) => roles.Any(role => Roles.Contains(role));
    public bool IsMaxRoleClient => MaxRole == Role.Client;
    public bool HasAdminRole => Roles.Contains(Role.Administrator);
    public bool HasProductionRole => Roles.Contains(Role.Production);
}
