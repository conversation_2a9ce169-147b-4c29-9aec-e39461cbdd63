using EMessa.DAL.Entities.Options;

namespace EMessa.Core.Features.ArticleOptionValuesRestrictions.Commands.GenerateOptionsRestrictionsForArticle;

public class ArticleOptionValuesRestrictionComparer : IEqualityComparer<ArticleOptionValuesRestriction>
{
    public bool Equals(ArticleOptionValuesRestriction? x, ArticleOptionValuesRestriction? y)
    {
        if (x == null || y == null) return false;

        return x.ArticleId == y.ArticleId
            && x.CoatId == y.CoatId
            && x.ColorId == y.ColorId
            && x.ThickId == y.ThickId
            && x.ModuleId == y.ModuleId
            && x.FoilId == y.FoilId
            && x.FeltId == y.FeltId
            && x.EmbossHeightId == y.EmbossHeightId;
    }

    public int GetHashCode(ArticleOptionValuesRestriction x)
    {
        return HashCode.Combine(x.ArticleId, x.CoatId, x.ColorId, x.ThickId, x.<PERSON>leId, x.FoilId, x.FeltId, x.Em<PERSON>s<PERSON>eightId);
    }
}
