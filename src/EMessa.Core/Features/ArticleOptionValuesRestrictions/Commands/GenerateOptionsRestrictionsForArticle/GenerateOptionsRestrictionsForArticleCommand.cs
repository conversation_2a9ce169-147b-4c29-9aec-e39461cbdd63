using EMessa.Core.Common.Results;
using EMessa.Core.Features.ArticleOptionValuesRestrictions.Services;
using EMessa.Core.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.ArticleOptionValuesRestrictions.Commands.GenerateOptionsRestrictionsForArticle;

public record GenerateOptionsRestrictionsForArticleCommand(params int[] ArticleIds) : IRequest<IResult>;

public class GenerateOptionsRestrictionsForArticleCommandHandler(
    ILogger<GenerateOptionsRestrictionsForArticleCommandHandler> logger,
    IOptionRestrictionsService optionRestrictionsService)
    : IRequestHandler<GenerateOptionsRestrictionsForArticleCommand, IResult>
{
    public async Task<IResult> Handle(GenerateOptionsRestrictionsForArticleCommand request, CancellationToken cancellationToken)
    {
        if (request.ArticleIds == null || request.ArticleIds.Length == 0)
            return await Result.SuccessAsync();

        try
        {
            foreach (var articleId in request.ArticleIds)
            {
                var result = await optionRestrictionsService.GenerateRestrictions(articleId, cancellationToken);

                if (!result.Succeeded)
                {
                    logger.LogWarning("Failed to generate restrictions for article {ArticleId}: {Messages}", articleId, result.Messages);
                    return await Result.FailAsync(result.Messages);
                }
            }

            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}
