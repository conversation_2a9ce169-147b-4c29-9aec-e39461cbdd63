using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.ArticleOptionValuesRestrictions.Commands.SaveArticleRestrictionsActiveAndOnlyInSale;

public record SaveArticleRestrictionsActiveAndOnlyInSaleRequest(int RestrictionId, bool IsActive, bool OnlyInSale);

public record SaveArticleRestrictionsActiveAndOnlyInSaleCommand(params SaveArticleRestrictionsActiveAndOnlyInSaleRequest[] SaveArticleRestrictionsActiveAndOnlyInSaleRequests) : IRequest<IResult>;

public class SaveArticleRestrictionActiveAndOnlyInSaleCommandHandler(
    ILogger<SaveArticleRestrictionActiveAndOnlyInSaleCommandHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbFactory)
    : IRequestHandler<SaveArticleRestrictionsActiveAndOnlyInSaleCommand, IResult>
{
    public async Task<IResult> Handle(SaveArticleRestrictionsActiveAndOnlyInSaleCommand request, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

            var ids = request.SaveArticleRestrictionsActiveAndOnlyInSaleRequests.Select(x => x.RestrictionId).ToList();

            var restrictions = await dbContext.ArticleOptionValuesRestrictions
                .AsTracking()
                .Where(x => ids.Contains(x.Id))
                .ToListAsync(cancellationToken);

            if (restrictions.Count == 0)
                return Result.Fail(ResultErrorMessages.NotFound);

            foreach (var item in request.SaveArticleRestrictionsActiveAndOnlyInSaleRequests)
            {
                var existing = restrictions.First(r => r.Id == item.RestrictionId);
                existing.IsActive = item.IsActive;
                existing.OnlyInSale = item.OnlyInSale;
            }

            await dbContext.SaveChangesAsync(cancellationToken);
            return Result.Success();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}
