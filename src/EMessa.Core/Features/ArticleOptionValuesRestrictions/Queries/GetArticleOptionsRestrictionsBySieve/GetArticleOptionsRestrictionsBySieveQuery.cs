using AutoMapper;
using AutoMapper.QueryableExtensions;
using EMessa.Core.Common;
using EMessa.Core.Common.Results;
using EMessa.Core.Features.ArticleOptionValuesRestrictions.Queries.GetOptionsRestrictionsForEdit;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Sieve.Models;

namespace EMessa.Core.Features.ArticleOptionValuesRestrictions.Queries.GetArticleOptionsRestrictionsBySieve;

public class GetArticleOptionsRestrictionsBySieveQuery : SieveGetAllQueryBase<EditArticleOptionRestrictionResponse>
{
    public IEnumerable<int> ArticleIds { get; set; }
    public GetArticleOptionsRestrictionsBySieveQuery(SieveModel sieveModel, params int[] articleIds) : base(sieveModel)
    {
        ArticleIds = articleIds;
    }

    public GetArticleOptionsRestrictionsBySieveQuery(params int[] articleIds) : base()
    {
        ArticleIds = articleIds;
    }
}

public class GetArticleOptionsRestrictionsBySieveQueryHandler(
    ILogger<GetArticleOptionsRestrictionsBySieveQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ISieveService sieveService,
    IMapper mapper)
    : IRequestHandler<GetArticleOptionsRestrictionsBySieveQuery, ListResult<EditArticleOptionRestrictionResponse>>
{
    public async Task<ListResult<EditArticleOptionRestrictionResponse>> Handle(GetArticleOptionsRestrictionsBySieveQuery request, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var restrictionsQuery = dbContext.ArticleOptionValuesRestrictions
                    .Include(x => x.Article)
                    .Include(x => x.Coat)
                    .Include(x => x.Color)
                    .Include(x => x.Thick)
                    .Include(x => x.Module)
                    .Include(x => x.Foil)
                    .Include(x => x.Felt)
                    .Include(x => x.EmbossHeight)
                    .Where(x => request.ArticleIds.Contains(x.ArticleId))
                    .OrderBy(x => x.Coat!.Value)
                        .ThenBy(x => x.Color!.Value)
                        .ThenBy(x => x.Thick!.Value)
                        .ThenBy(x => x.Module!.Value)
                        .ThenBy(x => x.Foil!.Value)
                        .ThenBy(x => x.Felt!.Value)
                        .ThenBy(x => x.EmbossHeight!.Value)
                    .AsNoTracking()
                    .ProjectTo<EditArticleOptionRestrictionResponse>(mapper.ConfigurationProvider)
                    .AsQueryable();

            var response = await sieveService
            .ExecuteSieveAsync<EditArticleOptionRestrictionResponse, EditArticleOptionRestrictionResponse>(
                request.SieveModel,
                restrictionsQuery,
                cancellationToken);

            return response;

        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return ListResult<EditArticleOptionRestrictionResponse>.Failure(ResultErrorMessages.UnexpectedError);
        }
    }
}