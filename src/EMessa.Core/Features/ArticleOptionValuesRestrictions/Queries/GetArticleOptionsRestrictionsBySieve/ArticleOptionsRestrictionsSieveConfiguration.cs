using EMessa.Core.Features.ArticleOptionValuesRestrictions.Queries.GetOptionsRestrictionsForEdit;
using Microsoft.IdentityModel.Tokens;
using Sieve.Services;

namespace EMessa.Core.Features.ArticleOptionValuesRestrictions.Queries.GetArticleOptionsRestrictionsBySieve;

public class ArticleOptionsRestrictionsSieveConfiguration : ISieveConfiguration
{
    public void Configure(SievePropertyMapper mapper)
    {
        mapper.Property<EditArticleOptionRestrictionResponse>(x => x.Id).CanFilter().CanSort();
        mapper.Property<EditArticleOptionRestrictionResponse>(x => x.ArticleId).CanFilter().CanSort();
        mapper.Property<EditArticleOptionRestrictionResponse>(x => x.ArticleName).CanFilter().CanSort();
        mapper.Property<EditArticleOptionRestrictionResponse>(x => x.ArticleCode).CanFilter().CanSort();
        mapper.Property<EditArticleOptionRestrictionResponse>(x => x.Coat).CanFilter().CanSort();
        mapper.Property<EditArticleOptionRestrictionResponse>(x => x.Color).CanFilter().CanSort();
        mapper.Property<EditArticleOptionRestrictionResponse>(x => x.Thick).CanFilter().CanSort();
        mapper.Property<EditArticleOptionRestrictionResponse>(x => x.Module).CanFilter().CanSort();
        mapper.Property<EditArticleOptionRestrictionResponse>(x => x.Foil).CanFilter().CanSort();
        mapper.Property<EditArticleOptionRestrictionResponse>(x => x.Felt).CanFilter().CanSort();
        mapper.Property<EditArticleOptionRestrictionResponse>(x => x.EmbossHeight).CanFilter().CanSort();
        mapper.Property<EditArticleOptionRestrictionResponse>(x => x.IsActive).CanFilter().CanSort();
        mapper.Property<EditArticleOptionRestrictionResponse>(x => x.IsEdited).CanFilter().CanSort();
        mapper.Property<EditArticleOptionRestrictionResponse>(x => x.OnlyInSale).CanFilter().CanSort();
    }

    public static IQueryable<EditArticleOptionRestrictionResponse> GlobalSearchFilter(
        IQueryable<EditArticleOptionRestrictionResponse> source, string op, string[] values)
    {
        var value = values.First();

        if (value.IsNullOrEmpty())
            return source;

        return op switch
        {
            "@=" => source.Where(x =>
                   x.Id.ToString().Contains(value)
                || x.ArticleId.ToString().Contains(value)
                || x.ArticleName.Contains(value)
                || x.ArticleCode.Contains(value)
                || (x.Coat != null && x.Coat.Contains(value))
                || (x.Color != null && x.Color.Contains(value))
                || (x.Thick != null && x.Thick.Contains(value))
                || (x.Module != null && x.Module.Contains(value))
                || (x.Foil != null && x.Foil.Contains(value))
                || (x.Felt != null && x.Felt.Contains(value))
                || (x.EmbossHeight != null && x.EmbossHeight.Contains(value))),
            _ => source
        };
    }
}
