using EMessa.Core.Common;
using EMessa.Base.Constants;
using EMessa.DAL.Entities.Options;
using EMessa.Core.Features.ArticleOptionValuesRestrictions.Commands.GenerateOptionsRestrictionsForArticle;
using EMessa.Core.Localizer;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using AutoMapper;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.Core.Common.Results;
using EMessa.Core.Features.ShoppingCart.Models;

namespace EMessa.Core.Features.ArticleOptionValuesRestrictions.Services;

public interface IOptionRestrictionsService
{
    public Task<IResult> GenerateRestrictions(
        int articleId,
        CancellationToken cancellationToken = default);

    public Task<List<ArticleOptionEditModel>> ProcessArticleOptionsAsync(
        int articleId,
        int selectedOptionId,
        int? selectedOptionValueId,
        List<ArticleOptionEditModel> requestOptionValues,
        bool isSaleArticle,
        CancellationToken cancellationToken = default);

    public Task<List<ArticleOptionEditModel>> GetOrderItemOptionsEditModelAsync(
        int articleId,
        int orderItemId,
        List<ArticleOptionEditModel> orderItemOptionValues,
        CancellationToken cancellationToken);
}

public class OptionRestrictionsService(
    IDbContextFactory<ApplicationDbContext> dbFactory,
    IMapper mapper,
    ILogger<OptionRestrictionsService> logger)
    : IOptionRestrictionsService
{
    public static readonly HashSet<string> SaleOptionCodes =
    [
        OptionCode.Coat,
        OptionCode.Color,
        OptionCode.Thickness
    ];
    
    public async Task<IResult> GenerateRestrictions(int articleId, CancellationToken cancellationToken = default)
    {
        await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);
        await using var transaction = await dbContext.Database.BeginTransactionAsync(cancellationToken);

        try
        {
            var article = await dbContext
                .Articles
                .Include(x => x.Options).ThenInclude(x => x.Values).ThenInclude(x => x.OptionValue)
                .ThenInclude(x => x.Option)
                // .Include(x => x.Options).ThenInclude(x => x.Option)
                .SingleOrDefaultAsync(x => x.Id == articleId, cancellationToken);

            if (article == null)
            {
                return await Result<Unit>.FailAsync("Nie odnaleziono produktu".Tr());
            }

            // Określenie wartości opcji w produkcie
            var cartesianOptionValues = article.Options
                .Select(x => x.Values.Select(z => z.OptionValue))
                .CartesianProduct()
                .Distinct();

            var allRestrictions = cartesianOptionValues
                .Select(optionValues =>
                {
                    // Stwórz słownik dla szybkiego lookup - rozwiązuje "multiple enumeration"
                    var optionDict = CreateOptionDictionary(optionValues);

                    return new ArticleOptionValuesRestriction
                    {
                        ArticleId = article.Id,
                        CoatId = optionDict.GetValueOrDefault(OptionCode.Coat),
                        ColorId = optionDict.GetValueOrDefault(OptionCode.Color),
                        ThickId = optionDict.GetValueOrDefault(OptionCode.Thickness),
                        ModuleId = optionDict.GetValueOrDefault(OptionCode.Module),
                        FoilId = optionDict.GetValueOrDefault(OptionCode.Foil),
                        FeltId = optionDict.GetValueOrDefault(OptionCode.Felt),
                        EmbossHeightId = optionDict.GetValueOrDefault(OptionCode.EmbossHeight),
                        IsActive = false,
                        IsEdited = false,
                        OnlyInSale = false
                    };
                }).ToList();

            //try catch here?
            var articleOptionValuesRestrictionComparer = new ArticleOptionValuesRestrictionComparer();
            var allRestrictionsDistinct = allRestrictions.Distinct(articleOptionValuesRestrictionComparer).ToList();
            var existingRestrictions = await dbContext
                .ArticleOptionValuesRestrictions
                .Where(x => x.ArticleId == articleId)
                .ToListAsync(cancellationToken);
            var newRestrictions = allRestrictionsDistinct
                .Except(existingRestrictions, articleOptionValuesRestrictionComparer).ToList();

            dbContext.ArticleOptionValuesRestrictions.AddRange(newRestrictions);

            await dbContext.SaveChangesAsync(cancellationToken);
            await transaction.CommitAsync(cancellationToken);

            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync(cancellationToken);
            logger.LogError(ex, ex.Message);
            return await Result.FailAsync("Błąd generowania ograniczeń opcji".Tr());
        }
    }

    private static Dictionary<string, int> CreateOptionDictionary(IEnumerable<OptionValue> optionValues)
    {
        return optionValues.ToDictionary(
            x => x.Option.Code,
            x => x.Id,
            StringComparer.OrdinalIgnoreCase);
    }

    public async Task<List<ArticleOptionEditModel>> ProcessArticleOptionsAsync(
        int articleId,
        int selectedOptionId,
        int? selectedOptionValueId,
        List<ArticleOptionEditModel> requestOptionValues,
        bool isSaleArticle,
        CancellationToken cancellationToken = default)
    {
        // 1. Załaduj domyślny model edycji opcji produktu
        var articleOptions = await GetDefaultArticleOptionsEditModel(articleId, cancellationToken);
        // TODO trzeba gdzieś jeszcze czytać kolejność opcji No wg produktu. Może tu ^^. Na razie globalnie.

        // 2. Wyznacz numer wybranej opcji. Tzw Lp=SelectedOption.No
        var selectedOptionNo = selectedOptionId > 0
            ? articleOptions.SingleOrDefault(x => x.Id == selectedOptionId)?.OptionNo ?? -1
            : -1;

        // 3. Zaaplikuj opcje wybrane przez użytkownika poniżej wybranego No // articleOption.OptionNo <= selectedOptionNo
        // user selected nothing
        // {"POW":[1,2,3,4],"KOL":[5,6,7,8,9,10,11,12,13,14,15,16,17,18],"GR":[19,20,21,22,23],"MOD":[24,25],"FOL":[26,27],"FILC":[28,29]}
        // {"POW":null,"KOL":null,"GR":null,"MOD":null,"FOL":null,"FILC":null}
        // user selected POW=2, KOL=3, GR=19
        // {"POW":[1,2,3,4],"KOL":[5,6,7,8,9,10,11,12,13,14,15,16,17,18],"GR":[19,20,21,22,23],"MOD":[24,25],"FOL":[26,27],"FILC":[28,29]}
        // {"POW":null,"KOL":null,"GR":null,"MOD":null,"FOL":null,"FILC":null}
        ApplyUserSelections(articleOptions, requestOptionValues, selectedOptionNo, selectedOptionId,
            selectedOptionValueId, isSaleArticle);
        // selectedOptionNo = 2, POW=2, KOL=3, GR=any, MOD=any, FOL=any, FILC=any, WP=any, ZAM=any
        // {"POW":[2],"KOL":[3],"GR":[19,20,21,22,23],"MOD":[24,25],"FOL":[26,27],"FILC":[28,29],"WP":[],"ZAM":[]}
        // user selected nothing
        // {"POW":[1,2,3,4],"KOL":[5,6,7,8,9,10,11,12,13,14,15,16,17,18],"GR":[19,20,21,22,23],"MOD":[24,25],"FOL":[26,27],"FILC":[28,29]}
        // {"POW":null,"KOL":null,"GR":null,"MOD":null,"FOL":null,"FILC":null}
        // user selected POW=2, KOL=3, GR=19
        // {"POW":[1,2,3,4],"KOL":[5,6,7,8,9,10,11,12,13,14,15,16,17,18],"GR":[19,20,21,22,23],"MOD":[24,25],"FOL":[26,27],"FILC":[28,29]}
        // {"POW":1,"KOL":5,"GR":19,"MOD":null,"FOL":null,"FILC":null}

        // 4. Wybrane opcje produktu dict
        var preselectedOptionValuesDict = GetPreselectOptionValuesDict(articleOptions);
        // {"POW":[1,2,3,4],"KOL":[5,6,7,8,9,10,11,12,13,14,15,16,17,18],"GR":[19,20,21,22,23],"MOD":[24,25],"FOL":[26,27],"FILC":[28,29]}
        // {"POW":[1],"KOL":[5],"GR":[19],"MOD":[24,25],"FOL":[26,27],"FILC":[28,29]}

        // Słownik prawdziwych zawężonych opcji produktu. Opcje produktu, zawężone do opcji dostępnych dla produktu w ramach wstępnie przefiltrowanych.
        // 5. Zawężanie wyborów do restrictions
        var restrictedOptionValuesDict = await GetRestrictions(
            articleId, preselectedOptionValuesDict, cancellationToken);
        // Jeśli użytkownik nie wybrał/zawęził jeszcze opcji, to będzie tak:
        // {"POW":[1,2,4],"KOL":[5,8,9,6,7],"GR":[19,22],"MOD":[24,25],"FOL":[26,27],"FILC":[28,29],"WP":[]}
        // Jeśli wybrał/zawęził userPOW=1 to będzie np tak:
        // {"POW":[1],"KOL":[5,8,9],"GR":[19,22],"MOD":[24,25],"FOL":[26,27],"FILC":[28,29],"WP":[]}

        // 6. Skopiuj gdy <=selectedOptionNo lub zastosuj zawężanie
        ApplyRestrictions(articleOptions, restrictedOptionValuesDict, requestOptionValues, selectedOptionNo);

        // 7. Ustawienie wartości domyślnych dla opcji
        // lub pomiń ustawienie wartości domyślnej dla opcji, jeśli SelectedOptionId == -1 (inicjalizacja)
        SetDefaultValues(articleOptions, selectedOptionId);

        return articleOptions;
    }

    public async Task<List<ArticleOptionEditModel>> GetOrderItemOptionsEditModelAsync(
        int articleId,
        int orderItemId,
        List<ArticleOptionEditModel> orderItemOptionValues,
        CancellationToken cancellationToken)
    {
        // 0. Przygotuj nie przekazane parametry za pomocą ostatniej opcji wg OptionNo, tzn z 'No.Last'
        var lastOptionValue = orderItemOptionValues.OrderBy(x => x.OptionNo).Last();
        var selectedOptionId = lastOptionValue.OptionId;
        var selectedOptionValueId = lastOptionValue.OptionValueId;
        
        // 1. Załaduj domyślny model edycji opcji produktu
        var articleOptions = await GetDefaultArticleOptionsEditModel(articleId, cancellationToken);

        // 2. Wyznacz numer wybranej opcji.
        var selectedOptionNo = lastOptionValue.OptionNo;

        // 3. Zaaplikuj wybrane opcje
        ApplyUserSelections(articleOptions, orderItemOptionValues, selectedOptionNo, selectedOptionId,
            selectedOptionValueId, false);

        // 3. Wybrane opcje produktu dict
        var preselectedOptionValuesDict = GetPreselectOptionValuesDict(articleOptions);

        // 4. Zawężanie wyborów do restrictions
        var restrictedOptionValuesDict =
            await GetRestrictions(articleId, preselectedOptionValuesDict, cancellationToken);

        // 5. Skopiuj gdy <= selectedOptionNo lub zastosuj zawężanie
        ApplyRestrictions(articleOptions, restrictedOptionValuesDict, orderItemOptionValues, selectedOptionNo);

        // 6. Ustawienie wartości domyślnych dla opcji
        SetDefaultValues(articleOptions, Int32.MaxValue);
        
        articleOptions.ForEach(x => x.OrderItemId = orderItemId);

        return articleOptions;
    }

    public async Task<List<ArticleOptionEditModel>> GetDefaultArticleOptionsEditModel(
        int articleId,
        CancellationToken cancellationToken)
    {
        await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

        var articleOptions = await dbContext
            .ArticleOptions
            .AsNoTracking()
            .Include(x => x.Option)
            .ThenInclude(x => x.Translations)
            .Include(x => x.Values)
            .ThenInclude(x => x.OptionValue)
            .ThenInclude(x => x.Translations)
            .Where(x => x.ArticleId == articleId)
            .OrderBy(x => x.Option.No)
            .ToListAsync(cancellationToken);

        return mapper.Map<List<ArticleOptionEditModel>>(articleOptions);
    }

    public void ApplyUserSelections(
        List<ArticleOptionEditModel> articleOptions,
        List<ArticleOptionEditModel> requestOptionValues,
        int selectedOptionNo,
        int selectedOptionId,
        int? selectedOptionValueId,
        bool isSaleArticle)
    {
        if (articleOptions.Count == 0 || requestOptionValues.Count == 0)
        {
            return;
        }

        var resetOptionValue = selectedOptionNo == 1;

        // Utworzenie słownika dla szybkiego dostępu do requestOptionValues po OptionCode
        var requestOptionsDict = requestOptionValues.ToDictionary(
            x => x.OptionCode,
            x => x,
            StringComparer.OrdinalIgnoreCase);

        articleOptions.ForEach(articleOption =>
        {
            // Szybkie wyszukiwanie po słowniku zamiast SingleOrDefault
            if (!requestOptionsDict.TryGetValue(articleOption.OptionCode, out var requestOptionValue))
                return;

            if (isSaleArticle && SaleOptionCodes.Contains(articleOption.OptionCode))
            {
                // Dla artykułów promocyjnych opcje z SaleOptionCodes używają wartości z requestOptionValues
                articleOption.OptionValueId = requestOptionValue.OptionValueId;
                articleOption.UserSelectedOptionValueId = requestOptionValue.OptionValueId;
                return;
            }
            
            articleOption.OptionValueId = articleOption.OptionNo <= selectedOptionNo
                ? requestOptionValue.OptionValueId
                : null;

            // Skopiuj wybór użytkownika lub null, jeśli reset
            if (articleOption.UseDefaultValue)
            {
                articleOption.UserSelectedOptionValueId = resetOptionValue 
                    ? null 
                    : requestOptionValue.OptionValueId;
            }

            // Ustawienie wybranej wartości dla edytowanej opcji
            if (articleOption.Id == selectedOptionId)
            {
                articleOption.OptionValueId = selectedOptionValueId;
                articleOption.UserSelectedOptionValueId = selectedOptionValueId;
            }
        });
    }

    public Dictionary<string, List<int>> GetPreselectOptionValuesDict(
        List<ArticleOptionEditModel> articleOptions)
    {
        // if x.OptionValueId.HasValue -> [x.OptionCode] -> [x.OptionValueId!.Value]
        // else -> [x.OptionCode] -> x.Values.Select(z => z.OptionValueId).ToList()
        return articleOptions
            .ToDictionary(x => x.OptionCode, x => x.OptionValueId.HasValue
                ? [x.OptionValueId!.Value]
                : x.Values.Select(z => z.OptionValueId).ToList());
    }

    public async Task<Dictionary<string, List<int>>> GetRestrictions(
        int articleId,
        Dictionary<string, List<int>> preselectedOptionValuesDict,
        CancellationToken cancellationToken)
    {
        await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

        // Usuń wartości null i puste listy ze słownika
        var filteredDict = preselectedOptionValuesDict
            .Where(kvp => kvp.Value is { Count: > 0 })
            .ToDictionary();
        // preselectedOptionValuesDict =
        // {"POW":[1,2,3,4],"KOL":[5,6,7,8,9,10,11,12,13,14,15,16,17,18],"GR":[19,20,21,22,23],"MOD":[24,25],"FOL":[26,27],"FILC":[28,29],"WP":[],"ZAM":[]}
        // filteredDict =
        // {"POW":[1,2,3,4],"KOL":[5,6,7,8,9,10,11,12,13,14,15,16,17,18],"GR":[19,20,21,22,23],"MOD":[24,25],"FOL":[26,27],"FILC":[28,29]}

        if (filteredDict.Count == 0)
        {
            return new Dictionary<string, List<int>>();
        }

        // Wyodrębnione listy wartości opcji
        var coatIds = filteredDict[OptionCode.Coat].Select(x => x as int?).ToArray();
        var colorIds = filteredDict[OptionCode.Color].Select(x => x as int?).ToArray();
        var thickIds = filteredDict[OptionCode.Thickness].Select(x => x as int?).ToArray();


        var query = dbContext.ArticleOptionValuesRestrictions
            .AsNoTracking()
            .Where(x => x.ArticleId == articleId && x.IsActive)
            // field NOT NULL and (array is empty /all/ or contains value /available/)
            .Where(x =>
                (x.CoatId != null && (coatIds.Length == 0 || coatIds.Contains(x.CoatId))))
            .Where(x =>
                (x.ColorId != null && (colorIds.Length == 0 || colorIds.Contains(x.ColorId))))
            .Where(x =>
                (x.ThickId != null && (thickIds.Length == 0 || thickIds.Contains(x.ThickId))));

        var lookForModule = filteredDict.ContainsKey(OptionCode.Module);
        if (lookForModule)
        {
            var moduleIds = filteredDict[OptionCode.Module]
                .Select(x => x as int?).ToArray();
            query = query.Where(x => x.ModuleId != null && moduleIds.Contains(x.ModuleId));
        }

        var lookForFoil = filteredDict.ContainsKey(OptionCode.Foil);
        if (lookForFoil)
        {
            var foilIds = filteredDict[OptionCode.Foil]
                .Select(x => x as int?).ToArray();
            query = query.Where(x => x.FoilId != null && foilIds.Contains(x.FoilId));
        }

        var lookForFelt = filteredDict.ContainsKey(OptionCode.Felt);
        if (lookForFelt)
        {
            var feltIds = filteredDict[OptionCode.Felt]
                .Select(x => x as int?).ToArray();
            query = query.Where(x => x.FeltId != null && feltIds.Contains(x.FeltId));
        }

        var lookForEmbossHeight = filteredDict.ContainsKey(OptionCode.EmbossHeight);
        if (lookForEmbossHeight)
        {
            var embossHeightIds = filteredDict[OptionCode.EmbossHeight]
                .Select(x => x as int?).ToArray();
            query = query.Where(x => x.EmbossHeightId != null && embossHeightIds.Contains(x.EmbossHeightId));
        }

        var results = await query
            .Select(x => new
            {
                x.CoatId,
                x.ColorId,
                x.ThickId,
                x.ModuleId,
                x.FoilId,
                x.FeltId,
                x.EmbossHeightId
            })
            .ToListAsync(cancellationToken);

        // Returns dictionary with option codes and their distinct values
        // Returns examples:
        // {"POW":[1,2,3,4],"KOL":[5,6,7,8,9,10,11,12,13,14,15,16,17,18],"GR":[19,20,21,22,23],"MOD":[24,25],"FOL":[26,27],"FILC":[28,29],"WP":[]}
        // {"POW":[1,2,4],"KOL":[5,8,9,6],"GR":[19,20,21,22,23],"MOD":[24,25],"FOL":[26,27],"FILC":[28,29],"WP":[]}
        return new Dictionary<string, List<int>>
        {
            [OptionCode.Coat] = results
                .Where(x => x.CoatId.HasValue)
                .Select(x => x.CoatId!.Value)
                .Distinct().ToList(),
            [OptionCode.Color] = results
                .Where(x => x.ColorId.HasValue)
                .Select(x => x.ColorId!.Value)
                .Distinct().ToList(),
            [OptionCode.Thickness] = results
                .Where(x => x.ThickId.HasValue)
                .Select(x => x.ThickId!.Value)
                .Distinct().ToList(),
            [OptionCode.Module] = results
                .Where(x => x.ModuleId.HasValue)
                .Select(x => x.ModuleId!.Value)
                .Distinct().ToList(),
            [OptionCode.Foil] = results
                .Where(x => x.FoilId.HasValue)
                .Select(x => x.FoilId!.Value)
                .Distinct().ToList(),
            [OptionCode.Felt] = results
                .Where(x => x.FeltId.HasValue)
                .Select(x => x.FeltId!.Value)
                .Distinct().ToList(),
            [OptionCode.EmbossHeight] = results
                .Where(x => x.EmbossHeightId.HasValue)
                .Select(x => x.EmbossHeightId!.Value).Distinct().ToList()
        };
    }

    public void ApplyRestrictions(
        List<ArticleOptionEditModel> articleOptions,
        Dictionary<string, List<int>> restrictedOptionValuesDict,
        List<ArticleOptionEditModel> orderItemOptionValues,
        int selectedOptionNo)
    {
        // Grupujemy optionValues według kodu opcji dla szybszego dostępu
        var optionValuesByCode = articleOptions
            .Where(ao => ao.OptionNo <= selectedOptionNo)
            .Join(
                orderItemOptionValues,
                ao => ao.Id,
                ov => ov.OptionId,
                (ao, ov) => new { OptionCode = ao.OptionCode, OptionValues = ao.Values })
            .GroupBy(x => x.OptionCode)
            .ToDictionary(g => g.Key, g => g.First().OptionValues);

        // Skopiuj gdy <=selectedOptionNo lub zastosuj zawężanie
        foreach (var articleOption in articleOptions)
        {
            // 1 Przepisz zawężanie do już wcześniej wybranych opcji wartości, <=selectedOptionNo
            if (articleOption.OptionNo <= selectedOptionNo)
            {
                if (optionValuesByCode.TryGetValue(articleOption.OptionCode, out var values))
                {
                    articleOption.Values = values;
                }
            }
            // 2 Zawężanie wyboru do restrictions
            else
            {
                // ✅ Bezpieczne sprawdzenie
                if (restrictedOptionValuesDict.TryGetValue(articleOption.OptionCode, out var narrowedIds))
                {
                    articleOption.Values = articleOption.Values
                        .Where(x => narrowedIds.Contains(x.OptionValueId))
                        .ToList();
                }
                // Opcjonalnie: logowanie gdy brak klucza
                else
                {
                    logger.LogWarning($"Brak restrictions dla {articleOption.OptionCode}");
                }
            }
        }
    }

    public void SetDefaultValues(List<ArticleOptionEditModel> articleOptions, int selectedOptionId)
    {
        if (selectedOptionId == -1) return; // Pomiń dla inicjalizacji

        // Ustawienie wartości domyślnych dla opcji
        foreach (var optionValue in articleOptions.Where(optionValue => optionValue.UseDefaultValue))
        {
            // Pomiń jeśli opcja ma już przypisaną wartość, to nie zmieniaj
            if (optionValue.OptionValueId.HasValue)
            {
                continue;
            }

            // Nadaj wartość domyślną, jeśli opcja ma tylko jedną wartość
            if (optionValue.Values.Count == 1)
            {
                optionValue.OptionValueId = optionValue.Values.First().OptionValueId;
            }
            // Jeśli opcja ma więcej niż jedną wartość, to nadaj wartość domyślną
            else
            {
                var userOptionValueId = optionValue.Values.FirstOrDefault(x =>
                    x.OptionValueId == optionValue.UserSelectedOptionValueId)?.OptionValueId;
                var defaultOptionValueId = optionValue.Values.FirstOrDefault(x =>
                    x.IsDefault)?.OptionValueId;
                optionValue.OptionValueId = userOptionValueId ?? defaultOptionValueId;
            }
        }

        // Ustaw ValueName i ValueCode dla opcji z przypisanymi wartościami
        articleOptions.Where(x => x.OptionValueId.HasValue).ToList().ForEach(x =>
        {
            var option = x.Values.FirstOrDefault(z => z.OptionValueId == x.OptionValueId);
            x.ValueName = option?.Value ?? "";
            x.ValueCode = option?.Code ?? "";
        });
    }
}