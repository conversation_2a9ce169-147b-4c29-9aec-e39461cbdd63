using EMessa.Core.Common;
using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Branches;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Sieve.Models;

namespace EMessa.Core.Features.Branches.Queries.GetAll;

public class GetAllBranchesQuery : SieveGetAllQueryBase<GetAllBranchesResponse>
{
    public GetAllBranchesQuery(SieveModel sieveModel) : base(sieveModel)
    { }

    public GetAllBranchesQuery() : base()
    { }
}

public class GetAllBranchesQueryHandler(
    IDbContextFactory<ApplicationDbContext> dbFactory,
    ISieveService sieveService)
    : IRequestHandler<GetAllBranchesQuery, ListResult<GetAllBranchesResponse>>
{
    public async Task<ListResult<GetAllBranchesResponse>> Handle(
        GetAllBranchesQuery request,
        CancellationToken cancellationToken)
    {
        await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

        var branches = dbContext
            .Branches
            .AsNoTracking()
            .AsQueryable();

        var response = await sieveService
            .ExecuteSieveAsync<Branch, GetAllBranchesResponse>(
                request.SieveModel,
                branches,
                cancellationToken);

        return response;
    }
}