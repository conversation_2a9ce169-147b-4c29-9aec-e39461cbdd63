using AutoMapper;
using EMessa.DAL.Entities.FilterAttributes;
using EMessa.Core.Features.ArticleFilterAttribute.Exceptions;
using EMessa.Core.Interfaces;
using EMessa.Core.Localizer;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using EMessa.DAL.Data;
using EMessa.Core.Common.Results;

namespace EMessa.Core.Features.ArticleFilterAttribute.Queries.GetFilterAttributesForCategory;

public class GetFilterAttributesForCategoryResponse
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string TranslatedDisplayName { get; set; } = string.Empty;
    public int OrdinaryNumber { get; set; }

    public List<FilterAttributeValueForCategory> Values { get; set; } = [];
}

public class FilterAttributeValueForCategory
{
    public int Id { get; set; }
    public string Code { get; set; } = null!;
    public string Value { get; set; } = null!;
    public string DisplayValue { get; set; } = null!;
    public string TranslatedDisplayValue { get; set; } = null!;
}

public record GetFilterAttributesForCategoryQuery(int CategoryId)
    : IRequest<IResult<List<GetFilterAttributesForCategoryResponse>>>;

public class GetFilterAttributesForCategoryQueryHandler(
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IMapper mapper,
    ILogger<GetFilterAttributesForCategoryQueryHandler> logger)
    : IRequestHandler<GetFilterAttributesForCategoryQuery, IResult<List<GetFilterAttributesForCategoryResponse>>>
{
    public async Task<IResult<List<GetFilterAttributesForCategoryResponse>>> Handle(
        GetFilterAttributesForCategoryQuery request, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var category = await dbContext.Categories
                .Include(x => x.FilterAttributes)
                .SingleOrDefaultAsync(x => x.Id == request.CategoryId, cancellationToken: cancellationToken);
            if (category == null) 
                throw new GetFilterAttributesForCategoryExceptions("Nie znaleziono kategorii.".Tr());

            var attributes = dbContext.FilterAttributes
                .Include(x => x.FilterAttibuteValues)
                .Where(x => x.IsActive == true && x.Categories.Any(x => x.Id == category.Id)).ToList();

            var result = mapper.Map<List<GetFilterAttributesForCategoryResponse>>(attributes);

            return await Result<List<GetFilterAttributesForCategoryResponse>>.SuccessAsync(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result<List<GetFilterAttributesForCategoryResponse>>.FailAsync(
                "Błąd pobrania atrybutów filtrowania".Tr());
        }
    }
}

//Create maoping from AttributeFilter to GetFilterAttributesForCategoryResponse
public class GetFilterAttributesForCategoryProfile : Profile
{
    public GetFilterAttributesForCategoryProfile()
    {
        CreateMap<FilterAttribute, GetFilterAttributesForCategoryResponse>()
            .ForMember(dest => dest.TranslatedDisplayName, opt => opt.MapFrom(src => src.DisplayName))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.Code, opt => opt.MapFrom(src => src.Code))
            .ForMember(dest => dest.OrdinaryNumber, opt => opt.MapFrom(src => src.OrdinaryNumber))
            .ForMember(x => x.Values, x => x.MapFrom(z => z.FilterAttibuteValues.ToList()));
    }
}

public class FilterAttributeValueForCategoryProfile : Profile
{
    public FilterAttributeValueForCategoryProfile()
    {
        CreateMap<FilterAttributeValue, FilterAttributeValueForCategory>()
            .ForMember(dest => dest.TranslatedDisplayValue, opt => opt.MapFrom(src => src.DisplayValue))
            .ForMember(dest => dest.Code, opt => opt.MapFrom(src => src.Code))
            .ForMember(dest => dest.Value, opt => opt.MapFrom(src => src.Value))
            .ForMember(dest => dest.DisplayValue, opt => opt.MapFrom(src => src.DisplayValue));
    }
}