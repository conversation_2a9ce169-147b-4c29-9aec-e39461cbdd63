using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.Core.Localizer;
using EMessa.Core.Services;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Identity;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Accounts.Commands.Login;

public record AccountLoginCommand(AccountLoginRequest Data) : IRequest<IResult>;

public class AccountLoginCommandHandler(
    ILogger<AccountLoginCommandHandler> logger,
    UserManager<ApplicationUser> userManager,
    SignInManager<ApplicationUser> signInManager,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ILockOrderService lockOrderService)
    : IRequestHandler<AccountLoginCommand, IResult>
{
    public async Task<IResult> Handle(AccountLoginCommand request, CancellationToken cancellationToken)
    {
        try
        {
            if (string.IsNullOrEmpty(request.Data.Email))
            {
                return await Result.FailAsync("Pole {0} jest wymagane.".Tr("Nazwa użytkownika"));
            }

            if (string.IsNullOrEmpty(request.Data.Password))
            {
                return await Result.FailAsync("Pole {0} jest wymagane.".Tr("Hasło"));
            }

            var user = await userManager.FindByNameAsync(request.Data.Email);

            if (user != null)
            {
                var result = await signInManager.CheckPasswordSignInAsync(user, request.Data.Password, true);

                if (!result.Succeeded)
                {
                    return await Result.FailAsync("Próba logowania nie powiodła się.".Tr());
                }

                if (result.IsLockedOut)
                {
                    return await Result.FailAsync(
                        "Konto jest zablokowane. Skontaktuj się ze swoim menedżerem konta.".Tr());
                }

                if (userManager.Options.SignIn.RequireConfirmedEmail &&
                    !await userManager.IsEmailConfirmedAsync(user))
                {
                    return await Result.FailAsync(
                        "Konto jest nieaktywne. Skontaktuj się ze swoim menedżerem konta.".Tr());
                }

                await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

                var userProfile = await dbContext.UserProfiles
                    .SingleAsync(x => x.UserName == user.UserName, cancellationToken);

                // Usuń wszystkie locki zamówień użytkownika przed zalogowaniem
                await lockOrderService.UnlockUserLocksAsync(userProfile.Id, cancellationToken);

                if (!userProfile.Active)
                {
                    logger.LogWarning("Próba logowania do nieaktywnego konta. Użytkownik {0}, e-mail {1}.", 
                        userProfile.UserName, userProfile.Email);
                    return await Result.FailAsync("Konto jest nieaktywne.".Tr());
                }

                await signInManager.SignInAsync(user, request.Data.RememberMe);

                userProfile.LastLoginDate = DateTime.Now;
                userProfile.LastLoginIp = request.Data.LoginIp;

                dbContext.UserProfiles.Update(userProfile);
                await dbContext.SaveChangesAsync(cancellationToken);

                return await Result.SuccessAsync();
            }

            return await Result.FailAsync("Próba logowania nie powiodła się.".Tr());
        }
        catch (Exception e)
        {
            logger.LogError(e, e.Message);
            return await Result.FailAsync("Operacja nie powiodła się.".Tr());
        }
    }
}