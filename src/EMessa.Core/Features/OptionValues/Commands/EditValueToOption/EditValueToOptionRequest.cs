namespace EMessa.Core.Features.OptionValues.Commands.EditValueToOption
{
    public class EditValueToOptionRequest
    {
        public int Id { get; set; }

        public int No { get; set; }

        public string Value { get; set; } = null!;

        public string Code { get; set; } = null!;
        public int OptionId { get; set; }

        public decimal WeightFactor { get; set; }

        public string ValueInfo { get; set; } = string.Empty;

        public bool IsDefault { get; set; } = false;

        /// <summary>
        /// Dodatkowa strefa przetłoczenia (w prawo od długości modułowej)
        /// </summary>
        public decimal EmbossZoneAddition { get; set; }
    }
}
