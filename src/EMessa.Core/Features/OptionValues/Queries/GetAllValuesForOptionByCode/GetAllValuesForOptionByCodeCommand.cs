using AutoMapper;
using EMessa.Core.Common.Results;
using EMessa.Core.Features.OptionValues.Queries.GetAllValueForOption;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.OptionValues.Queries.GetAllValuesForOptionByCode;

public record GetAllValuesForOptionByCodeCommand(string OptionCode) : IRequest<IResult<List<GetAllValuesForOptionResponse>>>;

public class GetAllValuesForOptionByCodeCommandHandler(
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IMapper mapper,
    ILogger<GetAllValuesForOptionQueryHandler> logger)
    : IRequestHandler<GetAllValuesForOptionByCodeCommand, IResult<List<GetAllValuesForOptionResponse>>>
{
    public async Task<IResult<List<GetAllValuesForOptionResponse>>> Handle(GetAllValuesForOptionByCodeCommand request, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var optionValues = await dbContext.OptionValues
                .AsNoTracking()
                .Where(ov => ov.Option.Code == request.OptionCode)
                .ToListAsync(cancellationToken);

            var result = mapper.Map<List<GetAllValuesForOptionResponse>>(optionValues);

            return await Result<List<GetAllValuesForOptionResponse>>.SuccessAsync(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result<List<GetAllValuesForOptionResponse>>.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}