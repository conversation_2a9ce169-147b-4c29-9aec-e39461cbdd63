using AutoMapper;
using EMessa.Core.Common.Results;
using EMessa.Core.Features.Customers.Queries.GetAll;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Customers.Queries.GetSelected;

public record GetSelectedCustomerQuery(int Id) : IRequest<Result<GetAllCustomersResponse>>;

public class GetSelectedCustomerQueryHandler(
    ILogger<GetSelectedCustomerQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IMapper mapper)
    : IRequestHandler<GetSelectedCustomerQuery, Result<GetAllCustomersResponse>>
{
    public async Task<Result<GetAllCustomersResponse>> Handle(GetSelectedCustomerQuery request, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);
            
            var customer = await dbContext.Customers
                .Include(x => x.Branch)
                .SingleOrDefaultAsync(x => x.Id == request.Id, cancellationToken);

            if (customer is null)
            {
                return await Result<GetAllCustomersResponse>.FailAsync(ResultErrorMessages.NotFound);
            }

            var result = mapper.Map<GetAllCustomersResponse>(customer);

            return await Result<GetAllCustomersResponse>.SuccessAsync(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result<GetAllCustomersResponse>.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
} 