using EMessa.Base.Constants;
using EMessa.Base.Enums;
using EMessa.Core.Common;
using EMessa.Core.Common.Results;
using EMessa.Core.Helpers;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Customers;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Sieve.Models;

namespace EMessa.Core.Features.Customers.Queries.GetAll;

public class GetAllCustomersQuery : SieveGetAllQueryBase<GetAllCustomersResponse>
{
    public GetAllCustomersQuery(SieveModel sieveModel) : base(sieveModel)
    { }

    public GetAllCustomersQuery() : base()
    { }
}

public class GetAllCustomersQueryHandler(
    ILogger<GetAllCustomersQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ISieveService sieveService,
    IAppStateService appStateService)
        : IRequestHandler<GetAllCustomersQuery, ListResult<GetAllCustomersResponse>>
{
    public async Task<ListResult<GetAllCustomersResponse>> Handle(GetAllCustomersQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var loggedUser = appStateService.UserData;
            var eMessaCustomerAppOwner = ConfigurationHelper.GetConfiguration<string>(ConfigurationCodes.EMessaUserCode);

            if (loggedUser.MaxRole == Role.Client
                || loggedUser.MaxRole == Role.ClientManager
                || string.IsNullOrEmpty(eMessaCustomerAppOwner))
            {
                return ListResult<GetAllCustomersResponse>.Failure(ResultErrorMessages.Unauthorized);
            }

            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var customersQuery = dbContext
                .Customers
                .Include(x => x.Branch)
                .AsNoTracking()
                .AsQueryable();

            if (loggedUser.MaxRole != Role.Administrator)
            {
                // Jeśli nie-admin, to nie pokazuj (klienta) właściciela aplikacji
                customersQuery = customersQuery.Where(x => x.ShortName != eMessaCustomerAppOwner);

                if (loggedUser.Roles.Contains(Role.Production) &&
                   (loggedUser.Roles.Contains(Role.TradeManager) || loggedUser.Roles.Contains(Role.Trade)))
                {
                    customersQuery = customersQuery.Where(x => loggedUser.FactoryId == x.FactoryId ||
                                                               loggedUser.BranchIds.Contains(x.BranchId));
                }
                else if (loggedUser.Roles.Contains(Role.Production))
                {
                    customersQuery = customersQuery.Where(x => loggedUser.FactoryId == x.FactoryId);
                }
                else if (loggedUser.Roles.Contains(Role.TradeManager) || loggedUser.Roles.Contains(Role.Trade))
                {
                    customersQuery = customersQuery.Where(x => loggedUser.BranchIds.Contains(x.BranchId));
                }
            }

            var response = await sieveService
                .ExecuteSieveAsync<Customer, GetAllCustomersResponse>(
                    request.SieveModel,
                    customersQuery,
                    cancellationToken);

            return response;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while handling {QueryName}: {Message}", nameof(GetAllCustomersQuery), ex.Message);
            return ListResult<GetAllCustomersResponse>.Failure(ResultErrorMessages.UnexpectedError);
        }
    }
}
