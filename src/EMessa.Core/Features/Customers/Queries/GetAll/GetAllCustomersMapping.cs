using AutoMapper;
using EMessa.Core.Common.Results;
using EMessa.DAL.Entities.Customers;

namespace EMessa.Core.Features.Customers.Queries.GetAll;

public class GetAllCustomersMapping : Profile
{
    public GetAllCustomersMapping()
    {
        CreateMap<Customer, GetCustomersResponseBase>();
        CreateMap<Customer, GetAllCustomersResponse>()
            .ForMember(m => m.BranchId, o => o.MapFrom(s => s.Branch.Id))
            .ForMember(m => m.BranchName, o => o.MapFrom(s => s.Branch.Name));
        CreateMap<ListResult<Customer>, ListResult<GetAllCustomersResponse>>();
    }
}