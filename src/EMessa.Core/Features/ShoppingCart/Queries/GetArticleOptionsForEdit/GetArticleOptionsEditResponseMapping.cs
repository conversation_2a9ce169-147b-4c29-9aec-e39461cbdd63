using AutoMapper;
using EMessa.DAL.Entities.ArticleOptions;
using System.Globalization;

namespace EMessa.Core.Features.ShoppingCart.Queries.GetArticleOptionsForEdit
{
    public class GetArticleOptionsEditResponseMapping : Profile
    {
        public GetArticleOptionsEditResponseMapping()
        {
            {
                CreateMap<ArticleOption, GetArticleOptionsEditResponse>()
                    .ForMember(x => x.OriginalName, opt => opt.MapFrom(x => x.Option.Name))

                    .ForMember(x => x.Name,

                        x => x.MapFrom(z =>
                            z.Option.Translations.FirstOrDefault(x => x.LanguageCode == CultureInfo.CurrentCulture.Name) == null
                            ? z.Option.Name
                            : z.Option.Translations.First(x => x.LanguageCode == CultureInfo.CurrentCulture.Name).Name))

                    .ForMember(x => x.Code, opt => opt.MapFrom(x => x.Option.Code))
                    
                    .ForMember(x => x.Values, opt => opt.MapFrom(x => x.Values))
                    .ForMember(d => d.UseDefaultValue, opt => opt.MapFrom(s => s.UseDefaultValue))
                    ;

                CreateMap<ArticleOptionValue, GetArticleOptionValuesEditResponse>()
                    .ForMember(x => x.Id, opt => opt.MapFrom(x => x.Id))
                    .ForMember(x => x.OptionId, opt => opt.MapFrom(x => x.OptionValue.Id))
                    //.ForMember(x => x.OptionValueId, opt => opt.MapFrom(x => x.OptionValueId))

                    .ForMember(x => x.OriginalValue, opt => opt.MapFrom(x => x.OptionValue.Value))
                    .ForMember(x => x.Value,
                        x => x.MapFrom(z =>
                            z.OptionValue.Translations.FirstOrDefault(x => x.LanguageCode == CultureInfo.CurrentCulture.Name) == null
                            ? z.OptionValue.Value
                            : z.OptionValue.Translations.First(x => x.LanguageCode == CultureInfo.CurrentCulture.Name).Value))

                    .ForMember(x => x.IsDefault, x => x.MapFrom(z => z.IsDefault ?? false))
                    .ForMember(x => x.OriginalValueInfo, opt => opt.MapFrom(x => x.OptionValue.ValueInfo))
                    .ForMember(x => x.ValueInfo,
                        x => x.MapFrom(z =>
                            z.OptionValue.Translations.FirstOrDefault(x => x.LanguageCode == CultureInfo.CurrentCulture.Name) == null
                            ? z.OptionValue.ValueInfo
                            : z.OptionValue.Translations.First(x => x.LanguageCode == CultureInfo.CurrentCulture.Name).ValueInfo))
                    ;
            }
        }
    }
}
