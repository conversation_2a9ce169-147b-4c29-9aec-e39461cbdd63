namespace EMessa.Core.Features.ShoppingCart.Queries.GetArticleOptionsForEdit
{
    public class GetArticleOptionValuesEditResponse
    {
        /// <summary>
        /// Id wartości opcji
        /// </summary>
        public int Id { get; set; }
        /// <summary>
        /// Id opcji
        /// </summary>
        public int OptionId { get; set; }
        public int OptionValueId { get; set; }

        /// <summary>
        /// Numer kolejny
        /// </summary>
        public int No { get; set; }
        /// <summary>
        /// Wartość przetłumaczona
        /// </summary>
        public string Value { get; set; } = null!;
        public string Code { get; set; } = null!;
        /// <summary>
        /// Oryginalna wartość
        /// </summary>
        public string OriginalValue { get; set; } = null!;
        public string ValueInfo { get; set; } = null!;
        public string OriginalValueInfo { get; set; } = null!;
        public bool IsDefault { get; set; }
    }
}
