using System.Globalization;
using AutoMapper;
using EMessa.Core.Common.Results;
using EMessa.Core.Features.ArticleOptionValuesRestrictions.Services;
using EMessa.Core.Features.ShoppingCart.Models;
using EMessa.Core.Features.ShoppingCart.Queries.GetAddNewItemModel;
using EMessa.Core.Interfaces;
using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Articles;
using EMessa.DAL.Entities.Orders;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.ShoppingCart.Queries.GetShoppingCartItem;

public record GetShoppingCartItemEditModelByOrderItemIdQuery(int OrderItemId)
    : IRequest<IResult<ShoppingCartItemEditModel>>;

public class GetShoppingCartItemEditModelByOrderItemIdQueryHandler(
    IOptionRestrictionsService optionRestrictionsService,
    I<PERSON>ap<PERSON> mapper,
    ILogger<GetShoppingCartItemEditModelByOrderItemIdQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbFactory)
    : IRequestHandler<GetShoppingCartItemEditModelByOrderItemIdQuery, IResult<ShoppingCartItemEditModel>>
{
    public async Task<IResult<ShoppingCartItemEditModel>> Handle(
        GetShoppingCartItemEditModelByOrderItemIdQuery request,
        CancellationToken cancellationToken)
    {
        await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

        try
        {
            var orderItem = await dbContext
                .OrderItems
                .Include(x => x.Article)
                .ThenInclude(x => x.Unit)
                .Include(x => x.Article)
                .ThenInclude(x => x.Options)
                .ThenInclude(x => x.Option)
                .ThenInclude(x => x.Values)
                .Include(x => x.RequestedSheets)
                .ThenInclude(x => x.OrderItemSheets)
                .Include(x => x.OptionValues)
                .ThenInclude(x => x.Option)
                .AsNoTracking()
                .AsSplitQuery()
                .FirstOrDefaultAsync(x => x.Id == request.OrderItemId, cancellationToken);

            if (orderItem == null)
            {
                logger.LogError("Nie odnaleziono przedmiotu zamówienia");
                return await Result<ShoppingCartItemEditModel>.FailAsync("Nie odnaleziono przedmiotu zamówienia".Tr());
            }

            var mappedArticle = mapper.Map<ShoppingCartItemEditModel>(orderItem);   
            
            var orderItemOptionValues = orderItem.OptionValues
                .Select(ov => new ArticleOptionEditModel
                {
                    OptionNo = ov.Option.No,
                    OptionCode = ov.Option.Code,
                    OptionId = ov.OptionId,
                    OptionValueId = ov.OptionValueId
                })
                .ToList();
            var optionValues = await optionRestrictionsService.GetOrderItemOptionsEditModelAsync(
                orderItem.ArticleId,
                orderItem.Id,
                orderItemOptionValues,
                cancellationToken);
            mappedArticle.OptionValues = optionValues;

            mappedArticle.RequestedSheets = mapper.Map<List<RequestedSheetEditModel>>(orderItem.RequestedSheets);
            // Mapped requested sheets. Odbudowa No i relacji RequestedSheet -> OrderItemRequestedSheetResponse. Potrzebne do poprawnego wyświetlania w UI
            var sheetIndex = 1;
            foreach (var requestedSheet in mappedArticle.RequestedSheets)
            {
                requestedSheet.No = sheetIndex++;
                var itemIndex = 1;
                foreach (var orderItemSheet in requestedSheet.OrderItemSheets)
                {
                    orderItemSheet.No = itemIndex++;
                    orderItemSheet.RequestedSheet = requestedSheet;
                }
            }

            return await Result<ShoppingCartItemEditModel>.SuccessAsync(mappedArticle);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result<ShoppingCartItemEditModel>.FailAsync(ResultErrorMessages.OperationFailed);
        }
    }
}

public class GetShoppingCartItemEditModelByOrderItemIdMapper : Profile
{
    public GetShoppingCartItemEditModelByOrderItemIdMapper()
    {
        CreateMap<OrderItemOptionValue, RequestedSheetEditModel>();
        
        CreateMap<OrderItemOptionValue, ArticleOptionEditModel>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.OrderItemId, opt => opt.MapFrom(src => src.OrderItemId))
            .ForMember(dest => dest.ArticleId, opt => opt.MapFrom(src => src.OrderItem.ArticleId))
            .ForMember(dest => dest.ArticleName, opt => opt.MapFrom(src => src.OrderItem.Article.Name))
            .ForMember(dest => dest.OptionId, opt => opt.MapFrom(src => src.OptionId))
            .ForMember(dest => dest.OptionValueId, opt => opt.MapFrom(src => src.OptionValueId))
            .ForMember(dest => dest.OptionCode, opt => opt.MapFrom(src => src.Option.Code))
            .ForMember(dest => dest.OptionName, opt => opt.MapFrom(src => src.Option.Name))
            .ForMember(dest => dest.OptionNo, opt => opt.MapFrom(src => src.Option.No))
            .ForMember(dest => dest.UserSelectedOptionValueId, opt => opt.MapFrom(src => src.OptionValueId))
            .ForMember(dest => dest.Values, opt => opt.Ignore()) // We'll populate this separately if needed
            .ForMember(dest => dest.UseDefaultValue, opt => opt.Ignore());

        // Dodatkowe mapowanie Article -> OrderItemEditArticleModel z tłumaczeniami
        CreateMap<Article, OrderItemEditArticleModel>()
            .ForMember(dst => dst.TranslatedName, opt =>
                opt.MapFrom(src =>
                    src.Translations.Any(t => t.LanguageCode == CultureInfo.CurrentCulture.Name)
                        ? src.Translations.First(t => t.LanguageCode == CultureInfo.CurrentCulture.Name).Name
                        : src.Name));

        CreateMap<OrderItem, ShoppingCartItemEditModel>()
            .ForMember(dest => dest.OrderItemId, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.ArticleId, opt => opt.MapFrom(src => src.ArticleId))
            .ForMember(dest => dest.ArticleName, opt => opt.MapFrom(src => src.Article.Name))
            .ForMember(dest => dest.ArticleWeight, opt => opt.MapFrom(src => src.Article.UnitWeight))
            .ForMember(dest => dest.ArticleUnit,
                opt => opt.MapFrom(src => src.Article.Unit == null ? "" : src.Article.Unit.Name))
            .ForMember(dest => dest.ArticleType, opt => opt.MapFrom(src => src.Article.Type))
            .ForMember(dest => dest.ArticleBaseCode, opt => opt.MapFrom(src => src.Article.Code))
            .ForMember(dest => dest.Code, opt => opt.MapFrom(src => src.Article.Code))
            .ForMember(dest => dest.UnitWeight, opt => opt.MapFrom(src => src.Article.UnitWeight))
            .ForMember(dest => dest.Article, opt => opt.MapFrom(src => src.Article))
            .ForMember(dest => dest.Comments, opt => opt.MapFrom(src => src.Comments))
            .ForMember(dest => dest.DraftEditable, opt => opt.MapFrom(src => src.DraftEditable))
            .ForMember(dest => dest.DraftOriginalFileName, opt => opt.MapFrom(src => src.DraftOriginalFileName))
            .ForMember(dest => dest.DraftHashedFileName, opt => opt.MapFrom(src => src.DraftHashedFileName))
            .ForMember(dest => dest.DraftDrawingSource, opt => opt.MapFrom(src => src.DraftDrawingSource))
            .ForMember(dest => dest.Quantity, opt => opt.MapFrom(src => src.Quantity))
            .ForMember(dest => dest.SumM2, opt => opt.MapFrom(src => src.SumM2))
            .ForMember(dest => dest.SumMb, opt => opt.MapFrom(src => src.SumMb))
            .ForMember(dest => dest.SumQuantity, opt => opt.MapFrom(src => src.SumQuantity))
            .ForMember(dest => dest.Weight, opt => opt.MapFrom(src => src.Weight))
            .ForMember(dest => dest.ProfileEditable, opt => opt.MapFrom(src => src.Article.ProfileEditable))
            .ForMember(dest => dest.RequestedSheets, opt => opt.MapFrom(src => src.RequestedSheets))
            .ForMember(dest => dest.OptionValues, opt => opt.MapFrom(src => src.OptionValues))
            ;

        CreateMap<RequestedOrderItemSheet, RequestedSheetEditModel>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.Quantity, opt => opt.MapFrom(src => src.Quantity))
            .ForMember(dest => dest.Length, opt => opt.MapFrom(src => src.Length))
            .ForMember(dest => dest.Width, opt => opt.MapFrom(src => src.Width))
            .ForMember(dest => dest.SplitN, opt => opt.MapFrom(src => src.SplitN))
            .ForMember(dest => dest.IsIncorrect, opt => opt.Ignore()) // To pole może wymagać specjalnej logiki
            .ForMember(dest => dest.OrderItemSheets, opt => opt.MapFrom(src => src.OrderItemSheets))
            ;
        
        CreateMap<OrderItemSheet, ArticleSheetEditModel>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.Quantity, opt => opt.MapFrom(src => src.Quantity))
            .ForMember(dest => dest.Length, opt => opt.MapFrom(src => src.Length))
            .ForMember(dest => dest.Width, opt => opt.MapFrom(src => src.Width))
            ;
    }
}