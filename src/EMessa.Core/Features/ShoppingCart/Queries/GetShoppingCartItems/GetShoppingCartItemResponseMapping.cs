using AutoMapper;
using EMessa.DAL.Entities.Orders;

namespace EMessa.Core.Features.ShoppingCart.Queries.GetShoppingCartItems
{
    public class GetShoppingCartItemResponseMapping : Profile
    {
        public GetShoppingCartItemResponseMapping()
        {
            CreateMap<OrderItem, GetShoppingCartOrderItemResponse>()
                .ForMember(dst => dst.ArticleId, opt => opt.MapFrom(ordItem => ordItem.ArticleId))
                .ForMember(dst => dst.ArticleName, opt => opt.MapFrom(ordItem => ordItem.Article.Name))
                .ForMember(dst => dst.ArticleType, opt => opt.MapFrom(ordItem => ordItem.Article.Type))
                .ForMember(dst => dst.Quantity, opt => opt.MapFrom(ordItem => ordItem.Quantity))
                .ForMember(dst => dst.OptionValues, opt => opt.MapFrom(ordItem => ordItem.OptionValues.OrderBy(x => x.Option.No).ToList()))
                .ForMember(dst => dst.UnitId, opt => opt.MapFrom(ordItem => ordItem.Article.ArticleUnitId))
                .ForMember(dst => dst.UnitName,
                    opt => opt.MapFrom(ordItem => (ordItem.Article != null && ordItem.Article.Unit != null) ? ordItem.Article.Unit.Name : ""))
                .ForMember(dst => dst.RequestedSheets, x => x.MapFrom(ordItem => ordItem.RequestedSheets))
                //.ForMember(dst => dst.Price, opt => opt.MapFrom(ordItem => ordItem.Price))
                .ForMember(dst => dst.SaleId, opt => opt.MapFrom(ordItem => ordItem.SaleId))
                ;
        }
    }
}