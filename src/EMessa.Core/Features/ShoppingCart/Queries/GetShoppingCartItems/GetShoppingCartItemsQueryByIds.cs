using MediatR;
using AutoMapper;
using EMessa.Base.Enums;
using Microsoft.EntityFrameworkCore;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.Core.Common.Results;
using Microsoft.Extensions.Logging;
using EMessa.Core.Features.Orders.Queries.Common;
using EMessa.Core.Features.Orders.ApiQueries.Common;

namespace EMessa.Core.Features.ShoppingCart.Queries.GetShoppingCartItems;

public record GetShoppingCartItemsQueryByIds(List<int> OrderItemIds)
    : IRequest<IResult<List<OrderItemResponse>>>;

public class GetShoppingCartItemsQueryByIdsHandler(
    IMapper mapper,
    IDbContextFactory<ApplicationDbContext> dbFactory,
    IAppStateService appStateService,
    ILogger<GetShoppingCartItemsQueryByIdsHandler> logger)
    : IRequestHandler<GetShoppingCartItemsQueryByIds, IResult<List<OrderItemResponse>>>
{
    public async Task<IResult<List<OrderItemResponse>>> Handle(
        GetShoppingCartItemsQueryByIds request,
        CancellationToken cancellationToken)
    {
        try
        {
            var loggedUser = appStateService.UserData;
            
            await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

            var filteredOrderItems = await dbContext
                .OrderItems
                .AsSplitQuery()
                .Include(x => x.Article).ThenInclude(x => x.Unit)
                .Include(x => x.OptionValues).ThenInclude(x => x.Option)
                .Include(x => x.OptionValues).ThenInclude(x => x.OptionValue)
                .Include(x => x.RequestedSheets).ThenInclude(x => x.OrderItemSheets)
                .Include(x => x.Order)
                .Where(x => request.OrderItemIds.Contains(x.Id)
                            && x.SelectedForOrder
                            && x.Order.Type == OrderType.ShoppingCart
                            && x.Order.CreatedById == loggedUser.UserProfileId)
                .ToListAsync(cancellationToken);

            var mappedOrderItems = mapper.Map<List<OrderItemResponse>>(filteredOrderItems);

            return await Result<List<OrderItemResponse>>.SuccessAsync(mappedOrderItems);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result<List<OrderItemResponse>>.FailAsync(
                "Wystąpił błąd podczas pobierania koszyka.");
        }
    }
}