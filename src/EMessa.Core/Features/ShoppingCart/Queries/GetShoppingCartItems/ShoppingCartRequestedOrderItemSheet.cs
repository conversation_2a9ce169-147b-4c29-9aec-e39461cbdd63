using EMessa.Core.Common;
using EMessa.DAL.Entities;
using EMessa.DAL.Entities.Orders;

namespace EMessa.Core.Features.ShoppingCart.Queries.GetShoppingCartItems;

public class ShoppingCartRequestedOrderItemSheet : AbstractArticleSheet, IEntity
{
    public int Id { get; set; }

    /// decimal Length { get; set; } // Requested, Dziedziczone
    /// decimal Width { get; set; } // Requested, Dziedziczone
    /// int Quantity { get; set; } // Requested, Dziedziczone
    public int? SplitN { get; set; } // Requested, can be null if not split
    
    public int OrderItemId { get; set; }
    public OrderItem OrderItem { get; set; } = null!;
    
    public List<OrderItemSheet> OrderItemSheets { get; set; } = [];

    public decimal SumQuantity => OrderItemSheets.Sum(x => x.Quantity);
    public decimal SumMb => OrderItemSheets.Sum(x => x.Mb);
    public decimal SumM2 => OrderItemSheets.Sum(x => x.M2);

    public decimal RoundSumMb => SystemRound.RoundingSums(SumMb);
    public decimal RoundSumM2 => SystemRound.RoundingSums(SumM2);
}