using AutoMapper;
using EMessa.DAL.Entities.Orders;

namespace EMessa.Core.Features.ShoppingCart.Queries.GetShoppingCartItems
{
    public class ShoppingCartItemOptionValueProfile : Profile
    {
        public ShoppingCartItemOptionValueProfile()
        {
            CreateMap<OrderItemOptionValue, ShoppingCartItemOptionValue>()
                .ForMember(x => x.OptionName, z => z.MapFrom(x => x.Option.Name))
                .ForMember(x => x.OptionCode, z => z.MapFrom(x => x.Option.Code))
                .ForMember(x => x.Value, z => z.MapFrom(x => x.OptionValue == null ? null : x.OptionValue.Value));
        }
    }
}