using MediatR;
using AutoMapper;
using EMessa.Base.Enums;
using Microsoft.EntityFrameworkCore;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.Core.Common.Results;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.ShoppingCart.Queries.GetShoppingCartItems;

public record GetShoppingCartOrderItemsQuery(int UserId) : IRequest<IResult<List<GetShoppingCartOrderItemResponse>>>;

public class GetShoppingCartItemsQueryHandler(
    IMapper mapper,
    IDbContextFactory<ApplicationDbContext> dbFactory,
    ILogger<GetShoppingCartItemsQueryHandler> logger)
    : IRequestHandler<GetShoppingCartOrderItemsQuery, IResult<List<GetShoppingCartOrderItemResponse>>>
{
    public async Task<IResult<List<GetShoppingCartOrderItemResponse>>> Handle(GetShoppingCartOrderItemsQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

            var cartOrder = await dbContext
                .Orders
                .AsSplitQuery()
                .Include(x => x.OrderItems).ThenInclude(x => x.Article).ThenInclude(x => x.Unit)
                .Include(x => x.OrderItems).ThenInclude(x => x.OptionValues).ThenInclude(x => x.Option)
                .Include(x => x.OrderItems).ThenInclude(x => x.OptionValues).ThenInclude(x => x.OptionValue)
                .Include(x => x.OrderItems).ThenInclude(x => x.RequestedSheets).ThenInclude(x => x.OrderItemSheets)
                .FirstOrDefaultAsync(x => 
                        x.Type == OrderType.ShoppingCart 
                        && x.CreatedById == request.UserId,
                    cancellationToken);

            if (cartOrder == null)
            {
                return await Result<List<GetShoppingCartOrderItemResponse>>.SuccessAsync(
                    new List<GetShoppingCartOrderItemResponse>()
                );
            }

            var mappedOrderItems = mapper.Map<List<GetShoppingCartOrderItemResponse>>(cartOrder.OrderItems);

            return await Result<List<GetShoppingCartOrderItemResponse>>.SuccessAsync(mappedOrderItems);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result<List<GetShoppingCartOrderItemResponse>>.FailAsync("Wystąpił błąd podczas pobierania koszyka.");
        }
    }
}