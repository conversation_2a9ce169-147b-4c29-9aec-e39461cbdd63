using MediatR;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using EMessa.Core.Localizer;
using EMessa.DAL.Entities.Articles;
using EMessa.Core.Features.ArticleOptionValuesRestrictions.Services;
using EMessa.Core.Features.ShoppingCart.Models;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.Core.Common.Results;

namespace EMessa.Core.Features.ShoppingCart.Queries.GetAddNewItemModel;

public record GetRestrictedArticleOptionsQuery(
    int ArticleId,
    int OrderItemId,
    bool IsSaleArticle,
    int SelectedOptionId, // -1, Inicjalizacja
    int? SelectedOptionValueId,
    List<ArticleOptionEditModel> OptionValues)
    : IRequest<IResult<List<ArticleOptionEditModel>>>;

public class GetRestrictedArticleOptionsQueryHandler(
    IDbContextFactory<ApplicationDbContext> dbFactory,
    ILogger<ArticleOptionEditModel> logger,
    IOptionRestrictionsService optionRestrictionsService)
    : IRequestHandler<GetRestrictedArticleOptionsQuery,
        IResult<List<ArticleOptionEditModel>>>
{
    public async Task<IResult<List<ArticleOptionEditModel>>> Handle(
        GetRestrictedArticleOptionsQuery request,
        CancellationToken cancellationToken)
    {
        await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);
        
        try
        {
            // Czy jest taki produkt?
            var article = await dbContext.Articles.AsNoTracking()
                .SingleOrDefaultAsync(x => x.Id == request.ArticleId, cancellationToken);
            if (article == null)
            {
                logger.LogError("Nie odnaleziono artykułu id {ArticleId}.", request.ArticleId);
                return await Result<List<ArticleOptionEditModel>>
                    .FailAsync("Nie odnaleziono artykułu.".Tr());
            }

            // Cała logika opcji w jednym wywołaniu
            var processedOptions = await optionRestrictionsService.ProcessArticleOptionsAsync(
                request.ArticleId,
                request.SelectedOptionId,
                request.SelectedOptionValueId,
                request.OptionValues,
                request.IsSaleArticle,
                cancellationToken);

            // Ustaw OrderItemId dla wszystkich opcji
            processedOptions.ForEach(x => x.OrderItemId = request.OrderItemId);

            return await Result<List<ArticleOptionEditModel>>.SuccessAsync(processedOptions);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result<List<ArticleOptionEditModel>>.FailAsync("Nieokreślony błąd operacji.".Tr());
        }
    }
}