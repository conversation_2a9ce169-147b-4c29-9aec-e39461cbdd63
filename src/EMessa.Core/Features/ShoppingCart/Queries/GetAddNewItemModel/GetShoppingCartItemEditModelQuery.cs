using AutoMapper;
using EMessa.DAL.Entities.ArticleOptions;
using EMessa.DAL.Entities.Articles;
using EMessa.Core.Features.ShoppingCart.Models;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Globalization;
using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.ShoppingCart.Queries.GetAddNewItemModel;

public class ShoppingCartItemEditModel : BaseOrderItemEditModel;

public record GetShoppingCartItemEditModelQuery(int ArticleId)
    : IRequest<IResult<ShoppingCartItemEditModel>>;

public class GetShoppingCartItemEditModelQueryHandler(
    IMapper mapper,
    ILogger<GetShoppingCartItemEditModelQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbFactory)
    : IRequestHandler<GetShoppingCartItemEditModelQuery, IResult<ShoppingCartItemEditModel>>
{
    public async Task<IResult<ShoppingCartItemEditModel>> Handle(
        GetShoppingCartItemEditModelQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

            var article = await dbContext
                .Articles
                .Include(x => x.Options).ThenInclude(x => x.Option).ThenInclude(x => x.Values)
                .Include(x => x.Unit)
                .FirstOrDefaultAsync(x => x.Id == request.ArticleId, cancellationToken);

            if (article == null)
            {
                logger.LogError("Nie odnaleziono artykułu");
                return await Result<ShoppingCartItemEditModel>.FailAsync("Nie odnaleziono artykułu".Tr());
            }

            var mappedArticle = mapper.Map<ShoppingCartItemEditModel>(article);
            mappedArticle.OptionValues = [];

            return await Result<ShoppingCartItemEditModel>.SuccessAsync(mappedArticle);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result<ShoppingCartItemEditModel>.FailAsync(ResultErrorMessages.OperationFailed);
        }
    }
}

public class ShoppingCartItemEditModelProfile : Profile
{
    public ShoppingCartItemEditModelProfile()
    {
        CreateMap<Article, ShoppingCartItemEditModel>()
            .ForMember(x => x.ArticleId, x => x.MapFrom(a => a.Id))
            .ForMember(x => x.ArticleName, x => x.MapFrom(a => a.Name))
            .ForMember(x => x.ArticleWeight, x => x.MapFrom(a => a.UnitWeight))
            .ForMember(x => x.ArticleUnit, x => x.MapFrom(a => a.Unit == null ? "" : a.Unit.Name))
            .ForMember(x => x.ArticleType, x => x.MapFrom(a => a.Type))
            .ForMember(x => x.ArticleBaseCode, x => x.MapFrom(a => a.Code))
            .ForMember(x => x.Code, x => x.MapFrom(a => a.Code))
            .ForMember(x => x.UnitWeight, x => x.MapFrom(a => a.UnitWeight))
            .ForMember(x => x.Article, x => x.MapFrom(a => a))
            ;
    }
}

public class ArticleOptionProfile : Profile
{
    public ArticleOptionProfile()
    {
        CreateMap<ArticleOption, ArticleOptionEditModel>()
            .ForMember(d => d.OriginalName, opt => opt.MapFrom(s => s.Option.Name))
            .ForMember(d => d.OptionName,
                opt => opt.MapFrom(s =>
                    s.Option.Translations.Any(t => t.LanguageCode == CultureInfo.CurrentCulture.Name)
                        ? s.Option.Translations.First(t => t.LanguageCode == CultureInfo.CurrentCulture.Name).Name
                        : s.Option.Name))
            .ForMember(d => d.OptionCode, opt => opt.MapFrom(s => s.Option.Code))
            .ForMember(d => d.Values, opt => opt.MapFrom(s => s.Values))
            .ForMember(d => d.UseDefaultValue, opt => opt.MapFrom(s => s.UseDefaultValue))
            ;
    }
}

public class ArticleOptionValueProfile : Profile
{
    public ArticleOptionValueProfile()
    {
        CreateMap<ArticleOptionValue, ArticleOptionValueEditModel>()
            .ForMember(x => x.Id, opt => opt.MapFrom(x => x.Id))
            .ForMember(x => x.OptionId, opt => opt.MapFrom(x => x.OptionValue.Id))
            //.ForMember(x => x.OptionValueId, opt => opt.MapFrom(x => x.OptionValueId))
            .ForMember(x => x.EmbossZoneAddition, opt => opt.MapFrom(src => src.EmbossZoneAddition))
            .ForMember(x => x.OriginalValue, opt => opt.MapFrom(x => x.OptionValue.Value))
            .ForMember(x => x.Code, opt => opt.MapFrom(x => x.OptionValue.Code))
            .ForMember(x => x.Value,
                x => x.MapFrom(z =>
                    z.OptionValue.Translations.FirstOrDefault(x => x.LanguageCode == CultureInfo.CurrentCulture.Name) ==
                    null
                        ? z.OptionValue.Value
                        : z.OptionValue.Translations.First(x => x.LanguageCode == CultureInfo.CurrentCulture.Name)
                            .Value))
            .ForMember(x => x.IsDefault, x => x.MapFrom(z => z.IsDefault ?? false))
            .ForMember(x => x.OriginalValueInfo, opt => opt.MapFrom(x => x.OptionValue.ValueInfo))
            .ForMember(x => x.ValueInfo,
                x => x.MapFrom(z =>
                    z.OptionValue.Translations.FirstOrDefault(x => x.LanguageCode == CultureInfo.CurrentCulture.Name) ==
                    null
                        ? z.OptionValue.ValueInfo
                        : z.OptionValue.Translations.First(x => x.LanguageCode == CultureInfo.CurrentCulture.Name)
                            .ValueInfo))
            ;
    }
}