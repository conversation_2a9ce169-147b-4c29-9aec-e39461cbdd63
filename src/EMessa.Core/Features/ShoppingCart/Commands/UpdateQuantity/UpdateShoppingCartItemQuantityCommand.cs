using EMessa.Core.Common;
using EMessa.Core.Common.Results;
using EMessa.Core.Features.ShoppingCart.Notifications.ShoppingCartChanged;
using EMessa.Core.Interfaces;
using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.ShoppingCart.Commands.UpdateQuantity;

public record UpdateShoppingCartItemQuantityCommand(int ShoppingCartItemId, decimal Quantity) 
    : IRequest<IResult>;

public class UpdateShoppingCartItemQuantityCommandHandler(
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IMediator mediator,
    ILogger<UpdateShoppingCartItemQuantityCommandHandler> logger)
    : ShoppingCartChangedNotificationBase(mediator), IRequestHandler<UpdateShoppingCartItemQuantityCommand, IResult>
{
    public async Task<IResult> Handle(UpdateShoppingCartItemQuantityCommand request,
        CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var shoppingCartItem = await dbContext
                .OrderItems
                .FirstOrDefaultAsync(x => x.Id == request.ShoppingCartItemId, cancellationToken);
            if (shoppingCartItem == null)
            {
                logger.LogError("Aktualizacja ilości produktu w koszyku, nie znaleziono produktu".Tr());
                return await Result.FailAsync("Nie znaleziono produktu".Tr());
            }

            shoppingCartItem.Quantity = request.Quantity;
            
            dbContext.Update(shoppingCartItem);
            
            await dbContext.SaveChangesAsync(cancellationToken);
            
            await NotifyShoppingCartChangedAsync(cancellationToken);
            
            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}