using EMessa.Core.Common.Results;
using EMessa.Core.Features.ShoppingCart.Notifications.ShoppingCartChanged;
using EMessa.Core.Interfaces;
using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.ShoppingCart.Commands.UpdateSelectedForOrder;

public record UpdateSelectedForOrderCommand(int ShoppingCartOrderItemId, bool SelectedForOrder)
    : IRequest<IResult>;

public class UpdateSelectedForOrderCommandHandler(
    ILogger<UpdateSelectedForOrderCommandHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IMediator mediator)
    : ShoppingCartChangedNotificationBase(mediator), IRequestHandler<UpdateSelectedForOrderCommand, IResult>
{
    public async Task<IResult> Handle(UpdateSelectedForOrderCommand request, CancellationToken cancellationToken)
    {
        await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

        try
        {
            var shoppingCartItem = await dbContext
                .OrderItems
                .AsTracking()
                .FirstOrDefaultAsync(x => x.Id == request.ShoppingCartOrderItemId, cancellationToken: cancellationToken);
            if (shoppingCartItem == null)
            {
                logger.LogError("Aktualizacja wyboru produktu w koszyku, nie znaleziono produktu".Tr());
                return await Result.FailAsync("Nie znaleziono produktu".Tr());
            }

            shoppingCartItem.SelectedForOrder = request.SelectedForOrder;
            dbContext.Update(shoppingCartItem);

            await dbContext.SaveChangesAsync(cancellationToken);

            await NotifyShoppingCartChangedAsync(cancellationToken);

            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}