using AutoMapper;
using EMessa.DAL.Entities.Orders;
using EMessa.Core.Localizer;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.Core.Common.Results;
using EMessa.Core.Features.ShoppingCart.Notifications.ShoppingCartChanged;
using EMessa.Base.Enums;

namespace EMessa.Core.Features.ShoppingCart.Commands.AddItem;

public class AddItemToShoppingCartCommand(int articleId, int customerId, int userId) : IRequest<IResult>
{
    public int ArticleId { get; set; } = articleId;
    public int CustomerId { get; init; } = customerId;
    public int UserProfileId { get; init; } = userId;
}

public class AddItemToShoppingCartCommandHandler(
    ILogger<AddItemToShoppingCartCommandHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    I<PERSON><PERSON>per mapper,
    IMediator mediator)
    : ShoppingCartChangedNotificationBase(mediator), IRequestHandler<AddItemToShoppingCartCommand, IResult>
{
    public async Task<IResult> Handle(AddItemToShoppingCartCommand request, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);
            await using var transaction = await dbContext.Database.BeginTransactionAsync(cancellationToken);

            var customer = await dbContext.Customers.SingleOrDefaultAsync(x => x.Id == request.CustomerId,
                cancellationToken: cancellationToken);
            if (customer == null)
            {
                logger.LogError("Błąd podczas dodawania produktu do koszyka, nie znaleziono klienta.".Tr());
                return await Result.FailAsync("Nie znaleziono klienta.".Tr());
            }

            var branch = await dbContext.UserBranches
                .FirstOrDefaultAsync(x => x.UserProfileId == request.UserProfileId,
                    cancellationToken: cancellationToken);
            if (branch == null)
            {
                branch = await dbContext.UserBranches
                    .FirstOrDefaultAsync(x => x.Id == customer.BranchId,
                        cancellationToken: cancellationToken);
                if (branch == null)
                {
                    logger.LogError("Błąd podczas dodawania produktu do koszyka, nie znaleziono oddziału.".Tr());
                    return await Result.FailAsync("Nie znaleziono oddziału.".Tr());
                }
            }

            var customerLocalization = await dbContext.CustomersLocalizations.FirstOrDefaultAsync(
                x => x.IsDefault == true && x.CustomerId == customer.Id, cancellationToken: cancellationToken);
            if (customerLocalization == null)
            {
                logger.LogError("Błąd podczas dodawania produktu do koszyka, nie znaleziono adresu.".Tr());
                return await Result.FailAsync("Nie znaleziono adresu.".Tr());
            }


            var shoppingCart = await dbContext.Orders.SingleOrDefaultAsync(
                x => x.Type == Base.Enums.OrderType.ShoppingCart && x.CustomerId == request.CustomerId &&
                     x.CreatedById == request.UserProfileId, cancellationToken: cancellationToken);
            if (shoppingCart == null)
            {
                var newShoppingcart = new Order()
                {
                    Type = Base.Enums.OrderType.ShoppingCart,
                    No = "",

                    CustomerId = customer.Id,
                    CustomerLocalizationId = customerLocalization.Id,

                    CreatedById = request.UserProfileId,

                    BranchId = branch.BranchId,

                    Status = Base.Enums.OrderStatus.New
                };

                var result = await AddShoppingCart(newShoppingcart, cancellationToken);
                if (!result.Succeeded) return await Result.FailAsync(result.Messages);

                //Wydaje mi się że ponowne sprawdzenie czy dodany koszyk istnieje jest w tym miejscu konieczne(do weryfikacji)
                shoppingCart = await dbContext.Orders.SingleOrDefaultAsync(x => x.Id == result.Data,
                    cancellationToken: cancellationToken);
                if (shoppingCart == null)
                {
                    logger.LogError("Błąd dodawania produktu do koszyka, nie znaleziono koszyka.".Tr());
                    return await Result.FailAsync("Nie znaleziono koszyka".Tr());
                }
            }


            shoppingCart.EditedById = request.UserProfileId;

            var editResult = await EditShoppingCart(shoppingCart, cancellationToken);
            if (!editResult.Succeeded) return await Result.FailAsync(editResult.Messages);

            var article = await dbContext.Articles
                .SingleOrDefaultAsync(x => x.Id == request.ArticleId,
                    cancellationToken: cancellationToken);
            if (article == null)
            {
                logger.LogError("Błąd dodawania produktu do koszyka, nie znaleziono produktu.".Tr());
                return await Result.FailAsync("Nie znaleziono produktu.".Tr());
            }

            var orderItem = await dbContext.OrderItems
                .SingleOrDefaultAsync(
                    x => x.ArticleId == article.Id && x.OrderId == shoppingCart.Id,
                    cancellationToken: cancellationToken);
            if (orderItem == null)
            {
                var newOrderItem = new OrderItem()
                {
                    OrderId = shoppingCart.Id,
                    ArticleId = article.Id,
                    Quantity = 1,
                    Weight = article.UnitWeight
                };

                orderItem = newOrderItem;

                var articleptions = await dbContext.ArticleOptions
                    .Where(x => x.ArticleId == orderItem.ArticleId)
                    .ToListAsync(cancellationToken: cancellationToken);
                foreach (var option in articleptions)
                {
                    var newItemOption = mapper.Map<OrderItemOptionValue>(option);
                    newItemOption.OrderItemId = shoppingCart.Id;
                    orderItem.OptionValues.Add(newItemOption);
                }

                await dbContext.OrderItems.AddAsync(orderItem, cancellationToken);
            }
            else if (orderItem != null && article.Type != ArticleType.Complex)
            {
                var increaseResult = await IncreaseItemQuantity(orderItem.Id, cancellationToken);
                if (!increaseResult.Succeeded) return await Result.FailAsync(increaseResult.Messages);
            }

            await dbContext.SaveChangesAsync(cancellationToken);
            await transaction.CommitAsync(cancellationToken);

            await NotifyShoppingCartChangedAsync(cancellationToken);

            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }

    private async Task<IResult<int>> AddShoppingCart(Order order, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var shoppingCart = await dbContext
                .Orders
                .SingleOrDefaultAsync(x =>
                        x.Type == Base.Enums.OrderType.ShoppingCart
                        && x.CustomerId == order.CustomerId
                        && x.CreatedById == order.CreatedById,
                    cancellationToken: cancellationToken);
            if (shoppingCart != null)
            {
                logger.LogError("Błąd podczas dodawania produktu do koszyka, tworzenie koszyka nie powiodło się.".Tr());
                return await Result<int>.FailAsync("Utworzenie koszyka nie powiodło się.".Tr());
            }

            await dbContext.Orders.AddAsync(order, cancellationToken);
            await dbContext.SaveChangesAsync(cancellationToken);

            return await Result<int>.SuccessAsync(order.Id);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result<int>.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }

    private async Task<IResult> EditShoppingCart(Order order, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var shoppingCart = dbContext.Orders.SingleOrDefault(x => x.Id == order.Id);
            if (shoppingCart == null)
            {
                logger.LogError("Błąd podczas dodawania produktu do koszyka, edycja koszyka nie powiodło się.".Tr());
                return await Result.FailAsync("Edycja koszyka nie powiodło się.".Tr());
            }

            shoppingCart.EditedById = order.EditedById;
            shoppingCart.LastEditDate = DateTime.UtcNow;

            dbContext.Orders.Update(shoppingCart);

            await dbContext.SaveChangesAsync(cancellationToken);

            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger?.LogError(ex, ex.Message);
            return await Result.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }

    private async Task<IResult> IncreaseItemQuantity(int itemId, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var shoppingCartItem =
                await dbContext.OrderItems.SingleOrDefaultAsync(x => x.Id == itemId,
                    cancellationToken: cancellationToken);
            if (shoppingCartItem == null)
            {
                logger.LogError("Błąd aktualizacji ilości produktu w koszyku, nie znaleziono produktu w koszyku.".Tr());
                return await Result.FailAsync("Nie znaleziono produktu w koszyku.".Tr());
            }

            var newQuantity = shoppingCartItem.Quantity + 1;
            shoppingCartItem.Quantity = newQuantity;

            dbContext.OrderItems.Update(shoppingCartItem);

            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}
