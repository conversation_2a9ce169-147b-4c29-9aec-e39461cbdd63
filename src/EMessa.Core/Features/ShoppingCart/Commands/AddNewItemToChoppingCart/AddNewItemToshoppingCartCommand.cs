using AutoMapper;
using EMessa.Core.Common.Results;
using EMessa.Core.Features.ShoppingCart.Queries.GetAddNewItemModel;
using EMessa.Core.Interfaces;
using EMessa.Core.Services.Calculators;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Orders;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace EMessa.Core.Features.ShoppingCart.Commands.AddNewItemToChoppingCart
{
    public record AddNewItemToShoppingCartCommand(ShoppingCartItemEditModel CartItem) : IRequest<IResult>;

    public class AddNewItemToShoppingCartCommandHandler(
        IDbContextFactory<ApplicationDbContext> contextFactory,
        IMapper mapper,
        ISumsCalculatorService sumCalculator)
        : IRequestHandler<AddNewItemToShoppingCartCommand, IResult>
    {
        public async Task<IResult> Handle(AddNewItemToShoppingCartCommand request, CancellationToken cancellationToken)
        {
            await using var dbContext = await contextFactory.CreateDbContextAsync(cancellationToken);

            // var shoppingCart = dbContext.Orders.FirstOrDefault(x => x.Type == Base.Enums.OrderType.ShoppingCart);

            var orderItem = mapper.Map<OrderItem>(request.CartItem);
            sumCalculator.RecalculateOrderItem(orderItem, request.CartItem.ArticleWeight, request.CartItem.ArticleUnit);

            dbContext.OrderItems.Add(orderItem);

            await dbContext.SaveChangesAsync(cancellationToken);

            return await Result.SuccessAsync();
        }
    }

    public class ShoppingCartItemEditModelProfile : Profile
    {
        public ShoppingCartItemEditModelProfile()
        {
            CreateMap<ShoppingCartItemEditModel, OrderItem>()
                .ForMember(dest => dest.Id, opt => opt.Ignore()) // Don't map Id as it's already set
                .ForMember(dest => dest.Article, opt => opt.Ignore()) // We'll handle Article separately
                .ForMember(dest => dest.ArticleId, opt => opt.MapFrom(src => src.ArticleId))

                .ForMember(x => x.Index, opt => opt.MapFrom(x => x.NewIndex))
                .ForMember(x => x.ArticleId, opt => opt.MapFrom(x => x.ArticleId))


                .ForMember(x => x.Quantity, opt => opt.MapFrom(x => x.Quantity))
                //.ForMember(x => x.Weight, opt => opt.MapFrom(x => x.UnitWeight)) // 
                .ForMember(x => x.Comments, opt => opt.MapFrom(x => x.Comments))

                //.ForMember(x => x.SumM2, opt => opt.MapFrom(x => _sumsCalculatorService.Sum())

                .ForMember(x => x.OptionValues, opt => opt.MapFrom(x => x.OptionValues))
                .ForMember(x => x.RequestedSheets, opt => opt.MapFrom(x => x.RequestedSheets))
                ;
            
        }
    }
}
