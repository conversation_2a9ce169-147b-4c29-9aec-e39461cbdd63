using EMessa.Base.Enums;
using EMessa.Core.Localizer;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.Core.Common.Results;
using EMessa.Core.Features.ShoppingCart.Notifications.ShoppingCartChanged;

namespace EMessa.Core.Features.ShoppingCart.Commands.DeleteItem;

public record DeleteOrderItemFromShoppingCartCommand(int ShoppingCartOrderItemId) : IRequest<IResult>;

public class DeleteOrderItemFromShoppingCartCommandHandler(
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    ILogger<DeleteOrderItemFromShoppingCartCommandHandler> logger,
    IMediator mediator)
    : ShoppingCartChangedNotificationBase(mediator), IRequestHandler<DeleteOrderItemFromShoppingCartCommand, IResult>
{
    public async Task<IResult> Handle(DeleteOrderItemFromShoppingCartCommand request, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var item = await dbContext
                .OrderItems
                .SingleOrDefaultAsync(x => 
                    x.Id == request.ShoppingCartOrderItemId, cancellationToken: cancellationToken);
            
            if (item == null)
            {
                logger.LogError("Bład usuwania produktu, nie odnaleziono produktu.".Tr());
                return await Result.FailAsync("Nie odnaleziono produktu.".Tr());
            }

            var shoppingCart = await dbContext
                .Orders
                .SingleOrDefaultAsync(x =>
                        x.Id == item.OrderId
                        && x.Type == OrderType.ShoppingCart,
                    cancellationToken: cancellationToken);
            if (shoppingCart == null)
            {
                logger.LogError("Błąd usuwania produktu, nie odnaleziono koszyka".Tr());
                return await Result.FailAsync("Nie odnaleziono koszyka.".Tr());
            }

            dbContext.OrderItems.Remove(item);

            await dbContext.SaveChangesAsync(cancellationToken);

            await NotifyShoppingCartChangedAsync(cancellationToken);

            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex.Message, ex);
            return await Result.FailAsync("Nieokreślony błąd aplikacji".Tr());
        }
    }
}