using AutoMapper;
using EMessa.DAL.Entities.ArticleOptions;
using EMessa.DAL.Entities.Orders;

namespace EMessa.Core.Features.ShoppingCart.Commands.AddItemOption
{
    public class AddItemOptionToShoppingcartItemMapping : Profile
    {
       public AddItemOptionToShoppingcartItemMapping() 
        {
            CreateMap<ArticleOption, OrderItemOptionValue>()
                .ForMember(x => x.Option, opt => opt.MapFrom(s => s.Option))
                .ForMember(x => x.OptionId, opt => opt.MapFrom(s => s.OptionId))
                .ForMember(x => x.Id, opt => opt.Ignore());
        }
    }
}
