using EMessa.Core.Features.Sales.Queries.CommonResponses;
using EMessa.Core.Interfaces;
using EMessa.DAL.Entities;
using EMessa.Base.Enums;

namespace EMessa.Core.Features.ShoppingCart.Models;

public class BaseOrderItemEditModel : AbstractOrderItemEditModel, IBaseOrderItemEditModel
{
    public int Id { get; init; }
    public OrderItemEditArticleModel Article { get; set; } = null!;
}

public class OrderItemEditArticleModel //dane config prod.
{
    public int Id { get; set; }
    public ArticleType Type { get; set; }
    public string Name { get; set; } = string.Empty;
    public string TranslatedName { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public bool IsDeleted { get; set; } = false;
    public bool IsSplitteable { get; set; }
    public bool IsSplittableNParts { get; set; }
    public bool ProfileEditable { get; set; }
    public string TooLongSheet { get; set; } = string.Empty;

    public bool QuantityEditable { get; set; } = true;
    public int DefaultQuantity { get; set; }

    public bool LengthEditable { get; set; } = true;
    public decimal DefaultLength { get; set; }

    public bool WidthEditable { get; set; } = true;
    public decimal DefaultWidth { get; set; }
    
    public bool HasAvailableSales { get; set; }
}

public abstract class AbstractOrderItemEditModel
{
    public int OrderItemId { get; set; }
    public int ArticleId { get; set; }
    public string ArticleName { get; set; } = null!;
    public ArticleType ArticleType { get; set; }

    public decimal Quantity { get; set; }

    public List<ArticleOptionEditModel> OptionValues { get; set; } = [];
    public List<RequestedSheetEditModel> RequestedSheets { get; set; } = [];
    public List<ArticleSheetEditModel> ViewOrderItemSheets { get; set; } = [];

    public string Code { get; set; } = null!;

    // base article for own products
    public string ArticleBaseCode { get; set; } = null!;

    //waga jednostkowa
    public string UnitWeight { get; set; } = null!;
    public string ArticleUnit { get; set; } = string.Empty;
    public decimal ArticleWeight { get; set; }

    public string Comments { get; set; } = "";

    public bool ProfileEditable { get; set; }
    public bool RequireProfile { get; set; }

    public decimal SumM2 { get; set; }
    public decimal SumMb { get; set; }
    public int SumQuantity { get; set; }
    public decimal Weight { get; set; }

    public bool DraftEditable { get; set; }
    public string? DraftOriginalFileName { get; set; }
    public string? DraftHashedFileName { get; set; }
    public string? DraftDrawingSource { get; set; }

    public bool IsSaleArticle { get; set; }
    public SaleArticleResponse? SaleArticle { get; set; }
    public decimal SaleAvailableLength { get; set; }
    public decimal SaleAvailableWeight { get; set; }

    public string NewIndex =>
        OptionValues.Any(x => x.OptionValueId == null)
            ? ""
            : string.Join("-", new[] { Code }.Concat(OptionValues.OrderBy(x => x.OptionNo)
                .Select(x => x.ValueCode)).ToList());
}

public class ArticleOptionEditModel
{
    public int Id { get; set; }
    public int OrderItemId { get; set; }
    public int ArticleId { get; set; }
    public string ArticleName { get; set; } = null!;

    /// <summary>
    /// Option.Id / Opcja produktu
    /// </summary>
    public int OptionId { get; set; }

    /// <summary>
    /// Selected value of option or null
    /// </summary>
    public int? OptionValueId { get; set; }

    /// <summary>
    /// OptionValue.Id / Wartość opcji 
    /// </summary>
    /// <remarks>OrderBy (Lp)</remarks>
    public int OptionNo { get; set; }

    public string OptionCode { get; set; } = null!;
    public string OptionName { get; set; } = null!;

    public string OriginalName { get; set; } = null!;

    public string? ValueName { get; set; }
    public string? ValueCode { get; set; }

    public List<ArticleOptionValueEditModel> Values { get; set; } = [];

    public bool UseDefaultValue { get; set; }
    public int? UserSelectedOptionValueId { get; set; }
}

public class ArticleOptionValueEditModel
{
    /// <summary>
    /// Id wartości opcji
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Id opcji
    /// </summary>
    public int OptionId { get; set; }

    public int OptionValueId { get; set; }
    /// <summary>
    /// Numer kolejny
    /// </summary>
    public int No { get; set; }

    /// <summary>
    /// Wartość przetłumaczona
    /// </summary>
    public string Value { get; set; } = null!;

    public string Code { get; set; } = null!;

    /// <summary>
    /// Oryginalna wartość
    /// </summary>
    public string OriginalValue { get; set; } = null!;

    public string ValueInfo { get; set; } = null!;
    public string OriginalValueInfo { get; set; } = null!;
    public bool IsDefault { get; set; }
    public decimal EmbossZoneAddition { get; set; } = 0;
}

public class ArticleSheetEditModel : AbstractArticleSheet
{
    public int Id { get; set; }
    public int No { get; set; }
    public RequestedSheetEditModel RequestedSheet { get; set; } = new();
}

public class RequestedSheetEditModel : AbstractArticleSheet
{
    public int Id { get; set; }
    public int No { get; set; }
    public bool IsIncorrect { get; set; }
    public int? SplitN { get; set; }
    public List<ArticleSheetEditModel> OrderItemSheets { get; set; } = [];

    public ArticleSheetEditModel ToArticleSheet() => new()
    {
        Quantity = Quantity,
        Length = Length,
        Width = Width
    };
}