using EMessa.Core.Exceptions;
using EMessa.Core.Expections;

namespace EMessa.Core.Features.OrderItems.Exceptions;

public class SavePngException : ListExceptions
{
    public SavePngException(List<string> messages) : base(string.Join(", ", messages))
    {
        Messages = [..messages];
    }

    public SavePngException(string? message) : base(message)
    {
    }

    public SavePngException(string? message, Exception? innerException) : base(message, innerException)
    {
    }
}