using EMessa.Core.Exceptions;
using EMessa.Core.Expections;

namespace EMessa.Core.Features.OrderItems.Exceptions;

public class RemoveFileHashException : ListExceptions
{
    public RemoveFileHashException(List<string> messages) : base(string.Join(", ", messages))
    {
        Messages = [..messages];
    }

    public RemoveFileHashException(string? message) : base(message)
    {
    }

    public RemoveFileHashException(string? message, Exception? innerException) : base(message, innerException)
    {
    }
}