using AutoMapper;
using EMessa.Core.Features.Articles.Queries.GetArticleForValidation;
using EMessa.Core.Features.OrderItems.Commands.AddOrderItemToShoppingCart;
using EMessa.Core.Features.OrderItems.Commands.ValidateSheet;
using EMessa.Core.Features.ShoppingCart.Queries.GetAddNewItemModel;

namespace EMessa.Core.Features.OrderItems.Mappings;

public class ArticleForValidationResponseProfile : Profile
{
    public ArticleForValidationResponseProfile()
    {
        CreateMap<ArticleForValidationResponse, ArticleSheetValidationModel>();
    }
}

public class ShoppingCartItemEditModelProfile : Profile
{
    public ShoppingCartItemEditModelProfile()
    {
        CreateMap<ShoppingCartItemEditModel, OrderItemEditModel>();
    }
}
