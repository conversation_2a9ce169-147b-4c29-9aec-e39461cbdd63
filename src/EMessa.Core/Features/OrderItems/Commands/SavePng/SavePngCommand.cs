using Microsoft.Extensions.Logging;
using EMessa.Core.Interfaces;
using EMessa.Core.Localizer;
using MediatR;
using Microsoft.Extensions.Configuration;
using EMessa.Core.Common.Results;

namespace EMessa.Core.Features.OrderItems.Commands.SavePng;

public record SavePngCommand : IRequest<IResult<SavePngResult>>
{
    public required byte[]? PngFile { get; init; }
    public required string FileName { get; init; }
}

public record SavePngResult
{
    public string? Error { get; init; }
    public string? OriginalFileName { get; init; }
    public string? HashFileName { get; init; }
}

public class SavePngHandler : IRequestHandler<SavePngCommand, IResult<SavePngResult>>
{
    private readonly ILogger<SavePngHandler> _logger;
    private readonly IRandomGeneratorService _randomGeneratorService;
    private readonly string _draftsPath;

    public SavePngHandler(
        ILogger<SavePngHandler> logger,
        IRandomGeneratorService randomGeneratorService,
        IConfiguration configuration)
    {
        _logger = logger;
        _randomGeneratorService = randomGeneratorService;

        var draftsPath = configuration["FileUploadSettings:DraftsPath"];
        if (draftsPath == null)
        {
            _logger.LogError("FileUploadSettings.DraftsPath is not set in appsettings.json");
            throw new InvalidOperationException(
                "FileUploadSettings.DraftsPath is not set in appsettings.json");
        }

        _draftsPath = draftsPath;
        if (!Directory.Exists(_draftsPath))
        {
            _logger.LogInformation("Creating directory: {DraftsPath}", _draftsPath);
            Directory.CreateDirectory(_draftsPath);
        }
    }

    public async Task<IResult<SavePngResult>> Handle(SavePngCommand request, CancellationToken cancellationToken)
    {
        try
        {
            if (request.PngFile == null || request.PngFile.Length == 0)
            {
                return await Result<SavePngResult>.FailAsync(new SavePngResult
                {
                    Error = "Brak pliku lub pusty plik".Tr()
                });
            }

            var originalFileName = Path.ChangeExtension(Path.GetFileName(request.FileName), ".png");
            var hashFileName = _randomGeneratorService.GenerateHashFileName(originalFileName, _draftsPath);

            var physicalPath = Path.Combine(_draftsPath, hashFileName);
            var directoryPath = Path.GetDirectoryName(physicalPath);

            if (directoryPath == null)
            {
                _logger.LogError("Error saving file, directoryPath is null");
                return await Result<SavePngResult>.FailAsync(new SavePngResult
                {
                    Error = "Błąd podczas zapisywania pliku".Tr()
                });
            }

            Directory.CreateDirectory(directoryPath);
            await File.WriteAllBytesAsync(physicalPath, request.PngFile, cancellationToken);

            var result = new SavePngResult
            {
                OriginalFileName = originalFileName,
                HashFileName = hashFileName
            };

            return await Result<SavePngResult>.SuccessAsync(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Błąd zapisu pliku PNG");
            return await Result<SavePngResult>.FailAsync(new SavePngResult
            {
                Error = "Wystąpił nieoczekiwany błąd".Tr()
            });
        }
    }
}