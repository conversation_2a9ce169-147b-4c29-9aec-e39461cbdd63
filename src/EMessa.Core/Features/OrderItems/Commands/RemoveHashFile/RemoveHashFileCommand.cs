using Microsoft.Extensions.Logging;
using EMessa.Core.Interfaces;
using EMessa.Core.Localizer;
using MediatR;
using Microsoft.Extensions.Configuration;
using EMessa.Core.Common.Results;

namespace EMessa.Core.Features.OrderItems.Commands.RemoveHashFile;

public record RemoveHashFileCommand : IRequest<IResult<string?>>
{
    public required string HashFileName { get; init; }
}

public class RemoveHashFileHandler : IRequestHandler<RemoveHashFileCommand, IResult<string?>>
{
    private readonly ILogger<RemoveHashFileHandler> _logger;
    private readonly IRandomGeneratorService _randomGeneratorService;
    private readonly string _draftsPath;

    public RemoveHashFileHandler(
        ILogger<RemoveHashFileHandler> logger,
        IRandomGeneratorService randomGeneratorService,
        IConfiguration configuration)
    {
        _logger = logger;
        _randomGeneratorService = randomGeneratorService;

        var draftsPath = configuration["FileUploadSettings:DraftsPath"];
        if (draftsPath == null)
        {
            _logger.LogError("FileUploadSettings.DraftsPath is not set in appsettings.json");
            throw new InvalidOperationException(
                "FileUploadSettings.DraftsPath is not set in appsettings.json");
        }

        _draftsPath = draftsPath;
        if (!Directory.Exists(_draftsPath))
        {
            _logger.LogInformation("Creating directory: {DraftsPath}", _draftsPath);
            Directory.CreateDirectory(_draftsPath);
        }
    }

    public async Task<IResult<string?>> Handle(RemoveHashFileCommand request, CancellationToken cancellationToken)
    {
        try
        {
            if (string.IsNullOrEmpty(request.HashFileName))
            {
                _logger.LogError("Brak nazwy pliku: {HashFileName}", request.HashFileName);
                return await Result<string?>.FailAsync("Brak nazwy pliku".Tr());
            }

            var physicalPath = Path.Combine(_draftsPath, request.HashFileName);
            if (!File.Exists(physicalPath))
            {
                _logger.LogError("Nie znaleziono pliku {HashFileName}", request.HashFileName);
                return await Result<string?>.FailAsync(
                    "Nie znaleziono pliku".Tr()
                );
            }

            File.Delete(physicalPath);
            
            return await Result<string?>.SuccessAsync();
        }
        catch (Exception ex)
        {
            const string errorMessage = "Błąd podczas usuwania pliku";
            _logger.LogError(ex, errorMessage);
            return await Result<string?>.FailAsync(errorMessage.Tr());
        }
    }
}