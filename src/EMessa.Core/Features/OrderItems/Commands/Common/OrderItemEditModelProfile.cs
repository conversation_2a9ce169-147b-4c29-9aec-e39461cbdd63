using AutoMapper;
using EMessa.Core.Features.OrderItems.Commands.AddOrderItemToShoppingCart;
using EMessa.Core.Features.ShoppingCart.Models;
using EMessa.DAL.Entities.Orders;

namespace EMessa.Core.Features.OrderItems.Commands.Common;

public class OrderItemEditModelProfile : Profile
{
    public OrderItemEditModelProfile()
    {
        // Mapowanie dla OrderItemEditModel -> OrderItem
        CreateMap<OrderItemEditModel, OrderItem>();
    
        // Mapowanie dla RequestedSheetEditModel -> RequestedOrderItemSheet
        CreateMap<RequestedSheetEditModel, RequestedOrderItemSheet>()
            .ForMember(dest => dest.OrderItemSheets, opt => 
                opt.MapFrom(src => src.OrderItemSheets));
    
        // Mapowanie dla ArticleSheetEditModel -> OrderItemRequestedSheetResponse
        CreateMap<ArticleSheetEditModel, OrderItemSheet>();
    
        // Mapowanie dla ArticleOptionEditModel -> OrderItemOptionValue, jeśli potrzebne
        //juz jest ponizej CreateMap<ArticleOptionEditModel, OrderItemOptionValue>();
    }
}

public class ArticleSheetEditModelProfile : Profile
{
    public ArticleSheetEditModelProfile()
    {
        CreateMap<ArticleSheetEditModel, RequestedOrderItemSheet>();
    }
}

public class ArticleOptionEditModelProfile : Profile
{
    public ArticleOptionEditModelProfile()
    {
        CreateMap<ArticleOptionEditModel, OrderItemOptionValue>();
    }
}
