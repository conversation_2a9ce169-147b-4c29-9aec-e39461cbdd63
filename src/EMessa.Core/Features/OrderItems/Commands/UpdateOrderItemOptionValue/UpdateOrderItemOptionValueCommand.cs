using EMessa.Core.Localizer;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.Core.Common.Results;

namespace EMessa.Core.Features.OrderItems.Commands.UpdateOrderItemOptionValue
{
    public class UpdateOrderItemOptionValueCommand : IRequest<IResult>
    {
        public int OrderItemOptionValueId { get; init; }

        public int ValueId { get; init; }

        public UpdateOrderItemOptionValueCommand(int valueId, int orderItemOptionValueId)
        {
            ValueId = valueId;
            OrderItemOptionValueId = orderItemOptionValueId;
        }
    }
    public class UpdateOrderItemOptionValueCommandHandler : IRequestHandler<UpdateOrderItemOptionValueCommand, IResult>
    {
        private readonly IDbContextFactory<ApplicationDbContext> _dbCofContextFactory;
        private readonly ILogger<UpdateOrderItemOptionValueCommandHandler> _logger;

        public UpdateOrderItemOptionValueCommandHandler(IDbContextFactory<ApplicationDbContext> dbCofContextFactory, ILogger<UpdateOrderItemOptionValueCommandHandler> logger)
        {
            _dbCofContextFactory = dbCofContextFactory;
            _logger = logger;
        }
        public async Task<IResult> Handle (UpdateOrderItemOptionValueCommand command, CancellationToken cancellationToken)
        {
            try
            {
                await using var dbContext = await _dbCofContextFactory.CreateDbContextAsync(cancellationToken);
                var orderItemOptionValue = await dbContext.OrderItemOptionValues.SingleOrDefaultAsync(x => x.Id == command.OrderItemOptionValueId);
                if (orderItemOptionValue == null) 
                {
                    _logger.LogError("Błąd ustawiania wartości opcji dla pozycji zamówienia. Nie znaleziono opcji w pozycji zamówienia.");
                    return await Result.FailAsync("Nie znaleziono opcji w pozycji zamówienia.".Tr());
                }
                var value = await dbContext.OptionValues.SingleOrDefaultAsync(x => x.Id == command.ValueId);
                if (value == null)
                {
                    _logger.LogError("Błąd ustawiania wartości opcji dla pozycji zamówienia. Nie znaleziono wartości opcji.");
                    return await Result.FailAsync("Nie znaleziono wartości opcji.".Tr());
                }
                orderItemOptionValue.OptionValueId = value.Id;
               
                dbContext.OrderItemOptionValues.Update(orderItemOptionValue);
                await dbContext.SaveChangesAsync();
                return await Result.SuccessAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message,ex);
                return await Result.FailAsync("Nieokreślony błąd operacji".Tr());
            }
        }
    }

}
