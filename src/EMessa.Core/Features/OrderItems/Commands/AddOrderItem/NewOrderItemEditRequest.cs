using EMessa.Base.Enums;

namespace EMessa.Core.Features.OrderItems.Commands.AddOrderItem
{
    public class NewOrderItemEditRequest
    {
        public ArticleType ArticleType { get; set; }
        public int ArticleId { get; set; }
        public string ArticleName { get; set; } = string.Empty;
        public decimal UnitWeight { get; set; }
        public int Quantity { get; set; }

        public List<NewOrderItemOptionValueEditModel> OptionValues { get; set; } = new ();
        public List<NewOrderItemSheetEditModel> Sheets { get; set; } = new ();

        public decimal SumM2 { get
            {
                return Sheets.Sum(x => x.M2);
            }
        }
        public decimal SumMb { get
            {
                return Sheets.Sum(x => x.Mb);
            }
        }

        public decimal Weight { get
            {
                return CalcWeight();
            }
        }

        private decimal CalcWeight()
        {
            return UnitWeight * SumM2;
        }
    }

    public class NewOrderItemSheetEditModel
    {
        public int Quantity { get; set; }
        public decimal Width { get; set; }
        public decimal Length { get; set; }
        public decimal M2 { get
            {
                return Width * Length * Quantity;
            }
        }

        public decimal Mb { get
            {
                return Length * Quantity;
            }
        }
    }
    public class NewOrderItemOptionValueEditModel
    {
        public NewOrderItemOptionValueEditModel(int optionId, string optionName)
        {
            OptionId = optionId;
            OptionName = optionName;
        }
        public int OptionId { get; set; }

        public string OptionName { get; set; } = null!;
        public int OptionValueId { get; set; }
        public string? OptionValue { get; set; }


    }
}
