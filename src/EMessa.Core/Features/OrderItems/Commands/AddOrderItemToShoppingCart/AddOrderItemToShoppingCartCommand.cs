using AutoMapper;
using EMessa.Base.Enums;
using EMessa.DAL.Entities.Orders;
using EMessa.Core.Features.ShoppingCart.Models;
using EMessa.Core.Localizer;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Users;
using EMessa.Core.Common.Results;

namespace EMessa.Core.Features.OrderItems.Commands.AddOrderItemToShoppingCart;

public class AddOrderItemToShoppingCartResponse;

public class OrderItemEditModel : AbstractOrderItemEditModel
{
    public int Id { get; set; }
}

public record AddOrderItemToShoppingCartCommand(OrderItemEditModel AddOrderItem)
    : IRequest<IResult<AddOrderItemToShoppingCartResponse>>;

public class AddOrderItemToShoppingCartCommandHandler(
    ILogger<AddOrderItemToShoppingCartCommandHandler> logger,
    IDbContextFactory<ApplicationDbContext> contextFactory,
    IMapper mapper,
    IAppStateService appStateService)
    : IRequestHandler<AddOrderItemToShoppingCartCommand, IResult<AddOrderItemToShoppingCartResponse>>
{
    public async Task<IResult<AddOrderItemToShoppingCartResponse>> Handle(AddOrderItemToShoppingCartCommand request,
        CancellationToken cancellationToken)
    {
        var orderItemEdit = request.AddOrderItem;

        if (orderItemEdit.OptionValues.Any(x => x.OptionValueId == null))
        {
            return await Result<AddOrderItemToShoppingCartResponse>.FailAsync("Brak określenia wartości opcji {0}".Tr()
                                                                              + string.Join(", ",
                                                                                  orderItemEdit.OptionValues
                                                                                      .Where(x => x.OptionValueId ==
                                                                                          null).Select(x =>
                                                                                          x.OptionName).ToList()));
        }

        if (orderItemEdit is { ArticleType: ArticleType.Complex, RequestedSheets.Count: 0 })
        {
            return await Result<AddOrderItemToShoppingCartResponse>.FailAsync("Brak wymiarów arkuszy".Tr());
        }

        var loggedUserProfileId = appStateService.GetUserProfileId();

        try
        {
            await using var dbContext = await contextFactory.CreateDbContextAsync(cancellationToken);

            var user = await GetUserProfile(dbContext, loggedUserProfileId, cancellationToken);
            if (user == null
                //|| user.UserBranches.Count == 0  //ask user K lub KM nie ma UserBranches, ale ma user.Customer.BranchId/Branch
                || user.Customer.Localisations.Count == 0)
            {
                return await Result<AddOrderItemToShoppingCartResponse>.FailAsync(
                    "Błąd pobrania danych użytkownika".Tr());
            }

            // Get shopping cart order (Get or create Order) //Koszyk
            var cartOrder = await GetCreateCartOrder(dbContext, user, cancellationToken);

            // Add new order item to shopping cart (Add OrderItem to Order)
            var addOrderItem = AddOrderItem(cartOrder, orderItemEdit);
            cartOrder.OrderItems.Add(addOrderItem);
            dbContext.OrderItems.Add(addOrderItem);

            // Save all changes
            await dbContext.SaveChangesAsync(cancellationToken);

            return await Result<AddOrderItemToShoppingCartResponse>.SuccessAsync(
                new AddOrderItemToShoppingCartResponse());
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result<AddOrderItemToShoppingCartResponse>.FailAsync("Nieokreślony błąd".Tr());
        }
    }

    private async Task<ApplicationUserProfile?> GetUserProfile(
        ApplicationDbContext dbContext,
        int userProfileId,
        CancellationToken cancellationToken)
    {
        return await dbContext.UserProfiles
            .Include(x => x.UserLocalizations).ThenInclude(x => x.CustomerLocalization)
            .Include(x => x.UserBranches).ThenInclude(x => x.Branch)
            .Include(x => x.Customer).ThenInclude(x => x.Localisations)
            .SingleOrDefaultAsync(x => x.Id == userProfileId, cancellationToken: cancellationToken);
    }

    private async Task<Order> GetCreateCartOrder(
        ApplicationDbContext dbContext,
        ApplicationUserProfile userProfile,
        CancellationToken cancellationToken)
    {
        var cartOrder = await dbContext
            .Orders
            .AsTracking()
            .Include(x => x.OrderItems)
            .SingleOrDefaultAsync(x =>
                    x.Type == OrderType.ShoppingCart
                    && x.CreatedById == userProfile.Id,
                cancellationToken);

        if (cartOrder == null)
        {
            // client localization or default customer localisation - ale to i tak będzie zmienione podczas generowania zamówienia
            // Id? var localisationId = user.UserLocalizations?.FirstOrDefault()?.Id ?? user.Customer.Localisations.First(x => x.IsDefault).Id;
            var localisationId = userProfile.UserLocalizations.FirstOrDefault()?.CustomerLocalizationId
                                 ?? userProfile.Customer.Localisations.First(x => x.IsDefault).Id;

            cartOrder = new Order
            {
                No = "",
                Type = OrderType.ShoppingCart, // <- Cart
                CustomerId = userProfile.CustomerId,
                // BranchId = userProfile.UserBranches.FirstOrDefault()?.BranchId ?? userProfile.Customer.BranchId,
                BranchId = userProfile.UserBranches.First().BranchId,
                CreatedDate = DateTime.Now,
                CreatedById = userProfile.Id,
                IsDeleted = false,
                CustomerLocalizationId = localisationId,
                Status = OrderStatus.New
            };

            dbContext.Orders.Add(cartOrder);
        }
        else
        {
            cartOrder.LastEditDate = DateTime.Now;
            cartOrder.EditedById = userProfile.Id;
        }

        return cartOrder;
    }

    private OrderItem AddOrderItem(
        Order cartOrder,
        OrderItemEditModel orderItemEdit)
    {
        var addOrderItem = mapper.Map<OrderItem>(orderItemEdit);

        addOrderItem.Id = 0;
        addOrderItem.OptionValues.ForEach(x => x.Id = 0);
        addOrderItem.RequestedSheets.ForEach(x =>
        {
            x.Id = 0;
            x.OrderItemSheets.ForEach(x => x.Id = 0);
        });
        addOrderItem.Index = orderItemEdit.NewIndex;
        addOrderItem.OrderId = cartOrder.Id;
        addOrderItem.SaleId = orderItemEdit.IsSaleArticle ? orderItemEdit.SaleArticle?.SaleId : null;

        //ask
        //todo
        // Cannot insert the value NULL into column 'CurrencyCode', table 'OrderItems'; column does not allow nulls. INSERT fails.
        // gdzie wybieramy walutę i BasePrice i FinalPrice
        addOrderItem.CurrencyCode = "PLN";
        addOrderItem.BasePrice = 0m;
        addOrderItem.FinalPrice = 0m;


        return addOrderItem;
    }
}