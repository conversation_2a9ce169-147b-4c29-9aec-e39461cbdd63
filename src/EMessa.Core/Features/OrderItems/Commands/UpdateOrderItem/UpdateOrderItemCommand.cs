using AutoMapper;
using EMessa.Base.Enums;
using EMessa.Core.Common.Results;
using EMessa.Core.Features.OrderItems.Commands.AddOrderItemToShoppingCart;
using EMessa.Core.Features.ShoppingCart.Models;
using EMessa.Core.Features.ShoppingCart.Queries.GetAddNewItemModel;
using EMessa.Core.Interfaces;
using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Orders;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.OrderItems.Commands.UpdateOrderItem;
//ask Walidujemy czy ten user moze edytowac ten orderItem?

public class UpdateOrderItemEditModel : BaseOrderItemEditModel;

//TODO trzeba by sprawdzać czy ten user może zmieniać ten orderItem, czyli czy jest jego wła<PERSON><PERSON><PERSON> lub czy to jest administrator/uprawniony edytor
public record UpdateOrderItemCommand(
    int OrderId,
    UpdateOrderItemEditModel UpdatedOrderItem)
    : IRequest<IResult>;

public class UpdateOrderItemCommandHandler(
    ILogger<AddOrderItemToShoppingCartCommandHandler> logger,
    IDbContextFactory<ApplicationDbContext> contextFactory,
    IMapper mapper)
    : IRequestHandler<UpdateOrderItemCommand, IResult>
{
    public async Task<IResult> Handle(UpdateOrderItemCommand request, CancellationToken cancellationToken)
    {
        var updatedOrderItem = request.UpdatedOrderItem;

        if (updatedOrderItem.OptionValues.Any(x => x.OptionValueId == null))
        {
            return await Result.FailAsync("Brak określenia wartości opcji {0}".Tr()
                                          + string.Join(", ",
                                              updatedOrderItem.OptionValues
                                                  .Where(x => x.OptionValueId == null)
                                                  .Select(x => x.OptionName).ToList()));
        }

        if (updatedOrderItem is { ArticleType: ArticleType.Complex, RequestedSheets.Count: 0 })
        {
            return await Result.FailAsync("Brak wymiarów arkuszy".Tr());
        }

        try
        {
            await using var dbContext = await contextFactory.CreateDbContextAsync(cancellationToken);
            
            var existingOrderItem = await dbContext
                .OrderItems
                .Include(x => x.Article)
                .ThenInclude(x => x.Options)
                .ThenInclude(x => x.Option)
                .ThenInclude(x => x.Values)
                .Include(x => x.RequestedSheets)
                .ThenInclude(x => x.OrderItemSheets)
                .Include(orderItem => orderItem.OptionValues)
                .ThenInclude(orderItemOptionValue => orderItemOptionValue.Option)
                .Include(orderItem => orderItem.OptionValues)
                .ThenInclude(orderItemOptionValue => orderItemOptionValue.OptionValue)
                .AsTracking()
                .AsSplitQuery()
                .FirstOrDefaultAsync(x => 
                    x.OrderId == request.OrderId && x.Id == request.UpdatedOrderItem.Id, cancellationToken);

            if (existingOrderItem == null)
            {
                logger.LogError("Nie odnaleziono przedmiotu zamówienia");
                return await Result.FailAsync("Nie odnaleziono przedmiotu zamówienia".Tr());
            }

            var updatedOrderItemMap = mapper.Map<OrderItem>(updatedOrderItem); // Wersja uaktualniona
            
            existingOrderItem.Comments = updatedOrderItemMap.Comments;
            existingOrderItem.DraftEditable = updatedOrderItemMap.DraftEditable;
            existingOrderItem.DraftDrawingSource = updatedOrderItemMap.DraftDrawingSource;
            existingOrderItem.DraftHashedFileName = updatedOrderItemMap.DraftHashedFileName;
            existingOrderItem.DraftOriginalFileName = updatedOrderItemMap.DraftOriginalFileName;
            existingOrderItem.Quantity = updatedOrderItemMap.Quantity;
            existingOrderItem.Index = updatedOrderItemMap.Index;
            existingOrderItem.SumM2 = updatedOrderItemMap.SumM2;
            existingOrderItem.SumMb = updatedOrderItemMap.SumMb;
            existingOrderItem.SumQuantity = updatedOrderItemMap.SumQuantity;
            existingOrderItem.Weight = updatedOrderItemMap.Weight;
            existingOrderItem.SelectedForOrder = updatedOrderItemMap.SelectedForOrder;

            UpdateRequestedSheets(dbContext, existingOrderItem, updatedOrderItemMap);
            UpdateOptionValues(dbContext, existingOrderItem, updatedOrderItemMap);

            dbContext.OrderItems.Update(existingOrderItem);

            await dbContext.SaveChangesAsync(cancellationToken);

            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }

    private void UpdateRequestedSheets(
        ApplicationDbContext dbContext,
        OrderItem existingOrderItem,
        OrderItem updatedOrderItem)
    {
        var existingOrderItemRequestedSheets = existingOrderItem.RequestedSheets;
        var updatedOrderItemRequestedSheets = updatedOrderItem.RequestedSheets;

        // Usuwanie existingOrderItemRequestedSheets, które nie istnieją w updatedOrderItem
        foreach (var existingSheet in existingOrderItemRequestedSheets.ToList())
        {
            if (updatedOrderItemRequestedSheets.Any(x => x.Id == existingSheet.Id))
                continue;
            dbContext.RequestedOrderItemSheets.Remove(existingSheet);
            existingOrderItemRequestedSheets.Remove(existingSheet);
        }

        // Dodaj nowe RequestedSheets (Id == 0)
        foreach (var newRequestedSheet in updatedOrderItemRequestedSheets.Where(x => x.Id == 0))
        {
            // Ustaw relację z OrderItem
            newRequestedSheet.OrderItemId = existingOrderItem.Id;

            dbContext.RequestedOrderItemSheets.Add(newRequestedSheet);
            existingOrderItemRequestedSheets.Add(newRequestedSheet);
        }

        // Dla istniejących RequestedSheets sprawdź czy trzeba zastąpić OrderItemSheets
        foreach (var updatedRequestedSheet in updatedOrderItemRequestedSheets.Where(x => x.Id > 0))
        {
            // Jeśli ma jakiekolwiek nowe OrderItemSheets, zastąp wszystkie
            if (!updatedRequestedSheet.OrderItemSheets.Any(x => x.Id == 0))
                continue;

            var existingRequestedSheet = existingOrderItemRequestedSheets
                .First(x => x.Id == updatedRequestedSheet.Id);

            // Usuń wszystkie stare OrderItemSheets (cascade delete załatwi resztę)
            dbContext.OrderItemSheets.RemoveRange(existingRequestedSheet.OrderItemSheets);

            // Dodaj wszystkie nowe
            foreach (var newSheet in updatedRequestedSheet.OrderItemSheets)
            {
                newSheet.RequestedOrderItemSheetId = existingRequestedSheet.Id;
                dbContext.OrderItemSheets.Add(newSheet);
            }
        }
    }

    private void UpdateOptionValues(
        ApplicationDbContext dbContext,
        OrderItem existingOrderItem,
        OrderItem updatedOrderItem)
    {
        var existingByOptionId = existingOrderItem.OptionValues
            .ToDictionary(x => x.OptionId, x => x);

        var updatedByOptionId = updatedOrderItem.OptionValues
            .Where(x => x.OptionValueId.HasValue)
            .ToDictionary(x => x.OptionId, x => x);

        foreach (var updatedOptionValue in updatedByOptionId.Values)
        {
            if (existingByOptionId.TryGetValue(updatedOptionValue.OptionId, out var existingOptionValue))
            {
                if (existingOptionValue.OrderItemId != updatedOptionValue.OrderItemId)
                {
                    throw new InvalidOperationException(
                        $"OrderItemId mismatch: existing '{existingOptionValue.OrderItemId}', updated '{updatedOptionValue.OrderItemId}'");
                }
                
                // Istnieje - sprawdź czy się zmieniło
                if (existingOptionValue.OptionValueId != updatedOptionValue.OptionValueId)
                {
                    // Zmieniło się - zaktualizuj tylko OptionValueId
                    existingOptionValue.OptionValueId = updatedOptionValue.OptionValueId;
                    dbContext.OrderItemOptionValues.Update(existingOptionValue);
                }

                // Usuń z słownika - został zrobiony. Pozostałe zostaną usunięte
                existingByOptionId.Remove(updatedOptionValue.OptionId);
            }
            else
            {
                // Nie istnieje - dodaj nowy
                var newOptionValue = new OrderItemOptionValue
                {
                    OrderItemId = existingOrderItem.Id,
                    OptionId = updatedOptionValue.OptionId,
                    OptionValueId = updatedOptionValue.OptionValueId
                };

                dbContext.OrderItemOptionValues.Add(newOptionValue);
                existingOrderItem.OptionValues.Add(newOptionValue);
            }
        }

        // Wszystkie pozostałe w słowniku to te, które należy usunąć
        foreach (var toRemove in existingByOptionId.Values)
        {
            dbContext.OrderItemOptionValues.Remove(toRemove);
            existingOrderItem.OptionValues.Remove(toRemove);
        }
    }
}