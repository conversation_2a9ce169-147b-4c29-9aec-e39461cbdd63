using EMessa.Core.Features.ShoppingCart.Models;

namespace EMessa.Core.Features.OrderItems.Commands.ValidateSheet;

public class ArticleSheetValidationResult
{
    public ArticleSheetValidationResultType Type { get; set; }
    public string Message { get; set; } = "";
    public List<ArticleSheetEditModel> ProposedSheets { get; set; } = [];
    public decimal TotalLength { get; set; } = 0;
    public bool DeformationWarning { get; set; } = false;
    public bool EmbossZoneError { get; set; } = false;
    public int? SplitN { get; set; }
}