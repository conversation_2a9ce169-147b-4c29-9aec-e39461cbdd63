using EMessa.Base.Enums;

namespace EMessa.Core.Features.OrderItems.Commands.ValidateSheet;

public class ArticleSheetValidationModel// : AbstractArticleSheet
{
    // article type
    public ArticleType Type { get; set; }
    public int ArticleId { get; set; }
    public bool WidthEditable { get; set; }
    public decimal DefaultWidth { get; set; }
    public decimal MinWidth { get; set; }
    public decimal MaxWidth { get; set; }
    public bool LengthEditable { get; set; }
    public decimal DefaultLength { get; set; }
    public decimal MinLength { get; set; }
    public decimal MaxLength { get; set; }
    public decimal MinThickness { get; set; }
    public decimal MaxThickness { get; set; }

    public bool QuantityEditable { get; set; } = true;
    public decimal DefaultQuantity { get; set; }
    public decimal MinQuantity { get; set; }
    public decimal MaxQuantity { get; set; }
    public decimal RequiredQuantity { get; set; }

    public bool IsSplitteable { get; set; }
    public bool IsSplittableNParts { get; set; }
    public decimal Module { get; set; } = 0;
    public bool HasModule => Module > 0;
    
    /// <summary>
    /// długoś<PERSON> zkładki
    /// </summary>
    public decimal OverlapLength { get; set; }
    /// <summary>
    /// długość zakazanej strefy przetłoczenia (w lewo od następnego pełnego modułu), np. 30mm
    /// </summary>
    public decimal EmbossZoneLength { get; set; }
    public decimal AdditionalEmbossLength { get; set; }

    public decimal SplitLength { get; set; }

    // maks. długa lapa, odcinak przed przetłoczeniem
    [Obsolete("Użyj EmbossZoneLength", true)]
    public decimal MaxLongPaw { get; set; }

    //długość początkowego odcinka przed przetłoczeniem
    [Obsolete("Użyj OverlapLengthLength", true)]
    public decimal InitialLength { get; set; }
}
