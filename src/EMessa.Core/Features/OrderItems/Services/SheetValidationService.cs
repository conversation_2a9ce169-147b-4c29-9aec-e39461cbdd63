using EMessa.Base.Constants;
using EMessa.Core.Features.ShoppingCart.Models;
using EMessa.Core.Localizer;
using EMessa.Core.Interfaces;
using EMessa.DAL.Entities;
using EMessa.Core.Features.OrderItems.Commands.ValidateSheet;

namespace EMessa.Core.Features.OrderItems.Services;

public class SheetValidationService : ISheetValidationService
{
    public List<ArticleSheetValidationResult> ValidateRequestedSheet(
        ArticleSheetValidationModel sheetValidationModel,
        IArticleSheet requestedSheet
    )
    {
        var results = new List<ArticleSheetValidationResult>();

        ValidateAllowEditLength(results, sheetValidationModel, requestedSheet);
        ValidateAllowEditWidth(results, sheetValidationModel, requestedSheet);
        ValidateMinLength(results, sheetValidationModel, requestedSheet);

        if (results.Count == 0)
        {
            ValidateMaxLength(results, sheetValidationModel, requestedSheet);
            ValidateMaxTechnologyLength(results, sheetValidationModel, requestedSheet);
            ValidateSplitLength(results, sheetValidationModel, requestedSheet);
            ValidateEmbossLength(results, sheetValidationModel, requestedSheet);
        }

        ProposeOriginalSize(results, requestedSheet);

        return results;
    }


    public List<ArticleSheetValidationResult> ValidateRequestedSheetWithSplit(
        ArticleSheetValidationModel articleData,
        IArticleSheet requestedSheet,
        int splitN
    )
    {
        var results = new List<ArticleSheetValidationResult>();

        ProposeNSplitSheets(results, articleData, requestedSheet, splitN);

        return results;
    }


    private void ProposeNSplitSheets(
        List<ArticleSheetValidationResult> results,
        ArticleSheetValidationModel articleData,
        IArticleSheet sheet,
        int splitN
    )
    {
        var isError = false;
        if (!articleData.IsSplitteable)
        {
            results.Add(new ArticleSheetValidationResult
            {
                Type = ArticleSheetValidationResultType.Error,
                Message = "Arkusz jest nie podzielny".Tr()
            });
            isError = true;
        }

        if (articleData.SplitLength == 0)
        {
            results.Add(new ArticleSheetValidationResult
            {
                Type = ArticleSheetValidationResultType.Error,
                Message = "Brak długości podziału arkusza".Tr()
            });
            isError = true;
        }

        if (sheet.Length < 2 * articleData.MinLength
            || sheet.Length > 5 * articleData.SplitLength)
        {
            results.Add(new ArticleSheetValidationResult
            {
                Type = ArticleSheetValidationResultType.Error,
                Message = "Wymiar jest poza zakresem".Tr()
            });
            isError = true;
        }

        if (isError)
            return;

        var (proposedLengthsTop, totalLengthTop, embossZoneError) = articleData.HasModule
            ? GetProposedLengthsByModule(articleData, sheet, splitN)
            : GetProposedLengths(articleData, sheet, splitN);

        results.Add(new ArticleSheetValidationResult
        {
            Type = ArticleSheetValidationResultType.NSplit,
            Message = "Podzielono arkusz ({0}) m, na ({1}) części"
                .Tr(sheet.Length.ToString("n3"), splitN.ToString("D")),
            ProposedSheets = GetProposedSheets(proposedLengthsTop, sheet.Quantity, sheet.Width),
            TotalLength = totalLengthTop,
            EmbossZoneError = embossZoneError,
            SplitN = splitN
        });
    }

    private void ProposeOriginalSize(List<ArticleSheetValidationResult> results, IArticleSheet requestedSheet)
    {
        var isDeformationWarning = results.Any(x => x.DeformationWarning);
        var isEmbossZoneError = results.Any(x => x.EmbossZoneError);
        if (isDeformationWarning && !isEmbossZoneError)
        {
            results.Add(new ArticleSheetValidationResult
            {
                Type = ArticleSheetValidationResultType.AllowOriginalSize,
                ProposedSheets =
                [
                    new ArticleSheetEditModel
                    {
                        Quantity = requestedSheet.Quantity,
                        Length = requestedSheet.Length,
                        Width = requestedSheet.Width
                    }
                ]
            });
        }
    }

    private void ValidateAllowEditLength(
        List<ArticleSheetValidationResult> results,
        ArticleSheetValidationModel articleData,
        IArticleSheet sheet)
    {
        if (!articleData.LengthEditable && articleData.DefaultLength != sheet.Length)
        {
            results.Add(new ArticleSheetValidationResult
            {
                Type = ArticleSheetValidationResultType.Warning,
                Message = "Długość arkusza nie może być edytowana".Tr(),
                ProposedSheets = GetProposedSheets(
                    [articleData.DefaultLength],
                    sheet.Quantity,
                    sheet.Width)
            });
        }
    }

    private void ValidateAllowEditWidth(
        List<ArticleSheetValidationResult> results,
        ArticleSheetValidationModel articleData,
        IArticleSheet sheet)
    {
        if (!articleData.WidthEditable && articleData.DefaultWidth != sheet.Width)
        {
            results.Add(new ArticleSheetValidationResult
            {
                Type = ArticleSheetValidationResultType.Error,
                Message = "Szerokość arkusza nie może być edytowana".Tr()
            });
        }
    }

    private void ValidateMinLength(
        List<ArticleSheetValidationResult> results,
        ArticleSheetValidationModel articleData,
        IArticleSheet sheet)
    {
        // Jeśli długość nie jest edytowalna to nie sprawdzamy minimalnej długości
        if (!articleData.LengthEditable) return;

        // Jeśli długość arkusza jest mniejsza niż minimalna długość to dodajemy błąd
        var minLength = articleData.HasModule
            ? articleData.Module + articleData.OverlapLength
            : articleData.MinLength;

        if (sheet.Length >= minLength) return;

        results.Add(new ArticleSheetValidationResult
        {
            Type = ArticleSheetValidationResultType.Warning,
            Message = "Minimalna długość arkusza to ({0}) m".Tr(minLength.ToString("n3")),
            ProposedSheets = GetProposedSheets(
                [minLength],
                sheet.Quantity,
                sheet.Width)
        });
    }

    private void ValidateMaxLength(
        List<ArticleSheetValidationResult> results,
        ArticleSheetValidationModel articleData,
        IArticleSheet sheet)
    {
        // Jeśli długość nie jest edytowalna to nie sprawdzamy minimalnej długości
        if (!articleData.LengthEditable) return;

        // Jest krótsza niż maksymalna długość
        if (sheet.Length <= articleData.MaxLength) return;

        if (!articleData.IsSplitteable)
        {
            results.Add(new ArticleSheetValidationResult
            {
                Type = ArticleSheetValidationResultType.Warning,
                Message = "Maksymalna długość arkusza to ({0}) m".Tr(articleData.MaxLength.ToString("n3")),
                ProposedSheets = GetProposedSheets(
                    [articleData.MaxLength],
                    sheet.Quantity,
                    sheet.Width)
            });
        }
    }

    private void ValidateMaxTechnologyLength(
        List<ArticleSheetValidationResult> results,
        ArticleSheetValidationModel articleData,
        IArticleSheet sheet)
    {
        if (!articleData.IsSplitteable
            || sheet.Length <= articleData.SplitLength
            || sheet.Length > articleData.MaxLength) return;

        if (!articleData.IsSplitteable)
        {
            results.Add(new ArticleSheetValidationResult
            {
                Type = ArticleSheetValidationResultType.Error,
                Message = "Arkusz jest nie podzielny".Tr()
            });
            return;
        }

        if (articleData.SplitLength == 0)
        {
            results.Add(new ArticleSheetValidationResult
            {
                Type = ArticleSheetValidationResultType.Error,
                Message = "Brak długości podziału arkusza".Tr()
            });
            return;
        }

        // TopZoneError. Obliczamy podział arkusza na części.
        var (proposedLengthsTop, totalLengthTop, embossZoneError) = articleData.HasModule
            ? GetProposedLengthsByModule(articleData, sheet)
            : GetProposedLengths(articleData, sheet);

        results.Add(new ArticleSheetValidationResult
        {
            Type = ArticleSheetValidationResultType.Warning,
            Message =
                "Długość arkusza ({0}) m jest większa niż długość zalecana ({1}) m ze względów technologicznych"
                    .Tr(sheet.Length.ToString("n3"), articleData.SplitLength.ToString("n3")),
            ProposedSheets = GetProposedSheets(proposedLengthsTop, sheet.Quantity, sheet.Width),
            TotalLength = totalLengthTop,
            DeformationWarning = true,
            EmbossZoneError = embossZoneError
        });

        // BottomZoneError. Proponujemy dodatkowy podział arkusza na części.
        if (embossZoneError && articleData.HasModule && proposedLengthsTop.Count > 0)
        {
            var proposedLengthsBottom = proposedLengthsTop.ToList();
            proposedLengthsBottom[^1] -= articleData.EmbossZoneLength;
            var totalLengthBottom = proposedLengthsBottom.Sum() -
                                    (proposedLengthsBottom.Count - 1) * articleData.OverlapLength;

            results.Add(new ArticleSheetValidationResult
            {
                Type = ArticleSheetValidationResultType.Warning,
                Message =
                    "Długość arkusza ({0}) m jest większa niż długość zalecana ({1}) m ze względów technologicznych"
                        .Tr(sheet.Length.ToString("n3"), articleData.SplitLength.ToString("n3")),
                ProposedSheets = GetProposedSheets(proposedLengthsBottom, sheet.Quantity, sheet.Width),
                TotalLength = totalLengthBottom,
                DeformationWarning = true,
                EmbossZoneError = embossZoneError
            });
        }
    }

    public void ValidateSplitLength(
        List<ArticleSheetValidationResult> results,
        ArticleSheetValidationModel articleData,
        IArticleSheet sheet)
    {
        if (!articleData.IsSplitteable
            || sheet.Length <= articleData.SplitLength
            || sheet.Length <= articleData.MaxLength) return;

        if (articleData.SplitLength == 0)
        {
            results.Add(new ArticleSheetValidationResult
            {
                Type = ArticleSheetValidationResultType.Error,
                Message = "Brak długości podziału arkusza".Tr()
            });
            return;
        }

        // TopZoneError. Obliczamy podział arkusza na części.
        var (proposedLengthsTop, totalLengthTop, embossZoneError) = articleData.HasModule
            ? GetProposedLengthsByModule(articleData, sheet)
            : GetProposedLengths(articleData, sheet);

        results.Add(new ArticleSheetValidationResult
        {
            Type = ArticleSheetValidationResultType.Warning,
            Message = "Długość arkusza ({0}) m przekracza maksymalną długość arkusza do podziału ({1}) m"
                .Tr(sheet.Length.ToString("n3"), articleData.SplitLength.ToString("n3")),
            ProposedSheets = GetProposedSheets(proposedLengthsTop, sheet.Quantity, sheet.Width),
            TotalLength = totalLengthTop,
            EmbossZoneError = embossZoneError
        });

        // BottomZoneError. Proponujemy dodatkowy podział arkusza na części.
        if (embossZoneError && articleData.HasModule && proposedLengthsTop.Count > 0)
        {
            var proposedLengthsBottom = proposedLengthsTop.ToList();
            proposedLengthsBottom[^1] -= articleData.EmbossZoneLength;
            var totalLengthBottom = proposedLengthsBottom.Sum() -
                                    (proposedLengthsBottom.Count - 1) * articleData.OverlapLength;

            results.Add(new ArticleSheetValidationResult
            {
                Type = ArticleSheetValidationResultType.Warning,
                Message = "Długość arkusza ({0}) m przekracza maksymalną długość arkusza do podziału ({1}) m"
                    .Tr(sheet.Length.ToString("n3"), articleData.SplitLength.ToString("n3")),
                ProposedSheets = GetProposedSheets(proposedLengthsBottom, sheet.Quantity, sheet.Width),
                TotalLength = totalLengthBottom,
                DeformationWarning = true,
                EmbossZoneError = embossZoneError
            });
        }
    }

    /// <summary>
    /// Walidacja wymiaru w strefie przetłoczenia/cięcia
    /// </summary>
    public void ValidateEmbossLength(
        List<ArticleSheetValidationResult> results,
        ArticleSheetValidationModel articleData,
        IArticleSheet sheet)
    {
        if (!articleData.HasModule) return;
        // Dodatkowa strefa przetłoczenia (np 5cm) jest juz dokładana w ArticleComplexEditComponent.razor
        // EmbossZoneAddition dokładam do OverlapLength i do EmbossZoneLength i wtedy to sobie działa
        // [120-30 -- 120] staje se wtedy [120-30+50 -- 120+50] gdzie OL=120, EZL=30, EZA=50

        if (sheet.Length < articleData.MaxLength && CheckCutInEmbossZone(articleData, sheet))
        {
            // TopZoneError. Proponujemy długość.
            var optimalLengthTop = Math.Floor(sheet.Length / articleData.Module) * articleData.Module +
                                   articleData.OverlapLength;
            results.Add(new ArticleSheetValidationResult
            {
                Type = ArticleSheetValidationResultType.Warning,
                Message =
                    "Cięcie arkusza ({0}) m w strefie przetłoczenia jest niedozwolone, optymalna długość arkusza wynosi ({1}) m"
                        .Tr(sheet.Length.ToString("n3"), optimalLengthTop.ToString("n3")),
                ProposedSheets = GetProposedSheets(
                    [optimalLengthTop],
                    sheet.Quantity,
                    sheet.Width),
                EmbossZoneError = true,
            });

            // BottomZoneError. Proponujemy długość.
            var optimalLengthBottom = optimalLengthTop - articleData.EmbossZoneLength;
            results.Add(new ArticleSheetValidationResult
            {
                Type = ArticleSheetValidationResultType.Warning,
                Message =
                    "Cięcie arkusza ({0}) m w strefie przetłoczenia jest niedozwolone, optymalna długość arkusza wynosi ({1}) m"
                        .Tr(sheet.Length.ToString("n3"), optimalLengthBottom.ToString("n3")),
                ProposedSheets = GetProposedSheets(
                    [optimalLengthBottom],
                    sheet.Quantity,
                    sheet.Width),
                EmbossZoneError = true,
            });
        }
    }

    protected bool CheckCutInEmbossZone(
        ArticleSheetValidationModel articleData,
        IArticleSheet sheet)
    {
        if (!articleData.HasModule) return false;
        var bottom = articleData.OverlapLength - articleData.EmbossZoneLength;
        var top = articleData.OverlapLength;
        var rest = sheet.Length % articleData.Module;
        return bottom < rest && rest < top;
    }

    public record GetProposedLengthsByModuleResult(
        List<decimal> ProposedLengths,
        decimal TotalLength,
        bool EmbossZoneError);

    protected GetProposedLengthsByModuleResult GetProposedLengths(
        ArticleSheetValidationModel articleData,
        IArticleSheet sheet,
        int? splitN = null,
        bool addOneMoreSheet = false)
    {
        var length = sheet.Length;

        // Oblicz na ile części musimy podzielić arkusz
        var sheetsQuantity = !splitN.HasValue
            ? (int)Math.Ceiling(length / articleData.SplitLength) + (addOneMoreSheet ? 1 : 0)
            : splitN.Value + (addOneMoreSheet ? 1 : 0);

        // Jeśli mamy tylko 1 arkusz, zwracamy pełną długość bez zakładek
        if (sheetsQuantity == 1)
        {
            return new GetProposedLengthsByModuleResult([length], length, false);
        }

        // Obliczamy bazową długość każdego arkusza (bez zakładek)
        var baseLength = Math.Round(
            length / sheetsQuantity,
            SystemConstants.RoundingLengthDecimals,
            MidpointRounding.AwayFromZero);

        // Tworzymy listę z bazowymi długościami i pełnymi zakładkami
        var proposedLengths = Enumerable
            .Repeat(baseLength + articleData.OverlapLength, sheetsQuantity)
            .ToList();

        // Obliczamy długość bazową z połowiczną zakładką
        var baseLengthWithHalfOverlap = Math.Round(
            baseLength + 0.5m * articleData.OverlapLength,
            SystemConstants.RoundingLengthDecimals,
            MidpointRounding.AwayFromZero);

        // Obliczamy resztę, która zostanie dodana do ostatniego arkusza
        var remainder = Math.Round(length - baseLength * sheetsQuantity,
            SystemConstants.RoundingLengthDecimals,
            MidpointRounding.AwayFromZero);

        // Ustawiamy połowiczne zakładki dla pierwszego i ostatniego arkusza
        proposedLengths[0] = baseLengthWithHalfOverlap;
        proposedLengths[^1] = baseLengthWithHalfOverlap + remainder;

        // Oblicz długośc całkowitą
        var totalLength = proposedLengths.Sum() - (proposedLengths.Count - 1) * articleData.OverlapLength;

        // Jeśli długość którejś z części jest większa niż zalecana długość części to zwiększamy ilość części
        return proposedLengths.Any(x => x > articleData.SplitLength)
            ? GetProposedLengths(articleData, sheet, splitN, true)
            : new GetProposedLengthsByModuleResult(proposedLengths, totalLength, false);
    }

    protected GetProposedLengthsByModuleResult GetProposedLengthsByModule(
        ArticleSheetValidationModel articleData,
        IArticleSheet sheet,
        int? splitN = null,
        bool addOneMoreSheet = false)
    {
        var length = sheet.Length;
        var module = articleData.Module;

        // Oblicz ile pełnych modułów się mieści
        var modulesQuantity = Math.Floor(length / module);

        // Oblicz na ile części musimy podzielić arkusz
        var sheetsQuantity = !splitN.HasValue
            ? (int)Math.Ceiling(length / articleData.SplitLength) + (addOneMoreSheet ? 1 : 0)
            : splitN.Value + (addOneMoreSheet ? 1 : 0);

        // Oblicz liczbę modułów na część (zaokrąglone do najbliższej liczby całkowitej)
        var modulesPerPart = Math.Round(modulesQuantity / sheetsQuantity, 0, MidpointRounding.AwayFromZero);

        // Oblicz standardową długość części (bez zakładki)
        var baseLength = modulesPerPart * module;

        // Generujemy arkusze
        var proposedLengths = Enumerable
            .Repeat(baseLength + articleData.OverlapLength, sheetsQuantity)
            .ToList();

        // Czy był błąd cięcia w strefie emboss
        var embossZoneError = false;

        // Obliczamy długość ostatniej części
        if (sheetsQuantity > 0)
        {
            // Obliczamy pozostałą długość ostatniej części
            var lastPartLength = length - baseLength * (sheetsQuantity - 1);
            // Długość ostatniego arkusza została zmieniona ze względów technologicznych (koniec arkusza w strefie przetłoczenia).
            embossZoneError = CheckCutInEmbossZone(articleData, sheet);

            // Jeśli cięcie w strefie emboss to zwiększamy długość ostatniej części do "topEmbossZone"
            if (embossZoneError)
            {
                // Wstawiamy długośc zaraz po EmbossZone = "TopZoneError".
                // Długość modułowa (wg 350 = N*350) + cała zakładka (=zakładka + dodatkowa strefa)
                lastPartLength = Math.Floor(lastPartLength / module) * module + articleData.OverlapLength;
                // Zaproponowano by ciąć arkusz przd EmboosZone = "BottomZoneError" i po EmbossZone = "TopZoneError"
                // "BottomZoneError" = "TopZoneError" - EmbossZoneLength
                // Obliczmy to piętro wyżej
            }

            // Ustawiamy długość ostatniej części
            proposedLengths[^1] = lastPartLength;
        }

        // Oblicz długośc całkowitą
        var totalLength = proposedLengths.Sum() - (proposedLengths.Count - 1) * articleData.OverlapLength;

        // Jeśli długość którejś z części jest większa niż zalecana długość części to zwiększamy ilość części
        return proposedLengths.Any(x => x > articleData.SplitLength)
            ? GetProposedLengthsByModule(articleData, sheet, splitN, true)
            : new GetProposedLengthsByModuleResult(proposedLengths, totalLength, embossZoneError);
    }

    protected List<ArticleSheetEditModel> GetProposedSheets(
        List<decimal> proposedLengths,
        int quantity,
        decimal width)
    {
        return proposedLengths
            .GroupBy(x => x)
            .Select(group => new ArticleSheetEditModel
            {
                Length = group.Key,
                Quantity = quantity * group.Count(),
                Width = width
            })
            .ToList();
    }
}