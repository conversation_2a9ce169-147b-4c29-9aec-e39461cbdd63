using EMessa.Base.Constants;
using EMessa.Base.Enums;
using EMessa.Core.Common.Results;
using EMessa.Core.Extensions;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.OrderItems.ApiCommands.UpdateOrderItemsStatus;

public record ApiUpdateOrderItemsStatusCommand(UpdateOrderItemsStatusRequest Request) : IRequest<IResult>;

public class ApiUpdateOrderItemsStatusCommandHandler(
    ILogger<ApiUpdateOrderItemsStatusCommandHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory)
    : IRequestHandler<ApiUpdateOrderItemsStatusCommand, IResult>
{
    public async Task<IResult> Handle(ApiUpdateOrderItemsStatusCommand request, CancellationToken cancellationToken)
    {
        try
        {
            foreach (var item in request.Request.OrderItems)
            {
                if (!OrderAccessConstants.AllowedTargetStatusesForAPI.Contains(item.OrderItemStatus))
                {
                    logger.LogWarning("Invalid OrderItem status {OrderItemStatus} for OrderItemId {OrderItemId}.", item.OrderItemStatus, item.OrderItemId);
                    return await Result.FailAsync(ResultErrorMessages.OperationFailed);
                }
            }

            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var orderItemIds = request.Request.OrderItems.Select(oi => oi.OrderItemId).ToList();

            var orderItems = await dbContext.OrderItems
                .Include(oi => oi.Order)
                    .ThenInclude(o => o.OrderItems)
                .AsTracking()
                .Where(oi => orderItemIds.Contains(oi.Id))
                .ToListAsync(cancellationToken);

            if (orderItems.Count == 0)
            {
                logger.LogWarning("No Orders found for the provided OrderItemIds: {OrderItemIds}.", string.Join(", ", orderItemIds));
                return await Result.FailAsync(ResultErrorMessages.NotFound);
            }

            foreach (var orderItem in orderItems)
            {
                var orderItemRequest = request.Request.OrderItems
                    .First(oi => oi.OrderItemId == orderItem.Id);

                if (!OrderAccessConstants.AllowedSourceStatusesForAPI.Contains(orderItem.Status))
                {
                    logger.LogWarning("Invalid OrderItem status {OrderItemStatus} for OrderItemId {OrderItemId}.", orderItemRequest.OrderItemStatus, orderItemRequest.OrderItemId);
                    return await Result.FailAsync(ResultErrorMessages.OperationFailed);
                }

                orderItem.Status = orderItemRequest.OrderItemStatus;

                if (orderItemRequest.OrderItemStatus == OrderStatus.Rejected)
                {
                    orderItem.ProductionDate = null;
                    orderItem.TransportDate = null;
                }
                else if (orderItemRequest.OrderItemStatus == OrderStatus.Production &&
                    orderItemRequest.ProductionDateUtc.HasValue)
                {
                    orderItem.ProductionDate = orderItemRequest.ProductionDateUtc;
                }
                else if (orderItemRequest.OrderItemStatus == OrderStatus.Transport &&
                        orderItemRequest.TransportDateUtc.HasValue)
                {
                    orderItem.TransportDate = orderItemRequest.TransportDateUtc;
                }
            }

            foreach (var order in orderItems.Select(oi => oi.Order).Distinct())
            {
                order.Status = order.GetLowestOrderItemStatus();
                order.LastEditDate = DateTime.UtcNow;
            }

            await dbContext.SaveChangesAsync(cancellationToken);

            return await Result.SuccessAsync();
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while updating Orders status.");
            return await Result.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}
