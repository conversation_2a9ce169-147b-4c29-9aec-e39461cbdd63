using EMessa.Base.Enums;

namespace EMessa.Core.Features.OrderItems.ApiCommands.UpdateOrderItemsStatus;

public class UpdateOrderItemsStatusRequest
{
    public List<UpdateOrderItemsStatus> OrderItems { get; set; } = [];
}

public class UpdateOrderItemsStatus : UpdateOrderItemsStatusBase
{
    public int OrderItemId { get; set; }
}

public class UpdateOrderItemsStatusBase
{
    public OrderStatus OrderItemStatus { get; set; }
    public DateTime? ProductionDateUtc { get; set; }
    public DateTime? TransportDateUtc { get; set; }
}