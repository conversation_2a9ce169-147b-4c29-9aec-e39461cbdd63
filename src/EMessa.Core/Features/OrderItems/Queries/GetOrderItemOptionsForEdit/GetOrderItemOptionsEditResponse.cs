namespace EMessa.Core.Features.OrderItems.Queries.GetOrderItemOptionsForEdit
{
    public class GetOrderItemOptionsEditResponse
    {
        public int OrderItemId { get; set; }
        public int ArticleId { get; set; }
        public string ArticleName { get; set; } = "";
        public int Id { get; set; }
        public int OptionId { get; set; }
        public int? OptionValueId { get; set; }
        public int No { get; set; }
        public string Code { get; set; } = null!;
        public string Name { get; set; } = null!;
        public string OriginalName { get; set; } = null!;

        public List<GetOrderItemOptionValuesEditResponse> Values { get; set; } = new();
      
    }
}
