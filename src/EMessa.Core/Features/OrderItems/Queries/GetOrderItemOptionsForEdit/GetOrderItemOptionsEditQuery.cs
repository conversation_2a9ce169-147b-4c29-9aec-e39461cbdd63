using MediatR;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using EMessa.Core.Interfaces;
using Microsoft.Extensions.Logging;
using EMessa.Core.Localizer;
using EMessa.DAL.Data;
using EMessa.Core.Common.Results;

namespace EMessa.Core.Features.OrderItems.Queries.GetOrderItemOptionsForEdit
{
    public record GetOrderItemOptionsEditQuery(int OrderItemId)
        : IRequest<IResult<List<GetOrderItemOptionsEditResponse>>>;

    public class GetOrderItemOptionsEditQueryHandler(
        IDbContextFactory<ApplicationDbContext> dbFactory,
        IMapper mapper,
        ILogger<GetOrderItemOptionsEditQueryHandler> logger)
        : IRequestHandler<GetOrderItemOptionsEditQuery, IResult<List<GetOrderItemOptionsEditResponse>>>
    {
        public async Task<IResult<List<GetOrderItemOptionsEditResponse>>> Handle(GetOrderItemOptionsEditQuery request,
            CancellationToken cancellationToken)
        {
            try
            {
                await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

                logger.LogInformation("GetArticleOptionsForEditQueryHandler request.OrderItemId {OrderItemId}",
                    request.OrderItemId);
                var orderItem = await dbContext.OrderItems.SingleOrDefaultAsync(x => x.Id == request.OrderItemId,
                    cancellationToken: cancellationToken);
                if (orderItem == null)
                {
                    logger.LogError("Nie znaleziono pozycji zamówienia lub koszyka. OIId: {0}.".Tr(),
                        request.OrderItemId);
                    return await Result<List<GetOrderItemOptionsEditResponse>>.FailAsync(
                        "Nie znaleziono pozycji zamówienia lub koszyka.".Tr());
                }

                var orderItemOptionValues = await dbContext.OrderItemOptionValues.AsNoTracking()
                    .Include(x => x.OrderItem)
                    .Include(x => x.OrderItem).ThenInclude(x => x.Article).ThenInclude(x => x.Translations)
                    .Include(x => x.Option).ThenInclude(x => x.Translations)
                    .Include(x => x.OptionValue).ThenInclude(x => x!.Translations)
                    .Include(x => x.Option)
                    .ThenInclude(x => x.Values.Where(c =>
                        c.ArticleOptionValues.Any(z => z.ArticleOption.ArticleId == orderItem.ArticleId)))
                    .ThenInclude(x => x.Translations)
                    .Where(x => x.OrderItemId == orderItem.Id)
                    .OrderBy(x => x.Option.No)
                    .ToListAsync(cancellationToken);

                foreach (var optionValue in orderItemOptionValues)
                {
                    // WARNING Option.UseDefaultValue or ArticleOption.UseDefaultValue?
                    if (optionValue.OptionValueId.HasValue == false && optionValue.Option.Values.Count == 1 &&
                        optionValue.Option.UseDefaultValue)
                    {
                        optionValue.OptionValueId = optionValue.Option.Values[0].Id;
                    }
                }

                var result = mapper.Map<List<GetOrderItemOptionsEditResponse>>(orderItemOptionValues);


                return await Result<List<GetOrderItemOptionsEditResponse>>.SuccessAsync(result);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, ex.Message);
                return await Result<List<GetOrderItemOptionsEditResponse>>.FailAsync("Nieokreślony błąd operacji."
                    .Tr());
            }
        }
    }
}