using AutoMapper;
using EMessa.DAL.Entities.Options;
using EMessa.DAL.Entities.Orders;
using System.Globalization;

namespace EMessa.Core.Features.OrderItems.Queries.GetOrderItemOptionsForEdit
{
    public class GetOrderItemOptionsEditResponseMapping : Profile
    {
        public GetOrderItemOptionsEditResponseMapping()
        {
            {
                CreateMap<OrderItemOptionValue, GetOrderItemOptionsEditResponse>()
                    .ForMember(x => x.OriginalName, opt => opt.MapFrom(x => x.Option.Name))

                    .ForMember(x => x.Name,
                        x => x.MapFrom(z =>
                            z.Option.Translations.FirstOrDefault(x => x.LanguageCode == CultureInfo.CurrentCulture.Name) == null
                            ? z.Option.Name
                            : z.Option.Translations.First(x => x.LanguageCode == CultureInfo.CurrentCulture.Name).Name))
                    .ForMember(x=>x.OptionValueId, opt=>opt.MapFrom(x=>x.OptionValueId))
                    .ForMember(x => x.Code, opt => opt.MapFrom(x => x.Option.Code))
                    
                    .ForMember(x => x.Values, opt => opt.MapFrom(x => x.Option.Values))
                    ;

                CreateMap<OptionValue, GetOrderItemOptionValuesEditResponse>()
                    .ForMember(x => x.Id, opt => opt.MapFrom(x => x.Id))
                    .ForMember(x => x.OptionId, opt => opt.MapFrom(x => x.Option.Id))
                    //.ForMember(x => x.OptionValueId, opt => opt.MapFrom(x => x.OptionValueId))

                    .ForMember(x => x.OriginalValue, opt => opt.MapFrom(x => x.Value))
                    .ForMember(x => x.Value,
                        x => x.MapFrom(z =>
                            z.Translations.FirstOrDefault(x => x.LanguageCode == CultureInfo.CurrentCulture.Name) == null
                            ? z.Value
                            : z.Translations.First(x => x.LanguageCode == CultureInfo.CurrentCulture.Name).Value))


                    .ForMember(x => x.OriginalValueInfo, opt => opt.MapFrom(x => x.ValueInfo))
                    .ForMember(x => x.ValueInfo,
                        x => x.MapFrom(z =>
                            z.Translations.FirstOrDefault(x => x.LanguageCode == CultureInfo.CurrentCulture.Name) == null
                            ? z.ValueInfo
                            : z.Translations.First(x => x.LanguageCode == CultureInfo.CurrentCulture.Name).ValueInfo))
                    ;
            }
        }
    }
}
