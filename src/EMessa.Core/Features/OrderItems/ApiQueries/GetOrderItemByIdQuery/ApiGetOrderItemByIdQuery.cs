using AutoMapper;
using AutoMapper.QueryableExtensions;
using EMessa.Core.Common.Results;
using EMessa.Core.Features.Orders.ApiQueries.Common;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.OrderItems.ApiQueries.GetOrderItemByIdQuery;

public record ApiGetOrderItemByIdQuery(int OrderItemId) : IRequest<IResult<OrderItemDTO>>;

public class ApiGetOrderItemByIdQueryHandler(
    ILogger<ApiGetOrderItemByIdQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IMapper mapper)
    : IRequestHandler<ApiGetOrderItemByIdQuery, IResult<OrderItemDTO>>
{
    public async Task<IResult<OrderItemDTO>> Handle(ApiGetOrderItemByIdQuery request, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var order = await dbContext.OrderItems
                .AsNoTracking()
                .ProjectTo<OrderItemDTO>(mapper.ConfigurationProvider)
                .FirstOrDefaultAsync(x => x.Id == request.OrderItemId, cancellationToken: cancellationToken);

            if (order is null)
            {
                logger.LogWarning("OrderItem with ID {OrderItemId} not found.", request.OrderItemId);
                return await Result<OrderItemDTO>.FailAsync(ResultErrorMessages.NotFound);
            }

            return await Result<OrderItemDTO>.SuccessAsync(order);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result<OrderItemDTO>.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}