using AutoMapper;
using EMessa.Core.Features.OrderItems.Commands.UpdateOrderItem;
using EMessa.Core.Features.ShoppingCart.Models;
using EMessa.DAL.Entities.Orders;

public class OrderItemsMappingProfile : Profile
{
    public OrderItemsMappingProfile()
    {
        CreateMap<BaseOrderItemEditModel, UpdateOrderItemEditModel>();
        CreateMap<UpdateOrderItemEditModel, OrderItem>()
            // Identyfikatory i nawigacje kontrolujemy ręcznie w handlerze
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.OrderId, opt => opt.Ignore())
            .ForMember(dest => dest.Order, opt => opt.Ignore())
            .ForMember(dest => dest.ArticleId, opt => opt.Ignore())
            .ForMember(dest => dest.Article, opt => opt.Ignore())
            // Ustaw aktualny indeks pozycji z wyliczanego NewIndex
            .ForMember(dest => dest.Index, opt => opt.MapFrom(src => src.NewIndex))
            // Kolekcje do edycji – delegujemy do istniejących profili mapujących elementy
            .ForMember(dest => dest.RequestedSheets, opt => opt.MapFrom(src => src.RequestedSheets))
            .ForMember(dest => dest.OptionValues, opt => opt.MapFrom(src => src.OptionValues))
            // Pola rozliczeniowe/cenowe – nie aktualizujemy w tym miejscu
            .ForMember(dest => dest.BasePrice, opt => opt.Ignore())
            .ForMember(dest => dest.FinalPrice, opt => opt.Ignore())
            .ForMember(dest => dest.CurrencyCode, opt => opt.Ignore())
            // Statusy/terminy – kontrolowane innymi ścieżkami biznesowymi
            .ForMember(dest => dest.Status, opt => opt.Ignore())
            .ForMember(dest => dest.ProductionDate, opt => opt.Ignore())
            .ForMember(dest => dest.TransportDate, opt => opt.Ignore())
            // Powiązania sprzedażowe – nie zmieniamy tu
            .ForMember(dest => dest.SaleId, opt => opt.Ignore())
            .ForMember(dest => dest.Sale, opt => opt.Ignore());
    }
}