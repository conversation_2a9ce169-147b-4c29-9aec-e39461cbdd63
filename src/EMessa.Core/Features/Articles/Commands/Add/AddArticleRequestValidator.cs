using EMessa.Core.Localizer;
using FluentValidation;
using EMessa.Base.Enums;

namespace EMessa.Core.Features.Articles.Commands.Add;

public class AddArticleRequestValidator : AbstractValidator<AddArticleRequest>
{
    public AddArticleRequestValidator()
    {
        RuleFor(x => x.Type).IsInEnum();

        RuleFor(x => x.Code)
            .NotEmpty().WithMessage("Pole {0} jest wymagane.".Tr("Kod"))
            .MaximumLength(60).WithMessage("Pole {0} może mieć maks. długość {1} znaków.".Tr("Kod", "60"));

        RuleFor(x => x.Name)
           .NotEmpty().WithMessage("Pole {0} jest wymagane.".Tr("Nazwa"))
           .MaximumLength(200).WithMessage("Pole {0} może mieć maks. długość {1} znaków.".Tr("Nazwa", "200"));

        RuleFor(x => x.No)
            .NotEmpty().WithMessage("Pole {0} jest wymagane.".Tr("Numer katalogowy"));

        RuleFor(x => x.UnitWeight)
            .NotEmpty().When(x => x.Type == ArticleType.Complex)
            .WithMessage("Pole {0} jest wymagane.".Tr("Waga jednostkowa"));
        
        RuleFor(x => x.BasePrice)
            .NotNull().GreaterThan(0).When(x => x.Type == ArticleType.Trade)
            .WithMessage("Pole {0} jest wymagane.".Tr("Cena bazowa"));
        
        RuleFor(x => x.ForeignCode)
            .MaximumLength(60)
            .WithMessage("Pole {0} może mieć maks. długość {1} znaków.".Tr("Kod obcy", "60"));

        RuleFor(x => x.DefaultLength)
            .NotNull().GreaterThan(0)
            .When(x => x.Type == ArticleType.Complex && x.LengthEditable == false)
            .WithMessage("Pole {0} jest wymagane.".Tr("Długość domyślna"));

        RuleFor(x => x.MinLength)
            .NotNull().GreaterThan(0).When(x => x.Type == ArticleType.Complex)
            .WithMessage("Pole {0} jest wymagane.".Tr("Minimalna długość"));

        RuleFor(x => x.MaxLength)
            .NotNull().GreaterThan(0).When(x => x.Type == ArticleType.Complex)
            .WithMessage("Pole {0} jest wymagane.".Tr("Maksymalna długość"));

        RuleFor(x => x.DefaultWidth)
            .NotNull().GreaterThan(0)
            .When(x => x.Type == ArticleType.Complex && x.WidthEditable == false)
            .WithMessage("Pole {0} jest wymagane.".Tr("Szerokość domyślna"));

        RuleFor(x => x.MinWidth)
            .NotNull().GreaterThan(0)
            .When(x => x.Type == ArticleType.Complex)
            .WithMessage("Pole {0} jest wymagane.".Tr("Minimalna długość"));

        RuleFor(x => x.MaxWidth)
            .NotNull().GreaterThan(0)
            .When(x => x.Type == ArticleType.Complex)
            .WithMessage("Pole {0} jest wymagane.".Tr("Maksymalna długość"));

        RuleFor(x => x.SplitLength)
            .NotNull().GreaterThan(0)
            .When(x => x.Type == ArticleType.Complex && x.IsSplitteable)
            .WithMessage("Pole {0} jest wymagane.".Tr("Długość podziału"));

        RuleFor(x => x.OverlapLength)
            .NotNull().GreaterThan(0).When(x => x.Type == ArticleType.Complex)
            .When(x => x.IsSplitteable)
            .WithMessage("Pole {0} jest wymagane.".Tr("Całkowita długość zakładki"));

        RuleFor(x => x.EmbossZoneLength)
            .NotNull().GreaterThanOrEqualTo(0).When(x => x.Type == ArticleType.Complex)
            .WithMessage("Pole {0} musi być równe lub wieksze od 0.".Tr("Długość zakazanej strefy przetłoczenia"));

        RuleFor(x => x.ArticleUnitId)
           .NotNull().GreaterThan(0).WithMessage("Pole {0} jest wymagane.".Tr("Jednostka"));

    }
}