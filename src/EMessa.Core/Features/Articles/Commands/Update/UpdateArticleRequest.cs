using EMessa.DAL.Entities;
using EMessa.Base.Enums;

namespace EMessa.Core.Features.Articles.Commands.Update;

public class UpdateArticleRequest : IArticleRequest
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public bool IsActive { get; set; } = true;
    public ArticleType Type { get; set; }
    public bool IsDeleted { get; set; } = false;
    public int No { get; set; }
    public decimal UnitWeight { get; set; }
    public int? ArticleUnitId { get; set; }

    public string Description { get; set; } = string.Empty;
    public string TooLongSheet { get; set; } = string.Empty;
    public string ForeignCode { get; set; } = string.Empty;
    public bool EditOrderCommentsEnable { get; set; }
    public decimal BasePrice { get; set; }
    public int QuantityWarning { get; set; }

    // opcje technologiczne
    public bool LengthEditable { get; set; } 
    public decimal DefaultLength { get; set; }
    public decimal MinLength { get; set; }
    public decimal MaxLength { get; set; }
    public decimal SplitLength { get; set; }
    public bool WidthEditable { get; set; } 
    public decimal DefaultWidth { get; set; }
    public decimal MinWidth { get; set; }
    public decimal MaxWidth { get; set; }
    public bool IsSplitteable { get; set; }
    public bool ProfileEditable { get; set; }
    public bool RequireProfile { get; set; }
    public bool IsSplittableNParts { get; set; }
    public decimal OverlapLength { get; set; }
    public decimal EmbossZoneLength { get; set; }

    [Obsolete("Użyj EmbossZoneLength", true)]
    public decimal MaxLongPaw { get; set; }

    [Obsolete("Użyj OverlapLengthLength", true)]
    public decimal InitialLength { get; set; }
}