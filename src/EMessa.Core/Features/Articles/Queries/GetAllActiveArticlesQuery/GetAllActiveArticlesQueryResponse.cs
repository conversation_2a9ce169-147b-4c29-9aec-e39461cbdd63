//using EMessa.Core.Common;
using EMessa.Base.Enums;

namespace EMessa.Core.Features.Articles.Queries.GetAllActiveArticlesQuery
{
    public class GetAllActiveArticlesQueryResponse
    {
        public int Id { get; set; }

        public string Name { get; set; } = string.Empty;

        public string Code { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;

        public ArticleType Type { get; set; }

        public bool IsDeleted { get; set; } = false;
    }
}
