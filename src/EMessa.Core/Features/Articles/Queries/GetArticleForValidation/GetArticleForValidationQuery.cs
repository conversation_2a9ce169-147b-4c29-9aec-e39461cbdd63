using AutoMapper;
using AutoMapper.QueryableExtensions;
using EMessa.DAL.Entities.Articles;
using EMessa.Core.Localizer;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using EMessa.DAL.Data;
using EMessa.Core.Common.Results;
using EMessa.Core.Features.OrderItems.Commands.ValidateSheet;

namespace EMessa.Core.Features.Articles.Queries.GetArticleForValidation;

public record GetArticleForValidationQuery(int ArticleId)
    : IRequest<Result<ArticleSheetValidationModel>>;

public class GetArticleForValidationQueryHandler(
    ILogger<GetArticleForValidationQueryHandler> logger,
    IMapper mapper,
    IDbContextFactory<ApplicationDbContext> contextFactory)
    : IRequestHandler<GetArticleForValidationQuery, Result<ArticleSheetValidationModel>>
{
    public async Task<Result<ArticleSheetValidationModel>> Handle(GetArticleForValidationQuery request,
        CancellationToken cancellationToken)
    {
        await using var dbContext = await contextFactory.CreateDbContextAsync(cancellationToken);

        try
        {
            var articleMap = await dbContext
                .Articles
                .Include(x => x.Options).ThenInclude(x => x.Option)
                .Include(x => x.Options).ThenInclude(x => x.Values).ThenInclude(x => x.OptionValue)
                .Where(x => x.Id == request.ArticleId)
                .ProjectTo<ArticleSheetValidationModel>(mapper.ConfigurationProvider)
                .SingleOrDefaultAsync(cancellationToken: cancellationToken);

            if (articleMap == null)
            {
                return await Result<ArticleSheetValidationModel>.FailAsync("Nie odnaleziono artykułu".Tr());
            }

            return await Result<ArticleSheetValidationModel>.SuccessAsync(articleMap);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Błąd pobrania artykułu");
            return await Result<ArticleSheetValidationModel>.FailAsync("Błąd pobrania artykułu".Tr());
        }
    }
}

public class ArticleForValidationResponseProfile : Profile
{
    public ArticleForValidationResponseProfile()
    {
        CreateMap<Article, ArticleSheetValidationModel>()
            .ForMember(x => x.ArticleId, opt => opt.MapFrom(src => src.Id));
            // tu nie mapujemy options bo to trzeba wziąć wartość modułu po ustawieniu w edycji OrderItem
            //.ForMember(x=>x.Module, x=>x.MapFrom(z=>
            //    z.Options.Any(x=>x.Option.Code.ToLower() == "mod") ? z.OptionValues.First(x=>x.Option.Code.ToLower() == "mod").va .Values.First().OptionValue.Value.ToDecimalOrNull() ?? 0 : 0))
            ;
    }
}