using AutoMapper;
using EMessa.Core.Common.Results;
using EMessa.Core.Features.Articles.Queries.GetAllBySieve;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Syncfusion.Blazor;

namespace EMessa.Core.Features.Articles.Queries.GetAll;

[Obsolete($"This query is deprecated. Use {nameof(GetAllArticlesBySieveQuery)} instead.")]
public class GetAllArticlesQuery : IRequest<ListResult<GetAllArticlesResponse>>
{
    public DataManagerRequest DataManagerRequest { get; set; }

    public GetAllArticlesQuery(DataManagerRequest dataManagerRequest)
    {
        DataManagerRequest = dataManagerRequest;
    }
}

[Obsolete($"This query is deprecated. Use {nameof(GetAllArticlesBySieveQueryHandler)} instead.")]
public class GetAllArticlesQueryHandler : IRequestHandler<GetAllArticlesQuery, ListResult<GetAllArticlesResponse>>
{
    private readonly IDbContextFactory<ApplicationDbContext> _dbContextFactory;
    private readonly IMapper _mapper;

    public GetAllArticlesQueryHandler(IDbContextFactory<ApplicationDbContext> dbContextFactory, IMapper mapper)
    {
        _dbContextFactory = dbContextFactory;
        _mapper = mapper;
    }

    public async Task<ListResult<GetAllArticlesResponse>> Handle(GetAllArticlesQuery request, CancellationToken cancellationToken)
    {
        await using var dbContext = await _dbContextFactory.CreateDbContextAsync(cancellationToken);
        var query = dbContext.Articles.Where(x=>x.IsDeleted != true).OrderBy(x => x.Id).AsQueryable();

        if (request.DataManagerRequest.Search is { Count: > 0 })
        {
            query = DataOperations.PerformSearching(query, request.DataManagerRequest.Search);
        }

        if (request.DataManagerRequest.Where is { Count: > 0 })
        {
            query = DataOperations.PerformFiltering(query, request.DataManagerRequest.Where, "OR");
        }

        if (request.DataManagerRequest.Sorted is { Count: > 0 })
        {
            query = DataOperations.PerformSorting(query, request.DataManagerRequest.Sorted);
        }

        var users = await query.ToListResultAsync(request.DataManagerRequest.Skip, request.DataManagerRequest.Take, cancellationToken);
        var result = _mapper.Map<ListResult<GetAllArticlesResponse>>(users);

        return result;
    }
}