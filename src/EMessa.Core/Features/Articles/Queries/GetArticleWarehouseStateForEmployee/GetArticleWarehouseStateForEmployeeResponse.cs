using EMessa.Base.Enums;

namespace EMessa.Core.Features.Articles.Queries.GetArticleWarehouseStateForEmployee;

public class GetArticleWarehouseStateForEmployeeResponse(ArticleQuantityLevel mainWarehouseQuantity, ArticleQuantityLevel branchWarehouseQuantity)
{
    public ArticleQuantityLevel MainWarehouseQuantity { get; set; } = mainWarehouseQuantity;
    public ArticleQuantityLevel BranchWarehouseQuantity { get; set; } = branchWarehouseQuantity;
}
