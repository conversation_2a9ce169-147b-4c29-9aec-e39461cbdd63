using EMessa.Base.Enums;
using EMessa.Base.Extensions;
using EMessa.Core.Common.Results;
using EMessa.Core.Extensions;
using EMessa.Core.Helpers;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Branches;
using MediatR;
using MessaApi.IndexTranslations;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using OptApi.ArticleStates;
using System.Threading;

namespace EMessa.Core.Features.Articles.Queries.GetArticleWarehouseStateForEmployee;

public record GetArticleWarehouseStateForEmployeeQuery(string ArticleIndex, params int[] BranchIds) : IRequest<IResult<GetArticleWarehouseStateForEmployeeResponse>>;

public class GetArticleWarehouseStateForEmployeeQueryHandler(
    ILogger<GetArticleWarehouseStateForEmployeeQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IMessaIndexTranslatorApi messaIndexTranslatorApi,
    ICapiArticleStateApi capiArticleStateApi)
    : IRequestHandler<GetArticleWarehouseStateForEmployeeQuery, IResult<GetArticleWarehouseStateForEmployeeResponse>>
{
    public async Task<IResult<GetArticleWarehouseStateForEmployeeResponse>> Handle(GetArticleWarehouseStateForEmployeeQuery request, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var branches = await GetBranchesByIds(dbContext, cancellationToken, request.BranchIds);
            List<string> branchWarehouseCodes = [.. GetBranchWarehouseCodes(branches)];

            var warehouseCodesConf = ConfigurationHelper.GetConfiguration<string>(ConfigurationCodes.WarehouseCodes);
            List<string> warehouseCodes = warehouseCodesConf?.Split(';').ToList() ?? [];

            List<string> allWareshouseCodes = [.. branchWarehouseCodes.Union(warehouseCodes).Distinct()];

            if (allWareshouseCodes.Count == 0)
            {
                logger.LogError("No warehouse codes configured.");
                return await Result<GetArticleWarehouseStateForEmployeeResponse>.FailAsync(ResultErrorMessages.UnexpectedError);
            }

            var translatedIndex = await messaIndexTranslatorApi.GetIndexTranslation(request.ArticleIndex.ToBase64());

            var articleState = await capiArticleStateApi.GetArticelState(new(allWareshouseCodes, [translatedIndex.Akronim]));
            if (articleState == null || articleState.Count == 0)
            {
                logger.LogError("No article state found for the given index.");
                return await Result<GetArticleWarehouseStateForEmployeeResponse>.FailAsync(ResultErrorMessages.NotFound);
            }

            var mainWarehouseStock = articleState
                .Where(x => warehouseCodes.Contains(x.WarehouseCode));
            var branchWarehouseStock = articleState
                .Where(x => branchWarehouseCodes.Contains(x.WarehouseCode));

            var response = new GetArticleWarehouseStateForEmployeeResponse(
                    mainWarehouseStock.GetQuantityLevelForCollection(),
                    branchWarehouseStock.GetQuantityLevelForCollection());
            return await Result<GetArticleWarehouseStateForEmployeeResponse>.SuccessAsync(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result<GetArticleWarehouseStateForEmployeeResponse>.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }

    private async Task<IEnumerable<Branch>> GetBranchesByIds(ApplicationDbContext dbContext, CancellationToken cancellationToken, params int[] branchIds)
    {
        return await dbContext.Branches
                .AsNoTracking()
                .Where(x => branchIds.Contains(x.Id))
                .ToListAsync(cancellationToken);
    }

    private IEnumerable<string> GetBranchWarehouseCodes(IEnumerable<Branch> branches)
    {
        return branches
            .SelectMany(branch => branch.SellWarehouseCode?.Split(';') ?? [])
            .Where(code => !string.IsNullOrWhiteSpace(code))
            .Distinct();
    }
}
