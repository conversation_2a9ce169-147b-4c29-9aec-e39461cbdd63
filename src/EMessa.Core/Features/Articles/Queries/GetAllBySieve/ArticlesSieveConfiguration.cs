using EMessa.DAL.Entities.Articles;
using Microsoft.IdentityModel.Tokens;
using Sieve.Services;

namespace EMessa.Core.Features.Articles.Queries.GetAllBySieve;

public class ArticlesSieveConfiguration : ISieveConfiguration
{
    public void Configure(SievePropertyMapper mapper)
    {
        mapper.Property<Article>(x => x.Id).CanFilter().CanSort();
        mapper.Property<Article>(x => x.Code).CanFilter().CanSort();
        mapper.Property<Article>(x => x.Name).CanFilter().CanSort();
        mapper.Property<Article>(x => x.Unit.Code).HasName("Unit").CanFilter().CanSort();
        mapper.Property<Article>(x => x.IsActive).CanFilter().CanSort();
        mapper.Property<Article>(x => x.Type).CanFilter().CanSort();
    }

    public static IQueryable<Article> GlobalSearchFilter(
        IQueryable<Article> source, string op, string[] values)
    {
        var value = values.First();

        if (value.IsNullOrEmpty())
            return source;

        return op switch
        {
            "@=" => source.Where(x =>
                   x.Code.Contains(value)
                || x.Name.Contains(value)
                || x.Unit.Code.Contains(value)
                || x.Type.ToString().Contains(value)),
            _ => source
        };
    }
}