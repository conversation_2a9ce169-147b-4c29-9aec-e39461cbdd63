using EMessa.Base.Enums;
using EMessa.Base.Extensions;
using EMessa.Core.Common.Results;
using EMessa.Core.Extensions;
using EMessa.Core.Helpers;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using MediatR;
using MessaApi.IndexTranslations;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using OptApi.ArticleStates;

namespace EMessa.Core.Features.Articles.Queries.GetArticleWarehouseStateForCustomer;

public record GetArticleWarehouseStateForCustomerQuery(string ArticleIndex) : IRequest<IResult<GetArticleWarehouseStateForCustomerResponse>>;

public class GetArticleWarehouseStateForCustomerQueryHandler(
    ILogger<GetArticleWarehouseStateForCustomerQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbContextFactory,
    IMessaIndexTranslatorApi messaIndexTranslator<PERSON><PERSON>,
    ICapiArticleStateApi capiArticleStateApi)
    : IRequestHandler<GetArticleWarehouseStateForCustomerQuery, IResult<GetArticleWarehouseStateForCustomerResponse>>
{
    public async Task<IResult<GetArticleWarehouseStateForCustomerResponse>> Handle(GetArticleWarehouseStateForCustomerQuery request, CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbContextFactory.CreateDbContextAsync(cancellationToken);

            var warehouseCodesConf = ConfigurationHelper.GetConfiguration<string>(ConfigurationCodes.WarehouseCodes);
            var warehouseCodes = warehouseCodesConf?.Split(';').ToList() ?? [];

            if (warehouseCodes.Count == 0)
            {
                logger.LogError("No warehouse codes configured.");
                return await Result<GetArticleWarehouseStateForCustomerResponse>.FailAsync(ResultErrorMessages.UnexpectedError);
            }

            var translatedIndex = await messaIndexTranslatorApi.GetIndexTranslation(request.ArticleIndex.ToBase64());

            var articleState = await capiArticleStateApi.GetArticelState(new(warehouseCodes, [translatedIndex.Akronim]));
            if (articleState == null || articleState.Count == 0)
            {
                logger.LogError("No article state found for the given index.");
                return await Result<GetArticleWarehouseStateForCustomerResponse>.FailAsync(ResultErrorMessages.NotFound);
            }

            var response = new GetArticleWarehouseStateForCustomerResponse(articleState.GetQuantityLevelForCollection());
            return await Result<GetArticleWarehouseStateForCustomerResponse>.SuccessAsync(response);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return await Result<GetArticleWarehouseStateForCustomerResponse>.FailAsync(ResultErrorMessages.UnexpectedError);
        }
    }
}
