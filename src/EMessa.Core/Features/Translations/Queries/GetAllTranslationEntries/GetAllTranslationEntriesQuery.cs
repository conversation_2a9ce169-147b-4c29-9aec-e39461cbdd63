using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Translations;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Sieve.Models;

namespace EMessa.Core.Features.Translations.Queries.GetAllTranslationEntries;

public record GetAllTranslationEntriesQuery(SieveModel SieveModel)
    : IRequest<ListResult<GetAllTranslationEntriesResponse>>;

public class GetAllTranslationsQueryHandler(
    IDbContextFactory<ApplicationDbContext> dbFactory,
    ISieveService sieveService)
    : IRequestHandler<GetAllTranslationEntriesQuery, ListResult<GetAllTranslationEntriesResponse>>
{
    public async Task<ListResult<GetAllTranslationEntriesResponse>> Handle(
        GetAllTranslationEntriesQuery request,
        CancellationToken cancellationToken)
    {
        await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

        var translationEntries = dbContext.TranslationEntries
            .Include(te => te.Translation)
            .AsNoTracking()
            .AsQueryable();

        var response = await sieveService
           .ExecuteSieveAsync<TranslationEntry, GetAllTranslationEntriesResponse>(
               request.SieveModel,
               translationEntries,
               cancellationToken);

        return response;
    }
}