using AutoMapper;
using EMessa.Base.Enums;
using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using EMessa.DAL.Data;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Rolls.Queries.GetRollsByOptions;

public record GetRollsByOptionsQuery(int ColorId, int CoatId, int ThickId, RollStatus MaxRollStatus = RollStatus.Active) : IRequest<ListResult<RollResponse>>;

public class GetRollByOptionsQueryHandler(
    ILogger<GetRollByOptionsQueryHandler> logger,
    IDbContextFactory<ApplicationDbContext> dbFactory,
    IRollDataService rollDataService,
    IMapper mapper)
    : IRequestHandler<GetRollsByOptionsQuery, ListResult<RollResponse>>
{
    public async Task<ListResult<RollResponse>> Handle(
        GetRollsByOptionsQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            await using var dbContext = await dbFactory.CreateDbContextAsync(cancellationToken);

            var optionValues = await dbContext
                .OptionValues
                .Where(x => x.Id == request.ColorId ||
                            x.Id == request.CoatId ||
                            x.Id == request.ThickId)
                .Select(x => new { x.Id, x.Code })
                .AsNoTracking()
                .ToListAsync(cancellationToken);

            var rollsFromApi = await rollDataService.GetRollsByOptionsAsync(
                coatCode: optionValues.First(x => x.Id == request.CoatId).Code,
                colorCode: optionValues.First(x => x.Id == request.ColorId).Code,
                thickCode: optionValues.First(x => x.Id == request.ThickId).Code,
                maxRollStatus: request.MaxRollStatus,
                cancellationToken: cancellationToken);

            var mappedRollsFromApi = mapper.Map<List<RollResponse>>(rollsFromApi) ?? [];

            return ListResult<RollResponse>.Success(mappedRollsFromApi, mappedRollsFromApi.Count);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error occurred while handling GetRollsByOptionsQuery: {0}", ex.Message);
            return ListResult<RollResponse>.Failure(ResultErrorMessages.UnexpectedError);
        }
    }
}
