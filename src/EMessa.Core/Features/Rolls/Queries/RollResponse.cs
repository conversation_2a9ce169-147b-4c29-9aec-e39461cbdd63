using EMessa.Base.Enums;

namespace EMessa.Core.Features.Rolls.Queries;

public class RollResponse
{
    public long RollId { get; set; }
    public string OrigNo { get; set; } = null!; // Numer producenta krążka
    public int RollNo { get; set; } // Numer krążka
    public RollStatus Status { get; set; }

    public string Coat { get; set; } = null!;
    public string CoatCode { get; set; } = null!;
    public string Color { get; set; } = null!;
    public string ColorCode { get; set; } = null!;
    public decimal Thick { get; set; }
    
    public string ThickCode { get; set; } = null!;
    public decimal Weight { get; set; }
    public decimal CurrentWeight { get; set; }

    /// <summary>Efektywność: waga blachy (kg/mb)</summary>
    public decimal Efficiency { get; set; }

    public DateTime RollRegisteredDate { get; set; }
    public string Supplier { get; set; } = null!;
    public DateTime CreatedDate { get; set; }
}
