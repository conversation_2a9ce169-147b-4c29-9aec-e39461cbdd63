using AutoMapper;
using EMessa.Core.Common.Results;
using EMessa.Core.Interfaces;
using MediatR;
using Microsoft.Extensions.Logging;

namespace EMessa.Core.Features.Rolls.Queries.GetRollsByNos;

public record GetRollsByNosQuery(params int[] RollNos) : IRequest<ListResult<RollResponse>>;

public class GetRollsByNosQueryHandler(
    ILogger<GetRollsByNosQueryHandler> logger,
    IRollDataService rollDataService,
    IMapper mapper)
    : IRequestHandler<GetRollsByNosQuery, ListResult<RollResponse>>
{
    public async Task<ListResult<RollResponse>> Handle(
        GetRollsByNosQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            var rollFromApi = await rollDataService.GetRollsByNosAsync(request.RollNos, cancellationToken);

            var mappedRollsFromApi = mapper.Map<List<RollResponse>>(rollFromApi);

            return ListResult<RollResponse>.Success(mappedRollsFromApi, mappedRollsFromApi.Count());
        }
        catch (Exception ex)
        {
            logger.LogError(ex, ex.Message);
            return ListResult<RollResponse>.Failure(ResultErrorMessages.UnexpectedError);
        }
    }
}
