using MessaApi.IndexTranslations;
using MessaApi.Orders;
using MessaApi.OrderStatus;
using Microsoft.Extensions.DependencyInjection;
using Refit;

namespace MessaApi
{
    public static class MessaApiRegisterer
    {
        /// <summary>
        /// Regoster Messa refit api clients
        /// </summary>
        /// <param name="services"></param>
        /// <param name="baseApiAddress">Vase API address ands with .../api</param>
        public static void Register(IServiceCollection services, string baseApiAddress, int timeoutSeconds = 10)
        {
            services.AddSingleton<IMessaApiClient, MessaApiClient>();

            services.AddRefitClient<IMessaIndexTranslatorApi>()
                .ConfigureHttpClient(c =>
                {
                    c.BaseAddress = new Uri(baseApiAddress);
                    c.Timeout = TimeSpan.FromSeconds(timeoutSeconds);
                });

            services.AddRefitClient<IMessaOrderStatusApi>()
                .ConfigureHttpClient(c =>
                {
                    c.BaseAddress = new Uri(baseApiAddress);
                    c.Timeout = TimeSpan.FromSeconds(timeoutSeconds);
                });

            services.AddRefitClient<IOrdersApi>()
                .ConfigureHttpClient(c =>
                {
                    c.BaseAddress = new Uri(baseApiAddress);
                    c.Timeout = TimeSpan.FromSeconds(timeoutSeconds);
                });
        }
    }
}
