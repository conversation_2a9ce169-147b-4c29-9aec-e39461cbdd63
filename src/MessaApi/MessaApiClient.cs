using MessaApi.IndexTranslations;
using MessaApi.Orders;
using MessaApi.OrderStatus;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MessaApi;
public interface IMessaApiClient
{
    public IMessaIndexTranslatorApi IndexTranslator { get; }
    public IMessaOrderStatusApi OrderStatus { get; }
    public IOrdersApi OrdersApi { get; }
}

public class MessaApiClient : IMessaApiClient
{
    // Implementation of the MessaApi methods will go here.
    public IMessaIndexTranslatorApi IndexTranslator { get; init; }
    public IMessaOrderStatusApi OrderStatus { get; init; }
    public IOrdersApi OrdersApi { get; init; }

    public MessaApiClient(IMessaIndexTranslatorApi indexTranslator, IMessaOrderStatusApi messaOrderStatusApi, IOrdersApi ordersApi)
    {
        IndexTranslator = indexTranslator ?? throw new ArgumentNullException(nameof(indexTranslator));
        OrderStatus = messaOrderStatusApi ?? throw new ArgumentNullException(nameof(messaOrderStatusApi));
        OrdersApi = ordersApi ?? throw new ArgumentNullException(nameof(ordersApi));
    }
}

