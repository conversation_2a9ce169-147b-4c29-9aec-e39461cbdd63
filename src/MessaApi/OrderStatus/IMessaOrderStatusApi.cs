using Refit;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MessaApi.OrderStatus;

public interface IMessaOrderStatusApi
{
    [Get("/MessaOrderStatus/emessaId/{emessaId}")]
    Task<MessaOrderStatusDTO> GetByEMessaId(int eMessaId);

    [Get("/MessaOrderStatus/emessaNo/{emessaNoBase64}")]
    Task<MessaOrderStatusDTO> GetByEMessaNo(string emessaNoBase64);

    [Get("/MessaOrderStatus/messaId/{messaId}")]
    Task<MessaOrderStatusDTO> GetByMessaId(long messaId);

    [Get("/MessaOrderStatus/messaNo/{messaNo}")]
    Task<MessaOrderStatusDTO> GetByMessaNo(int messaNo);
}