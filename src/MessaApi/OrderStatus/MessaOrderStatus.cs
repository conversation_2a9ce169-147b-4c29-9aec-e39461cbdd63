using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MessaApi.OrderStatus
{
    public enum MessaOrderStatus
    {
        [Display(Name = "", ShortName = "-")]
        Unknown = -1,
        [Display(Name = "Nowe", ShortName = "N")]
        New = 0,
        //[Display(Name = "Częściowo zaplanowane", ShortName = "CP")]
        //PartiallyPlaned = 1,
        [Display(Name = "Zaplanowane", ShortName = "P")]
        Planed = 2,
        [Display(Name = "W produkcji", ShortName = "W")]
        Production = 4,
        [Display(Name = "Magazyn", ShortName = "M")]
        Storage = 6,
        //[Display(Name = "Częściowo zrealizowane", ShortName = "CZ")]
        //PartiallyFinished = 7,
        [Display(Name = "Anulowane", ShortName = "A")]
        Cancelled = 8,
        [Display(Name = "Transport", ShortName = "T")]
        Transport = 10,
        [Display(Name = "Dostarczone", ShortName = "D")]
        Dostarczone = 12
    }

    public enum OrderItemTypeEnum
    {
        [Display(Name = "Nieokreślony", ShortName = "N", Description = "Nieokreślony")]
        Unknown = 0,
        [Display(Name = "Produkt", ShortName = "P", Description = "Produkt własny")]
        OrderItem = 1,
        [Display(Name = "Towar", ShortName = "T", Description = "Towar handlowy")]
        OrderTradeItem = 2,
        [Display(Name = "Usługa", ShortName = "U", Description = "Usługa")]
        Service = 3
    }
}
