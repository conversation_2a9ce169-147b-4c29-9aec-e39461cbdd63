using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace MessaApi.IndexTranslations
{
    public class MessaIndexTranslation
    {
        /// <summary>
        /// Id tłumaczenia
        /// </summary>
        public int Id { get; set; }
        /// <summary>
        /// Index artykułu z eMessa
        /// </summary>
        public string eMessaIndex { get; set; } = null!;
        /// <summary>
        /// Akronim z Comarch Optima
        /// </summary>
        public string Akronim { get; set; } = null!;
    }

    //public class IndexTranslationDTO
    //{
    //    [DataMember]
    //    public int Id { get; set; }
    //    [DataMember]
    //    public string eMessaIndex { get; set; }
    //    [DataMember]
    //    public string Akronim { get; set; }
    //}
}
