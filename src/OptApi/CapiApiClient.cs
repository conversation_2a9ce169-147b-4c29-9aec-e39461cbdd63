using OptApi.ArticleStates;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace OptApi;

public interface ICapiApiClient
{
    ICapiArticleStateApi ArticleStateApi { get; }
}

public class CapiApiClient : ICapiApiClient
{
    // Implementation of the CapiApiClient methods will go here.
    public ICapiArticleStateApi ArticleStateApi { get; private set; }
    public CapiApiClient(ICapiArticleStateApi articleStateApi)
    {
        ArticleStateApi = articleStateApi ?? throw new ArgumentNullException(nameof(articleStateApi));
    }
}
