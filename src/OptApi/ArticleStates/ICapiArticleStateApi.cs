using Refit;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace OptApi.ArticleStates;

public interface ICapiArticleStateApi
{
    [Get("/ArticleState/{warehouseCode}/{articleCode}")]
    Task<List<ArticleStateDTO>> GetArticelState(string warehouseCode, string articleCode);

    [Post("/ArticleState")]
    Task<List<ArticleStateDTO>> GetArticelState([Body] GetWarehouseArticleStateQuery query);
}
