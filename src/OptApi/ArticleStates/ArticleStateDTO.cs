using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace OptApi.ArticleStates;

public class ArticleStateDTO
{
    public int Id { get; set; }
    public string Code { get; set; } = null!;
    public string Name { get; set; } = null!;
    public decimal Quantity { get; set; }
    public string Unit { get; set; } = null!;
    public decimal MinQuantity { get; set; }
    public decimal MaxQuantity { get; set; }
    public decimal QuantityReserved { get; set; }
    public decimal QuantityNet { get; set; }
    /// <summary>
    /// Ilość zamawiana - do kontroli etykiet ilości mało, średnio, dużo
    /// </summary>
    public decimal OrderQuantity { get; set; }

    public int WarehouseId { get; set; }
    public string WarehouseCode { get; set; } = null!;
}
