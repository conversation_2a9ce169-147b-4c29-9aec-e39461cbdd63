namespace OptApi.ArticleStates;

public class GetWarehouseArticleStateQuery
{
    public GetWarehouseArticleStateQuery()
    { }

    public GetWarehouseArticleStateQuery(List<string> warehouseCodes, List<string> articleCodes, DateTime? date = null)
    {
        WarehouseCodes = warehouseCodes;
        ArticleCodes = articleCodes;
        Date = date;
    }

    public List<string> WarehouseCodes { get; set; } = [];
    public List<string> ArticleCodes { get; set; } = [];
    public DateTime? Date { get; set; }
}
