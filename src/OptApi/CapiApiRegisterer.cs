using Microsoft.Extensions.DependencyInjection;
using OptApi.ArticleStates;
using Refit;

namespace OptApi;

public static class CapiApiRegisterer
{
    /// <summary>
    /// Register Capi refit api clients
    /// </summary>
    /// <param name="services">IServiceCollection</param>
    /// <param name="baseApiAddress">base api addres and with */api</param>
    public static void Register(IServiceCollection services, string baseApiAddress, int timeoutSeconds = 10)
    {
        services.AddSingleton<ICapiApiClient, CapiApiClient>();

        services.AddRefitClient<ICapiArticleStateApi>()
            .ConfigureHttpClient(c =>
            {
                c.BaseAddress = new Uri(baseApiAddress);
                c.Timeout = TimeSpan.FromSeconds(timeoutSeconds);
            });
    }
}

