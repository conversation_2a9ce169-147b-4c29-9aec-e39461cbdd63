// wwwroot/js/emessa.js
import {
    downloadFileFromStream,
    uploadFile
} from './file-utils.js';
import {
    getBrowserLanguage,
    windowResized,
    forceResize,
    registerBlazorPage,
} from './blazor-utils.js';

window.EMessa ??= {};
Object.assign(window.EMessa, {
    // files
    downloadFileFromStream,
    uploadFile,
    // blazor
    getBrowserLanguage,
    forceResize,
    registerBlazorPage,
    windowResized
});

// Nasłuchiwanie zdarzeń resize
window.addEventListener('resize', windowResized);