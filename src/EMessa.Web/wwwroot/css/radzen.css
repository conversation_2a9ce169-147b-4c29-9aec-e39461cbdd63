:root {
}

body {
    position: relative;
    background: #FFFFFF;
}

/*dodaj w kontenerze aby inne style zadziałały*/
.emessa {

}

/* <PERSON><PERSON><PERSON><PERSON><PERSON> wygląd <PERSON><PERSON> (Google Material Icons) */
.rzi {
    font-variation-settings: 'FILL' 0, 'wght' 300, 'GRAD' 0, 'opsz' 48;
    font-size: 28px;
}

/* Domyślny wygląd ikonek Radzen (Font Awesome) */
@font-face {
    font-family: 'Font Awesome 6 Free';
    font-style: normal;
    font-weight: 900;
    src: url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/webfonts/fa-solid-900.woff2') format('woff2');
}

.rzi.font-awesome {
    font-family: 'Font Awesome 6 Free' !important;
    font-weight: 900;
    font-style: normal;
    font-size: 20px;
}

/* RZ */
:root {
    --esa-size-16: 16px;
    --esa-size-24: 24px;
    --esa-size-32: 32px;
}

.esa-button-size {
    width: var(--esa-size-16);
    height: var(--esa-size-16);
}

/* RZ */
.bg-add {
    @apply !bg-green-50;
}

.bg-edit {
    @apply !bg-amber-50;
}

.bg-esa-todo {
    @apply !bg-orange-500;
}

.bg-esa-add {
    @apply !bg-green-500;
}

.bg-esa-edit {
    @apply !bg-amber-500;
}

.bg-esa-delete {
    @apply !bg-red-500;
}

.bg-esa-add-form {
    @apply !bg-green-50;
}

.bg-esa-edit-form {
    @apply !bg-amber-50;
}

.bg-esa-disabled {
    background-color: var(--rz-base-200);
}

.esa-actions-row {
    @apply flex flex-row gap-2 justify-center;
}

.esa-actions-col {
    @apply flex flex-col gap-2 items-center;
}

/* RX TABS */
.rz-tabview-nav {
    flex-wrap: wrap !important;
}
/* RX TABS */

/* RX GRID */
.rz-grid-table-striped tbody tr.bg-row-selected td {
    @apply !bg-blue-100;
}
/* RX GRID */

/* RZ CHECKBOX */
:root {
    --chkbox-border-width: 2px;
    --chkbox-icon-color: var(--rz-base-light);
    --chkbox-size: 18px;
    --checkbox-icon-size: var(--chkbox-size);
    --rz-checkbox-icon-width: var(--chkbox-size);
    --rz-checkbox-icon-height: var(--chkbox-size);
    --rz-checkbox-icon-font-size: var(--chkbox-size);
    --rz-checkbox-width: var(--chkbox-size);
    --rz-checkbox-height: var(--chkbox-size);
    /*Enabled colors*/
    --chkbox-enabled-active-color: var(--rz-primary); /*Active Background*/
    --chkbox-enabled-inactive-color: var(--rz-base-light); /*Inactive Background*/
    --chkbox-enabled-border-color: var(--rz-primary); /*Active Border*/
    /*Disabled colors*/
    --chkbox-disabled-active-color: var(--rz-base-dark); /*Inactive Background*/
    --chkbox-disabled-inactive-color: var(--rz-base-light); /*Inactive Background*/
    --chkbox-disabled-border-color: var(--rz-base-dark); /*Inactive Border*/
}

.rz-chkbox-box .rzi-check:before {
    content: "check";
    font-variation-settings: 'FILL' 1, 'wght' 600, 'GRAD' 0, 'opsz' 48;
    font-weight: bold;
}

.rz-chkbox-box .rzi-times:before {
    content: "remove";
    font-variation-settings: 'FILL' 1, 'wght' 600, 'GRAD' 0, 'opsz' 48;
    font-weight: bold;
}

.rz-chkbox-box {
    position: absolute;
    cursor: pointer;
    inset: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border: var(--rz-input-border);
    border-width: var(--chkbox-border-width);
    border-radius: var(--rz-checkbox-border-radius);
    border-color: var(--chkbox-enabled-border-color);
    box-shadow: var(--rz-input-shadow);
    background-color: var(--chkbox-enabled-inactive-color);
    transition: var(--rz-transition-all);
}

.rz-chkbox-box:hover:not(.rz-state-disabled) {
    border-width: var(--chkbox-border-width);
}

.rz-chkbox-box.rz-state-disabled {
    cursor: initial;
    color: var(--rz-input-disabled-color);
    box-shadow: var(--rz-input-disabled-shadow);
    background-color: var(--chkbox-disabled-inactive-color);
    border-color: var(--chkbox-disabled-border-color);
    border-width: var(--chkbox-border-width);
    opacity: var(--rz-input-disabled-opacity);
}

.rz-chkbox-box .rzi {
    width: var(--checkbox-icon-size);
    height: var(--checkbox-icon-size);
    font-size: var(--rz-checkbox-icon-font-size);
    color: var(--chkbox-icon-color);
    vertical-align: middle;
    background-color: transparent;
    border-radius: var(--rz-checkbox-checked-icon-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Dla aktywnego (zaznaczonego) stanu */
.rz-chkbox-box.rz-state-active {
    background-color: var(--chkbox-enabled-active-color);
    border-color: var(--chkbox-enabled-active-color);
}

/* Dla aktywnego stanu po najechaniu */
.rz-chkbox-box.rz-state-active:hover:not(.rz-state-disabled) {
    background-color: var(--chkbox-enabled-active-color);
    border-color: var(--chkbox-enabled-active-color);
}

/* Dla wyłączonego aktywnego stanu */
.rz-chkbox-box.rz-state-active.rz-state-disabled {
    background-color: var(--chkbox-disabled-active-color);
    border-color: var(--chkbox-disabled-active-color);
    opacity: 0.5;
}

/* RZ CHECKBOX */

/* RZ NUMERIC */
:root {
    --emessa-numeric-color: var(--rz-text-color);
}

.emessa .rz-numeric,
.emessa .rz-numeric .rz-inputtext {
    border-radius: var(--rz-input-border-radius);
    border-color: var(--emessa-numeric-color);
}

.emessa .rz-numeric.rz-state-disabled,
.emessa .rz-numeric:disabled {
    border-color: var(--emessa-numeric-color);
    opacity: 0.7;
}

.emessa .rz-numeric.rz-state-disabled .rz-inputtext,
.emessa .rz-numeric:disabled .rz-inputtext {
    color: var(--emessa-numeric-color);
    -webkit-text-fill-color: var(--emessa-numeric-color);
}
/* RZ NUMERIC */

/* RZ DROPDOWN */
/* Custom styling for disabled RadzenDropDown to improve visibility */
.emessa .rz-dropdown.rz-state-disabled,
.emessa .rz-dropdown:disabled {
    opacity: 0.7;
    background-color: var(--rz-base-200);
}

.emessa .rz-dropdown.rz-state-disabled .rz-inputtext,
.emessa .rz-dropdown:disabled .rz-inputtext {
    color: var(--rz-base-900);
    -webkit-text-fill-color: var(--rz-base-900);
    background-color: var(--rz-base-200);
}

.emessa .rz-dropdown.rz-state-disabled .rz-dropdown-label,
.emessa .rz-dropdown:disabled .rz-dropdown-label {
    color: var(--rz-base-900);
    -webkit-text-fill-color: var(--rz-base-900);
}

/* Disabled dropdown border styling - ciemniejszy border tylko dla disabled */
.emessa .rz-dropdown.rz-state-disabled,
.emessa .rz-dropdown:disabled {
    border-color: var(--rz-base-400) !important;
}

/* Warning dropdown class for validation styling */
.emessa .warning-dropdown {
    border-color: var(--rz-warning) !important;
    box-shadow: 0 0 0 1px var(--rz-warning) !important;
}

.emessa .warning-dropdown:focus {
    border-color: var(--rz-base-400) !important;
    box-shadow: 0 0 0 2px var(--rz-warning-lighter) !important;
}

/* RZ DROPDOWN */


@media (max-width: 768px) {
    .rz-dialog:not(.rz-dialog-confirm):not(.rz-dialog-alert) {
        position: absolute;
        width: 100% !important;
        max-width: 95vw !important;
        max-height: 90vh !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        border-radius: 0 !important;
    }
}

/* Zakładki (Tabs) zawijają się, gdy nie mieszczą się na ekranie. */
ul[role=tablist] {
    flex-wrap: wrap;
}

/* Zmniejszenie domyślnego paddingu 20px dla contentu RadzenFieldset */
.rz-fieldset-content {
    padding: 14px;
}

/* Radzen dialog content */
.rz-dialog {
    padding: 0.5rem;
}

.rz-dialog-content {
    padding: 0.5rem;
}

.highlighted-grid-row td {
    background-color: var(--color-emerald-300) !important;
    color: #000;
}

.highlighted-grid-row:hover td {
    background-color: var(--color-emerald-400) !important;
}

/* Nadpisanie domyślnego stylu nagłówka RadzenDataGrid */
.rz-custom-header {
    width: 100%;
}