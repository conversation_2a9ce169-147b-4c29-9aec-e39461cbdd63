<Project Sdk="Microsoft.NET.Sdk.Web">
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <UserSecretsId>aspnet-EMessa.Web-e5d10a62-6cc5-4f07-8094-2858ab514c6c</UserSecretsId>
        <Configurations>Debug;Release;BDP_Develop</Configurations>
    </PropertyGroup>

    <PropertyGroup>
        <VersionPrefix>1.0</VersionPrefix>
        <TodayYYMMDD>$([System.DateTime]::UtcNow.ToString('yyMMdd'))</TodayYYMMDD>
        <Version>$(VersionPrefix).$(TodayYYMMDD)</Version>
        <AssemblyVersion>1.0.0.0</AssemblyVersion>
        <FileVersion>$(Version).0</FileVersion>
        <InformationalVersion>$(Version)</InformationalVersion>
    </PropertyGroup>

    <ItemGroup>
        <Content Remove="Shared\CardBody.razor" />
        <Compile Remove="Shared\DraftEditor\DraftPoint.cs" />
    </ItemGroup>

    <ItemGroup>
        <Content Include="postcss.config.js" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="FluentValidation" Version="11.11.0" />
        <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.11.0" />
        <PackageReference Include="MediatR" Version="12.5.0" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="8.0.10" />
        <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="8.0.5" />
        <PackageReference Include="Radzen.Blazor" Version="6.6.3" />
        <PackageReference Include="Scriban" Version="5.12.1" />
        <PackageReference Include="Serilog" Version="4.0.2" />
        <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
        <PackageReference Include="Serilog.Extensions.Logging" Version="8.0.0" />
        <PackageReference Include="Serilog.Settings.Configuration" Version="8.0.4" />
        <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
        <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
        <PackageReference Include="Swashbuckle.AspNetCore.Swagger" Version="9.0.3" />
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="9.0.3" />
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="9.0.3" />
        <PackageReference Include="Syncfusion.Blazor.Buttons" Version="27.1.51" />
        <PackageReference Include="Syncfusion.Blazor.Cards" Version="27.1.51" />
        <PackageReference Include="Syncfusion.Blazor.FileManager" Version="27.1.51" />
        <PackageReference Include="Syncfusion.Blazor.FileManager.PhysicalFileProvider" Version="27.1.51" />
        <PackageReference Include="Syncfusion.Blazor.Grid" Version="27.1.51" />
        <PackageReference Include="Syncfusion.Blazor.Navigations" Version="27.1.51" />
        <PackageReference Include="Syncfusion.Blazor.RichTextEditor" Version="27.1.51" />
        <PackageReference Include="Syncfusion.Blazor.Themes" Version="27.1.51" />
        <PackageReference Include="Syncfusion.Blazor.DropDowns" Version="27.1.51" />
        <PackageReference Include="Syncfusion.Blazor.TreeGrid" Version="27.1.51" />
        <PackageReference Include="Syncfusion.Licensing" Version="27.1.51" />
    </ItemGroup>

    <ItemGroup>
        <Compile Update="Resources\SfResources.Designer.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>SfResources.resx</DependentUpon>
        </Compile>
    </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Resources\SfResources.resx">
       <Generator>PublicResXFileCodeGenerator</Generator>
       <LastGenOutput>SfResources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="8.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="8.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.8">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\EMessa.Core\EMessa.Core.csproj" />
        <ProjectReference Include="..\EMessa.Services\EMessa.Services.csproj" />
        <ProjectReference Include="..\MessaApi\MessaApi.csproj" />
        <ProjectReference Include="..\OptApi\OptApi.csproj" />
		<ProjectReference Include="..\TranslationService\TranslationService.csproj" />
	</ItemGroup>
	<ItemGroup>
	  <Content Update="wwwroot\images\editor\readme.txt">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </Content>
	</ItemGroup>
    <ItemGroup>
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\css\animation.css" />
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\css\bootstrap.min.css" />
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\css\designer.css" />
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\css\fontello-codes.css" />
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\css\fontello-embedded.css" />
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\css\fontello-ie7-codes.css" />
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\css\fontello-ie7.css" />
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\css\fontello.css" />
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\lib\jquery-3.6.0.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\lib\jquery-3.6.0.min.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\lib\node\canvas.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\lib\node\extend.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\lib\node\self.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\lib\node\xml.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\lib\paper-core.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\lib\paper-core.min.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\lib\paper-full.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\lib\paper-full.min.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\lib\toolbar\.gitignore" />
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\lib\toolbar\bower.json" />
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\lib\toolbar\Gruntfile.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\lib\toolbar\jquery.toolbar.css" />
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\lib\toolbar\jquery.toolbar.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\lib\toolbar\jquery.toolbar.min.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\lib\toolbar\LICENSE.txt" />
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\lib\toolbar\package.json" />
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\lib\toolbar\README.md" />
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\lib\toolbar\toolbar.jquery.json" />
        <_ContentIncludedByDefault Remove="wwwroot\js\flashing\lib\victor.min.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\jQuery\jQuery.3.6.4.js" />
        <_ContentIncludedByDefault Remove="Pages\DraftEditor\DraftEditor.razor" />
        <_ContentIncludedByDefault Remove="Pages\DraftEditor\DraftEditor2.razor" />
        <_ContentIncludedByDefault Remove="Shared\SvgEditor2\SvgEditor2.razor" />
        <_ContentIncludedByDefault Remove="Shared\SvgEditor\SvgEditor.razor" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\buttons.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\const.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\css.css" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\css\animation.css" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\css\bootstrap.min.css" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\css\designer.css" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\css\fontello-codes.css" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\css\fontello-embedded.css" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\css\fontello-ie7-codes.css" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\css\fontello-ie7.css" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\css\fontello.css" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\designer.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\DimensionsClass.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\DrawLineClass.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\enums.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\events.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\extendPrototypes.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\font\fontello.eot" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\font\fontello.svg" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\font\fontello.ttf" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\font\fontello.woff" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\font\fontello.woff2" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\globals.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\index.html" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\LabelClass.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\lib\bootstrap.bundle.min.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\lib\fontawesome\74f909ebdb.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\lib\jquery-3.7.1.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\lib\jquery-3.7.1.min.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\lib\node\canvas.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\lib\node\extend.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\lib\node\self.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\lib\node\xml.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\lib\paper-core.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\lib\paper-core.min.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\lib\paper-full.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\lib\paper-full.min.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\lib\<EMAIL>" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\lib\<EMAIL>" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\lib\toolbar\.gitignore" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\lib\toolbar\bower.json" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\lib\toolbar\Gruntfile.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\lib\toolbar\jquery.toolbar.css" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\lib\toolbar\jquery.toolbar.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\lib\toolbar\jquery.toolbar.min.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\lib\toolbar\LICENSE.txt" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\lib\toolbar\package.json" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\lib\toolbar\README.md" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\lib\toolbar\toolbar.jquery.json" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\lib\victor.min.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\sizing.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\SvgEditor.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\tools.js" />
        <_ContentIncludedByDefault Remove="wwwroot\js\svgEditor\ToolsBarClass.js" />
    </ItemGroup>
    <ItemGroup>
        <!-- Ten skrypt uruchamiany z bun przypisze sekrety do zmiennych w pliku appsettings.json -->
        <Content Include="inject-secrets.ts" CopyToPublishDirectory="Always" />
        <Content Include="App_Offline.htm" CopyToPublishDirectory="Always" />
    </ItemGroup>
    
	<Choose>
		<When Condition="'$(Configuration)' == 'BDP_Develop'">
			<ItemGroup>
				<None Include="appsettings.json" CopyToOutputDirectory="Never" CopyToPublishDirectory="Always" />
				<None Include="appsettings.Development.json" CopyToOutputDirectory="Never" CopyToPublishDirectory="Never" />
				<Content Remove="appsettings.*.json" />
			</ItemGroup>
		</When>
    </Choose>
</Project>
