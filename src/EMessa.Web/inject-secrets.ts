#!/usr/bin/env bun
import { readFileSync, writeFileSync } from 'fs';

/**
 * Skrypt transformuje appsettings.json zamieniając wartości będące nazwami zmiennych środowiskowych
 * na rzeczywiste wartości z process.env
 *
 * Użycie: bun run transform-appsettings.ts [ścieżka-wejściowa] [ścieżka-wyjściowa]
 */

// Eksportujemy funkcję do testowania
export function isEnvironmentVariableName(value: unknown): value is string {
    // Sprawdza czy string wygląda jak nazwa zmiennej środowiskowej
    // - składa się tylko z wielkich liter, cyfr i podkreślników
    // - zaczyna się od wielkiej litery
    // - MUSI zawierać przynajmniej jeden podkreślnik
    return typeof value === 'string' && /^[A-Z][A-Z0-9_]*_[A-Z0-9_]*$/.test(value);
}

// Wydzielamy logikę przetwarzania konfiguracji do osobnej funkcji do testowania
export function processConfig(filePath: string, outputPath: string = filePath): { config: any, missingVars: string[] } {
    const jsonContent: string = readFileSync(filePath, 'utf8');
    const missingVars: string[] = [];

    // Transformuj konfigurację używając reviver function
    const config = JSON.parse(jsonContent, (key: string, value: any) => {
        if (isEnvironmentVariableName(value)) {
            const envValue = process.env[value];
            if (envValue !== undefined) {
                return envValue;
            } else {
                console.log(`⚠️  Nie znaleziono zmiennej środowiskowej: ${value}`);
                missingVars.push(value);
                return value; // Zostaw oryginalną wartość
            }
        }
        return value;
    });

    // Zapisz do pliku wyjściowego
    const outputJson: string = JSON.stringify(config, null, 2);
    writeFileSync(outputPath, outputJson, 'utf8');

    return { config, missingVars };
}

function main(): void {
    // Pobierz ścieżki do plików z argumentów lub użyj domyślnych
    const filePath: string = process.argv[2] || 'appsettings.json';
    const outputPath: string = process.argv[3] || 'appsettings.json';

    try {
        console.log(`🔍  Przetwarzam plik: ${filePath}`);
        if (filePath !== outputPath) {
            console.log(`📁  Plik wyjściowy: ${outputPath}`);
        }
        console.log('Zmienne środowiskowe muszą mieć podkreślnik _ i być wielką literą, mogą mieć cyfry');

        // Użyj funkcji processConfig
        const { config, missingVars } = processConfig(filePath, outputPath);
        
        console.log(`✅  Plik został zapisany jako: ${outputPath}`);

        if (missingVars.length > 0) {
            console.log(`⚠️  Brakujące zmienne środowiskowe (${missingVars.length}):`);
            missingVars.forEach(varName => {
                console.log(`   - ${varName}`);
            });

            // Zwróć kod błędu gdy brakuje zmiennych
            console.log('❌  Transformacja niepełna - brakuje zmiennych środowiskowych');
            process.exit(1);
        } else {
            console.log('🎉  Wszystkie zmienne środowiskowe zostały znalezione i zamienione!');
            process.exit(0);
        }

    } catch (error: unknown) {
        if (error && typeof error === 'object' && 'code' in error && error.code === 'ENOENT') {
            console.error(`❌  Nie można znaleźć pliku: ${filePath}`);
        } else if (error instanceof SyntaxError) {
            console.error(`❌  Błąd parsowania JSON: ${error.message}`);
        } else if (error instanceof Error) {
            console.error(`❌  Błąd: ${error.message}`);
        } else {
            console.error(`❌  Nieznany błąd: ${error}`);
        }
        process.exit(2);
    }
}

// Uruchom skrypt tylko gdy wywoływany bezpośrednio (nie importowany)
if (import.meta.main) {
    main();
}