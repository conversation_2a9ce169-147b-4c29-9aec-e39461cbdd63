<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trwa aktualizacja</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f5f5f5;
        }

        .card {
            background-color: white;
            padding: 24px 24px;
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border: 2px solid #ef4444;
        }

        .message {
            text-align: center;
            font-size: 24px;
            color: #333;
            margin: 0;
        }

        .spinner {
            margin-top: 20px;
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #ef4444;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: auto;
            margin-right: auto;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
    </style>
</head>
<body>
<div class="card">
    <div class="message">
        Trwa aktualizacja strony
        <div>
            <div class="spinner"></div>
        </div>
    </div>
</div>
<script>
    function checkIfAppIsBack() {
        fetch(window.location.href + '?' + new Date().getTime())
            .then(response => {
                if (!response.url.includes('App_Offline.htm')) {
                    window.location.reload();
                }
            })
            .catch(() => {
            });
    }

    setInterval(checkIfAppIsBack, 5000);
</script>
</body>
</html>