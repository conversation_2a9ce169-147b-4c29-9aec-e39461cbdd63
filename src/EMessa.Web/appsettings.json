{"ConnectionStrings": {"DefaultConnection": "DATABASE_CONNECTION_STRING"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Error"}}, "AllowedHosts": "*", "SyncfusionLicenseKey": "SYNCFUSION_LICENSE_KEY", "AdminEmail": "ADMIN_EMAIL", "AdminPassword": "ADMIN_PASSWORD", "EmailSettings": {"From": "EMAIL_FROM_ADDRESS", "SmtpServer": "SMTP_SERVER", "Port": 0, "Username": "SMTP_USERNAME", "Password": "SMTP_PASSWORD", "UseSsl": true}, "AzureTranslationApi": {"ApiAddress": "https://api-eur.cognitive.microsofttranslator.com", "ApiRegion": "westeurope", "ApiKey1": "AZURE_TRANSLATION_API_KEY1", "ApiKey2": "AZURE_TRANSLATION_API_KEY2"}, "GusApiUserKey": "GUS_API_USER_KEY", "BdpCustomerShortName": "BDP", "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Error", "Microsoft.EntityFrameworkCore": "Warning", "Microsoft.AspNetCore": "Error"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:u}] [{Level:u3}] {SourceContext} {Message:lj}{NewLine}{Exception}", "shared": false}}, {"Name": "File", "Args": {"path": "C:/log/emessa7/log.txt", "rollingInterval": "Day", "outputTemplate": "[{Timestamp:u}] [{Level:u3}] {SourceContext} {Message:lj}{NewLine}{Exception}", "shared": false}}], "Enrich": ["FromLogContext", "WithExceptionDetails"]}, "FileUploadSettings": {"DraftsPath": ".drafts", "SaleImagesPath": ".saleImages", "AllowedSaleImageExtensions": ["jpg", "jpeg", "png"], "MaxSizeOfSaleImagesInMb": 30}, "Gemini": {"ApiKey": "GEMINI_API_KEY"}, "RollsApi": {"RollsApiBaseAddress": "ROLLS_API_BASE_ADDRESS", "TimeoutSeconds": 10}, "MessaApi": {"MessaApiBaseAddress": "MESSA_API_BASE_ADDRESS", "TimeoutSeconds": 10}, "OptimaApi": {"OptimaApiBaseAddress": "OPTIMA_API_BASE_ADDRESS", "TimeoutSeconds": 10}}