@using EMessa.Core.Features.Customers.Queries.Get
@using EMessa.Core.Features.Factories.Queries.Get
@using EMessa.Core.Features.Orders.Queries.GetOrderItemDraft
@using EMessa.Web.Models
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor

@inject DialogService DialogService
@inject ILogger<SelectFactoryOnAcceptOrderDialog> Logger

<ProgressBar IsLoading="@_isLoading" Visible="@_isLoading" />

<RadzenText class="mb-2">
    @("Wybierz zakład realizacji zamówienia nr {0}.".Tr(OrderNo))
</RadzenText>

<RadzenText class="mb-3">
    @($"Domyślny zakład klienta '{_customer?.ShortName}': {_factory?.Name ?? "brak".Tr()}.")
</RadzenText>

<FactoriesSelector @bind-Value="@_factoryId"
                   ShowActiveOnly />

<RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.End" class="mt-4">
    <CancelRadzenButton Click="@OnCancel"
                        Size="@GetButtonSize()" />

    <SaveRadzenButton Text="@("Zatwierdź".Tr())"
                      Icon="check_circle"
                      Click="@OnAccept"
                      Size="@GetButtonSize()" />
</RadzenStack>

@code {
    [Parameter] public required string OrderNo { get; set; }
    [Parameter] public required int CustomerId { get; set; }

    private bool _isLoading;
    private GetCustomerResponse? _customer;
    private GetFactoryResponse? _factory;
    private int _factoryId;

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        _isLoading = true;

        var response = await Mediator.Send(new GetCustomerQuery(CustomerId));

        if (response.Succeeded)
        {
            _customer = response.Data;

            if (!response.Data.FactoryId.HasValue)
            {
                _isLoading = false;
                return;
            }

            _factoryId = response.Data.FactoryId.Value;

            var facotryResponse = await Mediator.Send(new GetFactoryQuery(_factoryId));

            if (facotryResponse.Succeeded)
            {
                _factory = facotryResponse.Data;
            }
        }

        _isLoading = false;

    }

    private void OnAccept()
    {
        DialogService.Close(_factoryId);
    }

    private void OnCancel() => DialogService.Close(null);

    private ButtonSize GetButtonSize() => AppStateService.IsLargeScreen ? ButtonSize.Medium : ButtonSize.Small;
}
