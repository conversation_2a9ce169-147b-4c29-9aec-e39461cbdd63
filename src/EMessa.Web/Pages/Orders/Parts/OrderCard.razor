@using EMessa.Core.Features.Orders.Queries.GetAllOrders
@using EMessa.Core.Features.Sales.Queries.GetAllSales
@using EMessa.Web.Components.RadzenComponents.Badges
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor

<div class="p-2 bg-white shadow-md rounded-lg border border-gray-200 text-wrap">
    <RadzenStack Orientation="Orientation.Horizontal" class="mb-1 text-xl font-semibold text-gray-800"
                 JustifyContent="JustifyContent.SpaceBetween">
        <RadzenStack>
            @Order.No
        </RadzenStack>
        <RadzenStack>
            <OrderStatusBadge Status="@Order.Status" />
        </RadzenStack>
    </RadzenStack>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Nr. Messa".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@Order.MessaNo</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Nr. własny".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@Order.CustomerNo</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Klient".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@Order.CustomerShortName</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Lokalizacja".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@Order.CustomerLocationName</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Oddział".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@Order.BranchName</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Utworzono".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@(Order.CreatedDate.ToDisplayLocalDateTime())</span>
    </div>
</div>

@code {
    [Parameter] public required GetAllOrdersResponse Order { get; set; }
}
