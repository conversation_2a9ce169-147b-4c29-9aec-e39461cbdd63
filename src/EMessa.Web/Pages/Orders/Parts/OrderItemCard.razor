@using EMessa.Base.Enums
@using EMessa.Core.Features.Orders.ApiQueries.Common
@using EMessa.Core.Features.Orders.Queries.Common
@using EMessa.Core.Features.Orders.Queries.GetAllOrders
@using EMessa.Core.Features.Sales.Queries.GetAllSales
@using EMessa.Web.Components.RadzenComponents.Badges
@using Ra<PERSON>zen
@using Radzen.Blazor

@inject TooltipService TooltipService

<div class="p-2 bg-white shadow-md rounded-lg border border-gray-200 text-wrap">
    <RadzenStack Orientation="Orientation.Horizontal" class="mb-1"
                 JustifyContent="JustifyContent.SpaceBetween"
                 AlignItems="AlignItems.Center">
        <RadzenStack class="text-xl font-semibold text-gray-800">
            @OrderItem.ArticleTranslatedName
        </RadzenStack>
        <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center">
            @if (OrderItem.SaleId != null)
            {
                <NavLink href="@($"/admin/sales/details/{OrderItem.SaleId}")">
                    <RadzenIcon Icon="percent" class="text-xl text-sky-600"
                                MouseEnter="@((er) => TooltipService.Open(er, "Produkt z promocji.".Tr(), new TooltipOptions() { Position = TooltipPosition.Left, Style = "white-space: normal; word-wrap: break-word;" }))" />
                </NavLink>
            }
            @if (OrderItem.ArticleType == ArticleType.Complex)
            {
                <div>
                    <OrderStatusBadge Status="@OrderItem.Status" />
                </div>
            }
        </RadzenStack>
    </RadzenStack>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Index".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@OrderItem.Index</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Ilość".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@OrderItem.Quantity</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Koszt".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@OrderItem.PriceCurrencyDisplay</span>
    </div>
</div>

@code {
    [Parameter] public required OrderItemResponse OrderItem { get; set; }
}
