@using EMessa.Core.Features.Orders.Queries.GetOrderItemDraft
@using EMessa.Web.Models
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor

@inject DialogService DialogService
@inject ILogger<SheetPreviewDialog> Logger

<ProgressBar IsLoading="@_isLoading" Visible="@_isLoading" />

@if(_draft is not null)
{
    <RadzenRow AlignItems="AlignItems.Start" RowGap="0.5rem" Gap="0.5rem" class="p-3">
        <RadzenImage Path="@_draft.PreviewUrl" />
    </RadzenRow>
}

@code {
    [Parameter] public required int OrderItemId { get; set; }

    private bool _isLoading;
    private GalleryImage? _draft;

    protected override async Task OnInitializedAsync()
    {
        await LoadImages();
    }

    private async Task LoadImages()
    {
        _isLoading = true;

        var result = await Mediator.Send(new GetOrderItemDraftQuery(OrderItemId));

        if (result.Succeeded)
        {
            _draft = new GalleryImage
            {
                Id = 1,
                FileName = result.Data.FileName,
                PreviewUrl = $"data:image/{result.Data.FileExtension};base64,{Convert.ToBase64String(result.Data.FileContent!)}"
            };
        }
        else
        {
            Logger.LogError("Failed to load order item draft: {Messages}", result.Messages);
            ToastService.Show(ToastType.LoadDataError, result.Messages);
        }

        await InvokeAsync(StateHasChanged);
        _isLoading = false;
    }

    private void OnCancel() => DialogService.Close();
}
