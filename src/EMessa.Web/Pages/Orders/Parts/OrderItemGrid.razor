@using EMessa.Base.Enums
@using EMessa.Core.Features.Orders.ApiQueries.Common
@using EMessa.Core.Features.Orders.Queries.Common
@using EMessa.Web.Components.RadzenComponents.Badges
@using EMessa.Web.Constants
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor

@inject DialogService DialogService
@inject TooltipService TooltipService

<RadzenDataGrid @ref="_grid" Data="@OrderItems" TItem="OrderItemResponse"
                AllowPaging="false" AllowColumnResize
                AllowSorting
                AllowFiltering=@AllowFiltering
                FilterMode="FilterMode.Simple"
                Density="Density.Compact"
                EmptyText="@RadzenDataGridConstants.EmptyText"
                ExpandMode="DataGridExpandMode.Single"
                RowRender="@OnRowRender"
                CellClick="@OnCellClick"
                class="mt-2"
                SelectionMode="DataGridSelectionMode.Single"
                @bind-Value="@_selectedRow"
                GridLines="@RadzenDataGridConstants.DefaultDataGridLines">
    <LoadingTemplate />

    <Template Context="data">
        <RadzenRow Gap="2rem" class="ml-6">

            <RadzenColumn SizeMD="12" SizeLG="5" Visible=@ShowOptionValuesTable>
                <RadzenDataGrid AllowFiltering="false" AllowPaging="false" AllowSorting AllowColumnResize=true
                                Data="@data.OptionValues" TItem="OrderItemProductOptionResponse"
                                EmptyText="@RadzenDataGridConstants.EmptyText"
                                Density="Density.Compact"
                                GridLines="@RadzenDataGridConstants.DefaultDataGridLines">
                    <HeaderTemplate>
                        <RadzenText Text="@("Opcje produktu".Tr())" TextAlign="TextAlign.Center"
                                    class="w-full text-center font-semibold text-base m-0" />
                    </HeaderTemplate>
                    <Columns>
                        <RadzenDataGridColumn Property="@nameof(OrderItemProductOptionResponse.OptionName)"
                                              Title="@("Opcja".Tr())" TextAlign="TextAlign.Right"
                                              WhiteSpace="WhiteSpace.Wrap" />

                        <RadzenDataGridColumn Property="@nameof(OrderItemProductOptionResponse.OptionValue)"
                                              Title="@("Wartość".Tr())" WhiteSpace="WhiteSpace.Wrap" />

                    </Columns>
                </RadzenDataGrid>
            </RadzenColumn>

            <RadzenColumn SizeMD="12" SizeLG="7" Visible=@ShowSheetsTable>
                <RadzenDataGrid AllowFiltering="false" AllowPaging="false" AllowSorting AllowColumnResize=true
                                Data="@data.RequestedSheets" TItem="OrderItemRequestedSheetResponse"
                                EmptyText="@RadzenDataGridConstants.EmptyText"
                                Density="Density.Compact"
                                GridLines="@RadzenDataGridConstants.DefaultDataGridLines">
                    <HeaderTemplate>

                        <RadzenText Text="@("Arkusze".Tr())" TextAlign="TextAlign.Center"
                                    class="w-full text-center font-semibold text-base m-0" />

                    </HeaderTemplate>
                    <Columns>
                        <RadzenDataGridColumn Property="@nameof(OrderItemRequestedSheetResponse.Quantity)"
                                              Title="@("Ilość".Tr())"
                                              TextAlign="TextAlign.Right"
                                              WhiteSpace="WhiteSpace.Wrap"
                                              Width="15%">
                            <Template Context="sheet">
                                @sheet.Quantity szt
                            </Template>
                            <FooterTemplate>
                                <RadzenText class="m-0 font-bold text-sm">
                                    @data.RequestedSheets.Sum(x => x.Quantity).ToString() szt
                                </RadzenText>
                            </FooterTemplate>
                        </RadzenDataGridColumn>

                        <RadzenDataGridColumn Property="@nameof(OrderItemRequestedSheetResponse.Length)"
                                              Title="@("Długość".Tr())" TextAlign="TextAlign.Right"
                                              WhiteSpace="WhiteSpace.Wrap">
                            <Template Context="sheet">
                                @sheet.Length.ToString("n3") mb
                            </Template>
                            <FooterTemplate>
                                <RadzenText class="m-0 font-bold text-sm">
                                    @("Długość całkowita: ") @data.RequestedSheets.Sum(x => x.SumMb).ToString() mb
                                </RadzenText>
                            </FooterTemplate>
                        </RadzenDataGridColumn>

                        <RadzenDataGridColumn Property="@nameof(OrderItemRequestedSheetResponse.Width)"
                                              Title="@("Szerokość".Tr())" TextAlign="TextAlign.Right"
                                              WhiteSpace="WhiteSpace.Wrap">
                            <Template Context="sheet">
                                @sheet.Width.ToString("n3") mb
                            </Template>
                            <FooterTemplate>
                                <RadzenText class="m-0 font-bold text-sm">
                                    @("Powierzchnia całkowita: ") @data.RequestedSheets.Sum(x => x.SumM2).ToString() m2
                                </RadzenText>
                            </FooterTemplate>
                        </RadzenDataGridColumn>
                    </Columns>
                </RadzenDataGrid>

            </RadzenColumn>
        </RadzenRow>
    </Template>

    <Columns>
        <RadzenDataGridColumn Title="@("Akcje".Tr())"
                              Visible="@(ShowActionsColumn)"
                              Width="6%"
                              TextAlign="TextAlign.Center"
                              Sortable="false"
                              Filterable="false">
            <Template Context="orderItem">
                <div class="@(AppStateService.IsLargeScreen ? "esa-actions-row" : "esa-actions-col")">
                    <EditRadzenButton IsAction Click="@(async () => await EditOrderItem.InvokeAsync(orderItem.Id))"/>
                    <DeleteRadzenButton IsAction Click="@(async () => await DeleteOrderItem.InvokeAsync(orderItem.Id))"/>
                </div>
            </Template>
        </RadzenDataGridColumn>

        <RadzenDataGridColumn Property="@nameof(OrderItemResponse.Status)"
                              Title="@("Status".Tr())"
                              Visible="@(AppStateService.IsLargeScreen)"
                              Width="12%"
                              WhiteSpace="WhiteSpace.Wrap"
                              TextAlign="TextAlign.Center"
                              Sortable
                              FilterProperty="@nameof(OrderItemResponse.Status)"
                              FilterValue="@_statusFilter">
            <FilterTemplate>
                <RadzenDropDown @bind-Value="@_statusFilter"
                                TValue="OrderStatus?"
                                Data="@_availableStatusFilter"
                                AllowClear>
                    <Template Context="data">
                        <OrderStatusBadge Status="@data" />
                    </Template>
                    <ValueTemplate Context="data">
                        <OrderStatusBadge Status="@data" />
                    </ValueTemplate>
                </RadzenDropDown>
            </FilterTemplate>
            <Template Context="orderItem">
                @*@if (orderItem.ArticleType == ArticleType.Complex)
                {
                    <OrderStatusBadge Status="@orderItem.Status" />
                }*@
                <OrderStatusBadge Status="@orderItem.Status" />
            </Template>
        </RadzenDataGridColumn>

        <RadzenDataGridColumn Property="@nameof(OrderItemResponse.ArticleTranslatedName)"
                              Title="@("Produkt".Tr())"
                              Visible="@(AppStateService.IsLargeScreen)"
                              Width="18%"
                              WhiteSpace="WhiteSpace.Wrap" />

        <RadzenDataGridColumn Property="@nameof(OrderItemResponse.Index)"
                              Visible="@(AppStateService.IsLargeScreen)"
                              Title="@("Index".Tr())" WhiteSpace="WhiteSpace.Wrap" />

        <RadzenDataGridColumn Property="@nameof(OrderItemResponse.DraftEditable)"
                              Visible="@(AppStateService.IsLargeScreen && ShowDraftColumn)"
                              Width="5%"
                              TextAlign="TextAlign.Center"
                              Title="@("Szkic".Tr())" WhiteSpace="WhiteSpace.Wrap">
            <Template Context="orderItem">
                @if (orderItem.DraftEditable)
                {
                    <RadzenButton Size="ButtonSize.ExtraSmall" Icon="frame_inspect"
                                  Click="@(async _ => await ShowSheetPreview(orderItem.Id))" />
                }
            </Template>
        </RadzenDataGridColumn>

        <RadzenDataGridColumn Property="@nameof(OrderItemResponse.Comments)"
                              Visible="@(AppStateService.IsLargeScreen)"
                              Title="@("Komentarz".Tr())" WhiteSpace="WhiteSpace.Wrap" />

        <RadzenDataGridColumn Property="@nameof(OrderItemResponse.QuantityUnitDisplay)"
                              Visible="@(AppStateService.IsLargeScreen)" Width="9%" TextAlign="TextAlign.Right"
                              Title="@("Ilość".Tr())" WhiteSpace="WhiteSpace.Wrap">
            <FooterTemplate>
                <RadzenText class="m-0 font-bold text-sm">
                    @("Suma".Tr()):
                </RadzenText>
            </FooterTemplate>
        </RadzenDataGridColumn>

        <RadzenDataGridColumn Property="@nameof(OrderItemResponse.PriceCurrencyDisplay)"
                              Visible="@(AppStateService.IsLargeScreen)" Width="9%" TextAlign="TextAlign.Right"
                              Title="@("Koszt".Tr())" WhiteSpace="WhiteSpace.Wrap">
            <FooterTemplate>
                <RadzenText class="m-0 font-bold text-sm">
                    @OrderItems.Sum(x => x.FinalPrice).ToString("n2") @OrderItems.FirstOrDefault()?.CurrencyCode
                </RadzenText>
            </FooterTemplate>
        </RadzenDataGridColumn>

        <RadzenDataGridColumn Property="@nameof(OrderItemResponse.SaleId)"
                              Visible="@(AppStateService.IsLargeScreen && ShowSaleColumn)"
                              Width="5%"
                              Title="@("Prom.".Tr())" TextAlign="TextAlign.Center" WhiteSpace="WhiteSpace.Wrap">
            <Template Context="orderItem">
                @if (orderItem.SaleId != null)
                {
                    @if (AppStateService.UserData.HasAnyRole(Role.Administrator, Role.SaleManager))
                    {
                        <NavLink href="@($"/admin/sales/details/{orderItem.SaleId}")">
                            <RadzenIcon Icon="percent" class="text-xl text-sky-600"
                                        MouseEnter="@((er) => TooltipService.Open(er, "Produkt z promocji.".Tr(), new TooltipOptions() { Position = TooltipPosition.Left, Style = "white-space: normal; word-wrap: break-word;" }))" />
                        </NavLink>
                    }
                    else
                    {
                        <RadzenIcon Icon="percent" class="text-xl text-sky-600"
                                    MouseEnter="@((er) => TooltipService.Open(er, "Produkt z promocji.".Tr(), new TooltipOptions() { Position = TooltipPosition.Left, Style = "white-space: normal; word-wrap: break-word;" }))" />
                    }
                }
            </Template>
        </RadzenDataGridColumn>

        <RadzenDataGridColumn Visible="@(!AppStateService.IsLargeScreen)" Filterable="false" Sortable="false">
            <Template Context="orderItem">
                <OrderItemCard OrderItem="@orderItem" />
            </Template>
            <FooterTemplate>
                <RadzenText class="m-0 font-bold text-base">
                    @("Suma".Tr()): @OrderItems.Sum(x => x.FinalPrice).ToString("n2") @OrderItems.FirstOrDefault()?.CurrencyCode
                </RadzenText>
            </FooterTemplate>
        </RadzenDataGridColumn>
    </Columns>
</RadzenDataGrid>

@code {
    [Parameter] public IEnumerable<OrderItemResponse> OrderItems { get; set; } = [];
    [Parameter] public bool ShowDraftColumn { get; set; }
    [Parameter] public bool ShowSaleColumn { get; set; }
    [Parameter] public bool ShowSheetsTable { get; set; }
    [Parameter] public bool ShowOptionValuesTable { get; set; }
    [Parameter] public bool ShowActionsColumn { get; set; }
    [Parameter] public bool AllowFiltering { get; set; }
    [Parameter] public EventCallback<int> EditOrderItem { get; set; }
    [Parameter] public EventCallback<int> DeleteOrderItem { get; set; }

    private RadzenDataGrid<OrderItemResponse> _grid = new();
    private IList<OrderItemResponse>? _selectedRow;
    private IEnumerable<OrderStatus> _statusesInTab = [OrderStatus.New, OrderStatus.Sent, OrderStatus.Accepted, OrderStatus.Production, OrderStatus.Warehouse, OrderStatus.Transport, OrderStatus.Rejected];
    private IEnumerable<OrderStatus> _availableStatusFilter = [];
    private OrderStatus? _statusFilter;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);

        if (firstRender)
        {
            _availableStatusFilter = OrderAccessConstants.GetStatusesForRole(AppStateService.UserData.Roles.ToArray()).ToList();
        }
    }

    private void OnCellClick(DataGridCellMouseEventArgs<OrderItemResponse> args)
    {
        var nonClickableColumns = new[] { nameof(OrderItemResponse.DraftEditable), nameof(OrderItemResponse.SaleId) };

        if ((args.Data.RequestedSheets.Any() || args.Data.OptionValues.Any()) &&
            !nonClickableColumns.Contains(args.Column.Property))
            _grid.ExpandRow(args.Data);
    }

    private void OnRowRender(RowRenderEventArgs<OrderItemResponse> args)
    {
        if ((!args.Data.RequestedSheets.Any() || !ShowSheetsTable) &&
            (!args.Data.OptionValues.Any() || !ShowOptionValuesTable))
            args.Expandable = false;
    }

    private async Task ShowSheetPreview(int orderItemId)
    {
        await DialogService.OpenAsync<SheetPreviewDialog>(
            "Podgląd szkicu".Tr(),
            new Dictionary<string, object>() { { "OrderItemId", orderItemId } },
            new DialogOptions()
            {
                Resizable = true,
                Draggable = true,
                Width = "auto",
                Height = "auto"
            });
    }


}
