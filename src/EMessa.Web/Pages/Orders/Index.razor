@page "/orders/index"
@attribute [Authorize(Roles = $"{Role.Administrator},{Role.TradeManager},{Role.Trade},{Role.Production},{Role.ClientManager},{Role.Client}")]

@using EMessa.Base.Enums
@using EMessa.Core.Features.Orders
@using EMessa.Core.Features.Orders.Queries.Common
@using EMessa.Core.Features.Orders.Queries.GetAllOrders
@using EMessa.Core.Features.Orders.Queries.GetOrderCountsByStatus
@using EMessa.Core.Features.Sales.Commands.CancelSale
@using EMessa.Core.Features.Sales.Commands.DeleteSale
@using EMessa.Core.Features.Sales.Queries.GetAllSales
@using EMessa.DAL.Entities.Articles
@using EMessa.Web.Components.RadzenComponents.Badges
@using EMessa.Web.Components.Grids
@using EMessa.Web.Constants
@using EMessa.Web.Pages.Orders.Parts
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor

@inject ILogger<Index> Lo<PERSON>
@inject IModalDialogService ModalDialogService
@inject IFakeOrdersService FakeOrdersService
@inject NavigationManager Navigation
@inject TooltipService TooltipService
@implements IDisposable

<IndexCard Title=@("Zamówienia".Tr())>
    <ChildContent>

        <RadzenStack Orientation="Orientation.Horizontal"
                     Visible="@(AppStateService.IsLargeScreen)"
                     class="m-2"
                     Gap=".2rem">
            <RadzenButton Text="@("Wszystkie".Tr())"
                          Size="ButtonSize.Medium"
                          ButtonStyle="ButtonStyle.Base"
                          Click="@(async () => { _statusFilter = null; await _grid.Reload(); })"
                          Disabled="@(_isLoading || _statusFilter is null)" />

            @foreach (var status in _statusesInTab.Where(status => _availableStatusFilter.Contains(status)))
            {
                <RadzenButton Text="@($"{@status.GetDisplayName()} ({GetStatusCount(status)})")"
                              Size="ButtonSize.Medium"
                              class="@($"{OrderStatusExtensions.ColorCssClassBg(status)} text-white")"
                              Click="@(async () => { _statusFilter = status; await _grid.Reload(); })"
                              Disabled="@(_isLoading || _statusFilter == status)" />
            }
        </RadzenStack>

        <RadzenDropDown @bind-Value=@_statusFilter
                        TValue="OrderStatus?"
                        Change="_grid.Reload"
                        Data="@_availableStatusFilter"
                        AllowClear
                        Placeholder="@($"{"Wybierz status...".Tr()} ({_orderStatusCounts.Sum(x => x.Count)} {"zamówień".Tr()})")"
                        Visible="@(!AppStateService.IsLargeScreen)">
            <Template Context="data">
                <OrderStatusBadge Status="@data"
                                  AdditionalText="@($"({GetStatusCount(data)})")" />
            </Template>
            <ValueTemplate Context="data">
                <OrderStatusBadge Status="@data"
                                  AdditionalText="@($"({GetStatusCount(data)})")" />
            </ValueTemplate>
        </RadzenDropDown>

        <GridToolbar Items="@([GridToolbar.Search])"
                     SearchTextChanged="@HandleSearch">

            <DateFromToFilter IsLoading="@_isLoading"
                              DateFrom="@_createdFrom"
                              DateTo="@_createdTo"
                              DateRangePreset="@DateFromToFilter.DateRangeOption.Last30Days"
                              OnChange="@(async (dates) => { _createdFrom = dates.CreatedFrom; _createdTo = dates.CreatedTo; await _grid.Reload(); })" />

        </GridToolbar>
        <ProgressBar IsLoading="@_isLoading" Visible="@_isLoading" />
        <RadzenDataGrid @ref="_grid" TItem="GetAllOrdersResponse"
                        Data="@_gridData" Count="@_count" LoadData="@LoadData" IsLoading="@_isLoading"
                        AllowFiltering FilterMode="FilterMode.Simple"
                        FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                        AllowPaging PageSize="@PageSize.DefaultPageSize" PageSizeOptions="@PageSize.Pages20To200"
                        PageSizeText="@PageSize.FormatPaginationInfo(_grid)"
                        AllowSorting AllowMultiColumnSorting
                        AllowColumnResize
                        RowClick="@((args) => { if (args.Data.OrderItems.Any()) { _grid.ExpandRow(args.Data); } _grid.SelectRow(args.Data); })"
                        RowDoubleClick="@((args) => Navigation.NavigateTo($"/orders/details/{args.Data.Id}"))"
                        Density="Density.Compact"
                        EmptyText="@RadzenDataGridConstants.EmptyText"
                        ExpandMode="DataGridExpandMode.Single"
                        SelectionMode="DataGridSelectionMode.Single"
                        @bind-Value="@_selectedRow"
                        RowRender="@OnRowRender"
                        GridLines="@RadzenDataGridConstants.DefaultDataGridLines"
                        class="mt-2">
            <LoadingTemplate />

            <Template Context="data">
                <RadzenRow class="ml-6">

                    <OrderItemGrid OrderItems="data.OrderItems"
                                   ShowSaleColumn />

                </RadzenRow>
            </Template>

            <Columns>
                <RadzenDataGridColumn Context="order "
                                      Title=@(AppStateService.IsLargeScreen ? "Akcje".Tr() : string.Empty)
                                      Filterable="false" Sortable="false"
                                      TextAlign="TextAlign.Center"
                                      Width="@(AppStateService.IsLargeScreen ? "55px" : "30px")"
                                      FrozenPosition="FrozenColumnPosition.Right">
                    <Template Context="order">
                        <div class="@(AppStateService.IsLargeScreen ? "flex flex-row gap-2 justify-center" : "flex flex-col gap-2 items-center")">
                            <DetailsNavRadzenButton Href="@($"/orders/details/{order.Id}")"
                                                    IsAction="true" />
                        </div>
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Property="@nameof(GetAllOrdersResponse.Status)" Title=@("Status".Tr())
                                      Width="160px" Visible="@AppStateService.IsLargeScreen"
                                      WhiteSpace="WhiteSpace.Wrap"
                                      TextAlign="TextAlign.Center"
                                      Sortable FilterProperty="@nameof(GetAllOrdersResponse.Status)"
                                      FilterOperator="FilterOperator.Equals" FilterValue="@_statusFilter">
                    <FilterTemplate>
                        <RadzenDropDown @bind-Value="@_statusFilter"
                                        TValue="OrderStatus?"
                                        Data="@_availableStatusFilter"
                                        AllowClear>
                            <Template Context="data">
                                <OrderStatusBadge Status="@data" />
                            </Template>
                            <ValueTemplate Context="data">
                                <OrderStatusBadge Status="@data" />
                            </ValueTemplate>
                        </RadzenDropDown>
                    </FilterTemplate>
                    <Template Context="status">
                        <OrderStatusBadge Status="@status.Status" />
                    </Template>
                </RadzenDataGridColumn>


                <RadzenDataGridColumn Property="@nameof(GetAllOrdersResponse.No)" Title=@("Numer".Tr())
                                      Width="12%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                      Sortable Filterable />

                <RadzenDataGridColumn Property="@nameof(GetAllOrdersResponse.MessaNo)" Title=@("Nr. Messa".Tr())
                                      Width="12%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                      Sortable Filterable />

                <RadzenDataGridColumn Property="@nameof(GetAllOrdersResponse.CustomerNo)" Title=@("Nr. własny".Tr())
                                      Width="30%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                      Sortable Filterable />

                <RadzenDataGridColumn Property="@nameof(GetAllOrdersResponse.CustomerShortName)" Title=@("Klient".Tr())
                                      Width="30%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                      Sortable Filterable />

                <RadzenDataGridColumn Property="@nameof(GetAllOrdersResponse.CustomerLocationName)" Title=@("Lokalizacja".Tr())
                                      Width="30%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                      Sortable Filterable />

                <RadzenDataGridColumn Property="@nameof(GetAllOrdersResponse.BranchName)" Title=@("Oddział".Tr())
                                      Width="30%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                      Sortable Filterable />

                <RadzenDataGridColumn Property="@nameof(GetAllOrdersResponse.CreatedDate)" Title=@("Utworzono".Tr())
                                      Width="20%" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable=false
                                      FormatString="@DateFormatProvider.DisplayDateFormat">
                    <Template Context="data">
                        @DateFormatProvider.ToDisplayLocalDateTime(data.CreatedDate)
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Visible="@(!AppStateService.IsLargeScreen)" Filterable="false" Sortable="false">
                    <Template Context="data">
                        <OrderCard Order="@data" />
                    </Template>
                </RadzenDataGridColumn>
            </Columns>
        </RadzenDataGrid>
    </ChildContent>

</IndexCard>

@code {
    private RadzenDataGrid<GetAllOrdersResponse> _grid = new();
    private IEnumerable<GetAllOrdersResponse> _gridData = [];
    private int _count;
    private bool _isLoading;
    private string? _searchString;
    private IEnumerable<OrderStatus> _statusesInTab = [OrderStatus.New, OrderStatus.Sent, OrderStatus.Accepted, OrderStatus.Preparation, OrderStatus.Production, OrderStatus.Warehouse, OrderStatus.Transport, OrderStatus.Rejected];
    private IEnumerable<OrderStatus> _availableStatusFilter = [];
    private OrderStatus? _statusFilter = null;
    private DateTime? _createdFrom;
    private DateTime? _createdTo;
    private IEnumerable<GetOrderCountsByStatusResponse> _orderStatusCounts = [];
    private IList<GetAllOrdersResponse>? _selectedRow;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);

        if (firstRender)
        {
            _availableStatusFilter = OrderAccessConstants.GetStatusesForRole(AppStateService.UserData.Roles.ToArray()).ToList();
        }
    }

    private async Task LoadData(LoadDataArgs args)
    {
        _isLoading = true;

        var sieveModel = args.ToSieveModel().ApplyGlobalSearchFilter(_searchString);

        sieveModel.AddFilterWhenNoNull(
            nameof(GetAllOrdersResponse.Status),
            _statusFilter,
            "==");

        sieveModel.AddFilterWhenNoNull(
            nameof(GetAllOrdersResponse.CreatedDate),
            _createdFrom.ToUtcDateTimeStringForSieveFilter(),
            ">=");

        sieveModel.AddFilterWhenNoNull(
            nameof(GetAllOrdersResponse.CreatedDate),
            _createdTo.ToUtcDateTimeStringForSieveFilter(),
            "<=");

        var response = await Mediator.Send(new GetAllOrdersQuery(sieveModel));

        await GetStatusesCount();

        if (response.Succeeded)
        {
            _gridData = response.Data;
            _count = response.TotalCount;
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError, response.Messages);
            _gridData = [];
            _count = 0;
        }

        _isLoading = false;
        await InvokeAsync(StateHasChanged);
    }

    private async Task GetStatusesCount()
    {
        var response = await Mediator.Send(new GetOrderCountsByStatusQuery(CreatedFrom: _createdFrom, CreatedTo: _createdTo));

        if (response.Succeeded)
        {
            _orderStatusCounts = response.Data;
        }
    }

    private string GetStatusCount(OrderStatus status)
    {
        return _orderStatusCounts.FirstOrDefault(x => x.Status == status)?.Count.ToString() ?? "0";
    }

    private void HandleSearch(string searchString)
    {
        _searchString = searchString;
        _grid.Reload();
    }

    private void OnRowRender(RowRenderEventArgs<GetAllOrdersResponse> args)
    {
        if (!args.Data.OrderItems.Any())
        {
            args.Expandable = false;
        }
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        if (AppStateService != null)
        {
            AppStateService.StateChanged += OnStateChanged;
        }
    }

    void IDisposable.Dispose()
    {
        if (AppStateService != null)
        {
            AppStateService.StateChanged -= OnStateChanged;
        }
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }
}
