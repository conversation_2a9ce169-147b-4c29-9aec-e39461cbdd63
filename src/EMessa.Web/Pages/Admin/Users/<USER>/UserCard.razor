@using EMessa.Core.Features.Users.Queries.GetAll
@using EMessa.Web.Constants
@using EMessa.Web.Extensions.EnumBadges
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor

<div class="p-3 bg-white shadow-md rounded-lg border border-gray-200 text-wrap">
    <div class="mb-2 text-xl font-semibold text-gray-800">
        @User.FullName
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Email".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@User.Email</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Klient".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@User.CustomerShortName</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Lokalizacje".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@User.Locations</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Oddziały klienta".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@User.CustomerBranchName</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Oddziały użytkownika".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@User.Branches</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Zakład".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@User.FactoryName</span>
    </div>

    <div class="mb-1 flex items-start">
        <span class="text-sm font-bold text-gray-600">@("Role".Tr()):</span>
        <div class="ml-2 flex flex-wrap gap-2">
            @foreach (var role in User.Roles)
            {
                <RadzenBadge IsPill="true"
                             Variant="Variant.Outlined"
                             Style="@GetRoleStyle(role, User.Roles)"
                             Text="@role.Tr()" />
            }
        </div>
    </div>

    <div class="flex flex-wrap items-center gap-6 mt-2">
        <div class="flex items-center gap-2">
            <RadzenCheckBox @bind-Value="@(User.Active)" Disabled />
            <span class="text-sm text-gray-700">@("Aktywny".Tr())</span>
        </div>

        <div class="flex items-center gap-2">
            <RadzenCheckBox @bind-Value="@(User.EmailNotifications)" Disabled />
            <span class="text-sm text-gray-700">@("Email".Tr())</span>
        </div>

        <div class="flex items-center gap-2">
            <RadzenCheckBox @bind-Value="@(User.SmsNotifications)" Disabled />
            <span class="text-sm text-gray-700">@("SMS".Tr())</span>
        </div>
    </div>
</div>

@code {
    [Parameter] public GetAllUsersResponse User { get; set; } = new();

    private string GetRoleStyle(string role, IEnumerable<string> userRoles)
    {
        var style = $"background-color:{RoleExtensions.RoleColorMap[role]};color:white;border:none;box-shadow:none;";

        return style;
    }
}
