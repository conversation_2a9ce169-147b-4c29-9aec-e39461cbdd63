@using System.Linq.Expressions;
@using EMessa.Core.Features.CustomerLocalizations.Queries.Get
@using Ra<PERSON>zen
@using Radzen.Blazor

<RadzenDropDown TValue="IEnumerable<GetCustomerLocalizationsResponse>"
                Placeholder="@("Wybierz".Tr())"
                Data="@_customerLocationsFiltered"
                Value="@_selectedValue"
                ValueChanged="@(async (value) => await SetSelectedValueAsync(value as IEnumerable<GetCustomerLocalizationsResponse>))"
                MaxSelectedLabels="99"
                Multiple
                AllowFiltering
                Chips
                AllowClear
                LoadData="@OnLoadData"
                class="w-full">
    <Template Context="context">
        @context.Name
        @if (!context.IsActive)
        {
            <RadzenBadge BadgeStyle="BadgeStyle.Danger" Variant="Variant.Outlined" Text="@("Nieaktywny".Tr())"
                         class="ml-2" />
        }
    </Template>
    <ValueTemplate Context="context">
        @context.Name
        @if (!context.IsActive)
        {
            <RadzenBadge BadgeStyle="BadgeStyle.Danger" Variant="Variant.Outlined" Text="@("Nieaktywny".Tr())"
                         class="ml-2" />
        }
    </ValueTemplate>
</RadzenDropDown>

@code {
    [CascadingParameter] public EditContext? EditContext { get; set; }
    [Parameter] public Expression<Func<object>>? For { get; set; }
    [Parameter] public int? CustomerId { get; set; }
    [Parameter] public bool ShowActiveOnly { get; set; } = true;
    [Parameter] public bool IncludeSelectedIfInactive { get; set; } = true;

    [Parameter] public IEnumerable<int>? Value { get; set; }
    [Parameter] public EventCallback<IEnumerable<int>?> ValueChanged { get; set; }

    private IEnumerable<GetCustomerLocalizationsResponse> _selectedValue = [];
    private IEnumerable<GetCustomerLocalizationsResponse> _customerLocations = [];
    private IEnumerable<GetCustomerLocalizationsResponse> _customerLocationsFiltered = [];
    private int? _customerId = null;

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        if (CustomerId is not null)
        {
            await Refresh();
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        if (_customerId is null && CustomerId is not null)
        {
            _customerId = CustomerId;
            await Refresh();

            _selectedValue = [];
            var value = Value as IEnumerable<int>;
            if (value?.Any() == true)
            {
                if (IncludeSelectedIfInactive)
                {
                    _selectedValue = _customerLocations.Where(x => value.Contains(x.Id)).ToList();
                }
                else
                {
                    _selectedValue = _customerLocations.Where(x => value.Contains(x.Id) && x.IsActive).ToList();
                }
            }
            await SetSelectedValueAsync(_selectedValue);
        }
        else if (_customerId is not null && _customerId != CustomerId)
        {
            _customerId = CustomerId;
            await Refresh();
            await SetSelectedValueAsync([]);
        }
    }

    public async Task Refresh()
    {
        if (CustomerId is not null)
        {
            var request = new GetCustomerLocalizationsQuery(CustomerId.Value);

            if (ShowActiveOnly && !IncludeSelectedIfInactive)
                request.SieveModel.Filters = "IsActive==true";

            var response = await Mediator.Send(request);

            if (response.Succeeded)
            {
                _customerLocations = response.Data;

                SetDropDownItems();
            }
            else
            {
                ToastService.Show(ToastType.LoadDataError, "Błąd pobierania lokalizacji".Tr());
            }
        }
        else
        {
            _customerLocations = [];
            SetDropDownItems();
        }
    }

    private void SetDropDownItems()
    {
        if (ShowActiveOnly)
            _customerLocationsFiltered = _customerLocations.Where(x => x.IsActive).ToList();
        else
            _customerLocationsFiltered = _customerLocations.ToList();
    }

    private void OnLoadData(LoadDataArgs args)
    {
        if (string.IsNullOrWhiteSpace(args.Filter))
        {
            SetDropDownItems();
        }
        else
        {
            var lower = args.Filter.ToLower();
            _customerLocationsFiltered = _customerLocations
                .Where(x => x.Name?.ToLower().Contains(lower) == true || x.City?.ToLower().Contains(lower) == true)
                .ToList();
        }

        InvokeAsync(StateHasChanged);
    }

    private async Task SetSelectedValueAsync(IEnumerable<GetCustomerLocalizationsResponse>? value)
    {
        _selectedValue = value ?? [];

        if (value is null)
            Value = Array.Empty<int>();
        else
            Value = value.Select(x => x.Id).ToArray();

        SetDropDownItems();

        await ValueChanged.InvokeAsync(Value);

        if (EditContext != null && For != null)
        {
            EditContext.NotifyFieldChanged(FieldIdentifier.Create(For));
        }

        await InvokeAsync(StateHasChanged);
    }
}
