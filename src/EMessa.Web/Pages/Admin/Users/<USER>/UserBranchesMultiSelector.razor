@using System.Linq.Expressions;
@using EMessa.Core.Features.Branches.Queries.GetAll
@using Ra<PERSON>zen
@using Radzen.Blazor

<RadzenDropDown TValue="IEnumerable<GetAllBranchesResponse>"
                Placeholder="@("Wybierz".Tr())"
                Data="@_customerLocationsFiltered"
                Value="@_selectedValue"
                ValueChanged="@(async (value) => await SetSelectedValueAsync(value as IEnumerable<GetAllBranchesResponse>))"
                MaxSelectedLabels="99"
                Multiple
                AllowFiltering
                Chips
                AllowClear
                LoadData="@OnLoadData"
                class="w-full">
    <Template Context="context">
        @context.Name
        @if (!context.IsActive)
        {
            <RadzenBadge BadgeStyle="BadgeStyle.Danger" Variant="Variant.Outlined" Text="@("Nieaktywny".Tr())"
                         class="ml-2" />
        }
    </Template>
    <ValueTemplate Context="context">
        @context.Name
        @if (!context.IsActive)
        {
            <RadzenBadge BadgeStyle="BadgeStyle.Danger" Variant="Variant.Outlined" Text="@("Nieaktywny".Tr())"
                         class="ml-2" />
        }
    </ValueTemplate>
</RadzenDropDown>

@code {
    [CascadingParameter] public EditContext? EditContext { get; set; }
    [Parameter] public Expression<Func<object>>? For { get; set; }
    [Parameter] public bool ShowActiveOnly { get; set; } = true;
    [Parameter] public bool IncludeSelectedIfInactive { get; set; } = true;

    [Parameter] public IEnumerable<int>? Value { get; set; }
    [Parameter] public EventCallback<IEnumerable<int>?> ValueChanged { get; set; }

    private IEnumerable<GetAllBranchesResponse> _selectedValue = [];
    private IEnumerable<GetAllBranchesResponse> _customerLocations = [];
    private IEnumerable<GetAllBranchesResponse> _customerLocationsFiltered = [];

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        await Refresh();
    }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        if (Value is IEnumerable<int> valueList)
        {
            if (!valueList.All(x => _selectedValue.Select(x => x.Id).Contains(x)))
            {
                await Refresh();

                _selectedValue = [];
                if (valueList.Any())
                {
                    if (IncludeSelectedIfInactive)
                    {
                        _selectedValue = _customerLocations.Where(x => valueList.Contains(x.Id)).ToList();
                    }
                    else
                    {
                        _selectedValue = _customerLocations.Where(x => valueList.Contains(x.Id) && x.IsActive).ToList();
                    }
                }
                await SetSelectedValueAsync(_selectedValue);
            }
        }
    }

    public async Task Refresh()
    {
        var request = new GetAllBranchesQuery();

        if (ShowActiveOnly && !IncludeSelectedIfInactive)
            request.SieveModel.Filters = "IsActive==true";

        var response = await Mediator.Send(request);

        if (response.Succeeded)
        {
            _customerLocations = response.Data;

            SetDropDownItems();
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError, "Błąd pobierania oddziałów".Tr());
        }
    }

    private void SetDropDownItems()
    {
        if (ShowActiveOnly)
            _customerLocationsFiltered = _customerLocations.Where(x => x.IsActive).ToList();
        else
            _customerLocationsFiltered = _customerLocations.ToList();
    }

    private void OnLoadData(LoadDataArgs args)
    {
        if (string.IsNullOrWhiteSpace(args.Filter))
        {
            SetDropDownItems();
        }
        else
        {
            var lower = args.Filter.ToLower();
            _customerLocationsFiltered = _customerLocations
                .Where(x => x.Name?.ToLower().Contains(lower) == true || x.City?.ToLower().Contains(lower) == true)
                .ToList();
        }

        InvokeAsync(StateHasChanged);
    }

    private async Task SetSelectedValueAsync(IEnumerable<GetAllBranchesResponse>? value)
    {
        _selectedValue = value ?? [];

        if (value is null)
            Value = Array.Empty<int>();
        else
            Value = value.Select(x => x.Id).ToArray();

        SetDropDownItems();

        await ValueChanged.InvokeAsync(Value);

        if (EditContext != null && For != null)
        {
            EditContext.NotifyFieldChanged(FieldIdentifier.Create(For));
        }

        await InvokeAsync(StateHasChanged);
    }
}
