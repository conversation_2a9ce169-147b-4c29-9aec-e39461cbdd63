@page "/admin/users/edit/{UserId:int?}"
@attribute [Authorize(Roles = $"{Role.Administrator},{Role.TradeManager},{Role.Trade}")]

@using EMessa.Base.Enums
@using EMessa.Core.Features.CustomerLocalizations.Queries.Get
@using EMessa.Core.Features.Factories.Commands.AddEdit
@using EMessa.Core.Features.Factories.Queries.Get
@using EMessa.Core.Features.Roles.GetAll
@using EMessa.Core.Features.Users.Commands.AddEdit
@using EMessa.Core.Features.Users.Queries.Get
@using EMessa.Web.Components.Editors
@using EMessa.Web.Constants
@using EMessa.Web.Pages.Admin.Users.Parts
@using <PERSON><PERSON>zen
@using Radzen.Blazor

@inject NavigationManager Navigation
@inject ILogger<AddEdit> Logger

<IndexCard Title=@(_isEditMode ? "Edytuj Użytkownika".Tr() : "<PERSON>daj <PERSON>".Tr())>
    <RadzenTemplateForm TItem="AddEditUserRequest"
                        Data="@_addEditUser"
                        Submit="@OnSubmit">
        <FluentValidationForRadzenComponent Validator="@(new AddEditUserValidatorBase())" />

        <RadzenRow AlignItems="AlignItems.Start" RowGap="1rem" Gap="1rem" class="p-4">

            <RadzenColumn Size="12" SizeMD="6" SizeLG="6">
                <RadzenRadioButtonList TValue="UserTypes"
                                       Value="@_addEditUser.UserType"
                                       Change="@OnUserTypeChanged"
                                       Disabled="@(_isEditMode && !AppStateService.UserData.HasAdminRole)"
                                       Orientation="Orientation.Horizontal">
                    <Items>
                        <RadzenRadioButtonListItem Text="@("Pracownik".Tr())" Value="@UserTypes.Employee" />
                        <RadzenRadioButtonListItem Text="@("Klient".Tr())" Value="@UserTypes.Client" />
                    </Items>
                </RadzenRadioButtonList>
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="6" SizeLG="6">
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="0.5rem">
                    <RadzenSwitch @bind-Value="_addEditUser.Active" />
                    <RadzenLabel Text=@("Aktywny".Tr()) />
                    <ValidationMessage For="@(() => _addEditUser.Active)" class="validation-message" />
                </RadzenStack>
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="6">
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="0.5rem">
                    <RadzenCheckBox @bind-Value="_addEditUser.VatPl" Disabled="true" />
                    <RadzenLabel Text="@("VAT PL TAK".Tr())" />
                </RadzenStack>

                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="0.5rem">
                    <RadzenCheckBox Value="@(!_addEditUser.VatPl && _addEditUser.FirstConfiguration && _addEditUser.UserType == UserTypes.Client)" Disabled="true" />
                    <RadzenLabel Text="@("VAT PL NIE".Tr())" />
                </RadzenStack>

                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="0.5rem">
                    <RadzenCheckBox @bind-Value="_addEditUser.VatOutsidePl" Disabled="true" />
                    <RadzenLabel Text="@("VAT poza PL".Tr())" />
                </RadzenStack>
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="6">
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="0.5rem">
                    <RadzenCheckBox @bind-Value="_addEditUser.CanRepresentCustomer" Disabled="true" />
                    <RadzenLabel Text="@("Zgoda na reprezentację".Tr())" />
                </RadzenStack>

                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="0.5rem">
                    <RadzenCheckBox @bind-Value="_addEditUser.GeneralSaleTermsAccepted" Disabled="true" />
                    <RadzenLabel Text="@("Akceptuję ogólne warunki sprzedaży (OWS)".Tr())" />
                </RadzenStack>

                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="0.5rem">
                    <RadzenCheckBox @bind-Value="_addEditUser.RegulationsAccepted" Disabled="true" />
                    <RadzenLabel Text="@("Akceptuję regulamin".Tr())" />
                </RadzenStack>
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                <RadzenLabel Text=@("Imię".Tr().AddText('*')) />
                <RadzenTextBox @bind-Value="_addEditUser.FirstName" class="w-full" />
                <ValidationMessage For="@(() => _addEditUser.FirstName)" class="validation-message" />
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                <RadzenLabel Text=@("Nazwisko".Tr().AddText('*')) />
                <RadzenTextBox @bind-Value="_addEditUser.LastName" class="w-full" />
                <ValidationMessage For="@(() => _addEditUser.LastName)" class="validation-message" />
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                <RadzenLabel Text=@("Klient".Tr().AddText('*')) />
                <CustomerSelector TValue="int?" @bind-Value="@_addEditUser.CustomerId"
                                  For="@(() => _addEditUser.CustomerId)" />
                <ValidationMessage For="@(() => _addEditUser.CustomerId)" class="validation-message" />
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                <RadzenLabel Text=@("Email".Tr()) Component="Email" />
                <RadzenTextBox @bind-Value="_addEditUser.Email" Name="Email" class="w-full" />
                <ValidationMessage For="@(() => _addEditUser.Email)" class="validation-message" />
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                <RadzenLabel Text=@("Telefon komórkowy".Tr()) />
                <RadzenTextBox @bind-Value="_addEditUser.SecondPhoneNumber" class="w-full" />
                <ValidationMessage For="@(() => _addEditUser.SecondPhoneNumber)" class="validation-message" />
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                <RadzenLabel Text=@("Telefon stacjonarny".Tr()) />
                <RadzenTextBox @bind-Value="_addEditUser.FirstPhoneNumber" class="w-full" />
                <ValidationMessage For="@(() => _addEditUser.FirstPhoneNumber)" class="validation-message" />
            </RadzenColumn>

            @if (_addEditUser.UserType == UserTypes.Employee)
            {
                <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                    <RadzenLabel Text=@("Optima login".Tr()) />
                    <RadzenTextBox @bind-Value="_addEditUser.XlLogin" class="w-full" />
                    <ValidationMessage For="@(() => _addEditUser.XlLogin)" class="validation-message" />
                </RadzenColumn>

                <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                    <RadzenLabel Text=@("Optima hasło".Tr()) />
                    <RadzenTextBox @bind-Value="_addEditUser.XlPassword" class="w-full" />
                    <ValidationMessage For="@(() => _addEditUser.XlPassword)" class="validation-message" />
                </RadzenColumn>
            }

            <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                <RadzenLabel Text=@("Role".Tr().AddText('*')) />
                <RadzenDropDown Context="userRoleSelector"
                                @bind-Value="@_addEditUser.Roles"
                                Change="@OnRolesChanged"
                                MaxSelectedLabels="99"
                                Multiple="true"
                                AllowSelectAll=false
                                Style="width:100%;"
                                ValueProperty="Id"
                                TextProperty="Name"
                                Data="@GetRoles()"
                                Chips>
                    <Template>
                        @((userRoleSelector as GetAllRolesResponse)?.Name.Tr())
                    </Template>

                    <ValueTemplate>
                        @((userRoleSelector as GetAllRolesResponse)?.Name.Tr())
                    </ValueTemplate>
                </RadzenDropDown>
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                <RadzenLabel Text=@("Lokalizacje".Tr().AddText('*')) />
                <CustomerLocationsMultiSelector @bind-Value="@_addEditUser.LocationIds"
                                                CustomerId="@_addEditUser.CustomerId"
                                                For="@(() => _addEditUser.LocationIds)"
                                                ShowActiveOnly IncludeSelectedIfInactive />
                <ValidationMessage For="@(() => _addEditUser.LocationIds)" class="validation-message" />
            </RadzenColumn>

            @if (_addEditUser.Roles.Contains(_allRoles.SingleOrDefault(x => x.Name == Role.Production)?.Id ?? 0))
            {
                <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                    <RadzenLabel Text=@("Zakład".Tr()) />
                    <FactoriesSelector @bind-Value="@_addEditUser.FactoryId"
                                       For="@(() => _addEditUser.FactoryId)"
                                       ShowActiveOnly IncludeSelectedIfInactive />
                    <ValidationMessage For="@(() => _addEditUser.FactoryId)" class="validation-message" />
                </RadzenColumn>
            }

            @if (_addEditUser.UserType == UserTypes.Employee)
            {
                <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                    <RadzenLabel Text=@("Oddziały".Tr().AddText('*')) />
                    <UserBranchesMultiSelector @bind-Value="@_addEditUser.BranchIds"
                                               For="@(() => _addEditUser.BranchIds)"
                                               ShowActiveOnly IncludeSelectedIfInactive />
                    <ValidationMessage For="@(() => _addEditUser.BranchIds)" class="validation-message" />
                </RadzenColumn>
            }

            <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                <RadzenLabel Text=@("Język powiadomień".Tr()) />
                <LanguageSelector @bind-Value="@_addEditUser.NotificationsLang" />
                <ValidationMessage For="@(() => _addEditUser.NotificationsLang)" class="validation-message" />

                <RadzenStack Orientation="Orientation.Horizontal" Gap="2rem" Style="margin-top: 1rem;">
                    <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="0.5rem">
                        <RadzenCheckBox @bind-Value="_addEditUser.EmailNotifications" TValue="bool" />
                        <RadzenLabel Text=@("Powiadomienia Email".Tr()) />
                        <ValidationMessage For="@(() => _addEditUser.EmailNotifications)" class="validation-message" />
                    </RadzenStack>

                    <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="0.5rem">
                        <RadzenCheckBox @bind-Value="_addEditUser.SmsNotifications" TValue="bool" />
                        <RadzenLabel Text=@("Powiadomienia SMS".Tr()) />
                        <ValidationMessage For="@(() => _addEditUser.SmsNotifications)" class="validation-message" />
                    </RadzenStack>
                </RadzenStack>
            </RadzenColumn>

        </RadzenRow>
        <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.End" class="rz-m-4">
            <RadzenButton ButtonType="ButtonType.Submit" Text="@("Zapisz".Tr())" Icon="save" ButtonStyle="ButtonStyle.Success" />
            <RadzenButton Text="@("Anuluj".Tr())" Icon="cancel" ButtonStyle="ButtonStyle.Light" Click="@OnCancel" />
        </RadzenStack>
    </RadzenTemplateForm>
</IndexCard>

@code {
    [Parameter]
    public int? UserId { get; set; }

    private bool _isEditMode => (UserId is > 0);
    private AddEditUserRequest _addEditUser = new() { Active = true };
    private IEnumerable<GetAllRolesResponse> _allRoles = [];

    protected override async Task OnInitializedAsync()
    {
        var roleResponse = await Mediator.Send(new GetAllRolesQuery());

        if (roleResponse.Succeeded)
        {
            _allRoles = roleResponse.Data;
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError);
        }

        if (UserId is > 0)
        {
            var response = await Mediator.Send(new GetUserQuery(UserId.Value));

            if (response.Succeeded)
            {
                _addEditUser = Mapper.Map<AddEditUserRequest>(response.Data);
            }
            else
            {
                ToastService.Show(ToastType.LoadDataError, string.Join(',', response.Messages));
            }
        }
    }

    private async Task OnSubmit()
    {
        try
        {
            var result = await Mediator.Send(new AddEditUserCommand(_addEditUser));

            if (result.Succeeded)
            {
                ToastService.Show(ToastType.SaveSuccess);
                Navigation.NavigateTo("/admin/users/index");
            }
            else
            {
                ToastService.Show(ToastType.SaveError, string.Join(',', result.Messages));
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas zapisywania użytkownika");
            ToastService.Show(ToastType.SaveError);
        }
    }

    private void OnCancel()
    {
        Navigation.NavigateTo("/admin/users/index");
    }

    private IEnumerable<GetAllRolesResponse> GetRoles()
    {

        if (_addEditUser.UserType == UserTypes.Employee)
        {
            return _allRoles.Where(x => x.Name is Role.Administrator or Role.TradeManager or Role.Trade or Role.Production or Role.SaleManager).ToList();
        }
        else
        {
            return _allRoles.Where(x => x.Name is Role.Client or Role.ClientManager).ToList();
        }
    }

    private async Task OnUserTypeChanged(UserTypes value)
    {
        if (_addEditUser.UserType == value)
            return;

        string message = string.Empty;
        string title = string.Empty;

        if (value == UserTypes.Client)
        {
            message = "Czy na pewno chcesz zmienić typ użytkownika z pracownik na klient? Po zmianie następujące pola zostaną wyczyszczone: 'Role', 'Optima login', 'Optima hasło', 'Oddziały' oraz 'Zakład'.";
            title = "Zmiana typu: Pracownik → Klient";
        }
        else
        {
            message = "Czy na pewno chcesz zmienić typ użytkownika z klient na pracownik? Po zmianie pole 'Role' zostanie wyczyszczone.";
            title = "Zmiana typu: Klient → Pracownik";
        }

        var typeChangeConfirm = await ModalDialogService.Confirmation(message.Tr(), title.Tr());

        if (!typeChangeConfirm)
            return;

        _addEditUser.Roles = [];

        if (value == UserTypes.Client)
        {
            _addEditUser.XlLogin = null;
            _addEditUser.XlPassword = null;
            _addEditUser.BranchIds = [];
        }

        _addEditUser.UserType = value;
    }

    private void OnRolesChanged(object value)
    {
        if (_addEditUser.Roles.Contains(_allRoles.SingleOrDefault(x => x.Name == Role.Production)?.Id ?? 0))
        {
            _addEditUser.FactoryId = null;
        }
    }
}
