@page "/admin/users/index"
@attribute [Authorize(Roles = $"{Role.Administrator},{Role.TradeManager},{Role.Trade},{Role.Production},{Role.ClientManager}")]

@using EMessa.Base.Enums
@using EMessa.Core.Features.Users.Commands.Delete
@using EMessa.Core.Features.Users.Queries.GetAll
@using EMessa.Core.Features.Users.Queries.GetAllSieve
@using EMessa.Web.Components.Editors
@using EMessa.Web.Components.Grids
@using EMessa.Web.Constants
@using EMessa.Web.Extensions.EnumBadges
@using EMessa.Web.Pages.Admin.Customers.Parts
@using EMessa.Web.Pages.Admin.Users.Parts
@using Radzen
@using Radzen.Blazor

@inject ILogger<Index> Logger
@inject NavigationManager Navigation
@implements IDisposable

<IndexCard Title=@("Użytkownicy".Tr())>
    <ChildContent>
        <div class="w-full">
            <RadzenStack Orientation="Orientation.Horizontal" Class="p-1 justify-start items-start gap-2 flex-wrap">
                <AddRadzenButton Click="@AddNewCustomer"/>
                <GridSearch @ref="_gridSearch" ValueChanged="@_grid.Reload" MarginLeftAuto />
            </RadzenStack>
            <ProgressBar IsLoading="@_isLoading" />
            <RadzenDataGrid @ref="_grid" TItem="GetAllUsersResponse"
                            Data="@_gridData" Count="@_count" LoadData="@LoadData" IsLoading="@_isLoading"
                            AllowFiltering FilterMode="FilterMode.Simple"
                            FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                            AllowPaging PageSize="@PageSize.DefaultPageSize" PageSizeOptions="@PageSize.Pages20To200" PageSizeText="@PageSize.FormatPaginationInfo(_grid)"
                            AllowSorting AllowMultiColumnSorting
                            AllowColumnResize
                            Density="Density.Compact"
                            EmptyText="@PageSize.NoDataText">

                <LoadingTemplate />

                <Columns>
                    <RadzenDataGridColumn Title=@(AppStateService.IsLargeScreen ? "Akcje".Tr() : string.Empty)
                                          Filterable="false" Sortable="false"
                                          TextAlign="Radzen.TextAlign.Center" Width="@(AppStateService.IsLargeScreen ? "55px" : "30px")"
                                          Frozen="true" FrozenPosition="Radzen.FrozenColumnPosition.Right">
                        <Template Context="customer">
                            <AuthorizeView Roles="@($"{Role.Administrator},{Role.TradeManager},{Role.Trade},{Role.Production}")">
                                <Authorized>
                                    <div class="@(AppStateService.IsLargeScreen ? "flex flex-row gap-2 justify-center" : "flex flex-col gap-2 items-center")">
                                        <RadzenButton ButtonStyle="ButtonStyle.Light" Icon="edit"
                                                      Size="ButtonSize.ExtraSmall"
                                                      Click="@(args => EditCustomer(customer))" />
                                        <RadzenButton ButtonStyle="ButtonStyle.Danger" Icon="delete"
                                                      Size="ButtonSize.ExtraSmall"
                                                      Click="@(args => DeleteCustomer(customer))" />
                                    </div>
                                </Authorized>
                            </AuthorizeView>
                        </Template>
                    </RadzenDataGridColumn>


                    <RadzenDataGridColumn Property="@nameof(GetAllUsersResponse.UserType)" Type="typeof(IEnumerable<string>)" Title=@("Typ".Tr())
                                          Width="85px" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                          Sortable="true" FilterProperty="@nameof(GetAllUsersResponse.UserType)" Filterable FilterOperator="FilterOperator.Equals" FilterValue="@_userTypeFilter">
                        <FilterTemplate>
                            <RadzenDropDown Context="userTypeSelector" @bind-Value="@_userTypeFilter" Multiple="false" AllowSelectAll=false Style="width:100%;"
                                            Change="@((value) => _userTypeFilter = value?.ToString())" Data="@_allUserTypeFilter" AllowClear>
                                <Template>
                                    @((userTypeSelector as string)?.Tr())
                                </Template>
                                <ValueTemplate>
                                    @((userTypeSelector as string)?.Tr())
                                </ValueTemplate>
                            </RadzenDropDown>
                        </FilterTemplate>
                        <Template Context="user">
                            @string.Concat(user.UserType.ToString().Tr())
                        </Template>
                    </RadzenDataGridColumn>
                    <RadzenDataGridColumn Property="@nameof(GetAllUsersResponse.FirstName)" Title=@("Imię".Tr())
                                          Width="120px" Visible="@AppStateService.IsLargeScreen"
                                          Sortable Filterable WhiteSpace="WhiteSpace.Wrap" />
                    <RadzenDataGridColumn Property="@nameof(GetAllUsersResponse.LastName)" Title=@("Nazwisko".Tr())
                                          Width="120px" Visible="@AppStateService.IsLargeScreen"
                                          Sortable Filterable WhiteSpace="WhiteSpace.Wrap" />
                    <RadzenDataGridColumn Property="@nameof(GetAllUsersResponse.Email)" Title=@("Email".Tr())
                                          Width="150px" Visible="@AppStateService.IsLargeScreen"
                                          Sortable Filterable WhiteSpace="WhiteSpace.Wrap" />
                    <RadzenDataGridColumn Property="@nameof(GetAllUsersResponse.CustomerShortName)" Title=@("Klient".Tr())
                                          Width="120px" Visible="@AppStateService.IsLargeScreen"
                                          Sortable Filterable WhiteSpace="WhiteSpace.Wrap" />
                    <RadzenDataGridColumn Property="@nameof(GetAllUsersResponse.Locations)" Title=@("Lokalizacje".Tr())
                                          Width="120px" Visible="@AppStateService.IsLargeScreen"
                                          Sortable="false" Filterable WhiteSpace="WhiteSpace.Wrap" />
                    <RadzenDataGridColumn Property="@nameof(GetAllUsersResponse.CustomerBranchName)" Title=@("Oddział klienta".Tr())
                                          Width="130px" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                          Sortable Filterable FilterProperty="@nameof(GetAllUsersResponse.CustomerBranchId)"
                                          FilterOperator="FilterOperator.Equals" FilterValue="@_branchFilter" >
                        <FilterTemplate>
                            <BranchesSelector @bind-Value="@_branchFilter"
                                              ShowActiveOnly="false"
                                              Clearable="true" />
                        </FilterTemplate>
                    </RadzenDataGridColumn>
                    <RadzenDataGridColumn Property="@nameof(GetAllUsersResponse.Branches)" Title=@("Oddziały użytkownika".Tr())
                                          Width="130px" Visible="@AppStateService.IsLargeScreen"
                                          Sortable="false" Filterable WhiteSpace="WhiteSpace.Wrap" />
                    <RadzenDataGridColumn Property="@nameof(GetAllUsersResponse.FactoryName)" Title=@("Zakład".Tr())
                                          Width="130px" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                          Sortable="false" Filterable FilterProperty="@nameof(GetAllUsersResponse.FactoryId)"
                                          FilterOperator="FilterOperator.Equals" FilterValue="@_factoryFilter">
                        <FilterTemplate>
                            <FactoriesSelector @bind-Value="@_factoryFilter"
                                              ShowActiveOnly="false"
                                              Clearable="true" />
                        </FilterTemplate>
                    </RadzenDataGridColumn>
                    <RadzenDataGridColumn Property="@nameof(GetAllUsersResponse.Roles)" Type="typeof(IEnumerable<string>)" Title=@("Role".Tr())
                                          Width="140px" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                          Sortable="false" Filterable FilterOperator="FilterOperator.Equals" FilterValue="@_roleFilter">
                        <FilterTemplate>
                            <RadzenDropDown Context="userRoleSelector" @bind-Value="@_roleFilter" Multiple="false" AllowSelectAll=false Style="width:100%;"
                                            Change="@((value) => _roleFilter = value?.ToString())" Data="@_allRolesFilter" AllowClear>
                                <Template>
                                    @* @((userRoleSelector as string)?.Tr()) *@    
                                    @if (userRoleSelector is string)
                                    {
                                        <RadzenBadge IsPill="true" Variant="Variant.Outlined"
                                                     Style="@GetRoleStyle(userRoleSelector, new List<string>())"
                                                     BadgeStyle="BadgeStyle.Secondary" Text="@((userRoleSelector as string)?.Tr())" />
                                    }
                                </Template>
                                <ValueTemplate>
                                    @* @((userRoleSelector as string)?.Tr()) *@
                                    @if (userRoleSelector is string)
                                    {
                                        <RadzenBadge IsPill="true" Variant="Variant.Outlined"
                                                     Style="@GetRoleStyle(userRoleSelector, new List<string>())"
                                                     BadgeStyle="BadgeStyle.Secondary" Text="@((userRoleSelector as string)?.Tr())" />
                                    }
                                </ValueTemplate>
                            </RadzenDropDown>
                        </FilterTemplate>
                        <Template Context="user">
                            @foreach (var role in user.Roles)
                            {
                                <RadzenBadge IsPill="true"
                                             Variant="Variant.Outlined"
                                             Style="@GetRoleStyle(role, user.Roles)"
                                             Text="@role.Tr()" />
                            }
                            @* @string.Join(", ", user.Roles.Select(role => role.Tr())) *@       
                        </Template>
                    </RadzenDataGridColumn>
                    <RadzenDataGridColumn Property="@nameof(GetAllUsersResponse.Active)" Title=@("Aktywny".Tr())
                                          TextAlign="TextAlign.Center" Width="75px" Visible="@AppStateService.IsLargeScreen"
                                          Sortable Filterable>
                        <Template Context="user">
                            <RadzenCheckBox @bind-Value="user.Active" Disabled TValue="bool" />
                        </Template>
                    </RadzenDataGridColumn>
                    <RadzenDataGridColumn Property="@nameof(GetAllUsersResponse.SmsNotifications)" Title=@("SMS".Tr())
                                          TextAlign="TextAlign.Center" Width="60px" Visible="@AppStateService.IsLargeScreen"
                                          Sortable Filterable>
                        <Template Context="user">
                            <RadzenCheckBox @bind-Value="user.SmsNotifications" Disabled TValue="bool" />
                        </Template>
                    </RadzenDataGridColumn>
                    <RadzenDataGridColumn Property="@nameof(GetAllUsersResponse.EmailNotifications)" Title=@("Email".Tr())
                                          TextAlign="TextAlign.Center" Width="75px" Visible="@AppStateService.IsLargeScreen"
                                          Sortable Filterable>
                        <Template Context="user">
                            <RadzenCheckBox @bind-Value="user.EmailNotifications" Disabled TValue="bool" />
                        </Template>
                    </RadzenDataGridColumn>

                    <RadzenDataGridColumn Visible="@(!AppStateService.IsLargeScreen)" Filterable="false" Sortable="false">
                        <Template Context="user">
                            <UserCard User="@user" />
                        </Template>
                    </RadzenDataGridColumn>
                </Columns>
            </RadzenDataGrid>
        </div>
    </ChildContent>
</IndexCard>

@code {
    private RadzenDataGrid<GetAllUsersResponse> _grid = new();
    private IEnumerable<GetAllUsersResponse> _gridData = [];
    private int _count;
    private bool _isLoading;
    private GridSearch? _gridSearch;
    private IEnumerable<string> _allRolesFilter = [Role.Client, Role.ClientManager, Role.SaleManager, Role.Trade, Role.TradeManager, Role.Production, Role.Administrator];
    private IEnumerable<string> _allUserTypeFilter = [UserTypes.Employee.ToString(), UserTypes.Client.ToString()];
    private string? _userTypeFilter;
    private string? _roleFilter;
    private int? _branchFilter;
    private int? _factoryFilter;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await _grid.Reload();
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    private async Task LoadData(LoadDataArgs args)
    {
        _isLoading = true;

        try
        {
            var sieveModel = args.ToSieveModel().ApplyGlobalSearchFilter(_gridSearch?.SearchValue);
            var response = await Mediator.Send(new GetAllUsersQuery(sieveModel));

            if (response.Succeeded)
            {
                _gridData = response.Data;
                _count = response.TotalCount;
            }
            else
            {
                ToastService.Show(ToastType.LoadDataError, string.Join(',', response.Messages));
                _gridData = [];
                _count = 0;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas ładowania danych użytkowników.");
            ToastService.Show(ToastType.LoadDataError);
            _gridData = [];
            _count = 0;
        }
        finally
        {
            _isLoading = false;
            await InvokeAsync(StateHasChanged);
        }
    }

    private void AddNewCustomer() => Navigation.NavigateTo("/admin/users/edit");

    private void EditCustomer(GetAllUsersResponse user) => Navigation.NavigateTo($"/admin/users/edit/{user.Id}");

    private async Task DeleteCustomer(GetAllUsersResponse user)
    {
        try
        {
            var confirmed = await ModalDialogService.DeleteConfirmationByName($"{user.FirstName} {user.LastName}");

            if (confirmed == true)
            {
                _isLoading = true;

                var response = await Mediator.Send(new DeleteUserCommand(user.Id));

                if (response.Succeeded)
                    ToastService.Show(ToastType.DeleteSuccess);
                else
                    ToastService.Show(ToastType.DeleteError, string.Join(',', response.Messages));

                await _grid.Reload();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, $"Błąd podczas usuwania użytkownika: {user.FirstName} {user.LastName}");
            ToastService.Show(ToastType.DeleteError);
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        if (AppStateService != null)
        {
            AppStateService.StateChanged += OnStateChanged;
        }
    }

    void IDisposable.Dispose()
    {
        if (AppStateService != null)
        {
            AppStateService.StateChanged -= OnStateChanged;
        }
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }

    private string GetRoleStyle(string role, IEnumerable<string> userRoles)
    {
        var style = $"background-color:{RoleExtensions.RoleColorMap[role]};color:white;border:none;box-shadow:none;margin-right:2px;";

        var roles = userRoles.ToArray();
        if (roles.Length > 1 && role != roles.FirstOrDefault())
        {
            style += "margin-top: 2px;";
        }

        return style;
    }
}
