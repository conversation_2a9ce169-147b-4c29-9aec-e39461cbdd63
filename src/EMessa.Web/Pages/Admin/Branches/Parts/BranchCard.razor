@using EMessa.Core.Features.Branches.Queries.GetAll;
@using Radzen.Blazor

<div class="p-3 bg-white shadow-md rounded-lg border border-gray-200 text-wrap">
    <div class="mb-2 text-xl font-semibold text-gray-800">
        @Branch.Code
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Nazwa".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@Branch.Name</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Adres".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">
            @($"{Branch.Address}, {Branch.PostCode} {Branch.City}")
        </span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Telefon".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@Branch.Phone</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Email".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@Branch.Email</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Kod magazynu".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@Branch.SellWarehouseCode</span>
    </div>

    <div class="flex items-center gap-2 mt-2">
        <RadzenCheckBox @bind-Value="@Branch.IsActive" Disabled />
        <span class="text-sm text-gray-700">@("Aktywny".Tr())</span>
    </div>
</div>

@code {
    [Parameter]
    public required GetAllBranchesResponse Branch { get; set; }
}
