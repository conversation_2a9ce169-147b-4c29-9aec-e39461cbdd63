@page "/admin/branches/edit/{BranchId:int?}"
@attribute [Authorize(Roles = Role.Administrator)]

@using EMessa.Core.Features.Branches.Commands.AddEdit
@using EMessa.Core.Features.Branches.Queries.Get
@using EMessa.Core.Features.Factories.Commands.AddEdit
@using EMessa.Core.Features.Factories.Queries.Get
@using Radzen
@using Radzen.Blazor
@using EMessa.Core.Features.Factories.Queries.GetAll

@inject NavigationManager Navigation
@inject ILogger<AddEdit> Logger

<IndexCard Title=@(BranchId is null ? "Dodaj <PERSON>ział".Tr() : "Edytuj Oddział".Tr())>
    <RadzenTemplateForm TItem="AddEditBranchRequest"
                        Data="@_addEditBranch"
                        Submit="@OnSubmit">
        <FluentValidationForRadzenComponent Validator="@(new AddEditBranchValidator())" />

        <RadzenRow AlignItems="AlignItems.Start" RowGap="1.5rem" Gap="1rem" class="rz-p-sm-12">

            <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                <RadzenLabel Text=@("Kod".Tr().AddText('*')) />
                <RadzenTextBox @bind-Value="_addEditBranch.Code" class="w-full" />
                <ValidationMessage For="@(() => _addEditBranch.Code)" class="validation-message" />
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                <RadzenLabel Text=@("Nazwa".Tr().AddText('*')) Component="Name" />
                <RadzenTextBox @bind-Value="_addEditBranch.Name" Name="Name" class="w-full" />
                <ValidationMessage For="@(() => _addEditBranch.Name)" class="validation-message" />
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                <RadzenLabel Text=@("Miasto".Tr().AddText('*')) Component="City" />
                <RadzenTextBox @bind-Value="_addEditBranch.City" Name="City" class="w-full" />
                <ValidationMessage For="@(() => _addEditBranch.City)" class="validation-message" />
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                <RadzenLabel Text=@("Kod pocztowy".Tr().AddText('*')) Component="PostCode" />
                <RadzenTextBox @bind-Value="_addEditBranch.PostCode" Name="PostCode" class="w-full" />
                <ValidationMessage For="@(() => _addEditBranch.PostCode)" class="validation-message" />
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                <RadzenLabel Text=@("Adres".Tr().AddText('*')) Component="Address" />
                <RadzenTextBox @bind-Value="_addEditBranch.Address" Name="Address" class="w-full" />
                <ValidationMessage For="@(() => _addEditBranch.Address)" class="validation-message" />
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                <RadzenLabel Text=@("Telefon".Tr().AddText('*')) Component="Phone" />
                <RadzenTextBox @bind-Value="_addEditBranch.Phone" Name="Phone" class="w-full" />
                <ValidationMessage For="@(() => _addEditBranch.Phone)" class="validation-message" />
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                <RadzenLabel Text=@("Email".Tr()) Component="Email" />
                <RadzenTextBox @bind-Value="_addEditBranch.Email" Name="Email" class="w-full" />
                <ValidationMessage For="@(() => _addEditBranch.Email)" class="validation-message" />
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                <RadzenLabel Text=@("Kod magazynu".Tr()) Component="SellWarehouseCode" />
                <RadzenTextBox @bind-Value="_addEditBranch.SellWarehouseCode" Name="SellWarehouseCode" class="w-full" />
                <ValidationMessage For="@(() => _addEditBranch.SellWarehouseCode)" class="validation-message" />
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="12" SizeLG="6" class="flex items-center min-h-[90px] gap-4">
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="0.5rem">
                    <RadzenSwitch @bind-Value="_addEditBranch.IsActive" Name="IsActive" />
                    <RadzenLabel Text=@("Aktywny".Tr()) Component="IsActive" />
                    <ValidationMessage For="@(() => _addEditBranch.IsActive)" class="validation-message" />
                </RadzenStack>
            </RadzenColumn>

        </RadzenRow>
        <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.End" class="rz-m-4">
            <RadzenButton ButtonType="ButtonType.Submit" Text="@("Zapisz".Tr())" Icon="save" ButtonStyle="ButtonStyle.Success" />
            <RadzenButton Text="@("Anuluj".Tr())" Icon="cancel" ButtonStyle="ButtonStyle.Light" Click="@OnCancel" />
        </RadzenStack>
    </RadzenTemplateForm>
</IndexCard>

@code {
    [Parameter]
    public int? BranchId { get; set; }

    private AddEditBranchRequest _addEditBranch = new();

    protected override async Task OnInitializedAsync()
    {
        if (BranchId is > 0)
        {
            var response = await Mediator.Send(new GetBranchQuery(BranchId.Value));

            if (response.Succeeded)
            {
                _addEditBranch = Mapper.Map<AddEditBranchRequest>(response.Data);
            }
            else
            {
                ToastService.Show(ToastType.LoadDataError, response.Messages);
            }
        }
    }

    private async Task OnSubmit()
    {
        try
        {
            var result = await Mediator.Send(new AddEditBranchCommand(Mapper.Map<AddEditBranchRequest>(_addEditBranch)));

            if (result.Succeeded)
            {
                ToastService.Show(ToastType.SaveSuccess);
                Navigation.NavigateTo("/admin/branches/index");
            }
            else
            {
                ToastService.Show(ToastType.SaveError, result.Messages);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas zapisywania oddziału");
            ToastService.Show(ToastType.SaveError);
        }
    }

    private void OnCancel()
    {
        Navigation.NavigateTo("/admin/branches/index");
    }
}
