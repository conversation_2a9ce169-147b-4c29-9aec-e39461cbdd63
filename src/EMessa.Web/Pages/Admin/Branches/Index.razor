@page "/admin/branches/index"
@attribute [Authorize(Roles = Role.Administrator)]

@using EMessa.Core.Features.Branches.Commands.Delete
@using EMessa.Core.Features.Branches.Queries.GetAll
@using EMessa.Web.Constants
@using EMessa.Web.Pages.Admin.Branches.Parts
@using Ra<PERSON>zen
@using Radzen.Blazor

@inject ILogger<Index> Logger
@inject NavigationManager Navigation
@implements IDisposable

<IndexCard Title=@("Oddziały".Tr())>
    <ChildContent>
        <RadzenStack Orientation="Orientation.Horizontal" Class="p-1 justify-start items-start gap-2 flex-wrap">
            <RadzenButton Text=@("Dodaj".Tr()) Icon="add"
                          ButtonStyle="ButtonStyle.Light" Click="@AddNewBranch" />

            <GridSearch @ref="_gridSearch" ValueChanged="@_grid.Reload" CssClass="ml-auto" />
        </RadzenStack>
        <ProgressBar IsLoading="@_isLoading" />
        <RadzenDataGrid @ref="_grid" TItem="GetAllBranchesResponse"
                        Data="@_gridData" Count="@_count" LoadData="@LoadData" IsLoading="@_isLoading"
                        AllowFiltering FilterMode="FilterMode.Simple"
                        FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                        PageSize="@PageSize.DefaultPageSize"
                        PageSizeOptions="@PageSize.Pages20To200"
                        PageSizeText="@PageSize.FormatPaginationInfo(_grid)"
                        AllowPaging
                        AllowSorting AllowMultiColumnSorting
                        AllowColumnResize
                        RowDoubleClick="@OnRowDoubleClick"
                        Density="Density.Compact"
                        EmptyText="@RadzenDataGridConstants.EmptyText">
            <LoadingTemplate />

            <Columns>
                <RadzenDataGridColumn Context="branch "
                                      Title=@(AppStateService.IsLargeScreen ? "Akcje".Tr() : string.Empty)
                                      Filterable="false" Sortable="false"
                                      TextAlign="TextAlign.Center"
                                      Width="@(AppStateService.IsLargeScreen ? "55px" : "30px")"
                                      Frozen="true" FrozenPosition="FrozenColumnPosition.Right">
                    <Template Context="branch">
                        <div
                            class="@(AppStateService.IsLargeScreen ? "flex flex-row gap-2 justify-center" : "flex flex-col gap-2 items-center")">
                            <RadzenButton ButtonStyle="ButtonStyle.Light" Icon="edit"
                                          Size="ButtonSize.ExtraSmall"
                                          Click="@(args => EditBranch(branch))" />
                            <RadzenButton ButtonStyle="ButtonStyle.Danger" Icon="delete"
                                          Size="ButtonSize.ExtraSmall"
                                          Click="@(args => DeleteBranch(branch))" />
                        </div>
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Property="@nameof(GetAllBranchesResponse.Code)" Title=@("Kod".Tr())
                                      Width="120px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable
                                      FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />

                <RadzenDataGridColumn Property="@nameof(GetAllBranchesResponse.Name)" Title=@("Nazwa".Tr())
                                      Width="200px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable
                                      FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />

                <RadzenDataGridColumn Property="@nameof(GetAllBranchesResponse.Address)" Title=@("Adres".Tr())
                                      Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable
                                      FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />

                <RadzenDataGridColumn Property="@nameof(GetAllBranchesResponse.PostCode)"
                                      Title=@("Kod pocztowy".Tr())
                                      Width="120px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable
                                      FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />

                <RadzenDataGridColumn Property="@nameof(GetAllBranchesResponse.City)" Title=@("Miasto".Tr())
                                      Width="150px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable
                                      FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />

                <RadzenDataGridColumn Property="@nameof(GetAllBranchesResponse.Phone)" Title=@("Telefon".Tr())
                                      Width="150px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable
                                      FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />

                <RadzenDataGridColumn Property="@nameof(GetAllBranchesResponse.Email)" Title=@("Email".Tr())
                                      Width="200px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable
                                      FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />

                <RadzenDataGridColumn Property="@nameof(GetAllBranchesResponse.SellWarehouseCode)" Title=@("Kod magazynu".Tr())
                                      Width="120px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable
                                      FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />

                <RadzenDataGridColumn Property="@nameof(GetAllBranchesResponse.IsActive)"
                                      Title=@("Aktywny".Tr()) Width="100px"
                                      TextAlign="TextAlign.Center" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable>
                    <Template Context="branch">
                        <RadzenCheckBox @bind-Value="branch.IsActive" Disabled TValue="bool" />
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Visible="@(!AppStateService.IsLargeScreen)" Filterable="false" Sortable="false">
                    <Template Context="branch">
                        <BranchCard Branch="@branch" />
                    </Template>
                </RadzenDataGridColumn>
            </Columns>
        </RadzenDataGrid>
    </ChildContent>
</IndexCard>

@code {
    private RadzenDataGrid<GetAllBranchesResponse> _grid = new();
    private IEnumerable<GetAllBranchesResponse> _gridData = [];
    private int _count;
    private bool _isLoading;
    private GridSearch? _gridSearch;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await _grid.Reload();
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    private async Task LoadData(LoadDataArgs args)
    {
        _isLoading = true;

        try
        {
            var sieveModel = args.ToSieveModel().ApplyGlobalSearchFilter(_gridSearch?.SearchValue);
            var response = await Mediator.Send(new GetAllBranchesQuery(sieveModel));

            if (response.Succeeded)
            {
                _gridData = response.Data;
                _count = response.TotalCount;
            }
            else
            {
                ToastService.Show(ToastType.LoadDataError);
                _gridData = [];
                _count = 0;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas ładowania danych oddziałów.");
            ToastService.Show(ToastType.LoadDataError);
            _gridData = [];
            _count = 0;
        }
        finally
        {
            _isLoading = false;
            await InvokeAsync(StateHasChanged);
        }
    }

    private void OnRowDoubleClick(DataGridRowMouseEventArgs<GetAllBranchesResponse> args)
    {
        EditBranch(args.Data);
    }

    private void AddNewBranch() => Navigation.NavigateTo("/admin/branches/edit");

    private void EditBranch(GetAllBranchesResponse branch) => Navigation.NavigateTo($"/admin/branches/edit/{branch.Id}");

    private async Task DeleteBranch(GetAllBranchesResponse branch)
    {
        try
        {
            var confirmed = await ModalDialogService.DeleteConfirmationByName(branch.Name);

            if (confirmed)
            {
                _isLoading = true;

                var response = await Mediator.Send(new DeleteBranchCommand(branch.Id));

                if (response.Succeeded)
                    ToastService.Show(ToastType.DeleteSuccess);
                else
                    ToastService.Show(ToastType.DeleteError, response.Messages);

                await _grid.Reload();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, $"Błąd podczas usuwania oddziału ID: {branch.Id}");
            ToastService.Show(ToastType.DeleteError);
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        if (AppStateService != null)
        {
            AppStateService.StateChanged += OnStateChanged;
        }
    }

    void IDisposable.Dispose()
    {
        if (AppStateService != null)
        {
            AppStateService.StateChanged -= OnStateChanged;
        }
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }
}
