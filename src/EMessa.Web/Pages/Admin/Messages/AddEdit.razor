@page "/admin/messages/edit/{MessageId:int?}"
@using EMessa.Core.Features.Messages.Commands.AddEdit
@using EMessa.Core.Features.Messages.Queries.Get
@using EMessa.Web.Pages.Admin.Messages.Parts
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor
@attribute [Authorize(Roles = Role.Administrator)]

@inject NavigationManager Navigation
@inject ILogger<AddEdit> Logger

<IndexCard Title=@(MessageId is null ? "Dodaj Wiadomość".Tr() : "Edytuj Wiadomość".Tr())>
    <ProgressBar IsLoading="@_isLoading" />
    <RadzenTabs RenderMode="TabRenderMode.Client">
        <Tabs>
            <RadzenTabsItem Text="@("Wiadomość".Tr())" Disabled=@_isLoading>
                <RadzenTemplateForm TItem="AddEditMessageCommand"
                                    Data="@_addEditMessage"
                                    Submit="@OnSubmit">
                    <FluentValidationForRadzenComponent Validator="@(new AddEditMessageValidator())" />

                    <RadzenRow AlignItems="AlignItems.Start" RowGap="0.5rem" Gap="1rem">

                        <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                            <RadzenLabel Text=@("Kod".Tr().AddText('*')) />
                            <RadzenTextBox @bind-Value="_addEditMessage.Code" class="w-full" />
                            <ValidationMessage For="@(() => _addEditMessage.Code)" class="validation-message" />
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                            <RadzenLabel Text=@("Nazwa".Tr().AddText('*')) />
                            <RadzenTextBox @bind-Value="_addEditMessage.Name" class="w-full" />
                            <ValidationMessage For="@(() => _addEditMessage.Name)" class="validation-message" />
                        </RadzenColumn>

                        <RadzenColumn Size="12" SizeMD="12" SizeLG="12">
                            <RadzenLabel Text=@("Temat".Tr().AddText('*')) />
                            <RadzenTextBox @bind-Value="_addEditMessage.Subject" class="w-full" />
                            <ValidationMessage For="@(() => _addEditMessage.Subject)" class="validation-message" />
                        </RadzenColumn>

                        <RadzenColumn Size="12" SizeMD="12" SizeLG="12">
                            <RadzenLabel Text=@("Treść".Tr().AddText('*')) />
                            <RadzenHtmlEditor @bind-Value=@_addEditMessage.Body style="height: 300px" />
                            <ValidationMessage For="@(() => _addEditMessage.Body)" class="validation-message" />
                        </RadzenColumn>

                    </RadzenRow>
                    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" class="mt-2">
                        <RadzenButton ButtonType="ButtonType.Submit" Disabled=@_isLoading Text="@("Zapisz".Tr())" Icon="save" ButtonStyle="ButtonStyle.Success" />
                    </RadzenStack>
                </RadzenTemplateForm>
            </RadzenTabsItem>
            <RadzenTabsItem Text="@("Tłumaczenia".Tr())" Disabled=@(MessageId is null || _isLoading)>
                <MessageTranslations @ref="@_messageTranslationsComponent" MessageId="@MessageId" />
            </RadzenTabsItem>
        </Tabs>
    </RadzenTabs>
    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" class="m-2">
        <RadzenButton Text="@("Anuluj".Tr())" Icon="arrow_back" ButtonStyle="ButtonStyle.Secondary" Click="@OnCancel" />
    </RadzenStack>
</IndexCard>

@code {
    [Parameter] public int? MessageId { get; set; }

    private MessageTranslations? _messageTranslationsComponent;
    private AddEditMessageCommand _addEditMessage = new();
    private bool _isLoading;

    protected override async Task OnInitializedAsync()
    {
        await GetMessageAsync();
    }

    private async Task GetMessageAsync()
    {
        if (MessageId is > 0)
        {
            var response = await Mediator.Send(new GetMessageQuery(MessageId.Value));

            if (response.Succeeded)
            {
                _addEditMessage = Mapper.Map<AddEditMessageCommand>(response.Data);
            }
            else
            {
                ToastService.Show(ToastType.LoadDataError, string.Join(',', response.Messages));
            }
        }
    }

    private async Task OnSubmit()
    {
        try
        {
            _isLoading = true;

            var result = await Mediator.Send(_addEditMessage);

            if (result.Succeeded)
            {
                MessageId = result.Data;
                await GetMessageAsync();

                ToastService.Show(ToastType.SaveSuccess);

                if (_messageTranslationsComponent is not null)
                {
                    var parameters = new Dictionary<string, object?>
                    {
                        { nameof(MessageTranslations.MessageId), MessageId }
                    };

                    await _messageTranslationsComponent.SetParametersAsync(ParameterView.FromDictionary(parameters));
                    await _messageTranslationsComponent.GetDataAsync();
                }
            }
            else
            {
                ToastService.Show(ToastType.SaveError, string.Join(',', result.Messages));
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas zapisywania wiadomości");
            ToastService.Show(ToastType.SaveError);
        }
        finally
        {
            _isLoading = false;
        }
    }

    private void OnCancel()
    {
        Navigation.NavigateTo("/admin/messages/index");
    }
}
