@page "/admin/messages/index"
@attribute [Authorize(Roles = Role.Administrator)]

@using EMessa.Core.Features.Messages.Commands.Delete
@using EMessa.Core.Features.Messages.Queries
@using EMessa.Core.Features.Messages.Queries.GetAll
@using EMessa.Web.Constants
@using EMessa.Web.Pages.Admin.Messages.Parts
@using Radzen
@using Radzen.Blazor

@inject ILogger<Index> Logger
@inject IModalDialogService ModalDialogService
@inject NavigationManager Navigation
@implements IDisposable

<IndexCard Title=@("Wiadomości".Tr())>
    <ChildContent>
        <GridToolbar Items="@( [ GridToolbar.Add, GridToolbar.Search])"
                     OnAction="@HandleAction"
                     SearchTextChanged="@HandleSearch">
            <ChildContent>
                <RadzenButton Click="@(_ => _grid.ExpandRows(_gridData))" Text=@("Rozwiń".Tr()) Icon="keyboard_arrow_down" ButtonStyle="ButtonStyle.Secondary" />
                <RadzenButton Click="@_grid.CollapseAll" Text=@("Zwiń".Tr()) Icon="keyboard_arrow_up" ButtonStyle="ButtonStyle.Secondary" />
            </ChildContent>
        </GridToolbar>
        <ProgressBar IsLoading="@_isLoading" />
        <RadzenDataGrid @ref="_grid" TItem="GetMessageResponse"
                        Data="@_gridData" Count="@_count" LoadData="@LoadData" IsLoading="@_isLoading"
                        AllowFiltering FilterMode="FilterMode.Simple"
                        FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                        AllowPaging PageSize="@PageSize.DefaultPageSize" PageSizeOptions="@PageSize.Pages20To200" PageSizeText="@PageSize.FormatPaginationInfo(_grid)"
                        AllowSorting AllowMultiColumnSorting
                        AllowColumnResize
                        RowDoubleClick="@((args) => EditMessage(args.Data))"
                        Density="Density.Compact"
                        EmptyText="@RadzenDataGridConstants.EmptyText"
                        ExpandMode="DataGridExpandMode.Multiple">
            <LoadingTemplate />

            <Template Context="message">
                <RadzenTabs>
                    <Tabs>
                        @foreach (var lang in SystemConstants.SupportedCulturesList)
                        {
                            var translation = message.Translations.FirstOrDefault(x => x.LanguageCode == lang);
                            <RadzenTabsItem Text="@lang">
                                <RadzenStack Orientation="Orientation.Vertical" Gap="0.2rem">
                                    <RadzenStack Orientation="Orientation.Vertical" Gap="0.2rem">
                                        <RadzenLabel Text="@("Temat".Tr())" />
                                        @if (lang == "pl")
                                        {
                                            <RadzenTextBox Value="@message.Subject" ReadOnly />
                                        }
                                        else
                                        {
                                            <RadzenTextBox Value="@translation?.Subject"
                                                           ReadOnly />
                                        }
                                    </RadzenStack>
                                    <RadzenStack Orientation="Orientation.Vertical" Gap="0.2rem">
                                        <RadzenLabel Text="@("Treść".Tr())" />
                                        @if (lang == "pl")
                                        {
                                            <RadzenHtmlEditor Value="@message.Body" ShowToolbar="false" Disabled class="min-h-[50px] max-h-[400px]" />
                                        }
                                        else
                                        {
                                            <RadzenHtmlEditor Value="@translation?.Body"
                                                              ShowToolbar="false"
                                                              Disabled
                                                              class="min-h-[50px] max-h-[400px]" />
                                        }
                                    </RadzenStack>
                                </RadzenStack>
                            </RadzenTabsItem>
                        }
                    </Tabs>
                </RadzenTabs>
            </Template>

            <Columns>
                <RadzenDataGridColumn Context="message "
                                      Title=@(AppStateService.IsLargeScreen ? "Akcje".Tr() : string.Empty)
                                      Filterable="false" Sortable="false"
                                      TextAlign="TextAlign.Center"
                                      Width="@(AppStateService.IsLargeScreen ? "55px" : "30px")"
                                      Frozen="true" FrozenPosition="FrozenColumnPosition.Right">
                    <Template Context="message">
                        <div class="@(AppStateService.IsLargeScreen ? "flex flex-row gap-2 justify-center" : "flex flex-col gap-2 items-center")">
                            <RadzenButton ButtonStyle="ButtonStyle.Light" Icon="edit"
                                          Size="ButtonSize.ExtraSmall"
                                          Click="@(_ => EditMessage(message))" />
                            <RadzenButton ButtonStyle="ButtonStyle.Danger" Icon="delete"
                                          Size="ButtonSize.ExtraSmall"
                                          Click="@(_ => DeleteMessage(message))" />
                        </div>
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Property="@nameof(GetMessageResponse.Code)" Title=@("Kod".Tr())
                                      Width="280px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable />
                <RadzenDataGridColumn Property="@nameof(GetMessageResponse.Name)" Title=@("Nazwa".Tr())
                                      Width="280px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable />
                <RadzenDataGridColumn Property="@nameof(GetMessageResponse.Subject)" Title=@("Temat".Tr())
                                      Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable />

                <RadzenDataGridColumn Visible="@(!AppStateService.IsLargeScreen)" Filterable="false" Sortable="false">
                    <Template Context="message">
                        <MessageCard Message="@message" />
                    </Template>
                </RadzenDataGridColumn>
            </Columns>
        </RadzenDataGrid>
    </ChildContent>
</IndexCard>

@code {
    private RadzenDataGrid<GetMessageResponse> _grid = new();
    private IEnumerable<GetMessageResponse> _gridData = [];
    private int _count;
    private bool _isLoading;
    private string? _searchString;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await _grid.Reload();
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    private async Task LoadData(LoadDataArgs args)
    {
        _isLoading = true;

        try
        {
            var sieveModel = args.ToSieveModel().ApplyGlobalSearchFilter(_searchString);
            var response = await Mediator.Send(new GetAllMessagesQuery(sieveModel));

            if (response.Succeeded)
            {
                _gridData = response.Data;
                _count = response.TotalCount;
            }
            else
            {
                ToastService.Show(ToastType.LoadDataError);
                _gridData = [];
                _count = 0;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas ładowania wiadomości");
            ToastService.Show(ToastType.LoadDataError);
            _gridData = [];
            _count = 0;
        }
        finally
        {
            _isLoading = false;
            await InvokeAsync(StateHasChanged);
        }
    }

    private void AddMessage() => Navigation.NavigateTo("/admin/messages/edit");

    private void EditMessage(GetMessageResponse message) => Navigation.NavigateTo($"/admin/messages/edit/{message.Id}");

    private async Task DeleteMessage(GetMessageResponse message)
    {
        try
        {
            var confirmed = await ModalDialogService.DeleteConfirmationByName(message.Name);

            if (confirmed)
            {
                _isLoading = true;

                var response = await Mediator.Send(new DeleteMessageCommand(message.Id));

                if (response.Succeeded)
                    ToastService.Show(ToastType.DeleteSuccess);
                else
                    ToastService.Show(ToastType.DeleteError, string.Join(',', response.Messages));

                await _grid.Reload();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, $"Błąd podczas usuwania wiadomości ID: {message.Id}");
            ToastService.Show(ToastType.DeleteError);
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private void HandleAction(string item)
    {
        switch (item)
        {
            case GridToolbar.Add:
                AddMessage();
                break;
        }
    }

    private void HandleSearch(string searchString)
    {
        _searchString = searchString;
        _grid.Reload();
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        if (AppStateService != null)
        {
            AppStateService.StateChanged += OnStateChanged;
        }
    }

    void IDisposable.Dispose()
    {
        if (AppStateService != null)
        {
            AppStateService.StateChanged -= OnStateChanged;
        }
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }
}
