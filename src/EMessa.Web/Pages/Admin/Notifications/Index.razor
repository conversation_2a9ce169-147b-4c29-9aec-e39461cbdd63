@page "/admin/notifications/index"
@attribute [Authorize(Roles = Role.Administrator)]

@using EMessa.Core.Features.Notifications.Commands.Delete
@using EMessa.Core.Features.Notifications.Queries.GetAll
@using EMessa.Web.Constants
@using EMessa.Web.Pages.Admin.Notifications.Parts
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor

@inject ILogger<Index> Logger
@inject NavigationManager Navigation
@implements IDisposable

<IndexCard Title=@("Komunikaty".Tr())>
    <ChildContent>
        <RadzenStack Orientation="Orientation.Horizontal" Class="p-1 justify-start items-start gap-2 flex-wrap">
            <AddRadzenButton Click="@AddNewNotification" />

            <GridSearch @ref="_gridSearch" ValueChanged="@_grid.Reload" CssClass="ml-auto" />
        </RadzenStack>
        <ProgressBar IsLoading="@_isLoading" />
        <RadzenDataGrid @ref="_grid" TItem="GetAllNotificationsResponse"
                        Data="@_gridData" Count="@_count" LoadData="@LoadData" IsLoading="@_isLoading"
                        AllowFiltering FilterMode="FilterMode.Simple"
                        FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                        PageSize="@PageSize.DefaultPageSize" PageSizeOptions="@PageSize.Pages20To200" PageSizeText="@PageSize.FormatPaginationInfo(_grid)"
                        AllowPaging
                        AllowSorting AllowMultiColumnSorting
                        AllowColumnResize
                        RowDoubleClick="@((args) => EditNotification(args.Data))"
                        Density="Density.Compact"
                        EmptyText="@RadzenDataGridConstants.EmptyText">
            <LoadingTemplate />

            <Columns>
                <GridActionsColumn TItem="GetAllNotificationsResponse"
                                             IsLargeScreen="@AppStateService.IsLargeScreen"
                                             Width="@(AppStateService.IsLargeScreen ? "90px" : "30px")">
                    <ContentTemplate Context="data">
                        <RadzenButton ButtonStyle="ButtonStyle.Info" Icon="person_check"
                                      Size="ButtonSize.ExtraSmall"
                                      Click="@(_ => NotificationConfirmation(data))" />
                        <EditRadzenButton IsAction Click="@(_ => EditNotification(data))" />
                        <DeleteRadzenButton IsAction Click="@(_ => DeleteNotification(data))" />
                    </ContentTemplate>
                </GridActionsColumn>

                <RadzenDataGridColumn Property="@nameof(GetAllNotificationsResponse.Name)" Title=@("Nazwa".Tr())
                                      Width="200px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable />
                <RadzenDataGridColumn Property="@nameof(GetAllNotificationsResponse.Title)" Title=@("Tytuł".Tr())
                                      Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                      Sortable Filterable />
                <RadzenDataGridColumn Property="@nameof(GetAllNotificationsResponse.CreatedDate)" Title=@("Utworzono".Tr())
                                      Width="200px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable="false">
                    <Template Context="notification">
                        @notification.CreatedDate.ToLocalTime().ToString("dd.MM.yyyy HH:mm")
                    </Template>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Property="@nameof(GetAllNotificationsResponse.CreatedByFullName)" Title=@("Utworzył".Tr())
                                      Width="200px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterOperator="FilterOperator.Contains" FilterProperty="CreatedBy" />
                <RadzenDataGridColumn Property="@nameof(GetAllNotificationsResponse.IsActive)"
                                      Title=@("Aktywny".Tr()) Width="100px"
                                      TextAlign="TextAlign.Center" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable>
                    <Template Context="notification">
                        <RadzenCheckBox @bind-Value="notification.IsActive" Disabled TValue="bool" />
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Visible="@(!AppStateService.IsLargeScreen)" Filterable="false" Sortable="false">
                    <Template Context="notification">
                        <NotificationCard Notification="@notification" />
                    </Template>
                </RadzenDataGridColumn>
            </Columns>
        </RadzenDataGrid>
    </ChildContent>
</IndexCard>

@code {
    private RadzenDataGrid<GetAllNotificationsResponse> _grid = new();
    private IEnumerable<GetAllNotificationsResponse> _gridData = [];
    private int _count;
    private bool _isLoading;
    private GridSearch? _gridSearch;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await _grid.Reload();
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    private async Task LoadData(LoadDataArgs args)
    {
        _isLoading = true;

        try
        {
            var sieveModel = args.ToSieveModel().ApplyGlobalSearchFilter(_gridSearch?.SearchValue);
            var response = await Mediator.Send(new GetAllNotificationsQuery(sieveModel));

            if (response.Succeeded)
            {
                _gridData = response.Data;
                _count = response.TotalCount;
            }
            else
            {
                ToastService.Show(ToastType.LoadDataError);
                _gridData = [];
                _count = 0;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas ładowania danych komunikatów.");
            ToastService.Show(ToastType.LoadDataError);
            _gridData = [];
            _count = 0;
        }
        finally
        {
            _isLoading = false;
            await InvokeAsync(StateHasChanged);
        }
    }

    private void AddNewNotification() => Navigation.NavigateTo("/admin/notifications/edit");

    private void EditNotification(GetAllNotificationsResponse notification) => Navigation.NavigateTo($"/admin/notifications/edit/{notification.Id}");

    private void NotificationConfirmation(GetAllNotificationsResponse notification) => Navigation.NavigateTo($"/admin/notifications/confirmations/{notification.Id}");

    private async Task DeleteNotification(GetAllNotificationsResponse notification)
    {
        try
        {
            var confirmed = await ModalDialogService.DeleteConfirmationByName(notification.Name);

            if (confirmed)
            {
                _isLoading = true;

                var response = await Mediator.Send(new DeleteNotificationCommand(notification.Id));

                if (response.Succeeded)
                    ToastService.Show(ToastType.DeleteSuccess);
                else
                    ToastService.Show(ToastType.DeleteError, string.Join(',', response.Messages));

                await _grid.Reload();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, $"Błąd podczas usuwania komunikatu ID: {notification.Id}");
            ToastService.Show(ToastType.DeleteError);
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        if (AppStateService != null)
        {
            AppStateService.StateChanged += OnStateChanged;
        }
    }

    void IDisposable.Dispose()
    {
        if (AppStateService != null)
        {
            AppStateService.StateChanged -= OnStateChanged;
        }
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }
}
