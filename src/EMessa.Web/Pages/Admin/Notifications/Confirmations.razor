@page "/admin/notifications/confirmations/{NotificationId:int}"
@attribute [Authorize(Roles = Role.Administrator)]

@using EMessa.Core.Features.Notifications.Commands.Delete
@using EMessa.Core.Features.Notifications.Queries.Get
@using EMessa.Core.Features.Notifications.Queries.GetAll
@using EMessa.Core.Features.Notifications.Queries.GetConfirmations
@using EMessa.Web.Constants
@using EMessa.Web.Pages.Admin.Notifications.Parts
@using Ra<PERSON>zen
@using Radzen.Blazor

@inject ILogger<Confirmations> Logger
@inject NavigationManager Navigation
@implements IDisposable

<IndexCard Title=@("Potwierdzenia komunikatu".Tr().AddText(": " + _notification.Name))>
    <ChildContent>
        <RadzenStack Orientation="Orientation.Horizontal" Class="p-1 justify-start items-start gap-2 flex-wrap">
            <RadzenButton Text="@("Powrót".Tr())" Icon="arrow_back" ButtonStyle="ButtonStyle.Secondary"
                          Click="@(_ => Navigation.NavigateTo("/admin/notifications/index"))" />

            <GridSearch @ref="_gridSearch" ValueChanged="@_grid.Reload" CssClass="ml-auto" />
        </RadzenStack>
        <ProgressBar IsLoading="@_isLoading" />
        <RadzenDataGrid @ref="_grid" TItem="GetNotificationConfirmationsResponse"
                        Data="@_gridData" Count="@_count" LoadData="@LoadData" IsLoading="@_isLoading"
                        AllowFiltering FilterMode="FilterMode.Simple"
                        FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                        PageSize="@PageSize.DefaultPageSize"
                        PageSizeOptions="@PageSize.Pages20To200"
                        PageSizeText="@PageSize.PageSizeText"
                        AllowPaging
                        AllowSorting AllowMultiColumnSorting
                        AllowColumnResize
                        Density="Density.Compact"
                        EmptyText="@RadzenDataGridConstants.EmptyText">
            <LoadingTemplate />

            <Columns>
                <RadzenDataGridColumn Property="@nameof(GetNotificationConfirmationsResponse.UserName)" Title=@("Nazwa użytkownika".Tr())
                                      Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable />
                <RadzenDataGridColumn Property="@nameof(GetNotificationConfirmationsResponse.ConfirmedDate)" Title=@("Data potwierdzenia".Tr())
                                      Width="200px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable="false">
                    <Template Context="notificationConfirmation">
                        @notificationConfirmation.ConfirmedDate?.ToLocalTime().ToString("dd.MM.yyyy HH:mm")
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Visible="@(!AppStateService.IsLargeScreen)" Filterable="false" Sortable="false">
                    <Template Context="notificationConfirmation">
                        <ConfirmationCard NotificationConfirmation="@notificationConfirmation" />
                    </Template>
                </RadzenDataGridColumn>
            </Columns>
        </RadzenDataGrid>
    </ChildContent>
</IndexCard>

@code {
    [Parameter] public int? NotificationId { get; set; }
    private GetNotificationResponse _notification = new();
    private RadzenDataGrid<GetNotificationConfirmationsResponse> _grid = new();
    private IEnumerable<GetNotificationConfirmationsResponse> _gridData = [];
    private int _count;
    private bool _isLoading;
    private GridSearch? _gridSearch;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            if (NotificationId is > 0)
            {
                var response = await Mediator.Send(new GetNotificationQuery(NotificationId.Value));

                if (response.Succeeded)
                {
                    _notification = response.Data;
                    await _grid.Reload();
                }
                else
                {
                    ToastService.Show(ToastType.LoadDataError);
                    _gridData = [];
                    _count = 0;
                }
            }
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    private async Task LoadData(LoadDataArgs args)
    {
        if (NotificationId is null)
            return;

        _isLoading = true;

        try
        {
            var sieveModel = args.ToSieveModel().ApplyGlobalSearchFilter(_gridSearch?.SearchValue);
            var response = await Mediator.Send(new GetNotificationConfirmationsQuery(NotificationId.Value, sieveModel));

            if (response.Succeeded)
            {
                _gridData = response.Data;
                _count = response.TotalCount;
            }
            else
            {
                ToastService.Show(ToastType.LoadDataError);
                _gridData = [];
                _count = 0;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas ładowania danych potwierdzeń komunikatów.");
            ToastService.Show(ToastType.LoadDataError);
            _gridData = [];
            _count = 0;
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        if (AppStateService != null)
        {
            AppStateService.StateChanged += OnStateChanged;
        }
    }

    void IDisposable.Dispose()
    {
        if (AppStateService != null)
        {
            AppStateService.StateChanged -= OnStateChanged;
        }
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }
}
