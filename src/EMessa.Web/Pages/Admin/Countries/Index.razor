@page "/admin/Countries/index"
@attribute [Authorize(Roles = Role.Administrator)]

@using EMessa.Core.Features.Countries.Commands.AddEdit
@using EMessa.Core.Features.Countries.Commands.Delete
@using EMessa.Core.Features.Countries.Queries.GetAll
@using EMessa.Web.Constants
@using EMessa.Web.Pages.Admin.Countries.Parties
@using Radzen
@using Radzen.Blazor

@inject ILogger<Index> Logger
@inject NavigationManager Navigation
@inject IMapper Mapper
@implements IDisposable

<IndexCard Title=@("Państwa".Tr())>
    <ChildContent>
        <div class="w-full">
            <GridToolbar Items="@([GridToolbar.Add, GridToolbar.Search])"
                         OnAction="@HandleAction"
                         SearchTextChanged="@HandleSearch">
            </GridToolbar>
            <ProgressBar IsLoading="@_isLoading" />
            <RadzenDataGrid @ref="_grid" TItem="GetAllCountriesResponse"
                            Data="@_gridData" Count="@_count" LoadData="@LoadData" IsLoading="@_isLoading"
                            AllowFiltering FilterMode="FilterMode.Simple"
                            FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                            AllowPaging PageSize="@PageSize.DefaultPageSize" PageSizeOptions="@PageSize.Pages20To200" PageSizeText="@PageSize.FormatPaginationInfo(_grid)"
                            AllowSorting AllowMultiColumnSorting
                            AllowColumnResize
                            Density="Density.Compact"
                            EmptyText="@PageSize.NoDataText"
                            EditMode="DataGridEditMode.Single"
                            RowEdit="@SetEditCountry"
                            RowDoubleClick="@((arg) => EditCountry(arg.Data))">

                <LoadingTemplate />

                <Columns>
                    <RadzenDataGridColumn Context="country" Title=@(AppStateService.IsLargeScreen ? "Akcje".Tr() : string.Empty) Filterable="false" Sortable="false"
                                          Width="@(AppStateService.IsLargeScreen ? "55px" : "30px")"
                                          Frozen="true" FrozenPosition="FrozenColumnPosition.Right">
                        <Template Context="country">
                            <div class="@(AppStateService.IsLargeScreen ? "flex flex-row gap-2 justify-center" : "flex flex-col gap-2 items-center")">
                                <EditRadzenButton IsAction Click="@(_ => EditCountry(country))" />
                                <DeleteRadzenButton IsAction Click="@(_ => DeleteCountry(country))" />
                            </div>
                        </Template>
                        <EditTemplate Context="country">
                            <div class="@(AppStateService.IsLargeScreen ? "flex flex-row gap-2 justify-center" : "flex flex-col gap-2 items-center")">
                                <SaveRadzenButton IsAction Click="@(_ => SaveEditCountry(country))" />
                                <CancelRadzenButton IsAction Click="@(_ => CancelCountry(country))" />
                            </div>
                        </EditTemplate>
                    </RadzenDataGridColumn>

                    <RadzenDataGridColumn Property="@nameof(GetAllCountriesResponse.Name)" Title=@("Nazwa".Tr())
                                          Sortable Filterable
                                          Visible="@(AppStateService.IsLargeScreen)">
                        <EditTemplate Context="country">
                            <RadzenTextBox @bind-Value="_addEditCountry.Name" class="w-full" />
                        </EditTemplate>
                    </RadzenDataGridColumn>

                    <RadzenDataGridColumn Property="@nameof(GetAllCountriesResponse.Code)" Title=@("Kod".Tr())
                                          Sortable Filterable
                                          Visible="@(AppStateService.IsLargeScreen)">
                        <EditTemplate Context="country">
                            <RadzenTextBox @bind-Value="_addEditCountry.Code" class="w-full" />
                        </EditTemplate>
                    </RadzenDataGridColumn>

                    <RadzenDataGridColumn Property="@nameof(GetAllCountriesResponse.VatCode)" Title=@("Kod VAT".Tr())
                                          Sortable Filterable
                                          Visible="@(AppStateService.IsLargeScreen)">
                        <EditTemplate Context="country">
                            <RadzenTextBox @bind-Value="_addEditCountry.VatCode" class="w-full" />
                        </EditTemplate>
                    </RadzenDataGridColumn>

                    <RadzenDataGridColumn Property="@nameof(GetAllCountriesResponse.LangCode)" Title=@("Kod języka".Tr())
                                          Sortable Filterable
                                          Visible="@(AppStateService.IsLargeScreen)">
                        <EditTemplate Context="country">
                            <RadzenTextBox @bind-Value="_addEditCountry.LangCode" class="w-full" />
                        </EditTemplate>
                    </RadzenDataGridColumn>

                    <RadzenDataGridColumn Property="@nameof(GetAllCountriesResponse.CurrencyCode)" Title=@("Kod waluty".Tr())
                                          Sortable Filterable
                                          Visible="@(AppStateService.IsLargeScreen)">
                        <EditTemplate Context="country">
                            <RadzenDropDown AllowClear="false" TValue="string"
                                            Data="@_availableCurrencies" AllowFiltering
                                            @bind-Value="_addEditCountry.CurrencyCode" class="w-full" />
                        </EditTemplate>
                    </RadzenDataGridColumn>

                    <RadzenDataGridColumn Visible="@(!AppStateService.IsLargeScreen)" Filterable="false" Sortable="false">
                        <Template Context="country">
                            <CountryCard Country="@country" />
                        </Template>
                        <EditTemplate Context="country">
                            <RadzenStack Orientation="Orientation.Vertical" AlignItems="AlignItems.Start" Gap="0.5rem">
                                <RadzenStack Orientation="Orientation.Vertical" AlignItems="AlignItems.Start" class="w-full">
                                    <RadzenLabel Text=@("Nazwa".Tr()) />
                                    <RadzenTextBox @bind-Value="_addEditCountry.Name" class="w-full" />
                                </RadzenStack>
                                <RadzenStack Orientation="Orientation.Vertical" AlignItems="AlignItems.Start" class="w-full">
                                    <RadzenLabel Text=@("Kod".Tr()) />
                                    <RadzenTextBox @bind-Value="_addEditCountry.Code" class="w-full" />
                                </RadzenStack>
                                <RadzenStack Orientation="Orientation.Vertical" AlignItems="AlignItems.Start" class="w-full">
                                    <RadzenLabel Text=@("Kod VAT".Tr()) />
                                    <RadzenTextBox @bind-Value="_addEditCountry.VatCode" class="w-full" />
                                </RadzenStack>
                                <RadzenStack Orientation="Orientation.Vertical" AlignItems="AlignItems.Start" class="w-full">
                                    <RadzenLabel Text=@("Kod języka".Tr()) />
                                    <RadzenTextBox @bind-Value="_addEditCountry.LangCode" class="w-full" />
                                </RadzenStack>
                                <RadzenStack Orientation="Orientation.Vertical" AlignItems="AlignItems.Start" class="w-full">
                                    <RadzenLabel Text=@("Kod waluty".Tr()) />
                                    <RadzenDropDown AllowClear="false" TValue="string"
                                                    Data="@_availableCurrencies" AllowFiltering
                                                    @bind-Value="_addEditCountry.CurrencyCode" class="w-full" />
                                </RadzenStack>
                            </RadzenStack>
                        </EditTemplate>
                    </RadzenDataGridColumn>
                </Columns>
            </RadzenDataGrid>
        </div>
    </ChildContent>
</IndexCard>

@code {
    private RadzenDataGrid<GetAllCountriesResponse> _grid = new();
    private IEnumerable<GetAllCountriesResponse> _gridData = [];
    private AddEditCountryCommand _addEditCountry = new();
    private string? _searchString;
    private int _count;
    private bool _isLoading;
    private List<string> _availableCurrencies = CountriesConstans.AllIsoCurrencyCodes;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await _grid.Reload();
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    private async Task LoadData(LoadDataArgs args)
    {
        _isLoading = true;

        try
        {
            var sieveModel = args.ToSieveModel().ApplyGlobalSearchFilter(_searchString);
            var response = await Mediator.Send(new GetAllCountriesQuery(sieveModel));

            if (response.Succeeded)
            {
                _gridData = response.Data;
                _count = response.TotalCount;
            }
            else
            {
                ToastService.Show(ToastType.LoadDataError);
                _gridData = [];
                _count = 0;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas ładowania danych państw.");
            ToastService.Show(ToastType.LoadDataError);
            _gridData = [];
            _count = 0;
        }
        finally
        {
            _isLoading = false;
            await InvokeAsync(StateHasChanged);
        }
    }

    private void SetEditCountry(GetAllCountriesResponse country)
    {
        if (_addEditCountry?.Id != country.Id)
        {
            _addEditCountry = Mapper.Map<AddEditCountryCommand>(country);
        }
    }

    private async Task EditCountry(GetAllCountriesResponse country)
    {
        if (!_grid.IsValid) return;
        await _grid.EditRow(country);
    }

    private async Task CancelCountry(GetAllCountriesResponse country)
    {
        _grid.CancelEditRow(country);
        _addEditCountry = new();
        await _grid.Reload();
    }

    private async Task SaveEditCountry(GetAllCountriesResponse country)
    {
        var validator = new AddEditCountryValidator();
        var result = await validator.ValidateAsync(_addEditCountry);

        if (!result.IsValid)
        {
            ToastService.Show(ToastType.SaveError, string.Join('\n', result.Errors));
            return;
        }

        var response = await Mediator.Send(_addEditCountry);

        if (response.Succeeded)
        {
            ToastService.Show(ToastType.SaveSuccess);
            _addEditCountry = new();
            _grid.CancelEditRow(country);
            await _grid.Reload();
        }
        else
        {
            ToastService.Show(ToastType.SaveError);
        }
    }

    async Task InsertRow()
    {
        if (!_grid.IsValid) return;

        var newItem = Mapper.Map<GetAllCountriesResponse>(_addEditCountry);

        if (!_gridData.Any())
        {
            _gridData = [newItem];
        }

        await _grid.InsertRow(newItem);
    }

    private async Task DeleteCountry(GetAllCountriesResponse country)
    {
        try
        {
            var confirmed = await ModalDialogService.DeleteConfirmationByName(country.Name);

            if (confirmed == true)
            {
                _isLoading = true;

                var response = await Mediator.Send(new DeleteCountryCommand(country.Id));

                if (response.Succeeded)
                    ToastService.Show(ToastType.DeleteSuccess);
                else
                    ToastService.Show(ToastType.DeleteError, string.Join(',', response.Messages));

                await _grid.Reload();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, $"Błąd podczas usuwania państwa ID: {country.Id}");
            ToastService.Show(ToastType.DeleteError);
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private void HandleAction(string item)
    {
        switch (item)
        {
            case GridToolbar.Add:
                InsertRow();
                break;
        }
    }

    private void HandleSearch(string searchString)
    {
        _searchString = searchString;
        _grid.Reload();
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        if (AppStateService != null)
        {
            AppStateService.StateChanged += OnStateChanged;
        }
    }

    void IDisposable.Dispose()
    {
        if (AppStateService != null)
        {
            AppStateService.StateChanged -= OnStateChanged;
        }
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }
}
