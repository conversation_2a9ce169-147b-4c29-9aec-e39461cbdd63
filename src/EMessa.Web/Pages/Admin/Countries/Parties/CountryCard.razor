@using EMessa.Core.Features.Countries.Queries.GetAll

<div class="p-3 bg-white shadow-md rounded-lg border border-gray-200 text-wrap">
    <div class="mb-2 text-xl font-semibold text-gray-800">
        @Country.Name
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Kod".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@Country.Code</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Kod VAT".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@Country.VatCode</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Kod języka".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@Country.LangCode</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Kod waluty".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@Country.CurrencyCode</span>
    </div>
</div>

@code {
    [Parameter] public required GetAllCountriesResponse Country { get; set; }
}