@page "/admin/customers/index"
@attribute [Authorize(Roles = $"{Role.Administrator},{Role.TradeManager},{Role.Trade},{Role.Production}")]

@using EMessa.Core.Features.Customers.Commands.Delete
@using EMessa.Core.Features.Customers.Queries.GetAll
@using EMessa.Web.Constants
@using EMessa.Web.Pages.Admin.Customers.Parts
@using Ra<PERSON>zen
@using Radzen.Blazor

@inject ILogger<Index> Logger
@inject NavigationManager Navigation
@implements IDisposable

<IndexCard Title=@("Klienci".Tr())>
    <ChildContent>
        <div class="w-full">
            <GridToolbar>
                <AddRadzenButton Click="@AddNewCustomer" />
                <GridSearch @ref="_gridSearch" ValueChanged="@_grid.Reload" MarginLeftAuto />
            </GridToolbar>
            <ProgressBar IsLoading="@_isLoading" />
            <RadzenDataGrid @ref="_grid" TItem="GetAllCustomersResponse"
                            Data="@_gridData" Count="@_count" LoadData="@LoadData" IsLoading="@_isLoading"
                            AllowFiltering FilterMode="FilterMode.Simple"
                            FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                            AllowPaging PageSize="@PageSize.DefaultPageSize" PageSizeOptions="@PageSize.Pages20To200" PageSizeText="@PageSize.FormatPaginationInfo(_grid)"
                            AllowSorting AllowMultiColumnSorting
                            AllowColumnResize
                            Density="Density.Compact"
                            EmptyText="@PageSize.NoDataText">

                <LoadingTemplate />

                <Columns>
                    <RadzenDataGridColumn Context="customer" Title=@(AppStateService.IsLargeScreen ? "Akcje".Tr() : string.Empty)
                                          Filterable="false" Sortable="false"
                                          TextAlign="Radzen.TextAlign.Center" Width="@(AppStateService.IsLargeScreen ? "55px" : "30px")"
                                          Frozen="true" FrozenPosition="Radzen.FrozenColumnPosition.Right">
                        <Template Context="customer">
                            <div class="@(AppStateService.IsLargeScreen ? "flex flex-row gap-2 justify-center" : "flex flex-col gap-2 items-center")">
                                <RadzenButton ButtonStyle="ButtonStyle.Light" Icon="edit"
                                              Size="ButtonSize.ExtraSmall"
                                              Click="@(args => EditCustomer(customer))" />
                                <RadzenButton ButtonStyle="ButtonStyle.Danger" Icon="delete"
                                              Size="ButtonSize.ExtraSmall"
                                              Click="@(args => DeleteCustomer(customer))" />
                            </div>
                        </Template>
                    </RadzenDataGridColumn>

                    <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.ShortName)" Title=@("Skrócona nazwa".Tr())
                                          Width="120px" Visible="@AppStateService.IsLargeScreen"
                                          Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                    <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.Name)" Title=@("Nazwa".Tr())
                                          Width="200px" Visible="@AppStateService.IsLargeScreen"
                                          Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                    <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.Address)" Title=@("Adres".Tr())
                                          Visible="@AppStateService.IsLargeScreen"
                                          Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                    <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.PostCode)" Title=@("Kod pocztowy".Tr())
                                          Width="120px" Visible="@AppStateService.IsLargeScreen"
                                          Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                    <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.City)" Title=@("Miasto".Tr())
                                          Width="150px" Visible="@AppStateService.IsLargeScreen"
                                          Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                    <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.TaxIdentificationNumber)" Title=@("NIP".Tr())
                                          Width="200px" Visible="@AppStateService.IsLargeScreen"
                                          Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                    <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.BranchName)" Title=@("Oddział".Tr())
                                          Width="200px" Visible="@AppStateService.IsLargeScreen"
                                          Sortable Filterable FilterProperty="@nameof(GetAllCustomersResponse.BranchId)" FilterOperator="FilterOperator.Equals" FilterValue="@_branchFilter"
                                          WhiteSpace="WhiteSpace.Wrap">
                        <FilterTemplate>
                            <BranchesSelector @bind-Value="@_branchFilter"
                                              ShowActiveOnly="false"
                                              Clearable="true" />
                        </FilterTemplate>
                    </RadzenDataGridColumn>
                    <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.IsActive)" Title=@("Aktywny".Tr()) Width="100px"
                                          TextAlign="TextAlign.Center" Visible="@AppStateService.IsLargeScreen"
                                          Sortable Filterable>
                        <Template Context="customer">
                            <RadzenCheckBox @bind-Value="customer.IsActive" Disabled TValue="bool" />
                        </Template>
                    </RadzenDataGridColumn>
                    <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.IsPrivate)"
                                          Title=@("Os. prywatna".Tr()) Width="120px"
                                          TextAlign="TextAlign.Center" Visible="@AppStateService.IsLargeScreen"
                                          Sortable Filterable>
                        <Template Context="customer">
                            <RadzenCheckBox @bind-Value="customer.IsPrivate" Disabled TValue="bool" />
                        </Template>
                    </RadzenDataGridColumn>

                    <RadzenDataGridColumn Visible="@(!AppStateService.IsLargeScreen)" Filterable="false" Sortable="false">
                        <Template Context="customer">
                            <CustomerCard Customer="@customer" />
                        </Template>
                    </RadzenDataGridColumn>
                </Columns>
            </RadzenDataGrid>
        </div>
    </ChildContent>
</IndexCard>

@code {
    private RadzenDataGrid<GetAllCustomersResponse> _grid = new();
    private IEnumerable<GetAllCustomersResponse> _gridData = [];
    private int _count;
    private bool _isLoading;
    private GridSearch? _gridSearch;
    private int? _branchFilter;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await _grid.Reload();
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    private async Task LoadData(LoadDataArgs args)
    {
        _isLoading = true;

        try
        {
            var sieveModel = args.ToSieveModel().ApplyGlobalSearchFilter(_gridSearch?.SearchValue);
            var response = await Mediator.Send(new GetAllCustomersQuery(sieveModel));

            if (response.Succeeded)
            {
                _gridData = response.Data;
                _count = response.TotalCount;
            }
            else
            {
                ToastService.Show(ToastType.LoadDataError);
                _gridData = [];
                _count = 0;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas ładowania danych klientów.");
            ToastService.Show(ToastType.LoadDataError);
            _gridData = [];
            _count = 0;
        }
        finally
        {
            _isLoading = false;
            await InvokeAsync(StateHasChanged);
        }
    }

    private void AddNewCustomer() => Navigation.NavigateTo("/admin/customers/edit");

    private void EditCustomer(GetAllCustomersResponse customer) => Navigation.NavigateTo($"/admin/customers/edit/{customer.Id}");

    private async Task DeleteCustomer(GetAllCustomersResponse customer)
    {
        try
        {
            var confirmed = await ModalDialogService.DeleteConfirmationByName(customer.Name);

            if (confirmed == true)
            {
                _isLoading = true;

                var response = await Mediator.Send(new DeleteCustomerCommand(customer.Id));

                if (response.Succeeded)
                    ToastService.Show(ToastType.DeleteSuccess);
                else
                    ToastService.Show(ToastType.DeleteError, string.Join(',', response.Messages));

                await _grid.Reload();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, $"Błąd podczas usuwania klienta ID: {customer.Id}");
            ToastService.Show(ToastType.DeleteError);
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        if (AppStateService != null)
        {
            AppStateService.StateChanged += OnStateChanged;
        }
    }

    void IDisposable.Dispose()
    {
        if (AppStateService != null)
        {
            AppStateService.StateChanged -= OnStateChanged;
        }
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }
}
