@using EMessa.Core.Features.Vies.Queries
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor
@inject IGusService GusService
@inject ILogger<GusDataDialog> <PERSON><PERSON>
@inject DialogService DialogService

<RadzenRow AlignItems="AlignItems.Start" RowGap="0.5rem" Gap="0.5rem">

    @if (_isLoading)
    {
        <RadzenColumn Size="12" SizeMD="12" SizeLG="12">
            <label>@("Wyszukiwanie danych w bazie GUS...".Tr())</label>
            <RadzenProgressBar Style="height: 5px;" ProgressBarStyle="ProgressBarStyle.Primary" Value="100" ShowValue="false" Mode="ProgressBarMode.Indeterminate" />
        </RadzenColumn>
    }
    else
    {
        <RadzenColumn Size="12" SizeMD="12" SizeLG="12">
            <RadzenLabel Text=@("NIP".Tr()) />
            <RadzenTextBox @bind-Value="_vatData.NationalOfficialBusinessRegister" ReadOnly class="w-full" />
        </RadzenColumn>

        <RadzenColumn Size="12" SizeMD="12" SizeLG="12" Visible="@(!string.IsNullOrEmpty(_vatData.Name))">
            <RadzenLabel Text=@("Nazwa".Tr()) />
            <RadzenTextArea @bind-Value="_vatData.Name" ReadOnly class="w-full" />
        </RadzenColumn>

        <RadzenColumn Size="12" SizeMD="12" SizeLG="12">
            <RadzenLabel Text=@("Stan na dzień".Tr()) />
            <RadzenDatePicker @bind-Value="_vatData.RequestDate" DateFormat="dd.MM.yyyy" ReadOnly class="w-full" />
        </RadzenColumn>

        <RadzenColumn Size="12" SizeMD="12" SizeLG="12">
            <RadzenLabel Text=@("Wynik weryfikacji:".Tr()) />
            @if (_vatData.VatValid)
            {
                <RadzenBadge BadgeStyle="BadgeStyle.Success" Variant="Variant.Filled" Text="@("Aktywny".Tr())"
                             class="ml-2" />
            }
            else
            {
                <RadzenBadge BadgeStyle="BadgeStyle.Danger" Variant="Variant.Filled" Text="@("Nieaktywny".Tr())"
                             class="ml-2" />
            }
        </RadzenColumn>
    }

</RadzenRow>

<RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.End" class="mt-4">
    <RadzenButton Text="@("Odśwież".Tr())" Icon="refresh" ButtonStyle="ButtonStyle.Light" Click="@CheckVatStatusInGus" Size="@GetButtonSize()" />
    <RadzenButton Text="@("Zamknij".Tr())" Icon="cancel" ButtonStyle="ButtonStyle.Light" Click="@OnCancel" Size="@GetButtonSize()" />
</RadzenStack>

@code {
    [Parameter] public required string TaxIdentificationNumber { get; set; }
    [Parameter] public required int CountryId { get; set; }

    private VatData _vatData = new();
    private bool _isLoading;

    private ButtonSize GetButtonSize() => AppStateService.IsLargeScreen ? ButtonSize.Medium : ButtonSize.Small;

    private void OnCancel() => DialogService.Close();

    public class VatData
    {
        public string NationalOfficialBusinessRegister { get; set; } = string.Empty;
        public bool VatValid { get; set; }
        public DateTime RequestDate { get; set; } = DateTime.Now;
        public string Name { get; set; } = string.Empty;
    }

    protected override async Task OnInitializedAsync()
    {
        await CheckVatStatusInGus();
    }

    private async Task CheckVatStatusInGus()
    {
        try
        {
            _isLoading = true;

            var vatInfo = await Mediator.Send(new GetViesDataQuery(CountryId, TaxIdentificationNumber));

            if (vatInfo.Succeeded || vatInfo.Data is not null)
            {
                _vatData.VatValid = vatInfo.Data.VatValid;
                _vatData.RequestDate = vatInfo.Data.RequestDate;
                _vatData.Name = vatInfo.Data.Name;
            }

            _vatData.NationalOfficialBusinessRegister = TaxIdentificationNumber;

        }
        catch (Exception ex)
        {
            Logger.LogError(ex, ex.Message);
            ToastService.Show(ToastType.LoadDataError, "Błąd wyszukiwania danych w bazie GUS".Tr());
        }
        finally
        {
            _isLoading = false;
        }
    }
}
