@using EMessa.Base.Enums
@using EMessa.Core.Features.Branches.Commands.Delete
@using EMessa.Core.Features.Branches.Queries.GetAll
@using EMessa.Core.Features.CustomerAccountManagers.Commands.Assign
@using EMessa.Core.Features.CustomerAccountManagers.Commands.Unassign
@using EMessa.Core.Features.CustomerAccountManagers.Queries.GetAssigned
@using EMessa.Core.Features.Customers.Commands.Delete
@using EMessa.Core.Features.Customers.Queries
@using EMessa.Core.Features.Customers.Queries.GetAll
@using EMessa.Web.Components.Editors
@using EMessa.Web.Components.Grids
@using EMessa.Web.Constants
@using EMessa.Web.Pages.Admin.Branches.Parts
@using EMessa.Web.Pages.Admin.Customers.Parts
@using Radzen
@using Radzen.Blazor

@inject ILogger<AccountManagersGrid> Logger
@inject NavigationManager Navigation
@inject IMapper Mapper
@implements IDisposable


<div class="w-full">
    @if (_isLoading)
    {
        <RadzenProgressBar Style="height: 5px;" ProgressBarStyle="ProgressBarStyle.Primary" Value="100" ShowValue="false" Mode="ProgressBarMode.Indeterminate" />
    }
    <RadzenStack Orientation="Orientation.Horizontal" Class="p-1 justify-start items-start gap-2 flex-wrap">
        <AccountManagersSelector @ref="@_accountManagersSelector"
                                 CustomerId="@CustomerId"
                                 TValue="int"
                                 @bind-Value="_addAccountManagerCommand.UserProfileId"
                                 AccountManagerType="@AccountManagerType"
                                 UnassignedOnly="true" />
        <RadzenButton Text=@("Dodaj".Tr()) Icon="add" Disabled="@(_addAccountManagerCommand.UserProfileId == 0)"
                      ButtonStyle="ButtonStyle.Light" Click="@AddCustomerTradeSuperiors" />
    </RadzenStack>
    <RadzenDataGrid @ref="_grid" TItem="GetAssignedAccountManagersForCustomerResponse"
                    Data="@_gridData" Count="@_count" IsLoading="@_isLoading"
                    AllowFiltering FilterMode="FilterMode.Simple"
                    FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                    AllowPaging PageSize="@PageSize.DefaultPageSize" PageSizeOptions="@PageSize.Pages20To200" PageSizeText="@PageSize.PageSizeText"
                    AllowSorting AllowMultiColumnSorting
                    AllowColumnResize
                    Density="Density.Compact"
                    EmptyText="@PageSize.NoDataText">

        <LoadingTemplate />

        <Columns>
            <RadzenDataGridColumn Context="customerSuperiors" Title=@("Akcje".Tr()) Filterable="false" Sortable="false"
                                  Width="@(AppStateService.IsLargeScreen ? "100px" : "50px")"
                                  Frozen="true" FrozenPosition="FrozenColumnPosition.Right">
                <Template Context="customerSuperiors">
                    <div class="@(AppStateService.IsLargeScreen ? "flex flex-row gap-2 justify-center" : "flex flex-col gap-2 items-center")">
                        <RadzenButton ButtonStyle="ButtonStyle.Danger" Icon="delete"
                                      Size="ButtonSize.ExtraSmall"
                                      Click="@(_ => DeleteCustomer(customerSuperiors))" />
                    </div>
                </Template>
            </RadzenDataGridColumn>

            <RadzenDataGridColumn Property="@nameof(GetAssignedAccountManagersForCustomerResponse.FirstName)" Title=@("Imię".Tr())
                                  Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />

            <RadzenDataGridColumn Property="@nameof(GetAssignedAccountManagersForCustomerResponse.LastName)" Title=@("Nazwisko".Tr())
                                  Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />

            <RadzenDataGridColumn Property="@nameof(GetAssignedAccountManagersForCustomerResponse.Email)" Title=@("Email".Tr())
                                  Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />

        </Columns>
    </RadzenDataGrid>
</div>

@code {
    [Parameter] public required int CustomerId { get; set; }
    [Parameter] public required AccountManagerType AccountManagerType { get; set; }

    private RadzenDataGrid<GetAssignedAccountManagersForCustomerResponse> _grid = new();
    private IEnumerable<GetAssignedAccountManagersForCustomerResponse> _gridData = [];
    private AssignCustomerAccountManagerCommand _addAccountManagerCommand = new();
    private int _count;
    private bool _isLoading;
    private AccountManagersSelector<int>? _accountManagersSelector;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await _grid.Reload();
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    private async Task LoadData()
    {
        if (CustomerId == 0)
        {
            ToastService.Show(ToastType.LoadDataError, "Brak wskazanego ID klienta".Tr());
            return;
        }

        _isLoading = true;

        try
        {
            var response = await Mediator.Send(new GetAssignedAccountManagersForCustomerQuery(CustomerId, AccountManagerType));

            if (response.Succeeded)
            {
                _gridData = response.Data;
                _count = response.TotalCount;
            }
            else
            {
                ToastService.Show(ToastType.LoadDataError);
                _gridData = [];
                _count = 0;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas ładowania danych opiekunów.");
            ToastService.Show(ToastType.LoadDataError);
            _gridData = [];
            _count = 0;
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task AddCustomerTradeSuperiors()
    {
        try
        {
            if (_addAccountManagerCommand.UserProfileId == 0)
            {
                ToastService.Show(ToastType.LoadDataError, "Nie wybrano opiekuna".Tr());
                return;
            }

            if (_gridData.Any(x => x.Id == _addAccountManagerCommand.UserProfileId))
            {
                ToastService.ShowInfo("Wybrany opiekun już istnieje".Tr());
                return;
            }

            _isLoading = true;

            _addAccountManagerCommand.CustomerId = CustomerId;
            _addAccountManagerCommand.AccountManagerType = AccountManagerType;

            var response = await Mediator.Send(_addAccountManagerCommand);

            if (response.Succeeded)
            {
                ToastService.Show(ToastType.SaveSuccess);

                if (_accountManagersSelector != null)
                    await _accountManagersSelector.RefreshSelector();

                await LoadData();
                await _grid.Reload();
            }
            else
            {
                ToastService.Show(ToastType.SaveError, string.Join(',', response.Messages));
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas dodawania opiekuna.");
            ToastService.Show(ToastType.SaveError);
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task DeleteCustomer(GetAssignedAccountManagersForCustomerResponse customerTradeSuperior)
    {
        try
        {
            var confirmed = await ModalDialogService.DeleteConfirmationByName($"{customerTradeSuperior.FirstName} {customerTradeSuperior.LastName}");

            if (confirmed == true)
            {
                _isLoading = true;

                var response = await Mediator.Send(new UnassignCustomerAccountManagerCommand(CustomerId, customerTradeSuperior.Id, AccountManagerType));

                if (response.Succeeded)
                    ToastService.Show(ToastType.DeleteSuccess);
                else
                    ToastService.Show(ToastType.DeleteError, string.Join(',', response.Messages));

                if (_accountManagersSelector != null)
                    await _accountManagersSelector.RefreshSelector();

                await LoadData();
                await _grid.Reload();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, $"Błąd podczas usuwania opiekuna o ID: {customerTradeSuperior.Id}");
            ToastService.Show(ToastType.DeleteError);
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        await LoadData();

        if (AppStateService != null)
        {
            AppStateService.StateChanged += OnStateChanged;
        }
    }

    void IDisposable.Dispose()
    {
        if (AppStateService != null)
        {
            AppStateService.StateChanged -= OnStateChanged;
        }
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }
}
