@page "/admin/customers/edit/{CustomerId:int?}"
@attribute [Authorize(Roles = Role.Administrator)]

@using EMessa.Base.Enums
@using EMessa.Core.Features.Customers.Commands.AddEdit
@using EMessa.Core.Features.Customers.Queries.Get
@using EMessa.Web.Components.Editors
@using EMessa.Web.Pages.Admin.Customers.Parts
@using Radzen
@using Radzen.Blazor

@inject NavigationManager Navigation
@inject ILogger<AddEdit> <PERSON><PERSON>
@inject DialogService DialogService

<IndexCard Title=@(IsEditMode ? "Edytuj Klienta".Tr() : "<PERSON>daj <PERSON>".Tr())>
    <RadzenTemplateForm TItem="AddEditCustomerRequest"
                        Data="@_addEditCustomer"
                        Submit="@OnSubmit">
        <FluentValidationForRadzenComponent @ref="@_validatorComponent"
                                            Validator="@(new AddEditCustomerValidator())" />

        <RadzenTabs RenderMode="TabRenderMode.Client">
            <Tabs>
                <RadzenTabsItem Text="@("Dane podstawowe".Tr())">
                    <RadzenRow AlignItems="AlignItems.Start" RowGap="0.5rem" Gap="1rem">
                        <RadzenColumn Size="12" SizeMD="12" SizeLG="12" class="flex flex-wrap items-center">
                            <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.End" class="rz-m-4 w-full">
                                <RadzenButton Size="ButtonSize.Small" Text="@("Szukaj klienta".Tr())" Icon="group_search" ButtonStyle="ButtonStyle.Light" Click="@SearchInGus" />
                                <RadzenButton Size="ButtonSize.Small" Text="@("Sprawdź status VAT".Tr())" Icon="verified_user" ButtonStyle="ButtonStyle.Light" Click="@SearchVat" />
                            </RadzenStack>
                            <div class="p-1 w-full"><hr /></div>
                        </RadzenColumn>

                        <RadzenColumn Size="12" SizeMD="12" SizeLG="6" class="flex items-center gap-4">
                            <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="0.5rem">
                                <RadzenSwitch @bind-Value="_addEditCustomer.IsActive" Name="IsActive" />
                                <RadzenLabel Text=@("Aktywny".Tr()) Component="IsActive" />
                                <ValidationMessage For="@(() => _addEditCustomer.IsActive)" class="validation-message" />
                            </RadzenStack>
                        </RadzenColumn>

                        <RadzenColumn Size="12" SizeMD="12" SizeLG="6" class="flex items-center gap-4">
                            <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="0.5rem">
                                <RadzenSwitch @bind-Value="_addEditCustomer.IsPrivate" Name="IsPrivate" />
                                <RadzenLabel Text=@("Osoba prywatna".Tr()) Component="IsPrivate" />
                                <ValidationMessage For="@(() => _addEditCustomer.IsPrivate)" class="validation-message" />
                            </RadzenStack>
                        </RadzenColumn>

                        <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                            <RadzenLabel Text=@("Państwo".Tr().AddText('*')) />
                            <CountriesSelector @bind-Value="@_addEditCustomer.CountryId"
                                               For="@(() => _addEditCustomer.CountryId)" />
                            <ValidationMessage For="@(() => _addEditCustomer.CountryId)" class="validation-message" />
                        </RadzenColumn>

                        <RadzenColumn Size="12" SizeMD="12" SizeLG="3">
                            <RadzenLabel Text=@("NIP".Tr().AddText(!_addEditCustomer.IsPrivate ? '*' : null)) />
                            <RadzenTextBox @bind-Value="_addEditCustomer.TaxIdentificationNumber" class="w-full" />
                            <ValidationMessage For="@(() => _addEditCustomer.TaxIdentificationNumber)" class="validation-message" />
                        </RadzenColumn>

                        <RadzenColumn Size="12" SizeMD="12" SizeLG="3">
                            <RadzenLabel Text=@("REGON".Tr()) />
                            <RadzenTextBox @bind-Value="_addEditCustomer.NationalOfficialBusinessRegister" class="w-full" />
                            <ValidationMessage For="@(() => _addEditCustomer.NationalOfficialBusinessRegister)" class="validation-message" />
                        </RadzenColumn>

                        <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                            <RadzenLabel Text=@("Skrócona nazwa".Tr().AddText('*')) />
                            <RadzenTextBox @bind-Value="_addEditCustomer.ShortName" class="w-full" />
                            <ValidationMessage For="@(() => _addEditCustomer.ShortName)" class="validation-message" />
                        </RadzenColumn>

                        <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                            <RadzenLabel Text=@("Pełna nazwa".Tr().AddText('*')) />
                            <RadzenTextBox @bind-Value="_addEditCustomer.Name" class="w-full" />
                            <ValidationMessage For="@(() => _addEditCustomer.Name)" class="validation-message" />
                        </RadzenColumn>

                        <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                            <RadzenLabel Text=@("Miasto".Tr()) Component="City" />
                            <RadzenTextBox @bind-Value="_addEditCustomer.City" Name="City" class="w-full" />
                            <ValidationMessage For="@(() => _addEditCustomer.City)" class="validation-message" />
                        </RadzenColumn>

                        <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                            <RadzenLabel Text=@("Kod pocztowy".Tr()) Component="PostCode" />
                            <RadzenTextBox @bind-Value="_addEditCustomer.PostCode" Name="PostCode" class="w-full" />
                            <ValidationMessage For="@(() => _addEditCustomer.PostCode)" class="validation-message" />
                        </RadzenColumn>

                        <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                            <RadzenLabel Text=@("Adres".Tr()) Component="Address" />
                            <RadzenTextBox @bind-Value="_addEditCustomer.Address" Name="Address" class="w-full" />
                            <ValidationMessage For="@(() => _addEditCustomer.Address)" class="validation-message" />
                        </RadzenColumn>

                        <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                            <RadzenLabel Text=@("Oddział".Tr().AddText('*')) />
                            <BranchesSelector @bind-Value="@_addEditCustomer.BranchId"
                                              For="@(() => _addEditCustomer.BranchId)"
                                              ShowActiveOnly
                                              IncludeSelectedIfInactive />
                            <ValidationMessage For="@(() => _addEditCustomer.BranchId)" class="validation-message" />
                        </RadzenColumn>

                        <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                            <RadzenLabel Text=@("Email".Tr()) Component="Email" />
                            <RadzenTextBox @bind-Value="_addEditCustomer.Email" Name="Email" class="w-full" />
                            <ValidationMessage For="@(() => _addEditCustomer.Email)" class="validation-message" />
                        </RadzenColumn>

                        <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                            <RadzenLabel Text=@("Telefon".Tr()) Component="Phone" />
                            <RadzenTextBox @bind-Value="_addEditCustomer.Phone" Name="Phone" class="w-full" />
                            <ValidationMessage For="@(() => _addEditCustomer.Phone)" class="validation-message" />
                        </RadzenColumn>

                        <RadzenColumn Size="12" SizeMD="12" SizeLG="6" class="flex items-center min-h-[90px] gap-4">
                            <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="0.5rem">
                                <RadzenSwitch @bind-Value="_addEditCustomer.FastProductionTrack" Name="FastProductionTrack" />
                                <RadzenLabel Text=@("Szybka ścieżka produkcji".Tr()) Component="FastProductionTrack" />
                                <ValidationMessage For="@(() => _addEditCustomer.FastProductionTrack)" class="validation-message" />
                            </RadzenStack>
                        </RadzenColumn>

                        <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                            <RadzenLabel Text=@("Zakład domyślny".Tr().AddText(_addEditCustomer.FastProductionTrack ? '*' : null)) />
                            <FactoriesSelector @bind-Value="@_addEditCustomer.FactoryId"
                                               For="@(() => _addEditCustomer.FactoryId)"
                                               ShowActiveOnly
                                               IncludeSelectedIfInactive
                                               Clearable />
                            <ValidationMessage For="@(() => _addEditCustomer.FactoryId)" class="validation-message" />
                        </RadzenColumn>
                    </RadzenRow>
                </RadzenTabsItem>
                <RadzenTabsItem Text="@("Warunki & Profil".Tr())">
                    <RadzenRow AlignItems="AlignItems.Start" RowGap="0.5rem" Gap="1rem">

                        <RadzenColumn Size="12" SizeMD="12" SizeLG="12">
                            <RadzenLabel Text=@("Warunki handlowe".Tr()) Component="BusinessTerms" />
                            <RadzenHtmlEditor @bind-Value=@_addEditCustomer.BusinessTerms style="height: 300px" />
                            <ValidationMessage For="@(() => _addEditCustomer.BusinessTerms)" class="validation-message" />
                        </RadzenColumn>

                        <RadzenColumn Size="12" SizeMD="12" SizeLG="12">
                            <RadzenLabel Text=@("Profil".Tr()) Component="ProfilInfo" />
                            <RadzenHtmlEditor @bind-Value=@_addEditCustomer.ProfilInfo style="height: 300px" />
                            <ValidationMessage For="@(() => _addEditCustomer.ProfilInfo)" class="validation-message" />
                        </RadzenColumn>
                    </RadzenRow>

                </RadzenTabsItem>
                @if (IsEditMode)
                {
                    <RadzenTabsItem Text="@("Lokalizacje".Tr())">
                        <CustomerLocalizationGrid CustomerId="@(CustomerId ?? 0)" />
                    </RadzenTabsItem>
                    <RadzenTabsItem Text="@("Handel - Opiekunowie".Tr())">
                        <AccountManagersGrid CustomerId="@(CustomerId ?? 0)" AccountManagerType="AccountManagerType.SalesAccountManager" />
                    </RadzenTabsItem>
                    <RadzenTabsItem Text="@("Produkcja - Opiekunowie".Tr())">
                        <AccountManagersGrid CustomerId="@(CustomerId ?? 0)" AccountManagerType="AccountManagerType.ProductionAccountManager" />
                    </RadzenTabsItem>
                }
            </Tabs>
        </RadzenTabs>

        <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.End" class="my-2 mx-4">
            <RadzenButton ButtonType="ButtonType.Submit" Text="@("Zapisz".Tr())" Icon="save" ButtonStyle="ButtonStyle.Success" />
            <RadzenButton Text="@("Anuluj".Tr())" Icon="cancel" ButtonStyle="ButtonStyle.Light" Click="@OnCancel" />
        </RadzenStack>
    </RadzenTemplateForm>
</IndexCard>

@code {
    [Parameter] public int? CustomerId { get; set; }

    private FluentValidationForRadzenComponent? _validatorComponent;
    private AddEditCustomerRequest _addEditCustomer = new()
        {
            Phone = string.Empty,
            Fax = string.Empty,
            Email = string.Empty
        };

    private bool IsEditMode => CustomerId is > 0;

    protected override async Task OnInitializedAsync()
    {
        if (IsEditMode)
        {
            var response = await Mediator.Send(new GetCustomerQuery(CustomerId!.Value));

            if (response.Succeeded)
            {
                _addEditCustomer = Mapper.Map<AddEditCustomerRequest>(response.Data);
            }
            else
            {
                ToastService.Show(ToastType.LoadDataError, string.Join(',', response.Messages));
            }
        }
    }

    private async Task OnSubmit()
    {
        try
        {
            var result = await Mediator.Send(new AddEditCustomerCommand(Mapper.Map<AddEditCustomerRequest>(_addEditCustomer)));

            if (result.Succeeded)
            {
                ToastService.Show(ToastType.SaveSuccess);
                Navigation.NavigateTo("/admin/customers/index");
            }
            else
            {
                ToastService.Show(ToastType.SaveError, string.Join(',', result.Messages));
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas zapisywania klienta");
            ToastService.Show(ToastType.SaveError);
        }
    }

    private void OnCancel()
    {
        Navigation.NavigateTo("/admin/customers/index");
    }

    private async Task SearchInGus()
    {
        if (string.IsNullOrEmpty(_addEditCustomer.TaxIdentificationNumber))
        {
            ToastService.ShowInfo("Proszę wpisać NIP".Tr());
            return;
        }

        await DialogService.OpenAsync<GusDataDialog>(
            "Wyszukiwarka GUS".Tr(),
            new Dictionary<string, object>() {
                { "TaxIdentificationNumber", _addEditCustomer.TaxIdentificationNumber },
                { "OnGusDataChanged", EventCallback.Factory.Create<GusDataDialog.GusData?>(this, OnGusDataChanged) } },
            new DialogOptions()
                {
                    Resizable = true,
                    Draggable = true,
                    Width = "520px",
                    Height = "auto"
                });
    }

    public void OnGusDataChanged(GusDataDialog.GusData? gusData)
    {
        if (gusData is null)
            return;

        _addEditCustomer.NationalOfficialBusinessRegister = gusData.NationalOfficialBusinessRegister;
        _addEditCustomer.Name = gusData.Name;
        _addEditCustomer.City = gusData.City;
        _addEditCustomer.PostCode = gusData.PostCode;
        _addEditCustomer.Address = gusData.Address;

        _validatorComponent?.CurrentEditContext.Validate();
    }

    private async Task SearchVat()
    {

        if (string.IsNullOrEmpty(_addEditCustomer.TaxIdentificationNumber))
        {
            ToastService.ShowInfo("Proszę wpisać NIP".Tr());
            return;
        }

        if (_addEditCustomer.CountryId == 0)
        {
            ToastService.ShowInfo("Proszę wybrać państwo".Tr());
            return;
        }

        await DialogService.OpenAsync<VatStatusDialog>(
           "Status VAT".Tr(),
           new Dictionary<string, object>() {
                { "TaxIdentificationNumber", _addEditCustomer.TaxIdentificationNumber },
                { "CountryId", _addEditCustomer.CountryId } },
           new DialogOptions()
               {
                   Resizable = true,
                   Draggable = true,
                   Width = "520px",
                   Height = "auto"
               });
    }
}
