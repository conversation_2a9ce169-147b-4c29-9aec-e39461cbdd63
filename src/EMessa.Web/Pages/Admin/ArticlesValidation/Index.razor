@page "/admin/articlesValidation/index"
@attribute [Authorize(Roles = Role.Administrator)]

@using EMessa.Base.Enums
@using EMessa.Core.Features.ArticleValidators.Commands.AddArticleValidatorCondition
@using EMessa.Core.Features.ArticleValidators.Commands.DeleteArticleValidatorCondition
@using EMessa.Core.Features.ArticleValidators.Commands.EditArticleValidatorCondition
@using EMessa.Core.Features.ArticleValidators.Queries.GetAllArticleValidatorCondition
@using EMessa.Core.Features.ArticleValidators.Queries.GetAllOptionCodes
@using EMessa.Core.Features.ArticleValidators.Queries.GetSelectedArticleValidator
@using EMessa.Core.Helpers
@using EMessa.Web.Constants
@using EMessa.Web.Helpers
@using EMessa.Web.Pages.Admin.ArticlesValidation.Parts
@using Ra<PERSON>zen
@using Radzen.Blazor

@inject ILogger<Index> Logger
@inject IJSRuntime JsRuntime
@inject IArticleValidatorService ArticleValidatorService
@inject IModalDialogService ModalDialogService
@implements IDisposable

<IndexCard Title=@("Walidacja produktów".Tr())>
    <ChildContent>
        <div class="flex flex-col w-full p-1 gap-2">
            <Card Title=@("Dostępne wartości zmiennych (opcji)".Tr()) TitleClass="p-1 text-base font-semibold"
                  ContentClass="flex flex-col gap-2">
                <div class="px-1">
                    <RefreshRadzenButton Click="@LoadAllOptionCodesAsync" />
                </div>
                <OptionsValidateVariables AllOptions="@_optionValidateVariables"
                                          OnSelectedVariable="@SelectedConstantVariableHandler" />
            </Card>

            <Card Title=@("Formuły".Tr())
                  TitleClass="px-2 py-1 text-base font-semibold"
                  ContentClass="f-col-1">
                <ChildContent>
                    <div class="f-col-2">
                        <GridToolbar
                            Items="@( [GridToolbar.Add, GridToolbar.Search])"
                            DisabledAdd="@(_editedCondition is not null)"
                            OnAddClick="@(() => AddNewCondition())"
                            SearchTextChanged="@HandleSearch" />
                        <ProgressBar IsLoading="@_isLoading" />
                        @if (_editedCondition != null)
                        {
                            <EditTemplate Condition="@_editedCondition"
                                                   OnSave="@(_ => SaveCondition(_editedCondition))"
                                                   OnCancel="@(_ => CancelEdit(_editedCondition))" />
                        }
                        <RadzenDataGrid @ref="_grid" TItem="GetAllArticleValidatorConditionResponse"
                                        Data="@_gridData" Count="@_totalCount"
                                        LoadData="@LoadData" IsLoading="@_isLoading"
                                        AllowFiltering FilterMode="FilterMode.Simple"
                                        FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                                        AllowPaging PageSize="@PageSize.DefaultPageSize"
                                        PageSizeOptions="@PageSize.Pages20To200"
                                        PageSizeText="@PageSize.PageSizeText"
                                        Visible="@(_editedCondition is null)"
                                        AllowSorting AllowMultiColumnSorting AllowColumnResize
                                        Density="Density.Compact"
                                        EditMode="DataGridEditMode.Single"
                                        RowClick="@SelectCondition"
                                        RowDoubleClick="@EditConditionDoubleClick"
                                        RowCreate="@CreateCondition"
                                        RowUpdate="@UpdateCondition"
                                        RowRender="@RowRenderCondition"
                                        ExpandMode="DataGridExpandMode.Single"
                                        EmptyText="@RadzenDataGridConstants.EmptyText">

                            <LoadingTemplate />

                            <Columns>
                                <GridActionsColumn TItem="GetAllArticleValidatorConditionResponse"
                                                   IsLargeScreen="@AppStateService.IsLargeScreen"
                                                   Width="@(AppStateService.IsLargeScreen ? "120px" : "30px")">
                                    <ContentTemplate Context="data">
                                        <CloneRadzenButton IsAction Click="@(_ => CloneCondition(data))" />
                                        <EditRadzenButton IsAction Click="@(_ => EditCondition(data))" />
                                        <DeleteRadzenButton IsAction Click="@(_ => DeleteCondition(data))" />
                                    </ContentTemplate>
                                </GridActionsColumn>

                                <RadzenDataGridColumn Visible="@(!AppStateService.IsLargeScreen)"
                                                      Sortable="false" Filterable="false">
                                    <Template Context="data">
                                        <ArticleValidatorCard ArticleValidator="data" />
                                    </Template>
                                </RadzenDataGridColumn>

                                <RadzenDataGridColumn Property="@nameof(GetAllArticleValidatorConditionResponse.Name)"
                                                      Title=@("Name".Tr())
                                                      Width="20%"
                                                      Visible="@AppStateService.IsLargeScreen"
                                                      Sortable Filterable
                                                      FilterOperators="@RadzenDataGridFilterOptions.TextFilters">
                                    <Template Context="data">
                                        <RadzenText Text="@data.Name" TextStyle="TextStyle.Body1"
                                                    TextOverflow="TextOverflow.Ellipsis" />
                                    </Template>
                                </RadzenDataGridColumn>

                                <RadzenDataGridColumn
                                    Property="@nameof(GetAllArticleValidatorConditionResponse.Description)"
                                    Title=@("Opis".Tr())
                                    Width="auto"
                                    Visible="@AppStateService.IsLargeScreen"
                                    Sortable Filterable
                                    FilterOperators="@RadzenDataGridFilterOptions.TextFilters">
                                    <Template Context="data">
                                        <RadzenText Text="@data.Description" TextStyle="TextStyle.Body1"
                                                    TextOverflow="TextOverflow.Ellipsis" />
                                    </Template>
                                </RadzenDataGridColumn>

                                <RadzenDataGridColumn
                                    Property="@nameof(GetAllArticleValidatorConditionResponse.IsActive)"
                                    Title=@("Aktywny".Tr())
                                    Width="100px"
                                    TextAlign="TextAlign.Center"
                                    Visible="@AppStateService.IsLargeScreen"
                                    Sortable Filterable>
                                    <Template Context="data">
                                        <RadzenRow JustifyContent="JustifyContent.Center">
                                            <RadzenCheckBox @bind-Value="data.IsActive" Disabled TValue="bool" />
                                        </RadzenRow>
                                    </Template>
                                </RadzenDataGridColumn>

                                <RadzenDataGridColumn
                                    Property="@nameof(GetAllArticleValidatorConditionResponse.ValidatorType)"
                                    Title=@("Rodzaj".Tr())
                                    Width="120px"
                                    TextAlign="TextAlign.Start"
                                    Visible="@AppStateService.IsLargeScreen"
                                    Sortable Filterable>
                                    <Template Context="data">
                                        <RadzenText Text="@data.ValidatorType.ToDisplayName()" />
                                    </Template>
                                    <FilterTemplate>
                                        <RadzenDropDown Value="@_selectedValidatorType"
                                                        Change="@OnValidatorTypeChanged"
                                                        Data="@(ArticleValidatorTypeExtensions.ToDropDownListItems())"
                                                        LoadData="@LoadData"
                                                        TextProperty="Text"
                                                        ValueProperty="Value"
                                                        Style="width:100%;"
                                                        AllowClear="true"
                                                        TValue="ArticleValidatorType?" />
                                    </FilterTemplate>
                                </RadzenDataGridColumn>
                            </Columns>
                        </RadzenDataGrid>
                    </div>
                </ChildContent>
            </Card>

            <Card Title=@("Zaznaczona formuła".Tr())
                  TitleClass="px-2 py-1 text-base font-semibold"
                  ContentClass="flex flex-col gap-2">
                <TitleContent>
                    @if (_selectedValidationCondition != null)
                    {
                        <RadzenBadge
                            BadgeStyle="BadgeStyle.Base"
                            Text="@_selectedValidationCondition.Name"
                            class="font-bold m-0.5 border-[1px] border-gray-500">
                        </RadzenBadge>
                    }
                </TitleContent>
                <ChildContent>
                    <ValidationOther ArticleValidators="@_articleValidators"
                                     SelectedValidationCondition="@_selectedValidationCondition"
                                     OnRefreshSelectedArticleValidators="@RefreshSelectedArticleValidators" />
                </ChildContent>
            </Card>
        </div>
    </ChildContent>
</IndexCard>

@code {

    #region Initialize, Dispoze

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await _grid.Reload();
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        await LoadAllOptionCodesAsync();
        LoadConstantsCodes();
        if (AppStateService != null)
        {
            AppStateService.StateChanged += OnStateChanged;
        }
    }

    void IDisposable.Dispose()
    {
        if (AppStateService != null)
        {
            AppStateService.StateChanged -= OnStateChanged;
        }
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }

    #endregion

    #region ValidateVariables

    private List<GetAllOptionCodesResponse> _optionValidateVariables = [];

    private async Task LoadAllOptionCodesAsync()
    {
        try
        {
            var response = await Mediator.Send(new GetAllOptionCodesQuery());
            if (response is { Succeeded: true, Data: var optionValidateVariables })
            {
                _optionValidateVariables = ValidateVariablesHelper.SanitizeResponses(optionValidateVariables);
            }
            else
            {
                ToastService.ShowError(response.Messages.String("<br />"));
            }
        }
        catch (Exception e)
        {
            Logger.LogError(e, e.Message);
            ToastService.Show(ToastType.LoadDataError);
        }
    }

    private async Task SelectedConstantVariableHandler(string arg)
    {
        await JsRuntime.InvokeVoidAsync("navigator.clipboard.writeText", arg);
    }

    #endregion

    #region ValidateConstans

    Dictionary<string, object?> _validateConstans = new();

    private void LoadConstantsCodes()
    {
        _validateConstans = ArticleValidatorService.GetConstants();
    }

    #endregion

    #region Grid Filters

    private String _searchString = "";

    private ArticleValidatorType? _selectedValidatorType;

    private void OnValidatorTypeChanged(object value)
    {
        _selectedValidatorType = (ArticleValidatorType?)value;
        _grid.Reload();
    }

    #endregion

    #region Grid ValidateConditions

    private RadzenDataGrid<GetAllArticleValidatorConditionResponse> _grid = new();
    private IEnumerable<GetAllArticleValidatorConditionResponse> _gridData = []; // Validator Conditions
    private GetAllArticleValidatorConditionResponse? _selectedValidationCondition;
    private GetAllArticleValidatorConditionResponse? _editedCondition;
    private int _totalCount;
    private bool _isLoading;
    private bool _isAddingNew;

    private async Task LoadData(LoadDataArgs args)
    {
        _isLoading = true;

        try
        {
            var sieveModel = args.ToSieveModel().ApplyGlobalSearchFilter(_searchString);
            if (_selectedValidatorType.HasValue)
            {
                sieveModel.Filters = $"{sieveModel.Filters},ValidatorType=={(int)_selectedValidatorType},";
            }

            var response = await Mediator.Send(new GetAllArticleValidatorConditionQuery(sieveModel));

            if (response.Succeeded)
            {
                _gridData = response.Data;
                _totalCount = response.TotalCount;
            }
            else
            {
                ToastService.Show(ToastType.LoadDataError);
                _gridData = [];
                _totalCount = 0;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas ładowania danych");
            ToastService.Show(ToastType.LoadDataError);
            _gridData = [];
            _totalCount = 0;
        }
        finally
        {
            _isLoading = false;
        }
    }

    void RowRenderCondition(RowRenderEventArgs<GetAllArticleValidatorConditionResponse> args)
    {
        if (args.Data is not { } condition)
            return;

        if (condition == _selectedValidationCondition)
        {
            HtmlAttributesHelper.AddClasses(args.Attributes, "bg-row-selected");
        }
    }

    private async Task SelectCondition(DataGridRowMouseEventArgs<GetAllArticleValidatorConditionResponse> args)
    {
        _selectedValidationCondition = args.Data;
        await LoadGetSelectedArticleValidatorAsync();
    }

    private void EditConditionDoubleClick(DataGridRowMouseEventArgs<GetAllArticleValidatorConditionResponse> args)
    {
        EditCondition(args.Data);
    }

    private void HandleSearch(string searchString)
    {
        _searchString = searchString;
        _grid.Reload();
    }

    private void AddNewCondition(GetAllArticleValidatorConditionResponse? clonedCondition = null)
    {
        GetAllArticleValidatorConditionResponse editedCondition;
        if (clonedCondition is not null)
        {
            editedCondition = clonedCondition;
            editedCondition.Id = 0;
            editedCondition.Name = $"{"(Kopia)".Tr()} {clonedCondition.Name}";
        }
        else
        {
            editedCondition = new GetAllArticleValidatorConditionResponse
            {
                Id = 0,
                Name = "",
                Description = "",
                IsActive = true,
                ValidatorType = ArticleValidatorType.Article // Domyślny typ
            };
        }

        // Dodaj tymczasowo do źródła danych
        var currentData = _gridData.ToList();
        currentData.Insert(0, editedCondition);
        _gridData = currentData;
        _totalCount++;

        // Przejdź do trybu edycji dla nowego wiersza
        _isAddingNew = true;
        EditCondition(editedCondition);
    }

    private void CloneCondition(GetAllArticleValidatorConditionResponse? selectedCondition = null)
    {
        if (selectedCondition is not null)
        {
            _selectedValidationCondition = selectedCondition;
        }

        if (_selectedValidationCondition is null)
            return;

        if (_isAddingNew && _editedCondition != null)
        {
            var currentData = _gridData.ToList();
            currentData.Remove(_editedCondition);
            _gridData = currentData;
            _totalCount--;
            _isAddingNew = false;
            _grid.CancelEditRow(_editedCondition);
            _editedCondition = null;
        }

        AddNewCondition(_selectedValidationCondition.CloneWithoutId());
    }

    private void EditCondition(GetAllArticleValidatorConditionResponse? selectedCondition = null)
    {
        if (selectedCondition is not null)
        {
            _selectedValidationCondition = selectedCondition;
        }

        _editedCondition = _selectedValidationCondition;

        if (_editedCondition is null)
            return;

        _grid.EditRow(_editedCondition);
        _grid.ExpandRow(_editedCondition);
    }

    private void CancelEdit(GetAllArticleValidatorConditionResponse condition)
    {
        if (_isAddingNew)
        {
            var currentData = _gridData.ToList();
            currentData.Remove(condition);
            _gridData = currentData;
            _totalCount--;
            _isAddingNew = false;
        }

        // _grid.CollapseRows([condition]);
        _grid.CollapseAll();
        _grid.CancelEditRow(condition);
        _editedCondition = null;
    }

    private async Task SaveCondition(GetAllArticleValidatorConditionResponse condition)
    {
        if (condition.Id == 0 && _isAddingNew)
        {
            // Dla nowych wierszy wywołujemy własną metodę dodającą rekord
            await CreateCondition(condition);
            _isAddingNew = false;
        }
        else
        {
            // To wywoła UpdateCondition (RowUpdate=UpdateCondition) i zapisze w DB
            await _grid.UpdateRow(condition);
        }

        _editedCondition = null;
    }

    private async Task UpdateCondition(GetAllArticleValidatorConditionResponse condition)
    {
        try
        {
            var response = await Mediator.Send(new EditArticleValidatorConditionCommand(condition));
            if (response.Succeeded)
            {
                await _grid.Reload();
                ToastService.ShowSuccess("Warunek został pomyślnie zaktualizowany.".Tr());

                return;
            }

            ToastService.ShowError(response.Messages.String("<br />"));
        }
        catch (Exception e)
        {
            Logger.LogError(e, e.Message);
            ToastService.ShowError("Błąd edycji danych.");
        }
    }

    private async Task CreateCondition(GetAllArticleValidatorConditionResponse newCondition)
    {
        try
        {
            var response = await Mediator.Send(new AddArticleValidatorConditionCommand(newCondition));
            if (response.Succeeded)
            {
                await _grid.Reload();
                ToastService.ShowSuccess("Warunek został pomyślnie dodany.");

                return;
            }

            ToastService.ShowError(response.Messages.String("<br />"));
        }
        catch (Exception e)
        {
            Logger.LogError(e, e.Message);
            ToastService.ShowError("Błąd podczas dodawania warunku.");
        }
    }

    private async Task DeleteCondition(GetAllArticleValidatorConditionResponse deleteCondition)
    {
        try
        {
            var confirmed = await ModalDialogService.DeleteConfirmationByName(deleteCondition.Name);

            if (confirmed)
            {
                _isLoading = true;

                var response = await Mediator.Send(new DeleteArticleValidatorConditionCommand(deleteCondition.Id));
                if (response.Succeeded)
                    ToastService.Show(ToastType.DeleteSuccess);
                else
                    ToastService.Show(ToastType.DeleteSuccess);

                await _grid.Reload();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas usuwania walidatora produktu ID: {FactoryId}", deleteCondition.Id);
            ToastService.Show(ToastType.DeleteError);
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    #endregion

    #region Selected validator

    List<GetSelectedArticleValidatorResponse> _articleValidators = [];

    private async Task LoadGetSelectedArticleValidatorAsync()
    {
        var articleValidatorConditionId = _selectedValidationCondition?.Id ?? 0;
        if (articleValidatorConditionId <= 0)
        {
            _articleValidators = [];
            return;
        }

        try
        {
            var response = await Mediator.Send(new GetSelectedArticleValidatorQuery(articleValidatorConditionId));
            if (response is { Succeeded: true, Data: var articleValidators })
            {
                _articleValidators = articleValidators;
            }
            else
            {
                ToastService.ShowError(response.Messages.String("<br />"));
            }
        }
        catch (Exception e)
        {
            Logger.LogError(e, e.Message);
            ToastService.Show(ToastType.LoadDataError);
        }
    }

    private async Task RefreshSelectedArticleValidators()
    {
        await LoadGetSelectedArticleValidatorAsync();
    }

    #endregion

}
