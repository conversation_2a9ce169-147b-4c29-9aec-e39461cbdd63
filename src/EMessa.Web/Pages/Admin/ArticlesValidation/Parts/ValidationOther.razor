@using EMessa.Core.Features.ArticleValidatorConditionTranslations.Commands.AddEdit
@using EMessa.Core.Features.ArticleValidatorConditionTranslations.Queries.GetArticleValidatorConditionTranslation
@using EMessa.Core.Features.ArticleValidators.Commands.AddArticleValidator
@using EMessa.Core.Features.ArticleValidators.Commands.DeleteArticleValidator
@using EMessa.Core.Features.ArticleValidators.Queries.GetAllArticleValidatorCondition
@using EMessa.Core.Features.ArticleValidators.Queries.GetSelectedArticleValidator
@using EMessa.Web.Constants
@using EMessa.Web.Helpers
@using Radzen
@using Radzen.Blazor

@inject ILogger<ValidationOther> Logger
@inject IMultiTranslationService MultiTranslationService
@implements IDisposable

<RadzenTabs>
    <Tabs>
        <RadzenTabsItem Text="@("Produkty".Tr())" Disabled="@(SelectedValidationCondition is null)">
            <div class="f-col-2 bg-gray-100">
                <div class="w-full flex flex-col gap-1 p-2">
                    @if (_showSelectArticles)
                    {
                        <SelectArticles ValidationConditionId="@SelectedValidationCondition!.Id"
                                        OnSave="@OnAddArticleToValidator"
                                        OnClose="@OnCloseSelectArticles" />
                    }
                    else
                    {
                        <RadzenText Text="@("Użyte w produktach".Tr())" TextStyle="TextStyle.Subtitle2" />
                        <GridToolbar
                            Items="@( [GridToolbar.Add])"
                            DisabledAdd="@(SelectedValidationCondition is null)"
                            OnAddClick="@(() => AddNewValidator(null))" />
                        <RadzenDataGrid @ref="_validatorsGrid" TItem="GetSelectedArticleValidatorResponse"
                                        Data="@ArticleValidators"
                                        AllowFiltering FilterMode="FilterMode.Simple"
                                        FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                                        AllowPaging
                                        PageSize="@PageSize.DefaultPageSize"
                                        PageSizeOptions="@PageSize.Pages20To200"
                                        PageSizeText="@PageSize.PageSizeText"
                                        AllowSorting
                                        Density="Density.Compact"
                                        RowClick="@SelectValidator"
                                        RowRender="@RowRenderValidator"
                                        EmptyText="@RadzenDataGridConstants.EmptyText">

                            <LoadingTemplate />

                            <Columns>
                                <GridActionsColumn TItem="GetSelectedArticleValidatorResponse"
                                                   IsLargeScreen="@AppStateService.IsLargeScreen"
                                                   Width="@(AppStateService.IsLargeScreen ? "100px" : "30px")">
                                    <ContentTemplate Context="data">
                                        <RadzenButton ButtonStyle="ButtonStyle.Danger" Icon="delete"
                                                      class="bg-esa-delete"
                                                      Size="ButtonSize.ExtraSmall"
                                                      Click="@(_ => DeleteValidator(data))" />
                                    </ContentTemplate>
                                </GridActionsColumn>

                                <RadzenDataGridColumn TItem="GetSelectedArticleValidatorResponse"
                                                      Title="@("Połączony produkt".Tr())"
                                                      Visible="@(!AppStateService.IsLargeScreen)">
                                    <Template Context="data">
                                        <div class="f-col-1">
                                            <span class="font-bold text-wrap">@data.ArticleCode</span>
                                            <span class="text-sm text-wrap">@data.ArticleName</span>
                                        </div>
                                    </Template>
                                </RadzenDataGridColumn>

                                <RadzenDataGridColumn TItem="GetSelectedArticleValidatorResponse"
                                                      Property="Id"
                                                      Visible="false" />

                                <RadzenDataGridColumn TItem="GetSelectedArticleValidatorResponse"
                                                      Property="ArticleCode"
                                                      Title="@("Kod".Tr())"
                                                      Width="150px"
                                                      Visible="@AppStateService.IsLargeScreen" />

                                <RadzenDataGridColumn TItem="GetSelectedArticleValidatorResponse"
                                                      Property="ArticleName"
                                                      Title="@("Nazwa".Tr())"
                                                      Visible="@AppStateService.IsLargeScreen" />
                            </Columns>
                        </RadzenDataGrid>
                    }
                </div>
            </div>
        </RadzenTabsItem>

        <RadzenTabsItem Text="@("Tłumaczenia".Tr())" Disabled="@(SelectedValidationCondition is null)">
            <div class="f-col-1 py-2 bg-gray-100">
                <div class="px-1">
                    <GridToolbar Items="@( [GridToolbar.Translate])"
                                 DisabledTranslate="@(_isTranslating || _isLoading)"
                                 OnTranslateClick="@TranslateHandler">
                        <ChildContent>
                            <SaveRadzenButton Text="@("Zapisz wszystko".Tr())"
                                              Disabled="@(_isSaving || _isLoading)"
                                              Click="@SaveTranslations" />
                        </ChildContent>
                    </GridToolbar>
                </div>
                <ProgressBar IsLoading="@_isLoading" />
                <RadzenTabs class="p-1">
                    <Tabs>
                        @foreach (var lang in SystemConstants.SupportedCulturesList)
                        {
                            var translation = _translations.FirstOrDefault(x => x.LanguageCode == lang);

                            <RadzenTabsItem Text="@lang">
                                @if (translation != null)
                                {
                                    <RadzenTemplateForm TItem="GetArticleValidatorConditionTranslationResponse"
                                                        Data="@translation">
                                        <RadzenStack Orientation="Orientation.Vertical" Gap="0.5rem" class="p-1">
                                            
                                            <FluentValidationForRadzenComponent
                                                Validator="@(new ArticleValidatorConditionTranslationValidator())"/>

                                            <RadzenStack Orientation="Orientation.Vertical" Gap="0.2rem">
                                                <RadzenLabel Text="@("Komunikat błędu".Tr())"/>
                                                <RadzenTextArea @bind-Value="@translation.ErrorMessage"
                                                                Placeholder="@("Tłumaczenie komunikatu błędu".Tr())"
                                                                Disabled="@(string.IsNullOrEmpty(SelectedValidationCondition?.ErrorMessage))"
                                                                class="w-full h-14"/>
                                                <ValidationMessage For="@(() => translation.ErrorMessage)"
                                                                   class="validation-message"/>
                                            </RadzenStack>
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="0.2rem">
                                                <RadzenLabel Text="@("Komunikat ostrzeżenia".Tr())"/>
                                                <RadzenTextArea @bind-Value="@translation.ConfirmMessage"
                                                                Placeholder="@("Tłumaczenie komunikat ostrzeżenia".Tr())"
                                                                Disabled="@(string.IsNullOrEmpty(SelectedValidationCondition?.ConfirmMessage))"
                                                                class="w-full h-14"/>
                                                <ValidationMessage For="@(() => translation.ConfirmMessage)"
                                                                   class="validation-message"/>
                                            </RadzenStack>
                                            <GridToolbar Items="@( [GridToolbar.Save, GridToolbar.Cancel])"
                                                         DisabledSave="@(_isSaving || _isLoading)"
                                                         DisabledCancel="@_isLoading"
                                                         OnSaveClick="@(() => SaveTranslation(lang))"
                                                         OnCancelClick="@(() => CancelTranslation())"/>
                                        </RadzenStack>
                                    </RadzenTemplateForm>
                                }
                            </RadzenTabsItem>
                        }
                    </Tabs>
                </RadzenTabs>
            </div>
        </RadzenTabsItem>
    </Tabs>
</RadzenTabs>

@code {

    #region Parameters

    [Parameter]
    public required List<GetSelectedArticleValidatorResponse> ArticleValidators { get; set; }

    [Parameter]
    public GetAllArticleValidatorConditionResponse? SelectedValidationCondition { get; set; }

    [Parameter]
    public EventCallback OnRefreshSelectedArticleValidators { get; set; }

    #endregion

    #region Initializations, Dispose

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
        _showSelectArticles = false;
        await LoadTranslations();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await _validatorsGrid.Reload();
        }

        await base.OnAfterRenderAsync(firstRender);
    }


    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        if (AppStateService != null)
        {
            AppStateService.StateChanged += OnStateChanged;
        }
    }

    void IDisposable.Dispose()
    {
        if (AppStateService != null)
        {
            AppStateService.StateChanged -= OnStateChanged;
        }
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }

    #endregion

    #region Validators

    private RadzenDataGrid<GetSelectedArticleValidatorResponse> _validatorsGrid = new();

    private GetSelectedArticleValidatorResponse? _selectedValidator;
    private bool _showSelectArticles;
    private bool CanOpenDialogAddArticleValidator => SelectedValidationCondition?.Id > 0;


    private void RowRenderValidator(RowRenderEventArgs<GetSelectedArticleValidatorResponse> args)
    {
        if (args.Data is not { } condition)
            return;

        if (condition == _selectedValidator)
        {
            HtmlAttributesHelper.AddClasses(args.Attributes, "bg-row-selected");
        }
    }

    private void SelectValidator(DataGridRowMouseEventArgs<GetSelectedArticleValidatorResponse> args)
    {
        _selectedValidator = args.Data;
    }

    private void AddNewValidator(GetSelectedArticleValidatorResponse? validator)
    {
        if (CanOpenDialogAddArticleValidator)
        {
            ShowSelectArticles();
        }
    }

    private void ShowSelectArticles()
    {
        _showSelectArticles = SelectedValidationCondition is not null;
    }

    private async Task DeleteValidator(GetSelectedArticleValidatorResponse? validator)
    {
        //todo ten try-catch jest zbyteczny
        try
        {
            var deleteValidator = validator ?? _selectedValidator;

            var confirmed = await ModalDialogService.DeleteConfirmationByName(deleteValidator!.ArticleName);
            if (confirmed)
            {
                var response = await Mediator.Send(new DeleteArticleValidatorCommand(deleteValidator!.Id));
                if (response.Succeeded)
                {
                    ToastService.Show(ToastType.DeleteSuccess);
                    await OnRefreshSelectedArticleValidators.InvokeAsync();
                }
                else
                {
                    ToastService.Show(ToastType.DeleteError, response.Messages);
                }

                await _validatorsGrid.Reload();
            }
        }
        catch (Exception e)
        {
            Logger.LogError(e, e.Message);
            ToastService.Show(ToastType.DeleteError);
        }
    }

    private async Task OnAddArticleToValidator(List<int> selectedArticleIds)
    {
        if (SelectedValidationCondition?.Id is > 0 && selectedArticleIds.Count > 0)
        {
            try
            {
                var response = await Mediator.Send(new AddArticleValidatorArticlesCommand(SelectedValidationCondition.Id, selectedArticleIds));
                if (response is { Succeeded: true })
                {
                    await OnRefreshSelectedArticleValidators.InvokeAsync();
                }
                else
                {
                    ToastService.Show(ToastType.SaveError, response.Messages);
                }
            }
            catch (Exception e)
            {
                Logger.LogError(e, e.Message);
                ToastService.Show(ToastType.SaveError);
            }
        }
    }

    private void OnCloseSelectArticles()
    {
        _showSelectArticles = false;
    }

    #endregion

    #region Translations

    private List<GetArticleValidatorConditionTranslationResponse> _translations = [];
    private bool _isLoading;
    private bool _isTranslating;
    private bool _isSaving;

    private async Task LoadTranslations()
    {
        if (SelectedValidationCondition?.Id is > 0)
        {
            _isLoading = true;

            try
            {
                var response = await Mediator.Send(new GetArticleValidatorConditionTranslationQuery(SelectedValidationCondition.Id));
                if (response.Succeeded)
                {
                    _translations = response.Data;
                }
                else
                {
                    _translations = [];
                    ToastService.Show(ToastType.LoadDataError);
                }
            }
            catch (Exception e)
            {
                Logger.LogError(e, e.Message);
                ToastService.Show(ToastType.LoadDataError);
            }

            InitializeMissingTranslations();

            _isLoading = false;
        }
    }

    private void InitializeMissingTranslations()
    {
        foreach (var lang in SystemConstants.SupportedCulturesList)
        {
            if (_translations.Any(x => x.LanguageCode == lang))
                continue;

            _translations.Add(new GetArticleValidatorConditionTranslationResponse
            {
                LanguageCode = lang,
                ArticleValidatorConditionId = SelectedValidationCondition?.Id ?? 0,
                ErrorMessage = lang == "pl" ? SelectedValidationCondition?.ErrorMessage ?? "" : "",
                ConfirmMessage = lang == "pl" ? SelectedValidationCondition?.ConfirmMessage ?? "" : "",
            });
        }
    }

    private async Task TranslateHandler()
    {
        if (SelectedValidationCondition?.Id is > 0)
        {
            _isTranslating = true;
            _isLoading = true;
            var success = true;

            try
            {
                var errorMessageTask = MultiTranslationService
                    .TranslateWithHtml(SelectedValidationCondition.ErrorMessage, SystemConstants.SupportedCulturesList, true);
                var confirmMessageTask = MultiTranslationService
                    .TranslateWithHtml(SelectedValidationCondition.ConfirmMessage, SystemConstants.SupportedCulturesList, true);

                await Task.WhenAll(errorMessageTask, confirmMessageTask);

                var errorMessageResult = await errorMessageTask;
                var confirmMessageResult = await confirmMessageTask;

                success &= errorMessageResult.Succeeded;
                if (errorMessageResult.Succeeded)
                {
                    foreach (var languageCode in SystemConstants.SupportedCulturesList)
                    {
                        var errorTranslation = errorMessageResult.Data.FirstOrDefault(x => x.Key == languageCode);

                        var translation = _translations.FirstOrDefault(x => x.LanguageCode == languageCode);
                        if (translation == null) continue;

                        translation.ErrorMessage = errorTranslation.Value ?? translation.ErrorMessage;
                    }
                }
                else
                {
                    ToastService.Show(ToastType.TranslateError, errorMessageResult.Messages);
                }

                success &= confirmMessageResult.Succeeded;

                if (confirmMessageResult.Succeeded)
                {
                    foreach (var languageCode in SystemConstants.SupportedCulturesList)
                    {
                        var confirmTranslation = confirmMessageResult.Data.FirstOrDefault(x => x.Key == languageCode);

                        var translation = _translations.FirstOrDefault(x => x.LanguageCode == languageCode);
                        if (translation == null) continue;

                        translation.ConfirmMessage = confirmTranslation.Value ?? translation.ConfirmMessage;
                    }
                }
                else
                {
                    ToastService.Show(ToastType.TranslateError, confirmMessageResult.Messages);
                }

                if (success)
                {
                    ToastService.Show(ToastType.TranslateSuccess);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, ex.Message);
                ToastService.Show(ToastType.TranslateError);
            }
            finally
            {
                _isTranslating = false;
                _isLoading = false;
            }
        }
    }

    private async Task SaveTranslations()
    {
        if (SelectedValidationCondition?.Id is > 0)
        {
            _isSaving = true;
            _isLoading = true;

            try
            {
                var response = await Mediator.Send(new AddEditArticleValidatorConditionTranslationCommand(SelectedValidationCondition.Id, _translations));
                if (response.Succeeded)
                {
                    ToastService.Show(ToastType.SaveSuccess);
                }
                else
                {
                    ToastService.Show(ToastType.SaveError, response.Messages);
                }
            }
            catch (Exception e)
            {
                Logger.LogError(e, e.Message);
                ToastService.Show(ToastType.SaveError);
            }
            finally
            {
                _isSaving = false;
                _isLoading = false;
            }
        }
    }

    private async Task SaveTranslation(string lang)
    {
        if (SelectedValidationCondition?.Id is > 0)
        {
            _isSaving = true;
            _isLoading = true;

            try
            {
                var translation = _translations.FirstOrDefault(x => x.LanguageCode == lang);
                if (translation == null) return;

                var response = await Mediator.Send(new AddEditArticleValidatorConditionTranslationCommand(SelectedValidationCondition.Id, [translation]));
                if (response.Succeeded)
                {
                    ToastService.Show(ToastType.SaveSuccess);
                }
                else
                {
                    ToastService.Show(ToastType.SaveError, response.Messages);
                }
            }
            catch (Exception e)
            {
                Logger.LogError(e, e.Message);
                ToastService.Show(ToastType.SaveError);
            }
            finally
            {
                _isSaving = false;
                _isLoading = false;
            }
        }
    }

    private async Task CancelTranslation()
    {
        await LoadTranslations();
    }

    #endregion

}

