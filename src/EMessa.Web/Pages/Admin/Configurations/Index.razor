@page "/admin/configurations/index"
@attribute [Authorize(Roles = Role.Administrator)]

@using EMessa.Base.Enums
@using EMessa.Core.Features.Configurations.Commands.AddEdit
@using EMessa.Core.Features.Configurations.Commands.Delete
@using EMessa.Core.Features.Configurations.Commands.Reload
@using EMessa.Core.Features.Configurations.Commands.SetCustomerAppOwner
@using EMessa.Core.Features.Configurations.Queries.GetAll
@using EMessa.Core.Features.Customers.Queries.GetActive
@using EMessa.Web.Components.Grids
@using EMessa.Web.Constants
@using EMessa.Web.Helpers
@using EMessa.Web.Pages.Admin.Configurations.Parts
@using <PERSON><PERSON><PERSON>
@using Ra<PERSON>zen.Blazor
@using static EMessa.Web.Pages.Admin.Configurations.Parts.EditTemplate

@inject ILogger<Index> Logger
@inject NavigationManager Navigation
@inject IMapper Mapper
@implements IDisposable

<IndexCard Title=@("Konfiguracja".Tr())>
    <ChildContent>
        <div class="w-full p-1 flex flex-col gap-2">
            <GridToolbar Items="@( ["Add", "Search"])"
                         ItemsDisabled="@(new Dictionary<string, bool>
                                       {
                                           { "Add", _editedConfiguration is not null },
                                       })"
                         OnAction="@HandleAction"
                         SearchTextChanged="@HandleSearch" />

            <ProgressBar IsLoading="@_isLoading" />

            @if (_editedConfiguration is not null)
            {
                <EditTemplate Configuration="@_editedConfiguration"
                              OnSave="@SaveConfiguration"
                              OnCancel="@CancelEdit"
                              ConfigurationCodeItems="@GetConfigurationCodesSelectItems" />
            }

            <RadzenDataGrid @ref="_grid" TItem="GetAllConfigurationsResponse"
                            Data="@_gridData" Count="@_count" LoadData="@LoadData" IsLoading="@_isLoading"
                            AllowFiltering FilterMode="FilterMode.Simple"
                            FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                            AllowPaging PageSize="@PageSize.DefaultPageSize" PageSizeOptions="@PageSize.Pages20To200" PageSizeText="@PageSize.FormatPaginationInfo(_grid)"
                            Visible="@(_editedConfiguration is null)"
                            AllowSorting AllowMultiColumnSorting
                            AllowColumnResize
                            Density="Density.Compact"
                            EmptyText="@RadzenDataGridConstants.EmptyText"
                            RowClick="@SelectConfiguration"
                            RowDoubleClick="@(args => StartEditConfiguration(args.Data))"
                            RowRender="@RowRenderConfiguration">

                <LoadingTemplate />

                <Columns>
                    <GridActionsColumn TItem="GetAllConfigurationsResponse"
                                       IsLargeScreen="@AppStateService.IsLargeScreen"
                                       Width="@(AppStateService.IsLargeScreen ? "90px" : "30px")">
                        <ContentTemplate Context="data">
                            <EditRadzenButton IsAction Click="@(_ => StartEditConfiguration(data))" Disabled="@(_editedConfiguration is not null)" />
                            <DeleteRadzenButton IsAction Click="@(_ => DeleteConfiguration(data))" Disabled="@(_editedConfiguration is not null)" />
                        </ContentTemplate>
                    </GridActionsColumn>

                    <RadzenDataGridColumn Property="@nameof(GetAllConfigurationsResponse.Code)"
                                          Title=@("Kod".Tr())
                                          Sortable Filterable
                                          Visible="@(AppStateService.IsLargeScreen)" />

                    <RadzenDataGridColumn Property="@nameof(GetAllConfigurationsResponse.Name)"
                                          Title=@("Nazwa".Tr())
                                          Sortable Filterable
                                          Visible="@(AppStateService.IsLargeScreen)" />

                    <RadzenDataGridColumn Property="@nameof(GetAllConfigurationsResponse.Type)"
                                          Title=@("Typ".Tr())
                                          Sortable Filterable
                                          Visible="@(AppStateService.IsLargeScreen)" />

                    <RadzenDataGridColumn Property="@nameof(GetAllConfigurationsResponse.Value)"
                                          Title=@("Wartość".Tr())
                                          Sortable Filterable
                                          Visible="@(AppStateService.IsLargeScreen)" />

                    <RadzenDataGridColumn Visible="@(!AppStateService.IsLargeScreen)" Filterable="false" Sortable="false">
                        <Template Context="data">
                            <ConfigurationGridCard Configuration="@data" />
                        </Template>
                    </RadzenDataGridColumn>
                </Columns>
            </RadzenDataGrid>

            <RadzenCard class="mt-4">
                <ChildContent>
                    <RadzenStack Gap="1rem" class="p-1">
                        <RadzenText TextStyle="TextStyle.H6" TagName="TagName.H3" class="font-semibold">
                            @("Zarządzanie właścicielem aplikacji".Tr())
                        </RadzenText>
                        <RadzenRow AlignItems="AlignItems.End" Gap="1rem">
                            <RadzenColumn Size="12" SizeMD="8">
                                <RadzenLabel Text=@("Wybierz nowego właściciela".Tr()) Component="customerDropDown" class="font-medium" />
                                <RadzenDropDown TValue="GetActiveCustomersResponse"
                                                Data="@_customers"
                                                @bind-Value="@_selectedCustomer"
                                                AllowFiltering
                                                Placeholder="@("Wybierz klienta".Tr())"
                                                TextProperty="@nameof(GetActiveCustomersResponse.Name)"
                                                class="w-full"
                                                Name="customerDropDown" />
                            </RadzenColumn>
                            <RadzenColumn Size="12" SizeMD="4">
                                <SaveRadzenButton Text="@("Zmień właściciela".Tr())" Click="@AppOwnerChangedHandler" />
                            </RadzenColumn>
                        </RadzenRow>
                    </RadzenStack>
                </ChildContent>
            </RadzenCard>
        </div>
    </ChildContent>
</IndexCard>

@code {
    private RadzenDataGrid<GetAllConfigurationsResponse> _grid = new();
    private IEnumerable<GetAllConfigurationsResponse> _gridData = [];
    private GetAllConfigurationsResponse? _selectedConfiguration;
    private AddEditConfigurationCommand? _editedConfiguration; // Used for the external EditTemplate
    private List<GetActiveCustomersResponse> _customers = [];
    private GetActiveCustomersResponse? _selectedCustomer;

    private string? _searchString;
    private int _count;
    private bool _isLoading;

    private List<ConfigurationCodesSelectItem> GetConfigurationCodesSelectItems =>
        Enum.GetNames(typeof(ConfigurationCodes))
            .Select(x => new ConfigurationCodesSelectItem(x, _gridData.Any(y => y.Code == x && (_editedConfiguration == null || y.Id != _editedConfiguration.Id))))
            .OrderBy(x => x.CannotBeSelected)
            .ThenBy(x => x.Code)
            .ToList();

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await _grid.Reload();

            var customersResult = await Mediator.Send(new GetActiveCustomersQuery(AppStateService.UserData.UserProfileId));
            if (customersResult.Succeeded)
            {
                _customers = customersResult.Data;
                _selectedCustomer = _customers.FirstOrDefault(x => x.IsAppOwner);
                await InvokeAsync(StateHasChanged);
            }
            else
            {
                ToastService.Show(ToastType.LoadDataError);
            }
        }
        await base.OnAfterRenderAsync(firstRender);
    }

    private async Task LoadData(LoadDataArgs args)
    {
        _isLoading = true;
        try
        {
            var sieveModel = args.ToSieveModel().ApplyGlobalSearchFilter(_searchString);
            var response = await Mediator.Send(new GetAllConfigurationsQuery(sieveModel));

            if (response.Succeeded)
            {
                _gridData = response.Data;
                _count = response.TotalCount;
            }
            else
            {
                ToastService.Show(ToastType.LoadDataError, response.Messages.String());
                _gridData = [];
                _count = 0;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas ładowania danych konfiguracji.");
            ToastService.Show(ToastType.LoadDataError);
            _gridData = [];
            _count = 0;
        }
        finally
        {
            _isLoading = false;
            await InvokeAsync(StateHasChanged);
        }
    }

    void RowRenderConfiguration(RowRenderEventArgs<GetAllConfigurationsResponse> args)
    {
        if (args.Data is not { } config) return;
        if (config == _selectedConfiguration)
        {
            HtmlAttributesHelper.AddClasses(args.Attributes, "bg-row-selected");
        }
    }

    private void SelectConfiguration(DataGridRowMouseEventArgs<GetAllConfigurationsResponse> args)
    {
        _selectedConfiguration = args.Data;
    }

    private void HandleAction(string item)
    {
        switch (item)
        {
            case GridToolbar.Add:
                AddNewConfiguration();
                break;
        }
    }

    private void HandleSearch(string searchString)
    {
        _searchString = searchString;
        _grid.Reload(); // Debounce this if performance is an issue
    }

    private void AddNewConfiguration()
    {
        _editedConfiguration = new AddEditConfigurationCommand
            {
                Id = 0, // Important for new item
                Type = "Text", // Default type
                Value = string.Empty,
                Name = string.Empty,
                Code = string.Empty // Will be selected from DropDown
            };
        _selectedConfiguration = null; // Clear selection when adding new
        StateHasChanged();
    }

    private void StartEditConfiguration(GetAllConfigurationsResponse configuration)
    {
        if (_editedConfiguration is not null) return; // Already editing

        _selectedConfiguration = configuration; // Ensure it's selected
        _editedConfiguration = Mapper.Map<AddEditConfigurationCommand>(configuration);
        StateHasChanged();
    }

    private void CancelEdit(AddEditConfigurationCommand configuration)
    {
        _editedConfiguration = null;
        StateHasChanged();
    }

    private async Task SaveConfiguration(AddEditConfigurationCommand configurationToSave)
    {
        var validator = new AddEditConfigurationValidator();
        var result = await validator.ValidateAsync(configurationToSave);

        if (!result.IsValid)
        {
            ToastService.ShowError(string.Join("<br />", result.Errors.Select(e => e.ErrorMessage)));
            return;
        }

        _isLoading = true;
        StateHasChanged();

        try
        {
            var response = await Mediator.Send(configurationToSave);

            if (response.Succeeded)
            {
                ToastService.Show(ToastType.SaveSuccess);
                await ReloadConfigurationAsync();
                _editedConfiguration = null;
                await _grid.Reload();
            }
            else
            {
                ToastService.Show(ToastType.SaveError, response.Messages.String());
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas zapisywania konfiguracji.");
            ToastService.Show(ToastType.SaveError);
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private async Task DeleteConfiguration(GetAllConfigurationsResponse configuration)
    {
        if (_editedConfiguration is not null) return; // Don't allow delete while editing

        try
        {
            var confirmed = await ModalDialogService.DeleteConfirmationByName(configuration.Name);

            if (confirmed == true)
            {
                _isLoading = true;
                StateHasChanged();

                var response = await Mediator.Send(new DeleteConfigurationCommand(configuration.Id));

                if (response.Succeeded)
                {
                    ToastService.Show(ToastType.DeleteSuccess);
                    await ReloadConfigurationAsync();
                    if (_selectedConfiguration?.Id == configuration.Id)
                    {
                        _selectedConfiguration = null;
                    }
                }
                else
                {
                    ToastService.Show(ToastType.DeleteError, response.Messages.String());
                }
                await _grid.Reload();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, $"Błąd podczas usuwania konfiguracji ID: {configuration.Id}");
            ToastService.Show(ToastType.DeleteError);
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private async Task ReloadConfigurationAsync()
    {
        var response = await Mediator.Send(new ReloadConfigurationsCommand());
        if (!response.Succeeded)
        {
            Logger.LogWarning($"Nie udało się przeładować konfiguracji: {string.Join(", ", response.Messages)}");
            ToastService.ShowWarning("Nie udało się przeładować konfiguracji.");
        }
    }

    private async Task AppOwnerChangedHandler()
    {
        if (_selectedCustomer is null)
        {
            ToastService.ShowError("Nie wybrano właściciela aplikacji".Tr());
            return;
        }

        try
        {
            var confirmed = await ModalDialogService.Confirmation(
                dialogMessage: "Czy na pewno chcesz zmienić właściciela aplikacji na '{0}'?".Tr(_selectedCustomer.Name),
                dialogTitle: "Zmiana właściciela aplikacji".Tr());

            var setAppOwnerResult = await Mediator.Send(new SetCustomerAppOwnerCommand(_selectedCustomer.Id));
            if (setAppOwnerResult.Succeeded)
            {
                ToastService.ShowSuccess("Zmieniono właściciela aplikacji".Tr());
                await ReloadConfigurationAsync();
                await _grid.Reload();
            }
            else
            {
                ToastService.ShowError(string.Join(", ", setAppOwnerResult.Messages));
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas zmiany właściciela aplikacji");
            ToastService.ShowError("Wystąpił błąd podczas zmiany właściciela aplikacji".Tr());
        }
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        if (AppStateService != null)
        {
            AppStateService.StateChanged += OnStateChanged;
        }
    }

    void IDisposable.Dispose()
    {
        if (AppStateService != null)
        {
            AppStateService.StateChanged -= OnStateChanged;
        }
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }
}