@using EMessa.Core.Expections
@using EMessa.Core.Features.FilterAttributes.Queries.GetAll
@using EMessa.Core.DataAdapters
@using EMessa.Core.Exceptions
@using EMessa.Core.Features.FilterAttributesValue.Queries.GetAllValuesForAttribute
@using EMessa.Core.Features.FilterAttributesValue.Queries.GetAllValuesForAttriute
@using Syncfusion.Blazor.Data
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Inputs;
@using EMessa.Web.Components.Grids;

@inject IMediator _mediator
@inject IMapper _mapper
@inject ILogger<ValuesToAttributeComponent> _logger
@implements IDisposable


<SfGrid @ref="_flterAttributeVaulesGrid" TValue="GetAllFilterAttributeValuesResponse" Query="@FilterAttributeVaulesGridQuery" AdaptiveUIMode="@(AppStateService.GridState.AdaptiveMode)"
        EnableAdaptiveUI="@(AppStateService.GridState.EnableAdaptiveUI)"
        RowRenderingMode="@(AppStateService.GridState.RowDirection)"
        AllowFiltering="@(AppStateService.AppScreenSize == AppScreenSize.Large)"
        AllowPaging="true" AllowSorting="true" AllowSelection="true" Toolbar="@SfGridToolbar.FullToolbar">
    <GridPageSettings PageSize="20" PageSizes="@(new string[] { "20", "50", "100", "200" })"></GridPageSettings>
    <GridFilterSettings Mode="FilterBarMode.Immediate" ShowFilterBarStatus="true" ImmediateModeDelay="500"></GridFilterSettings>
    <GridEvents OnActionBegin="OnActionHandler" OnActionFailure="ActionFailureHandler" RowSelected="FilterAttributeValueRowSelected" TValue="GetAllFilterAttributeValuesResponse"></GridEvents>
    <SfDataManager AdaptorInstance="@typeof(FilterAttributeValueDataAdaptor)" Adaptor="Adaptors.CustomAdaptor"></SfDataManager>
    <GridSelectionSettings Mode="@Syncfusion.Blazor.Grids.SelectionMode.Row" Type="Syncfusion.Blazor.Grids.SelectionType.Single"></GridSelectionSettings>
    <GridEditSettings ShowDeleteConfirmDialog="true" AllowEditing="true" AllowAdding="true" AllowDeleting="true" Mode="EditMode.Normal">
        <Validator>
            <EMessa.Web.Components.FluentValidator TValidator="GetAllFilterAttributeValuesResponseValidator"/>
        </Validator>
        <Template>
            @{
                GetAllFilterAttributeValuesResponse filterAttributeValue = (GetAllFilterAttributeValuesResponse)context;
                <div class="py-4 px-2 bg-green-50 min-h-full flex flex-wrap">
                    
                    <div class="px-2 w-full md:w-2/12">
                        <label class="">@("Kod".Tr())</label>
                        <SfTextBox ID="FilterAttributeValueCode" @bind-Value="@(filterAttributeValue.Code)"></SfTextBox>
                        <ValidationMessage For="@(() => filterAttributeValue.Code)" />
                    </div>
                    <div class="px-2 w-full md:w-6/12">
                        <label class="">@("Wartość".Tr())</label>
                        <SfTextBox ID="FilterAttributeValue" @bind-Value="@(filterAttributeValue.Value)"></SfTextBox>
                        <ValidationMessage For="@(() => filterAttributeValue.Value)" />
                    </div>
                    <div class="px-2 w-full md:w-6/12">
                        <label class="">@("Wartość wyświetlana".Tr())</label>
                        <SfTextBox ID="FilterAttributeDisplayValue" @bind-Value="@(filterAttributeValue.DisplayValue)"></SfTextBox>
                        <ValidationMessage For="@(() => filterAttributeValue.DisplayValue)" />
                    </div>
                    <div class="pt-2 w-full">
                        <EMessa.Web.Components.Grids.GridSaveActions TItem="GetAllFilterAttributeValuesResponse" DefaultGrid="@_flterAttributeVaulesGrid"></EMessa.Web.Components.Grids.GridSaveActions>
                    </div>
                </div>
            }
        </Template>
    </GridEditSettings>
    <GridColumns>
        <GridColumn Field=@nameof(GetAllFilterAttributesResponse.Id) Visible="false" IsPrimaryKey="true"></GridColumn>
        <GridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllFilterAttributeValuesResponse.Code) HeaderText="@("Kod".Tr())" FilterSettings="@GridFilters.BarContains"></GridColumn>
        <GridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllFilterAttributeValuesResponse.Value) HeaderText="@("Wartość".Tr())" FilterSettings="@GridFilters.BarContains"></GridColumn>
        <GridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllFilterAttributeValuesResponse.DisplayValue) HeaderText="@("Wartość wyświetlana".Tr())" FilterSettings="@GridFilters.BarContains"></GridColumn>
        <GridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Width="60" AllowFiltering="false" TextAlign="TextAlign.Center">
            <GridColumn HideAtMedia="@($"(max-width: {(int)AppScreenSize.Medium}px)")" HeaderText="@("Wartości atrybutów".Tr())">
                    <Template>
                        @{
                        var FilterAttributeValue = context as GetAllFilterAttributeValuesResponse;
                        <EMessa.Web.Pages.Admin.FilterAttributes.Parts.ValuesToAttributeCard FilterAttributeValue="@FilterAttributeValue"></EMessa.Web.Pages.Admin.FilterAttributes.Parts.ValuesToAttributeCard>
                        }
                    </Template>
                </GridColumn>
        </GridColumn>
    </GridColumns>
</SfGrid>
<div class="my-2">
    <h1 class="font-bold">@("Tłumaczenia wartości atrybutów".Tr())</h1>
    <FilterAttributeValueTranslationsComponent FilterAttributeValue="@SelectedFilterAttributeValue" ></FilterAttributeValueTranslationsComponent>
</div>
@code {
    [Parameter]
    public GetAllFilterAttributesResponse FilterAttribute { get; set; } = new();
    [CascadingParameter]
    public Components.AppToast _toast { get; set; } = new();
    public Query FilterAttributeVaulesGridQuery { get; set; } = new();
    private SfGrid<GetAllFilterAttributeValuesResponse> _flterAttributeVaulesGrid = new();

    private GetAllFilterAttributeValuesResponse? SelectedFilterAttributeValue;


    protected override async Task OnInitializedAsync()
    {
        AppStateService.StateChanged += OnStateChanged;
        await base.OnInitializedAsync();
    }
    private void OnActionHandler(ActionEventArgs<GetAllFilterAttributeValuesResponse> args)
    {
        var action = args.Action;
        if(action =="Add")
        {
            args.Data.FilterAttributeId = FilterAttribute.Id;
        }
    }

    protected override void OnParametersSet()
    {

        if (FilterAttribute != null)
        {
            FilterAttributeVaulesGridQuery.AddParams("attributeIdParameter", FilterAttribute.Id);
            _flterAttributeVaulesGrid.Refresh();
        }
        base.OnParametersSet();
    }

    private async void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            await InvokeAsync(StateHasChanged);
        }
    }

    private async void ActionFailureHandler(Syncfusion.Blazor.Grids.FailureEventArgs e)
    {
        var listExceptions = e.Error as ListExceptions;
        if (listExceptions != null)
        {
            await _toast!.ShowError(listExceptions.Messages.String());
            return;
        }
        else
        {
            await _toast!.ShowError("Nieokreślony błąd operacji".Tr());
        }
    }
    void IDisposable.Dispose()
    {
        AppStateService.StateChanged -= OnStateChanged;
    }

    private void FilterAttributeValueRowSelected(RowSelectEventArgs<GetAllFilterAttributeValuesResponse> args)
    {
        if (args.Data == null) return;
        SelectedFilterAttributeValue = args.Data;
    }
}