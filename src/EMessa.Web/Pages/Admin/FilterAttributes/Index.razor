@page "/admin/filterAttributes/index"
@attribute [Authorize(Roles = Role.Administrator)]
@using Syncfusion.Blazor.Data
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Buttons
@using EMessa.Core.Features.FilterAttributes.Queries.GetAll
@using EMessa.Core.DataAdapters
@using Syncfusion.Blazor.Inputs
@using EMessa.Web.Components.Grids
@using Syncfusion.Blazor.Popups
@using EMessa.Web.Pages.Admin.FilterAttributes.Parts

@inject ILogger<Index> _logger
@inject NavigationManager NavigationManager
@inject IMediator _mediator
@implements IDisposable

<IndexCard Title=@("Atrybuty filtrowania".Tr())>
    <ChildContent>
        <div class="flex-1">
            <SfGrid @ref="_defaultGrid" TValue="GetAllFilterAttributesResponse"
                    AdaptiveUIMode="@(AppStateService.GridState.AdaptiveMode)"
                    EnableAdaptiveUI="@(AppStateService.GridState.EnableAdaptiveUI)"
                    RowRenderingMode="@(AppStateService.GridState.RowDirection)"
                    AllowFiltering="@(AppStateService.AppScreenSize == AppScreenSize.Large)"
                    AllowPaging="true" AllowSorting="true" AllowSelection="true"
                    Toolbar="@SfGridToolbar.FullToolbar">
                <GridFilterSettings Mode="FilterBarMode.Immediate" ShowFilterBarStatus="true" ImmediateModeDelay="500"/>
                <SfDataManager AdaptorInstance="@typeof(FilterAttributeDataAdaptor)" Adaptor="Adaptors.CustomAdaptor"/>
                <GridPageSettings PageSize="20" PageSizes="@(new string[] { "20", "50", "100", "200" })"/>
                <GridEvents OnActionFailure="ActionFailureHandler" CommandClicked="OnCommandClicked" RowSelected="OnRowSelected" TValue="GetAllFilterAttributesResponse"/>
                <GridEditSettings ShowDeleteConfirmDialog="true" AllowEditing="true" AllowAdding="true" AllowDeleting="true" Mode="EditMode.Normal">
                    <Validator>
                        <EMessa.Web.Components.FluentValidator TValidator="GetAllFilterAttributesResponseValidator"/>
                    </Validator>
                    <Template>
                        @{
                            var filterAttributes = (GetAllFilterAttributesResponse)context;
                            <div class="py-4 px-2 bg-green-50 min-h-full flex flex-wrap">
                                <div class="px-2 w-full md:w-2/12">
                                    <label class="">@("Lp".Tr())</label>
                                    <SfNumericTextBox ID="Code" @bind-Value="@(filterAttributes.OrdinaryNumber)"/>
                                    <ValidationMessage For="@(() => filterAttributes.OrdinaryNumber)"/>
                                </div>
                                <div class="px-2 w-full md:w-5/12">
                                    <label class="">@("Kod".Tr())</label>
                                    <SfTextBox ID="Code" @bind-Value="@(filterAttributes.Code)"/>
                                    <ValidationMessage For="@(() => filterAttributes.Code)"/>
                                </div>
                                <div class="px-2 w-full md:w-5/12">
                                    <label class="">@("Nazwa".Tr())</label>
                                    <SfTextBox ID="Code" @bind-Value="@(filterAttributes.Name)"/>
                                    <ValidationMessage For="@(() => filterAttributes.Name)"/>
                                </div>
                                <div class="px-2 w-full md:w-10/12">
                                    <label class="control-label">@("Wyświetlana nazwa".Tr())</label>
                                    <SfTextBox ID="Code" @bind-Value="@(filterAttributes.DisplayName)"/>
                                    <ValidationMessage For="@(() => filterAttributes.DisplayName)"/>
                                </div>
                                <div class="px-2 w-full md:w-2/12">
                                    <div>
                                        <label class="control-label">@("Aktywny".Tr())</label>
                                    </div>
                                    <div class="ml-4">
                                        <SfCheckBox TChecked="bool" @bind-Checked="@(filterAttributes.IsActive)"/>
                                    </div>
                                </div>

                                <div class="pt-2 w-full">
                                    <EMessa.Web.Components.Grids.GridSaveActions TItem="GetAllFilterAttributesResponse" DefaultGrid="@_defaultGrid"></EMessa.Web.Components.Grids.GridSaveActions>
                                </div>
                            </div>
                        }
                    </Template>
                </GridEditSettings>
                <GridColumns>
                    <GridColumn Field=@nameof(GetAllFilterAttributesResponse.Id) Visible="false" IsPrimaryKey="true"/>
                    <GridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllFilterAttributesResponse.OrdinaryNumber) HeaderText="@("Lp".Tr())" Width="60" AllowEditing="false" FilterSettings="@GridFilters.BarContains"/>
                    <GridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllFilterAttributesResponse.Code) HeaderText="@("Kod".Tr())" Width="550" FilterSettings="@GridFilters.BarContains"/>
                    <GridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllFilterAttributesResponse.Name) HeaderText="@("Nazwa".Tr())" FilterSettings="@GridFilters.BarContains"/>
                    <GridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllFilterAttributesResponse.DisplayName) HeaderText="@("Nazwa wyświetlana".Tr())" FilterSettings="@GridFilters.BarContains"/>
                    <GridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllFilterAttributesResponse.IsActive) HeaderText="@("Aktywny".Tr())" Width="85" TextAlign="TextAlign.Center" HeaderTextAlign="TextAlign.Left" DisplayAsCheckBox="true"/>
                    <GridColumn HideAtMedia="@($"(max-width: {(int)AppScreenSize.Medium}px)")" HeaderText="@("Atrybuty filtrowania".Tr())" Field=@nameof(GetAllFilterAttributesResponse.Id)>
                        <Template>
                            @{
                                var filterAttribute = (GetAllFilterAttributesResponse)context;
                                <FilterAttributeCard FilterAttribute="@filterAttribute"/>
                            }
                        </Template>
                    </GridColumn>
                </GridColumns>
            </SfGrid>
            <SfDialog @ref="_dialog" Width="250px" Visible="false" ShowCloseIcon="true" IsModal="true">
                <DialogTemplates>
                    <Header>@("Usuń atrybut".Tr())</Header>
                    <Content>@("Czy na pewno chcesz usunąć ten Atrybut?".Tr())</Content>
                </DialogTemplates>
            </SfDialog>
            <div class="mt-4">
                <FilterAttributeTabPage FilterAttribute="@_selectedFilterAttributes"/>
            </div>
        </div>
    </ChildContent>
</IndexCard>
<AppToast @ref="_toast"/>

@code {
    private AppToast? _toast;
    private SfDialog _dialog = new();
    private SfGrid<GetAllFilterAttributesResponse> _defaultGrid = new();

    private GetAllFilterAttributesResponse _selectedFilterAttributes = new();
    //private bool selected = false;
    private readonly FilterSettings _barContainsFilterSettings = new() { Operator = Operator.Contains, Type = Syncfusion.Blazor.Grids.FilterType.FilterBar };

    protected override async Task OnInitializedAsync()
    {
        AppStateService.StateChanged += OnStateChanged;
        await base.OnInitializedAsync();
    }

    private void OnCommandClicked(CommandClickEventArgs<GetAllFilterAttributesResponse> args)
    {
        if (args.CommandColumn.Type == CommandButtonType.None && args.CommandColumn.ID == "ViewFilterAttribute")
        {
           NavigationManager.NavigateTo("/admin/filterAttributes/view/" + args.RowData.Id);
        }
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }

    private void OnRowSelected(RowSelectEventArgs<GetAllFilterAttributesResponse> args)
    {
        _selectedFilterAttributes = args.Data;
    }

    private async Task ActionFailureHandler(Syncfusion.Blazor.Grids.FailureEventArgs e)
    {
        _logger.LogError(e.Error, "FilterAttributes");
        await _toast!.ShowError("Błąd operacji");
    }

    void IDisposable.Dispose()
    {
        AppStateService.StateChanged -= OnStateChanged;
    }
}
