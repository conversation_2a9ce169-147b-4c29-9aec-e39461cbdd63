@page "/admin/uilocalizations/index"
@attribute [Authorize(Roles = Role.Administrator)]

@using System.Globalization
@using EMessa.Base.Models
@using EMessa.Core.Extensions
@using EMessa.Core.Features.Translations.Commands.ReloadTranslations
@using EMessa.Core.Features.Translations.Commands.Delete
@using EMessa.Core.Features.Translations.Commands.EditTranslationEntry
@using EMessa.Core.Features.Translations.Commands.Import
@using EMessa.Core.Features.Translations.Queries.ExportAll
@using EMessa.Core.Features.Translations.Queries.GetAllTranslationEntries
@using EMessa.Core.Services
@using EMessa.Web.Components.Grids
@using EMessa.Web.Constants
@using EMessa.Web.Pages.Admin.LocalizationResource.Parts
@using Radzen
@using TextAlign = Radzen.TextAlign
@using Radzen.Blazor

@inject ILogger<Index> Logger
@inject DialogService DialogService
@inject ITranslationManagerService TranslationManagerService
@inject IMultiTranslationService MultiTranslationService
@inject ITranslationExportImportService TranslationExportImportService
@inject IJSRuntime JsRuntime
@implements IDisposable

<style>
    /*.rz-button.rz-light {*/
    /*    background-color: var(--rz-base-200);*/
    /*    color: var(--rz-text-color);*/
    /*}*/

    /* Podstawowe style dla komponentu fileupload */
    .rz-fileupload {
        border: none !important;
        background-color: transparent !important;
        height: 100% !important;
    }

    /* Ukrycie listy plików */
    .rz-fileupload-content {
        display: none !important;
    }

    /* Style paska przycisków */
    .rz-fileupload-buttonbar {
        padding: 0 !important;
        height: 100% !important;
    }

    /* Style dla przycisku Choose/Import */
    .rz-fileupload-choose {
        background-color: var(--rz-base-200) !important;
        color: var(--rz-text-color) !important;
        border: 1px solid var(--rz-border-color) !important;
    }

    .rz-fileupload-choose:hover {
        background-color: var(--rz-base-300) !important;
        color: var(--rz-text-color) !important;
    }

    .rz-fileupload-choose.rz-button {
        height: 100% !important;
        display: flex !important;
        align-items: center !important;
        width: 100% !important;
    }

    /* Style dla inputa wewnątrz przycisku */
    .rz-fileupload-choose.rz-button input[type="file"] {
        height: 100% !important;
    }

    /* Style dla tekstu przycisku */
    .rz-fileupload-choose .rz-button-text {
        color: var(--rz-text-color) !important;
        margin: auto !important;
        font-weight: 600 !important;
    }
</style>

<IndexCard Title=@("Tłumaczenia UI".Tr() + $" - {_totalCount}")>
    <ChildContent>
        <div title="@("Wybierz język".Tr())"
             class="p-1 px-2 flex flex-row flex-wrap gap-2 items-center justify-start">
            <div class="mr-4">@("Wybierz język".Tr())</div>
            @if (_isLargeScreen)
            {
                <RadzenSelectBar TValue="string"
                                 Data="@LocalizedCultures"
                                 @bind-Value="@DropDownListItemCode"
                                 TextProperty="Text"
                                 ValueProperty="Code"
                                 Size="ButtonSize.Small" />
            }
            else
            {
                <RadzenDropDown TValue="string" class="w-28"
                                Data="@LocalizedCultures"
                                @bind-Value="@DropDownListItemCode"
                                TextProperty="Text"
                                ValueProperty="Code"
                                Placeholder="@("Wybierz język".Tr())" />
            }
        </div>
        <RadzenStack Orientation="Orientation.Horizontal" Class="p-1 justify-start items-start gap-2 flex-wrap">
            <RadzenSplitButton Icon="sync" Text=@("Zapisz zmiany".Tr())
                               ButtonStyle="ButtonStyle.Success"
                               Visible="@(_translationsToUpdate.Count > 0)"
                               Click="@SaveChangesSplitButtonHandler">
                <ChildContent>
                    <RadzenSplitButtonItem Icon="undo"
                                           Text="@("Anuluj zmiany".Tr())"
                                           Value="cancelAll" />
                </ChildContent>
            </RadzenSplitButton>

            <RadzenSplitButton Icon="sync" Text=@("Przeładuj tłumaczenia".Tr())
                               ButtonStyle="ButtonStyle.Light"
                               Click="@RefreshSplitButtonHandler">
                <ChildContent>
                    <RadzenSplitButtonItem Icon="@(_isEditingAll ? "done" : "edit")"
                                           Text="@(_isEditingAll ? "Zakończ edycję wszystkich".Tr() : "Edytuj wszystkie".Tr())"
                                           Value="editAll" />
                </ChildContent>
            </RadzenSplitButton>

            <RadzenSplitButton Icon="auto_fix" Text="@("Aktualizuj wpisy tłumaczeń".Tr())"
                               ButtonStyle="ButtonStyle.Light"
                               Click="@(args => args?.Value == null ? UpdateTranslationsHandler() : AutoTranslateHandler())">
                <ChildContent>
                    <RadzenSplitButtonItem Icon="translate" Text="@("Tłumacz automatycznie".Tr())" Value="auto" />
                </ChildContent>
            </RadzenSplitButton>

            <RadzenSplitButton Icon="download" Text="@("Eksport XLS".Tr())"
                               ButtonStyle="ButtonStyle.Light"
                               Click="@(args => Export(args?.Value ?? XlsxType))">
                <ChildContent>
                    <RadzenSplitButtonItem Icon="table_rows" Text="@("Eksport CSV".Tr())" Value="@CsvType" />
                    <RadzenSplitButtonItem Icon="data_object" Text="@("Eksport JSON".Tr())" Value="@JsonType" />
                </ChildContent>
            </RadzenSplitButton>

            <RadzenCard Variant="Variant.Outlined" class="p-0">
                <RadzenUpload Icon="upload" ChooseText="@("Import".Tr())"
                              Auto="true"
                              Multiple="false"
                              Accept=".xls,.xlsx,.csv,.json"
                              InputAttributes="@(new Dictionary<string, object> { { "aria-label", "select file" } })"
                              Change="@OnFileChange" />
            </RadzenCard>

            <GridSearch @ref="_gridSearch" ValueChanged="@_grid.Reload" CssClass="ml-auto" />
        </RadzenStack>
        <ProgressBar Mode="@(_isImporting ? ProgressBarMode.Determinate : ProgressBarMode.Indeterminate)"
                     Value="@ProgressValue" />
        <RadzenDataGrid @ref="_grid" TItem="GetAllTranslationEntriesResponse"
                        Data="@_gridData" Count="@_totalCount"
                        LoadData="@LoadData" IsLoading="@_isLoading"
                        AllowFiltering FilterMode="FilterMode.Simple"
                        FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                        AllowPaging PageSize="@PageSize.DefaultPageSize" PageSizeOptions="@PageSize.Pages20To200"
                        PageSizeText="@PageSize.FormatPaginationInfo(_grid)"
                        AllowSorting AllowMultiColumnSorting AllowColumnResize
                        Density="Density.Compact" EditMode="DataGridEditMode.Multiple"
                        RowUpdate="@OnUpdateTranslation"
                        RowDoubleClick="@OnRowDoubleClick"
                        EmptyText="@RadzenDataGridConstants.EmptyText">
            <LoadingTemplate />

            <Columns>
                <RadzenDataGridColumn Context="factory"
                                      Title=@("Akcje".Tr())
                                      Width="@(_isLargeScreen ? "100px" : "50px")"
                                      Filterable="false" Sortable="false"
                                      TextAlign="TextAlign.Center"
                                      Frozen="true" FrozenPosition="FrozenColumnPosition.Right">
                    <Template Context="translation">
                        <div class="@(_isLargeScreen ? "flex flex-row gap-2 justify-center" : "flex flex-col gap-2 items-center")">
                            <RadzenButton Icon="edit" ButtonStyle="ButtonStyle.Base"
                                          Size="ButtonSize.ExtraSmall"
                                          Click="@(_ => EditTranslation(translation))" />
                            <RadzenButton Icon="delete" ButtonStyle="ButtonStyle.Danger"
                                          Size="ButtonSize.ExtraSmall"
                                          Click="@(_ => DeleteTranslation(translation))" />
                        </div>
                    </Template>
                    <EditTemplate Context="translation">
                        <div class="@(_isLargeScreen ? "flex flex-row gap-2 justify-center" : "flex flex-col gap-2 items-center")">
                            <RadzenButton ButtonStyle="ButtonStyle.Success" Icon="check"
                                          Size="ButtonSize.ExtraSmall"
                                          Disabled="@_isTranslating"
                                          Click="@(_ => SaveTranslation(translation))" />
                            <RadzenButton ButtonStyle="ButtonStyle.Light" Icon="close"
                                          Size="ButtonSize.ExtraSmall"
                                          Disabled="@_isTranslating"
                                          Click="@(_ => CancelTranslation(translation))" />
                            <RadzenButton ButtonStyle="ButtonStyle.Primary" Icon="translate"
                                          Size="ButtonSize.ExtraSmall"
                                          Disabled="@_isTranslating"
                                          Click="@(_ => AutoTranslateTranslation(translation))" />
                        </div>
                    </EditTemplate>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Property="@nameof(GetAllTranslationEntriesResponse.BaseText)" Title=@("Klucz".Tr())
                                      Width="300px" Visible="@_isLargeScreen"
                                      Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters">
                    <Template Context="translation">
                        @translation.BaseText
                    </Template>
                    <EditTemplate Context="translation">
                        <RadzenTextArea @bind-Value="translation.BaseText" Style="width: 100%; min-height: 100px"
                                        Name="BaseText" Disabled="true" />
                    </EditTemplate>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Property="@nameof(GetAllTranslationEntriesResponse.TranslatedText)"
                                      Title=@("Tłumaczenie".Tr())
                                      Visible="@_isLargeScreen"
                                      Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters">
                    <Template Context="translation">
                        @translation.TranslatedText
                    </Template>
                    <EditTemplate Context="translation">
                        <RadzenTextArea @bind-Value="translation.TranslatedText" Style="width: 100%; min-height: 100px"
                                        Name="TranslatedText" />
                    </EditTemplate>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Property="@nameof(GetAllTranslationEntriesResponse.Translate)"
                                      Title=@("Przetłumacz".Tr())
                                      Width="120px" Visible="@_isLargeScreen"
                                      TextAlign="TextAlign.Center"
                                      Sortable Filterable>
                    <Template Context="translation">
                        <RadzenCheckBox @bind-Value="translation.Translate" Disabled TValue="bool" />
                    </Template>
                    <EditTemplate Context="translation">
                        <RadzenCheckBox @bind-Value="translation.Translate" TValue="bool" />
                    </EditTemplate>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Property="@nameof(GetAllTranslationEntriesResponse.Verify)" Title=@("Zweryfikuj".Tr())
                                      Width="120px" Visible="@_isLargeScreen"
                                      TextAlign="TextAlign.Center"
                                      Sortable Filterable>
                    <Template Context="translation">
                        <RadzenCheckBox @bind-Value="translation.Verify" Disabled TValue="bool" />
                    </Template>
                    <EditTemplate Context="translation">
                        <RadzenCheckBox @bind-Value="translation.Verify" TValue="bool" />
                    </EditTemplate>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Title=@("Tłumaczenia".Tr()) Visible="@(!_isLargeScreen)"
                                      Filterable="false" Sortable="false">
                    <Template Context="translation">
                        <TranslationCard Data="@translation" />
                    </Template>
                    <EditTemplate Context="translation">
                        <div class="p-2 flex flex-col gap-2">
                            <div class="flex flex-col gap-2">
                                <label class="font-bold">@("Klucz".Tr())</label>
                                <RadzenTextBox @bind-Value="translation.BaseText" Style="width: 100%" Disabled="true" />
                            </div>
                            <div class="flex flex-col flex-wrap gap-2 justify-between">
                                <label class="font-bold">@("Tłumaczenie".Tr())</label>
                                <RadzenTextArea @bind-Value="translation.TranslatedText"
                                                Style="width: 100%; min-height: 100px" />
                                <div class="flex flex-row gap-2 flex-wrap justify-start">
                                    <div class="flex flex-nowrap gap-2 items-center">
                                        <RadzenCheckBox TValue="bool" @bind-Value="translation.Translate" />
                                        <label class="me-2">@("Przetłumacz".Tr())</label>
                                    </div>
                                    <div class="flex flex-nowrap gap-2 items-center">
                                        <RadzenCheckBox TValue="bool" @bind-Value="translation.Verify" />
                                        <label class="me-2">@("Zweryfikuj".Tr())</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </EditTemplate>
                </RadzenDataGridColumn>
            </Columns>
        </RadzenDataGrid>
    </ChildContent>
</IndexCard>

@code {
    private int _totalCount;
    private bool _isLoading;
    private bool _isImporting;
    private bool _isTranslating;
    private bool _isEditingAll;
    private bool _isLargeScreen = true;
    private RadzenDataGrid<GetAllTranslationEntriesResponse> _grid = new();
    private List<GetAllTranslationEntriesResponse> _gridData = [];
    private readonly List<GetAllTranslationEntriesResponse> _translationsToUpdate = [];
    private DropDownListItem _dropDownListItem = new() { Code = "pl", Text = "Polski" };
    private GridSearch _gridSearch = new();

    private string DropDownListItemCode
    {
        get => _dropDownListItem.Code;
        set
        {
            _dropDownListItem = SystemConstants.SupportedCultures.Single(x => x.Code == value);
            _grid.Reload();
        }
    }

    private IEnumerable<dynamic> LocalizedCultures => SystemConstants.SupportedCultures
        .Select(c => new
        {
            c.Code,
            Text = c.Text.Tr()
        });

    private double _importProgress;
    private double ProgressValue => _isImporting ? _importProgress : _isLoading ? 50 : 0;

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        AppStateService.StateChanged += OnStateChanged;
    }

    void IDisposable.Dispose()
    {
        AppStateService.StateChanged -= OnStateChanged;
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _isLargeScreen = AppStateService.IsLargeScreen;
            _ = InvokeAsync(StateHasChanged);
        }
    }

    protected override void OnParametersSet()
    {
        base.OnParametersSet();
        _dropDownListItem = SystemConstants.SupportedCultures.SingleOrDefault(x => x.Code == CultureInfo.CurrentCulture.Name)
                            ?? SystemConstants.SupportedCultures.Single(x => x.Code == "pl");
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            await _grid.Reload();
        }
    }

    private async Task LoadData(LoadDataArgs args)
    {
        _isLoading = true;

        try
        {
            var sieveModel = args.ToSieveModel().ApplyGlobalSearchFilter(_gridSearch.SearchValue);
            sieveModel.Filters = $"{sieveModel.Filters},LanguageCode=={_dropDownListItem.Code},";
            var response = await Mediator.Send(new GetAllTranslationEntriesQuery(sieveModel));
            if (response.Succeeded)
            {
                _gridData = response.Data;
                _totalCount = response.TotalCount;
            }
            else
            {
                ToastService.ShowError("Błąd ładowania danych".Tr());
                _gridData = [];
                _totalCount = 0;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas ładowania danych tłumaczeń");
            ToastService.Show(ToastType.LoadDataError);
            _gridData = [];
            _totalCount = 0;
        }
        finally
        {
            _isLoading = false;
            await InvokeAsync(StateHasChanged);
        }
    }

    private async Task EditTranslation(GetAllTranslationEntriesResponse translationEntry)
    {
        if (!_grid.IsValid) return;
        await _grid.EditRow(translationEntry);
    }

    private async Task SaveTranslation(GetAllTranslationEntriesResponse translationEntry)
    {
        await _grid.UpdateRow(translationEntry);
    }

    private void CancelTranslation(GetAllTranslationEntriesResponse translationEntry)
    {
        _translationsToUpdate.Remove(translationEntry);
        _grid.CancelEditRow(translationEntry);
    }

    private async Task AutoTranslateTranslation(GetAllTranslationEntriesResponse translationEntry)
    {
        _isTranslating = true;
        translationEntry.TranslatedText = "";
        var translations = await MultiTranslationService.Translate(translationEntry.BaseText, [translationEntry.LanguageCode]);
        if (translations.Succeeded)
        {
            if (translations.Data.TryGetValue(translationEntry.LanguageCode, out var translatedText))
            {
                translationEntry.TranslatedText = translatedText;
                translationEntry.Translate = false;
                translationEntry.Verify = true;
            }
        }

        _isTranslating = false;
    }

    private void OnUpdateTranslation(GetAllTranslationEntriesResponse translationEntry)
    {
        if (_translationsToUpdate.All(t => t.Id != translationEntry.Id))
        {
            _translationsToUpdate.Add(translationEntry);
        }
        else
        {
            var index = _translationsToUpdate.FindIndex(t => t.Id == translationEntry.Id);
            if (index >= 0)
            {
                _translationsToUpdate[index] = translationEntry;
            }
        }
    }

    private async Task OnRowDoubleClick(DataGridRowMouseEventArgs<GetAllTranslationEntriesResponse> args)
    {
        await EditTranslation(args.Data);
    }

    private async Task SaveChangesHandler()
    {
        try
        {
            _isLoading = true;
            var success = true;

            foreach (var translation in _translationsToUpdate)
            {
                var response = await Mediator.Send(new EditTranslationEntryCommand(
                        new EditTranslationEntryRequest(
                            translation.Id,
                            translation.TranslatedText,
                            translation.Translate,
                            translation.Verify
                        )
                    )
                );

                if (!response.Succeeded)
                {
                    success = false;
                    ToastService.ShowError($"Błąd aktualizacji tłumaczenia: {translation.BaseText}");
                }
            }

            if (success)
            {
                ToastService.Show(ToastType.SaveSuccess);
                _translationsToUpdate.Clear();
            }

            await _grid.Reload();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas zapisywania zmian tłumaczeń");
            ToastService.ShowError("Błąd zapisywania zmian tłumaczeń".Tr());
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task DeleteTranslation(GetAllTranslationEntriesResponse translationEntry)
    {
        try
        {
            var confirmed = await DialogService.Confirm(
                "Czy na pewno chcesz usunąć to tłumaczenie".Tr(),
                "Potwierdzenie usunięcia".Tr(),
                new ConfirmOptions { OkButtonText = "Tak".Tr(), CancelButtonText = "Nie".Tr() });

            if (confirmed == true)
            {
                _isLoading = true;

                var response = await Mediator.Send(new DeleteTranslationCommand(translationEntry.TranslationId));

                if (response.Succeeded)
                    ToastService.Show(ToastType.DeleteSuccess);
                else
                    ToastService.Show(ToastType.DeleteError, string.Join(',', response.Messages));

                await _grid.Reload();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, $"Błąd podczas usuwania tłumaczenia ID: {translationEntry.Id}");
            ToastService.Show(ToastType.DeleteError, "Błąd podczas usuwania tłumaczenia".Tr());
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private async Task ReloadTranslationsHandler()
    {
        await Mediator.Send(new ReloadTranslationsCommand());
    }

    private async Task ToggleEditAllHandler()
    {
        if (_gridData.Count == 0)
        {
            _isEditingAll = false;
            return;
        }

        _isEditingAll = !_isEditingAll;

        if (_isEditingAll)
        {
            foreach (var translation in _gridData)
            {
                await _grid.EditRow(translation);
            }
        }
        else
        {
            foreach (var translation in _gridData)
            {
                _grid.CancelEditRow(translation);
            }
        }
    }

    private void CancelEditAllHandler()
    {
        if (_gridData.Count == 0) return;

        try
        {
            foreach (var translation in _gridData)
            {
                _grid.CancelEditRow(translation);
            }

            _isEditingAll = false;

            _translationsToUpdate.Clear();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas anulowania edycji wszystkich wierszy");
            ToastService.ShowError("Błąd podczas anulowania edycji wszystkich wierszy".Tr());
        }
    }

    private async Task SaveChangesSplitButtonHandler(RadzenSplitButtonItem args)
    {
        switch (args?.Value)
        {
            case null:
                await SaveChangesHandler();
                break;
            case "cancelAll":
                CancelEditAllHandler();
                break;
        }
    }

    private async Task RefreshSplitButtonHandler(RadzenSplitButtonItem args)
    {
        switch (args?.Value)
        {
            case null:
                await ReloadTranslationsHandler();
                break;
            case "editAll":
                await ToggleEditAllHandler();
                break;
        }
    }

    private async Task UpdateTranslationsHandler()
    {
        await TranslationManagerService.UpdateTranslationsAsync();
    }

    private async Task AutoTranslateHandler()
    {
        await TranslationManagerService.AutoTranslateAsync(CancellationToken.None);
        await _grid.Reload();
    }

    #region Export/Import

    private const string XlsxType = "XLSX";
    private const string CsvType = "CSV";
    private const string JsonType = "JSON";
    private const string BaseLanguageColumn = "BaseText";

    public async Task Export(string type)
    {
        _isLoading = true;
        try
        {
            var languageCodes = SystemConstants.SupportedCulturesList;

            var translations = await LoadTranslationsAsync(languageCodes);
            if (translations == null)
            {
                throw new Exception("Nie udało się pobrać tłumaczeń");
            }

            var timeStamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var dataColumns = new List<string> { BaseLanguageColumn }.Concat(languageCodes.Select(x => x.FirstCharToUpper())).ToArray();
            var dataRows = translations.Select(t => t.DataRow).ToArray();

            MemoryStream? memoryStream = null;
            string fileExtension = null!;
            string mimeType = null!;

            switch (type)
            {
                case XlsxType:
                    memoryStream = TranslationExportImportService.ExportToExcel(dataColumns, dataRows, timeStamp);
                    fileExtension = "xlsx";
                    mimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                    break;
                case CsvType:
                    memoryStream = TranslationExportImportService.ExportToCsv(dataColumns, dataRows);
                    fileExtension = "csv";
                    mimeType = "text/csv";
                    break;
                case JsonType:
                    memoryStream = TranslationExportImportService.ExportToJson(dataColumns, dataRows);
                    fileExtension = "json";
                    mimeType = "application/json";
                    break;
            }

            if (memoryStream != null)
            {
                var fileName = $"translations_{_dropDownListItem.Code.ToUpper()}_{timeStamp}.{fileExtension}";
                var base64Data = Convert.ToBase64String(memoryStream.ToArray());
                await JsRuntime.InvokeVoidAsync("EMessa.downloadFileFromStream", fileName, mimeType, base64Data);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas ładowania danych tłumaczeń");
            ToastService.ShowError("Błąd podczas ładowania danych tłumaczeń");
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task<IEnumerable<ExportAllTranslationsResponse>?> LoadTranslationsAsync(IEnumerable<string> languageCodes)
    {
        var response = await Mediator.Send(new ExportAllTranslationEntriesQuery(languageCodes));
        if (response is { Succeeded: true, Data: var translations })
        {
            return translations;
        }

        ToastService.ShowError("Błąd ładowania danych".Tr());
        return null;
    }

    private async Task OnFileChange(UploadChangeEventArgs args)
    {
        _isImporting = true;
        _importProgress = 0;
        await InvokeAsync(StateHasChanged);

        try
        {
            // Print.WriteInline("Files uploaded:", args.Files.Count());
            if (args.Files.Count() != 1) return;

            var file = args.Files.First();
            // Print.WriteInline($"Uploaded: {file.Name} / {file.Size} bytes");

            var extension = Path.GetExtension(file.Name).ToLower();

            var fileType = extension switch
            {
                ".xlsx" or ".xls" => XlsxType,
                ".csv" => CsvType,
                ".json" => JsonType,
                _ => null
            };

            if (fileType == null)
            {
                ToastService.ShowError("Nieobsługiwany format importu");
                return;
            }

            await using var stream = file.OpenReadStream(maxAllowedSize: 10 * 1024 * 1024); // 10MB limit

            var importResults = fileType switch
            {
                XlsxType => await TranslationExportImportService.ImportFromExcel(stream),
                CsvType => await TranslationExportImportService.ImportFromCsv(stream),
                JsonType => await TranslationExportImportService.ImportFromJson(stream),
                _ => throw new ArgumentOutOfRangeException(nameof(fileType), fileType, "Nieobsługiwany format importu".Tr())
            };

            var count = 0;
            var total = importResults.Count;
            var success = true;
            foreach (var importResult in importResults)
            {
                count++;
                // co 5 update _importProgress lub dla ostatniego elementu
                if (count % 5 == 0 || count == total)
                {
                    _importProgress = (double)count / total * 100;
                    await InvokeAsync(StateHasChanged);
                }

                try
                {
                    var response = await Mediator.Send(new ImportTranslationCommand(importResult));
                    if (!response.Succeeded)
                    {
                        success = false;
                    }
                }
                catch (Exception)
                {
                    success = false;
                }
            }

            if (!success)
            {
                Logger.LogError("Wystąpiły błędy podczas importowania tłumaczeń");
                ToastService.ShowError("Wystąpiły błędy podczas importowania tłumaczeń");
            }

            await _grid.Reload();
        }
        catch (Exception)
        {
            ToastService.ShowError("Błąd podczas importowania tłumaczeń");
        }
        finally
        {
            _isImporting = false;
            _importProgress = 0;
        }
    }

    #endregion

}