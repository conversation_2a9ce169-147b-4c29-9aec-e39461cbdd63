@using EMessa.Core.Features.ArticleCategory.Commands.AddArticleToCategory
@using EMessa.Core.Features.ArticleCategory.Commands.RemoveArticleFromCategory
@using EMessa.Core.Features.ArticleCategory.Queries.GetArticlesForCategory
@using EMessa.Core.Features.Articles.Queries.GetAll
@using Syncfusion.Blazor.Grids
@inject IMediator _mediator;
@inject ILogger<CategoryArticlesComponent> _logger;

<div class="">
    <h1 class="text-lg">@("Dodaj artykuły do kategorii".Tr())</h1>
</div>
@if (!_errorMessage.IsEmpty())
{
    <div class="m-3 p-3 text-red-500">
        @_errorMessage
    </div>
}
<div class="flex flex-wrap">
    <div class="w-full md:w-1/2 p-2">
        <div>
            <h2 class="text-md py-1">@("Produkty w kategorii".Tr())</h2>
        </div>
        <SfGrid TValue="GetArticlesForCategoryQueryResponse" DataSource="@_articlesInCategory" AllowFiltering="true">
            <GridEditSettings  ShowDeleteConfirmDialog="false" AllowDeleting="false"></GridEditSettings>
            <GridEvents CommandClicked="ArticleInCategoryCommandClicked" TValue="GetArticlesForCategoryQueryResponse"></GridEvents>
            <GridColumns>
                <GridColumn Field=@nameof(GetArticlesForCategoryQueryResponse.Id) Visible="false" IsPrimaryKey="true"></GridColumn>
                <GridColumn Field=@nameof(GetArticlesForCategoryQueryResponse.Name) HeaderText="@("Nazwa".Tr())" FilterSettings="@GridFilters.BarContains"></GridColumn>
                @if (AllowArticlesRemove) { 
                    <GridColumn Width="60" AllowFiltering="false" TextAlign="TextAlign.Center">
                        <GridCommandColumns>
                            <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat p-1 m-0" })"></GridCommandColumn>
                        </GridCommandColumns>
                    </GridColumn>
                }
                
            </GridColumns>
        </SfGrid>
    </div>
    <div class="w-full md:w-1/2 p-2">
        <div>
            <h2 class="text-md py-1">@("Wszysktie produkty".Tr())</h2>
        </div>
        <SfGrid TValue="GetAllArticlesResponse" DataSource="@_nonExistingArticles" AllowFiltering="true">
            <GridEditSettings ShowDeleteConfirmDialog="false" AllowAdding="false" AllowDeleting="false"></GridEditSettings>
            <GridEvents CommandClicked="AllArticlesCommandClicked" TValue="GetAllArticlesResponse"></GridEvents>
            <GridColumns>
                <GridColumn Field=@nameof(GetAllArticlesResponse.Id) Visible="false" IsPrimaryKey="true"></GridColumn>
                <GridColumn Field=@nameof(GetAllArticlesResponse.Name) HeaderText="@("Nazwa".Tr())" FilterSettings="@GridFilters.BarContains"></GridColumn>
                @if (AllowArticlesAdd)
                {
                    <GridColumn Width="60" AllowFiltering="false" TextAlign="TextAlign.Center">
                        <GridCommandColumns>
                            <GridCommandColumn ID="Add" Type="CommandButtonType.None" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-add", CssClass = "e-flat p-1 m-0" })"></GridCommandColumn>
                        </GridCommandColumns>
                    </GridColumn>
                }
                
            </GridColumns>
        </SfGrid>
    </div>

</div>

@code {
    [Parameter]
    public int? CategoryId { get; set; }

    [Parameter]
    public bool AllowArticlesAdd { get; set; }
    [Parameter]
    public bool AllowArticlesRemove { get; set; }

    [CascadingParameter]
    public AppToast _toast { get; set; }

    private List<GetAllArticlesResponse> _allArticles = [];
    private List<GetAllArticlesResponse> _nonExistingArticles = [];
    private List<GetArticlesForCategoryQueryResponse> _articlesInCategory = [];
    private string _errorMessage = "";

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        await LoadAllArticles();
    }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
        await LoadArticlesForCategory();

    }
    
    private async Task LoadAllArticles()
    {
        var allArticles = await _mediator.Send(new GetAllArticlesQuery(new DataManagerRequest { Skip = 0, Take = int.MaxValue }));
        if (allArticles.Succeeded)
        {
            _allArticles = allArticles.Data;
            _nonExistingArticles = _allArticles;
        }
    }

    private async Task LoadArticlesForCategory()
    {
        if(CategoryId.HasValue && CategoryId.Value > 0)
        {
            var result = await _mediator.Send(new GetArticlesForCategoryQuery(CategoryId ?? 0));
            if (result.Succeeded)
            {
                _articlesInCategory = result.Data;
                var inCategoryIds = _articlesInCategory.Select(x => x.Id).ToList();
                var allIds = _allArticles.Select(x => x.Id).ToList();
                var notInCategoryIds = allIds.Except(inCategoryIds).ToList();
                _nonExistingArticles = _allArticles.Where(x => notInCategoryIds.Contains(x.Id)).ToList(); 
            }
            else
            {
                await _toast!.ShowError(result.Messages.String("<br />"));
            }
        }
    }
    
    private async Task ArticleInCategoryCommandClicked(CommandClickEventArgs<GetArticlesForCategoryQueryResponse> args)
    {
        if (args.CommandColumn.Type == CommandButtonType.Delete && AllowArticlesRemove)
        {
            var result = await _mediator.Send(new RemoveArticleFromCategoryCommand(args.RowData.Id, CategoryId ?? 0));
            if (result.Succeeded)
            {
                await LoadArticlesForCategory();
            }
            else
            {
                await _toast.ShowError(result.Messages.String("<br />"));
            }
        }
    }

    private async Task AllArticlesCommandClicked(CommandClickEventArgs<GetAllArticlesResponse> args)
    {
        if (args.CommandColumn.Type == CommandButtonType.None && args.CommandColumn.ID == "Add" && AllowArticlesAdd)
        {
            var result = await _mediator.Send(new AddArticleToCategoryCommand(args.RowData.Id, CategoryId ?? 0));
            if (result.Succeeded)
            {
                await LoadArticlesForCategory();
            }
            else
            {
                _logger.LogError(result.Messages.String());
                await _toast!.ShowError(result.Messages.String("<br />"));
            }
        }
    }
}
