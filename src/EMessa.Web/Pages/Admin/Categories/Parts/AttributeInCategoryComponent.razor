@using EMessa.Core.DataAdapters
@using EMessa.Core.Exceptions
@using EMessa.Core.Features.Categories.Queries.GetAll
@using EMessa.Core.Features.FilterAttributeCategory.Commands.DeleteAttributeFromCategoryCommand
@using EMessa.Core.Features.FilterAttributeCategory.Queries.GetAllAttributesForCategory
@using Syncfusion.Blazor.Data
@using Syncfusion.Blazor.Grids

using EMessa.Core.Features.AppState;
@inject IMediator _mediator

<SfGrid @ref="_categoriesFilterAttributeGrid" TValue="GetAllAttributesForCategoryResponse" Query="@FilterAttributeCategoriesGridQuery" AdaptiveUIMode="@(AppStateService.GridState.AdaptiveMode)"
        EnableAdaptiveUI="@(AppStateService.GridState.EnableAdaptiveUI)"
        RowRenderingMode="@(AppStateService.GridState.RowDirection)"
        AllowFiltering="@(AppStateService.AppScreenSize == AppScreenSize.Large)"
        AllowPaging="true" AllowSorting="true" AllowSelection="true">
    <GridPageSettings PageSize="20" PageSizes="@(new string[] { "20", "50", "100", "200" })"/>
    <GridFilterSettings Mode="FilterBarMode.Immediate" ShowFilterBarStatus="true" ImmediateModeDelay="500"></GridFilterSettings>
    <GridEvents CommandClicked="CategoryInAttributeCommandClicked" OnActionFailure="ActionFailureHandler" TValue="GetAllAttributesForCategoryResponse"></GridEvents>
    <SfDataManager AdaptorInstance="@typeof(CategoryFilterAttributeDataAdaptor)" Adaptor="Adaptors.CustomAdaptor"></SfDataManager>
    <GridColumns>
        <GridColumn Field=@nameof(GetAllAttributesForCategoryResponse.Id) Visible="false" IsPrimaryKey="true"></GridColumn>
        <GridColumn Field=@nameof(GetAllAttributesForCategoryResponse.Name) HeaderText="@("Nazwa".Tr())" FilterSettings="@GridFilters.BarContains"></GridColumn>
        <GridColumn Width="60" AllowFiltering="false" TextAlign="TextAlign.Center">
            <GridCommandColumns>
                <GridCommandColumn Type="CommandButtonType.Delete" ButtonOption="@(new CommandButtonOptions() { IconCss = "e-icons e-delete", CssClass = "e-flat p-1 m-0 Width=10%" })"></GridCommandColumn>
            </GridCommandColumns>
        </GridColumn>
    </GridColumns>
</SfGrid>

@code {
    [Parameter]
    public GetAllCategoriesResponse Category { get; set; } = new();
    [Parameter]
    public EventCallback<bool?> OnCategoryRemoved { get; set; }
    [CascadingParameter]
    public Components.AppToast _toast { get; set; } = new();

    public Query FilterAttributeCategoriesGridQuery { get; set; } = new();
    private SfGrid<GetAllAttributesForCategoryResponse> _categoriesFilterAttributeGrid = new();

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
    }

    protected override void OnParametersSet()
    {
        if (Category != null)
        {
            FilterAttributeCategoriesGridQuery.AddParams("categoryIdParameter", Category.Id);
            _categoriesFilterAttributeGrid.Refresh();
        }
        base.OnParametersSet();
    }

    public async Task Refresh()
    {
        await _categoriesFilterAttributeGrid.Refresh();
    }

    private async void ActionFailureHandler(FailureEventArgs e)
    {
        var listExceptions = e.Error as ListExceptions;
        if (listExceptions != null)
        {
            await _toast!.ShowError(listExceptions.Messages.String());
        }
        else
        {
            await _toast!.ShowError("Nieokreślony błąd operacji".Tr());
        }
    }

    private async Task CategoryInAttributeCommandClicked(CommandClickEventArgs<GetAllAttributesForCategoryResponse> args)
    {
        if (args.CommandColumn.Type == CommandButtonType.Delete)
        {
            var result = await _mediator.Send(new DeleteAttributeFromCategoryCommand(args.RowData.Id, Category.Id));
            if (result.Succeeded)
            {
                await _categoriesFilterAttributeGrid.Refresh();
                await OnCategoryRemoved.InvokeAsync(true);
            }
            else
            {
                await _toast.ShowError(result.Messages.String("<br />"));
            }
        }
    }
}