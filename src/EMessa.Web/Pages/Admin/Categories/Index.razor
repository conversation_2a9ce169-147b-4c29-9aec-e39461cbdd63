@page "/admin/categories/index"
@attribute [Authorize(Roles = Role.Administrator)]
@using EMessa.Core.Expections
@using EMessa.Web.Pages.Admin.Categories.Parts
@using Syncfusion.Blazor.Data
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Navigations;
@using EMessa.Core.Features.Categories.Queries.GetAll
@using EMessa.Core.Features.Categories.Commands.ChangeParent
@using EMessa.Core.DataAdapters
@using EMessa.Core.Exceptions
@using Syncfusion.Blazor.Inputs;
@using EMessa.Web.Components.Grids;
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.TreeGrid
@using Action = Syncfusion.Blazor.Grids.Action
@using Syncfusion.Blazor.RichTextEditor

@inject IMediator _mediator
@inject ILogger<Index> _logger
@implements IDisposable

<IndexCard Title=@("Kategorie".Tr())>
    <ChildContent>
        <div class="flex-1">
            <SfTreeGrid @ref="_defaultGrid" TValue="GetAllCategoriesResponse" IdMapping="@nameof(GetAllCategoriesResponse.Id)" ParentIdMapping="@nameof(GetAllCategoriesResponse.ParentId)"
                        AllowRowDragAndDrop="true" AllowSelection="true" TreeColumnIndex="@columnIndex" HasChildMapping="HasChildren"
                        AdaptiveUIMode="@(AppStateService.GridState.AdaptiveMode)"
                        EnableAdaptiveUI="@(AppStateService.GridState.EnableAdaptiveUI)"
                        RowRenderingMode="@(AppStateService.GridState.RowDirection)"
                        Toolbar="@SfGridToolbar.FullToolbar">
                <TreeGridEditSettings AllowEditing="true" AllowAdding="true" AllowDeleting="true" ShowDeleteConfirmDialog="true" Mode="Syncfusion.Blazor.TreeGrid.EditMode.Row">
                    <Validator>
                        <FluentValidator TValidator="GetAllCategoriesResponseValidator"/>
                    </Validator>
                    <Template>
                        @{
                            GetAllCategoriesResponse category = (GetAllCategoriesResponse)context;
                            <div class="py-4 px-2 bg-green-50 min-h-full flex flex-wrap">
                                <div class="px-2 w-full md:w-4/12">
                                    <label class="">@("Kod".Tr())</label>
                                    <SfTextBox ID="Code" @bind-Value="@(category.Code)"></SfTextBox>
                                    <ValidationMessage For="@(() => category.Code)"/>
                                </div>
                                <div class="px-2 w-full md:w-8/12">
                                    <label class="">@("Nazwa".Tr())</label>
                                    <SfTextBox ID="Name" @bind-Value="@(category.Name)"></SfTextBox>
                                    <ValidationMessage For="@(() => category.Name)"/>
                                </div>
                                <div class="px-2 w-full">
                                    <label class="control-label">@("Aktywna".Tr())</label>
                                    <SfCheckBox TChecked="bool" @bind-Checked="@category.IsActive"></SfCheckBox>
                                </div>
                                <div class="px-2 w-full md:w-12/12">
                                    <label class="control-label">@("Opis".Tr())</label>


                                    <SfRichTextEditor @bind-Value="@(category.Description)" Height="300" EditorMode="EditorMode.HTML" EnableHtmlSanitizer="true" EnableResize="true">
                                        <RichTextEditorToolbarSettings Items="@RichTextToolbarHelper.FullToolbar" Type="ToolbarType.Expand"/>
                                        <RichTextEditorImageSettings SaveUrl="api/Image/Save" Path="/images/editor/"/>
                                    </SfRichTextEditor>
                                    <ValidationMessage For="@(() => category.Description)"/>
                                </div>

                                <div class="pt-2 w-full">
                                    <EMessa.Web.Components.TreeGrid.TreeGridSaveAction TItem="GetAllCategoriesResponse"
                                                                                       DefaultGrid="@_defaultGrid"></EMessa.Web.Components.TreeGrid.TreeGridSaveAction>
                                </div>
                            </div>
                        }
                    </Template>
                </TreeGridEditSettings>
                <TreeGridPageSettings PageSize="20" PageSizes="@(new string[] { "20", "50", "100", "200" })"/>
                <TreeGridSelectionSettings Type="Syncfusion.Blazor.Grids.SelectionType.Multiple"/>
                <TreeGridFilterSettings Type="Syncfusion.Blazor.TreeGrid.FilterType.FilterBar" Mode="FilterBarMode.Immediate" ShowFilterBarStatus="true" ImmediateModeDelay="500"/>
                <TreeGridEvents OnActionBegin="ActionBeginHandler"
                                OnActionFailure="ActionFailureHandler"
                                OnActionComplete="ActionCompleteHandler"
                                RowDragStarting="OnStartDrop"
                                RowDropped="OnRowDrop"
                                RowSelecting="OnSelectRowItem"
                                RowSelected="RowSelected"
                                TValue="GetAllCategoriesResponse"/>
                <SfDataManager AdaptorInstance="@typeof(CategoryDataAdaptor)" Adaptor="Adaptors.CustomAdaptor"/>
                <TreeGridColumns>
                    <TreeGridColumn HideAtMedia="@($"(max-width: {(int)AppScreenSize.Medium}px)")" HeaderText="@("Kategorie".Tr())" Field=@nameof(GetAllCategoriesResponse.Id)>
                        <Template>
                            @{
                                var category = context as GetAllCategoriesResponse;
                                <EMessa.Web.Pages.Admin.Categories.Parts.CategoryCard Category="@category"></EMessa.Web.Pages.Admin.Categories.Parts.CategoryCard>
                            }
                        </Template>
                    </TreeGridColumn>
                    <TreeGridColumn Field=@nameof(GetAllCategoriesResponse.Id) Visible="false" IsPrimaryKey="true"></TreeGridColumn>
                    <TreeGridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Width="120" Field=@nameof(GetAllCategoriesResponse.OrdinaryNumber) AllowEditing="false" HeaderText="@("Lp".Tr())" FilterSettings="@GridFilters.BarContains"></TreeGridColumn>
                    <TreeGridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllCategoriesResponse.Code) HeaderText="@("Kod".Tr())" FilterSettings="@GridFilters.BarContains"></TreeGridColumn>
                    <TreeGridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllCategoriesResponse.Name) HeaderText="@("Nazwa".Tr())" FilterSettings="@GridFilters.BarContains"></TreeGridColumn>
                    <TreeGridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllCategoriesResponse.Description) HeaderText="@("Opis".Tr())" FilterSettings="@GridFilters.BarContains"></TreeGridColumn>
                    <TreeGridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllCategoriesResponse.IsActive) HeaderText="@("Aktywna".Tr())" DisplayAsCheckBox="true"></TreeGridColumn>
                </TreeGridColumns>
            </SfTreeGrid>
            <SfDialog @ref="_dialog" Width="250px" Visible="false" ShowCloseIcon="true" IsModal="true">
                <DialogTemplates>
                    <Header>@("Usuń kategorię".Tr())</Header>
                    <Content>@("Czy na pewno chcesz usunąć tą Kategorię?".Tr())</Content>
                </DialogTemplates>
            </SfDialog>
            <SfTab class="mt-4">
                <TabItems>
                    <TabItem>
                        <ChildContent>
                            <TabHeader Text="@("Produkty".Tr())"></TabHeader>
                        </ChildContent>
                        <ContentTemplate>
                            <div class="my-2">
                                <EMessa.Web.Pages.Admin.Categories.Parts.CategoryArticlesComponent AllowArticlesAdd="true" AllowArticlesRemove="true" CategoryId="SelectedParentId"/>
                            </div>
                        </ContentTemplate>
                    </TabItem>
                    <TabItem>
                        <ChildContent>
                            <TabHeader Text="@("Atrybuty filtrowania".Tr())"></TabHeader>
                        </ChildContent>
                        <ContentTemplate>
                            <div class="px-2  min-h-full flex flex-wrap">
                                <div class="w-full md:w-1/2 md:pe-1">
                                    <h1 class="text-xl my-1">@("Atrybuty w kategorii".Tr())</h1>
                                    @{
                                        <EMessa.Web.Pages.Admin.Categories.Parts.AttributeInCategoryComponent @ref="CategoryAttributeComponent" Category="@SelectedCategory" OnCategoryRemoved="@(async (e) => await AllAttributesComponent.Refresh())"> </EMessa.Web.Pages.Admin.Categories.Parts.AttributeInCategoryComponent>
                                    }
                                </div>
                                <div class="w-full md:w-1/2 md:ps-1">
                                    <h1 class="text-xl my-1">@("Wszystkie atrybuty".Tr())</h1>
                                    @{
                                        <EMessa.Web.Pages.Admin.Categories.Parts.AttributeWithoutCategoryComponent @ref="AllAttributesComponent" Category="@SelectedCategory" OnCategoryAdded="@(async (e) => await CategoryAttributeComponent.Refresh())"> </EMessa.Web.Pages.Admin.Categories.Parts.AttributeWithoutCategoryComponent>
                                    }
                                </div>
                            </div>
                        </ContentTemplate>
                    </TabItem>
                    <TabItem>
                        <ChildContent>
                            <TabHeader Text="@("Tłumaczenia".Tr())"></TabHeader>
                        </ChildContent>
                        <ContentTemplate>
                            <div class="my-2">
                                <CategoriesTranslationsComponent CategoryId="@(SelectedParentId ?? 0)"></CategoriesTranslationsComponent>
                            </div>
                        </ContentTemplate>
                    </TabItem>
                </TabItems>
            </SfTab>
        </div>
    </ChildContent>
</IndexCard>
<AppToast @ref="_toast"/>


@code {
    [CascadingParameter]
    private AppToast? _toast { get; set; }
    private List<GetAllCategoriesResponse> CurrentViewOnStartDrag { get; set; }
    private List<GetAllCategoriesResponse> DraggedRow { get; set; }
    private SfDialog _dialog = new();
    private SfTreeGrid<GetAllCategoriesResponse> _defaultGrid = new();
    private List<GetAllCategoriesResponse> CategoryList = new();
    private int? SelectedRowParentId { get; set; } = null;
    private int? SelectedParentId { get; set; } = null;
    private int columnIndex { get; set; }
    private GetAllCategoriesResponse SelectedCategory { get; set; } = new();
    private readonly FilterSettings _barContainsFilterSettings = new() { Operator = Operator.Contains, Type = Syncfusion.Blazor.Grids.FilterType.FilterBar };
    AttributeInCategoryComponent CategoryAttributeComponent = new();
    AttributeWithoutCategoryComponent AllAttributesComponent = new();


    protected override async Task OnInitializedAsync()
    {
        if (AppStateService.AppScreenSize <= AppScreenSize.Medium)
        {
            columnIndex = 1;
        }
        else
        {
            columnIndex = 2;
        }

        AppStateService.StateChanged += OnStateChanged;
        await base.OnInitializedAsync();
    }
    private void OnStartDrop(RowDragStartingEventArgs<GetAllCategoriesResponse> args)
    {
        DraggedRow = args.Data;
        CurrentViewOnStartDrag = _defaultGrid.GetCurrentViewRecords();
    }

    private async void AddNewBranch()
    {
        await _defaultGrid.AddRecordAsync();
    }
    private async void OnRowDrop(RowDroppedEventArgs<GetAllCategoriesResponse> args)
    {
        try
        {
            int dragFromIndex = args.FromIndex;
            int dropToIndex = args.DropIndex;

            var movingCategory = args.Data[0];

            var targetCategory = CurrentViewOnStartDrag[dropToIndex];
            var actionType = args.Target.ID;
            var dragView = _defaultGrid.GetCurrentViewRecords();

            if (actionType == "Child")
            {

                if (movingCategory.ParentId == targetCategory.Id || targetCategory.Id == null)
                {
                    await _toast!.ShowError("Kategoria znajduję się już w tej grupie".Tr());
                }
                else
                {
                    var updateResult = await _mediator.Send(new ChangeCategoryParentCommand(movingCategory, targetCategory, ChangeCategoryActionType.Child));
                    if (!updateResult.Succeeded)
                    {

                        await _toast!.ShowError(updateResult.Messages.String());
                    }
                    await _defaultGrid.RefreshAsync();
                }
            }
            else if (actionType == "Below")
            {
                var updateResult = await _mediator.Send(new ChangeCategoryParentCommand(movingCategory, targetCategory, ChangeCategoryActionType.Below));
                if (!updateResult.Succeeded)
                {
                    _logger.LogError(updateResult.Messages.String());
                    await _toast!.ShowError(updateResult.Messages.String());
                }
                await _defaultGrid.RefreshAsync();
            }
            else if (actionType == "Above")
            {
                var updateResult = await _mediator.Send(new ChangeCategoryParentCommand(movingCategory, targetCategory, ChangeCategoryActionType.Above));
                if (!updateResult.Succeeded)
                {
                    _logger.LogError(updateResult.Messages.String());

                    await _toast!.ShowError(updateResult.Messages.String());
                }
                await _defaultGrid.RefreshAsync();
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, e.Message);
            await _toast!.ShowError("Nieokreślony błąd operacji");
        }
    }

    private void ActionBeginHandler(ActionEventArgs<GetAllCategoriesResponse> args)
    {
        if (args.RequestType == Action.Add)
        {
            if (SelectedParentId == null || SelectedParentId == 0)
            {
                args.Data.ParentId = 1;
            }
            else
            {
                args.Data.ParentId = SelectedParentId;
            }
        }
    }

    private async void OnSelectRowItem(RowSelectingEventArgs<GetAllCategoriesResponse> args)
    {
        if (args.Data.Id != 0)
        {
            SelectedParentId = args.Data.Id;
        }
    }


    private async void RowSelected(RowSelectEventArgs<GetAllCategoriesResponse> args)
    {
        if (args.Data.Id != null)
        {
            SelectedCategory = args.Data;
            SelectedParentId = args.Data.Id;
        }
    }

    private async void ActionFailureHandler(Syncfusion.Blazor.Grids.FailureEventArgs e)
    {
        var listExceptions = e.Error as ListExceptions;
        if (listExceptions != null)
        {
            await _toast!.ShowError(listExceptions.Messages.String());
            return;
        }
        else
        {
            await _toast!.ShowError("Nieokreślony błąd operacji".Tr());
        }
    }

    private async void OnStateChanged(ComponentBase? source, string property)
    {
        if (AppStateService.AppScreenSize <= AppScreenSize.Medium)
        {
            columnIndex = 1;
        }
        else
        {
            columnIndex = 2;
        }
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            await InvokeAsync(StateHasChanged);
        }
    }

    private async void ActionCompleteHandler(ActionEventArgs<GetAllCategoriesResponse> args)
    {
        // TODO W stringach zapisane, Action.Add?
        if (args.Action is "Add" or "Update")
        {
            Console.WriteLine(args.RequestType);
            if (args.RequestType == Action.Save)
            {
                await _defaultGrid.RefreshAsync();
            }
        }
        else
        {
            args.Cancel = true;
        }
    }

    void IDisposable.Dispose()
    {
        AppStateService.StateChanged -= OnStateChanged;
    }
}
