@page "/admin/customerGroups/index"
@attribute [Authorize(Roles = $"{Role.Administrator},{Role.TradeManager},{Role.Trade},{Role.Production}")]

@using EMessa.Base.Enums
@using EMessa.Core.Features.CustomerGroups.Commands.DeleteCustomerGroup
@using EMessa.Core.Features.CustomerGroups.Queries.GetAll
@using EMessa.Core.Features.Customers.Commands.Delete
@using EMessa.Core.Features.Customers.Queries.GetAll
@using EMessa.Web.Components.Editors
@using EMessa.Web.Components.Grids
@using EMessa.Web.Constants
@using EMessa.Web.Pages.Admin.Customers.Parts
@using EMessa.Web.Resources
@using Microsoft.Extensions.Localization
@using Radzen
@using Radzen.Blazor

@inject ILogger<Index> Logger
@inject NavigationManager Navigation
@implements IDisposable

<IndexCard Title=@("Grupy klientów".Tr())>
    <ChildContent>
        <div class="w-full">
            <GridToolbar Items="@([GridToolbar.Add, GridToolbar.Search])"
                         OnAction="@HandleAction"
                         SearchTextChanged="@HandleSearch">
            </GridToolbar>
            <ProgressBar IsLoading="@_isLoading" Visible="@_isLoading" />
            <RadzenDataGrid @ref="_grid" TItem="GetAllCustomerGroupsResponse"
                            Data="@_gridData" Count="@_count" LoadData="@LoadData" IsLoading="@_isLoading"
                            AllowFiltering FilterMode="FilterMode.Simple"
                            FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                            AllowPaging PageSize="@PageSize.DefaultPageSize" PageSizeOptions="@PageSize.Pages20To200" PageSizeText="@PageSize.FormatPaginationInfo(_grid)"
                            AllowSorting AllowMultiColumnSorting
                            AllowColumnResize
                            Density="Density.Compact"
                            EmptyText="@PageSize.NoDataText"
                            RowDoubleClick="@((args) => EditCustomerGroup(args.Data))">

                <LoadingTemplate />

                <Columns>
                    <RadzenDataGridColumn Title=@(AppStateService.IsLargeScreen ? "Akcje".Tr() : string.Empty)
                                          Filterable="false" Sortable="false"
                                          TextAlign="Radzen.TextAlign.Center" Width="@(AppStateService.IsLargeScreen ? "55px" : "30px")"
                                          Frozen="true" FrozenPosition="Radzen.FrozenColumnPosition.Right">
                        <Template Context="data">
                            <div class="@(AppStateService.IsLargeScreen ? "flex flex-row gap-2 justify-center" : "flex flex-col gap-2 items-center")">
                                <EditRadzenButton IsAction
                                                  Click="@(args => EditCustomerGroup(data))" />
                                <DeleteRadzenButton IsAction
                                                    Click="@(args => DeleteCustomerGroup(data))" />
                            </div>
                        </Template>
                    </RadzenDataGridColumn>

                    <RadzenDataGridColumn Property="@nameof(GetAllCustomerGroupsResponse.Code)" Title=@("Kod".Tr())
                                          Width="15%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                          Sortable Filterable />

                    <RadzenDataGridColumn Property="@nameof(GetAllCustomerGroupsResponse.Name)" Title=@("Nazwa".Tr())
                                          Width="25%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                          Sortable Filterable />

                    <RadzenDataGridColumn Property="@nameof(GetAllCustomerGroupsResponse.Description)" Title=@("Opis".Tr())
                                          Width="30%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                          Sortable Filterable />

                    <RadzenDataGridColumn Property="@nameof(GetAllCustomerGroupsResponse.Type)" Title=@("Typ".Tr())
                                          Width="15%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                          Sortable Filterable FilterProperty="@nameof(GetAllCustomerGroupsResponse.Type)"
                                          FilterOperator="FilterOperator.Equals" FilterValue="@_articleTypeFilter">
                        <FilterTemplate>
                            <RadzenDropDown @bind-Value="@_articleTypeFilter"
                                            Data="@_allArticleTypeFilter"
                                            TValue="CustomerGroupType?"
                                            AllowClear="true">
                                <Template Context="data">
                                    @GetArticleType(data)
                                </Template>
                                <ValueTemplate Context="data">
                                    @GetArticleType(data)
                                </ValueTemplate>
                            </RadzenDropDown>
                        </FilterTemplate>
                        <Template Context="data">
                            @GetArticleType(data.Type)
                        </Template>
                    </RadzenDataGridColumn>

                    <RadzenDataGridColumn Property="@nameof(GetAllCustomerGroupsResponse.CustomersCount)" Title=@("Liczba klientów".Tr())
                                          Width="10%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                          Sortable Filterable FilterMode="FilterMode.SimpleWithMenu" />

                    <RadzenDataGridColumn Visible="@(!AppStateService.IsLargeScreen)" Filterable="false" Sortable="false">
                        <Template Context="data">
                            <EMessa.Web.Pages.Admin.CustomerGroups.Parts.CustomerGroupCard CustomerGroup="@data" />
                        </Template>
                    </RadzenDataGridColumn>
                </Columns>
            </RadzenDataGrid>
        </div>
    </ChildContent>
</IndexCard>

@code {
    private RadzenDataGrid<GetAllCustomerGroupsResponse> _grid = new();
    private IEnumerable<GetAllCustomerGroupsResponse> _gridData = [];
    private int _count;
    private bool _isLoading;
    private string? _searchString;
    private List<CustomerGroupType> _allArticleTypeFilter = Enum.GetValues<CustomerGroupType>().ToList();
    private CustomerGroupType? _articleTypeFilter;

    public static string GetArticleType(dynamic type)
    {
        if (type is CustomerGroupType articleType)
        {
            return articleType.GetDisplayName().Tr();
        }

        if (type is string typeStr && Enum.TryParse<CustomerGroupType>(typeStr, ignoreCase: true, out var parsedStatus))
        {
            return parsedStatus.GetDisplayName().Tr();
        }

        return "";
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await _grid.Reload();
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    private async Task LoadData(LoadDataArgs args)
    {
        _isLoading = true;

        var sieveModel = args.ToSieveModel().ApplyGlobalSearchFilter(_searchString);
        var response = await Mediator.Send(new GetAllCustomerGroupsQuery(sieveModel));

        if (response.Succeeded)
        {
            _gridData = response.Data;
            _count = response.TotalCount;
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError);
            _gridData = [];
            _count = 0;
        }

        _isLoading = false;
        await InvokeAsync(StateHasChanged);
    }

    private void AddNewCustomerGroup() => Navigation.NavigateTo("/admin/customerGroups/edit");

    private void EditCustomerGroup(GetAllCustomerGroupsResponse customerGroup) => Navigation.NavigateTo($"/admin/customerGroups/edit/{customerGroup.Id}");

    private async Task DeleteCustomerGroup(GetAllCustomerGroupsResponse customerGroup)
    {

        var confirmed = await ModalDialogService.DeleteConfirmationByName(customerGroup.Name);

        if (confirmed == true)
        {
            _isLoading = true;

            var response = await Mediator.Send(new DeleteCustomerGroupCommand(customerGroup.Id));

            if (response.Succeeded)
                ToastService.Show(ToastType.DeleteSuccess);
            else
                ToastService.Show(ToastType.DeleteError, string.Join(',', response.Messages));

            await _grid.Reload();
        }

        _isLoading = false;
        StateHasChanged();
    }

    private void HandleAction(string item)
    {
        switch (item)
        {
            case GridToolbar.Add:
                AddNewCustomerGroup();
                break;
        }
    }

    private void HandleSearch(string searchString)
    {
        _searchString = searchString;
        _grid.Reload();
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        if (AppStateService != null)
        {
            AppStateService.StateChanged += OnStateChanged;
        }
    }

    void IDisposable.Dispose()
    {
        if (AppStateService != null)
        {
            AppStateService.StateChanged -= OnStateChanged;
        }
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }
}
