@page "/admin/customerGroups/addCustomers/{CustomerGroupId:int}"
@attribute [Authorize(Roles = $"{Role.Administrator},{Role.SaleManager}")]

@using EMessa.Core.Features.CustomerGroups.Commands.AddCustomerToGroup
@using EMessa.Core.Features.CustomerGroups.Queries.GetCustomerGroupMembers
@using EMessa.Core.Features.Customers.Commands.Delete
@using EMessa.Core.Features.Customers.Queries.GetAll
@using EMessa.Core.Features.Sales.Commands.AddSaleCustomer
@using EMessa.Core.Features.Sales.Queries.GetSaleCustomers
@using EMessa.Web.Components.Editors
@using EMessa.Web.Components.Grids
@using EMessa.Web.Constants
@using EMessa.Web.Pages.Admin.Customers.Parts
@using Radzen
@using Radzen.Blazor

@inject ILogger<AddCutomersToGroup> Logger
@inject NavigationManager Navigation
@implements IDisposable

<RowHighlightedAlertInfo Text="@("Kolorem oznaczono klientów, którzy są już członkami tej grupy.".Tr())" />

<div class="w-full">
    <RadzenFieldset Text="@("Klienci do wybrania".Tr())">

        <RadzenStack Orientation="Orientation.Horizontal" Class="p-1 justify-start items-start gap-2 flex-wrap">
            <RadzenButton Text=@("Dodaj do listy wybranych".Tr().AddText($" ({_customersToAdd?.Count ?? 0})")) Icon="group_add"
                          ButtonStyle="ButtonStyle.Primary" Click="@AddCustomers"
                          Disabled="@(_isLoading || _customersToAdd?.Any() != true)" />
            <GridSearch @ref="_gridSearch" ValueChanged="@_availableCustomersGrid.Reload" CssClass="ml-auto" />
        </RadzenStack>
        <ProgressBar IsLoading="@_isLoading" Visible="@_isLoading" />
        <RadzenDataGrid @ref="_availableCustomersGrid" TItem="GetAllCustomersResponse"
                        Data="@_availableCustomers" Count="@_availableCustomersTotalCount" LoadData="@LoadData" IsLoading="@_isLoading"
                        AllowFiltering FilterMode="FilterMode.Simple"
                        FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                        AllowPaging PageSize="@PageSize.DefaultPageSize" PageSizeOptions="@PageSize.Pages20To200" PageSizeText="@PageSize.FormatPaginationInfo(_availableCustomersGrid)"
                        AllowSorting AllowMultiColumnSorting
                        AllowColumnResize
                        Density="Density.Compact"
                        EmptyText="@PageSize.NoDataText"
                        SelectionMode="DataGridSelectionMode.Multiple"
                        @bind-Value="@_customersToAdd"
                        RowRender="@OnRowRender"
                        GridLines="@RadzenDataGridConstants.DefaultDataGridLines">

            <LoadingTemplate />

            <Columns>
                <RadzenDataGridColumn Width="35px" Sortable="false" Filterable="false">
                    <HeaderTemplate>
                        <RadzenCheckBox TabIndex="-1" TriState="false" TValue="bool?" InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "Select all items" } })"
                                        Value="@(_customersToAdd == null || _customersToAdd?.Any() != true ? false : !_availableCustomers.All(i => _customersToAdd.Contains(i)) ? null : _availableCustomers.Any(i => _customersToAdd.Contains(i)))"
                                        Change="@(args => _customersToAdd = args == true ? _availableCustomers.ToList() : null)" />
                    </HeaderTemplate>
                    <Template Context="data">
                        <RadzenCheckBox TabIndex="-1"
                                        TriState="false"
                                        Value="@(_customersToAdd != null && _customersToAdd.Contains(data))"
                                        InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "Select item" } })"
                                        TValue="bool" />
                    </Template>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.ShortName)" Title=@("Skrócona nazwa".Tr())
                                      Width="120px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.Name)" Title=@("Nazwa".Tr())
                                      Width="200px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.Address)" Title=@("Adres".Tr())
                                      Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.PostCode)" Title=@("Kod pocztowy".Tr())
                                      Width="120px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.City)" Title=@("Miasto".Tr())
                                      Width="150px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.TaxIdentificationNumber)" Title=@("NIP".Tr())
                                      Width="200px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.BranchName)" Title=@("Oddział".Tr())
                                      Width="200px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterProperty="@nameof(GetAllCustomersResponse.BranchId)" FilterOperator="FilterOperator.Equals" FilterValue="@_branchFilter"
                                      WhiteSpace="WhiteSpace.Wrap">
                    <FilterTemplate>
                        <BranchesSelector @bind-Value="@_branchFilter"
                                          ShowActiveOnly="false"
                                          Clearable="true" />
                    </FilterTemplate>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.IsActive)" Title=@("Aktywny".Tr()) Width="100px"
                                      TextAlign="TextAlign.Center" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable>
                    <Template Context="customer">
                        <RadzenCheckBox @bind-Value="customer.IsActive" Disabled TValue="bool" />
                    </Template>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.IsPrivate)"
                                      Title=@("Os. prywatna".Tr()) Width="120px"
                                      TextAlign="TextAlign.Center" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable>
                    <Template Context="customer">
                        <RadzenCheckBox @bind-Value="customer.IsPrivate" Disabled TValue="bool" />
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Visible="@(!AppStateService.IsLargeScreen)" Filterable="false" Sortable="false">
                    <Template Context="customer">
                        <CustomerCard Customer="@customer" />
                    </Template>
                </RadzenDataGridColumn>
            </Columns>
        </RadzenDataGrid>
    </RadzenFieldset>

    <RadzenFieldset Text="@("Wybrani klienci".Tr())">
        <RadzenStack Orientation="Orientation.Horizontal" Class="p-1 justify-start items-start gap-2 flex-wrap">
            <RadzenButton Text=@("Usuń z listy wybranych".Tr().AddText($" ({_customersToRemove?.Count ?? 0})")) Icon="group_remove"
                          ButtonStyle="ButtonStyle.Primary" Click="@RemoveCustomers"
                          Disabled="@(_isLoading || _customersToRemove?.Any() != true)" />
        </RadzenStack>
        <RadzenDataGrid @ref="_pendingCustomersGrid" TItem="GetAllCustomersResponse"
                        Data="@_pendingCustomers" Count="@(_pendingCustomers.Count)" IsLoading="@_isLoading"
                        AllowFiltering FilterMode="FilterMode.Simple"
                        FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                        AllowPaging PageSize="@PageSize.DefaultPageSize" PageSizeOptions="@PageSize.Pages20To200" PageSizeText="@PageSize.FormatPaginationInfo(_pendingCustomersGrid)"
                        AllowSorting AllowMultiColumnSorting
                        AllowColumnResize
                        Density="Density.Compact"
                        EmptyText="@PageSize.NoDataText"
                        SelectionMode="DataGridSelectionMode.Multiple"
                        @bind-Value="@_customersToRemove"
                        RowRender="@OnRowRender"
                        GridLines="@RadzenDataGridConstants.DefaultDataGridLines">

            <LoadingTemplate />

            <Columns>
                <RadzenDataGridColumn Width="35px" Sortable="false" Filterable="false">
                    <HeaderTemplate>
                        <RadzenCheckBox TabIndex="-1" TriState="false" TValue="bool?" InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "Select all items" } })"
                                        Value="@(_customersToRemove == null || _customersToRemove?.Any() != true ? false : !_pendingCustomers.All(i => _customersToRemove.Contains(i)) ? null : _pendingCustomers.Any(i => _customersToRemove.Contains(i)))"
                                        Change="@(args => _customersToRemove = args == true ? _pendingCustomers.ToList() : null)" />
                    </HeaderTemplate>
                    <Template Context="data">
                        <RadzenCheckBox TabIndex="-1"
                                        TriState="false"
                                        Value="@(_customersToRemove != null && _customersToRemove.Contains(data))"
                                        InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "Select item" } })"
                                        TValue="bool" />
                    </Template>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.ShortName)" Title=@("Skrócona nazwa".Tr())
                                      Width="120px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.Name)" Title=@("Nazwa".Tr())
                                      Width="200px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.Address)" Title=@("Adres".Tr())
                                      Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.PostCode)" Title=@("Kod pocztowy".Tr())
                                      Width="120px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.City)" Title=@("Miasto".Tr())
                                      Width="150px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.TaxIdentificationNumber)" Title=@("NIP".Tr())
                                      Width="200px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.BranchName)" Title=@("Oddział".Tr())
                                      Width="200px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterProperty="@nameof(GetAllCustomersResponse.BranchId)" FilterOperator="FilterOperator.Equals" FilterValue="@_branchFilter"
                                      WhiteSpace="WhiteSpace.Wrap">
                    <FilterTemplate>
                        <BranchesSelector @bind-Value="@_branchFilter"
                                          ShowActiveOnly="false"
                                          Clearable="true" />
                    </FilterTemplate>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.IsActive)" Title=@("Aktywny".Tr()) Width="100px"
                                      TextAlign="TextAlign.Center" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable>
                    <Template Context="customer">
                        <RadzenCheckBox @bind-Value="customer.IsActive" Disabled TValue="bool" />
                    </Template>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.IsPrivate)"
                                      Title=@("Os. prywatna".Tr()) Width="120px"
                                      TextAlign="TextAlign.Center" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable>
                    <Template Context="customer">
                        <RadzenCheckBox @bind-Value="customer.IsPrivate" Disabled TValue="bool" />
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Visible="@(!AppStateService.IsLargeScreen)" Filterable="false" Sortable="false">
                    <Template Context="customer">
                        <CustomerCard Customer="@customer" />
                    </Template>
                </RadzenDataGridColumn>
            </Columns>
        </RadzenDataGrid>
    </RadzenFieldset>
    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" class="rz-m-4">
        <SaveRadzenButton Text="@("Dodaj klientów do grupy".Tr())" Click="@OnSubmit"
                          Disabled="@(_isLoading || _pendingCustomers?.Any() != true)" />
        <CancelRadzenButton Click="@OnCancel" Disabled="@_isLoading" />
    </RadzenStack>
</div>

@code {
    [Parameter] public required int CustomerGroupId { get; set; }
    private RadzenDataGrid<GetAllCustomersResponse> _availableCustomersGrid = new();
    private IEnumerable<GetAllCustomersResponse> _availableCustomers = [];
    private int _availableCustomersTotalCount;
    private RadzenDataGrid<GetAllCustomersResponse> _pendingCustomersGrid = new();
    private List<GetAllCustomersResponse> _pendingCustomers = new();
    private bool _isLoading;
    private GridSearch? _gridSearch;
    private int? _branchFilter;
    private IList<GetAllCustomersResponse>? _customersToAdd;
    private IList<GetAllCustomersResponse>? _customersToRemove;
    private List<GetAllCustomersResponse> _customersAlreadyInGroup = new();

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await _availableCustomersGrid.Reload();
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    private async Task GetCustomerGroupMembersAsync()
    {
        _isLoading = true;

        var response = await Mediator.Send(new GetCustomerGroupMembersQuery(CustomerGroupId));

        if (response.Succeeded)
        {
            _customersAlreadyInGroup = response.Data.ToList();
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError);
        }

        _isLoading = false;
    }

    private async Task LoadData(LoadDataArgs args)
    {
        _isLoading = true;

        await GetCustomerGroupMembersAsync();

        var sieveModel = args.ToSieveModel().ApplyGlobalSearchFilter(_gridSearch?.SearchValue);
        var response = await Mediator.Send(new GetAllCustomersQuery(sieveModel));

        if (response.Succeeded)
        {
            _availableCustomers = response.Data.Where(x => _pendingCustomers.All(y => y.Id != x.Id));
            _availableCustomersTotalCount = response.TotalCount;
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError);
            _availableCustomers = [];
            _availableCustomersTotalCount = 0;
        }

        _isLoading = false;
        await InvokeAsync(StateHasChanged);
    }

    void OnRowRender(RowRenderEventArgs<GetAllCustomersResponse> args)
    {
        if (_customersAlreadyInGroup.Any(x => x.Id == args.Data.Id))
        {
            args.Attributes.Add("class", "highlighted-grid-row");
        }
    }

    private void AddCustomers()
    {
        if (_customersToAdd != null && _customersToAdd.Any())
        {
            _pendingCustomers.AddRange(_customersToAdd);
            _pendingCustomers = _pendingCustomers.Distinct().ToList();
        }
        _customersToAdd = null;

        _availableCustomersGrid.Reload();
        _pendingCustomersGrid.Reload();
    }

    private void RemoveCustomers()
    {
        if (_customersToRemove != null && _customersToRemove.Any())
        {
            _pendingCustomers.RemoveAll(c => _customersToRemove.Contains(c));
        }
        _customersToRemove = null;

        _availableCustomersGrid.Reload();
        _pendingCustomersGrid.Reload();
    }

    private void OnCancel() => Navigation.NavigateTo($"/admin/customerGroups/edit/{CustomerGroupId}");

    private async Task OnSubmit()
    {
        if (_pendingCustomers.Count == 0)
        {
            ToastService.Show(ToastType.SaveError, "Nie wybrano żadnych klientów do dodania.".Tr());
            return;
        }

        if (_pendingCustomers.All(x => _customersAlreadyInGroup.Any(y => y.Id == x.Id)))
        {
            ToastService.ShowWarning("Wszyscy wybrani klienci są członkami tej grupy.".Tr());
            return;
        }

        var command = new AddCustomerToGroupCommand(
            CustomerGroupId, _pendingCustomers.Where(x => _customersAlreadyInGroup.All(y => y.Id != x.Id)).Select(c => c.Id).ToList());
        var result = await Mediator.Send(command);

        if (result.Succeeded)
        {
            ToastService.Show(ToastType.SaveSuccess);
            Navigation.NavigateTo($"/admin/customerGroups/edit/{CustomerGroupId}");
        }
        else
        {
            ToastService.Show(ToastType.SaveError, string.Join(',', result.Messages));
        }
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        if (AppStateService != null)
        {
            AppStateService.StateChanged += OnStateChanged;
        }
    }

    void IDisposable.Dispose()
    {
        if (AppStateService != null)
        {
            AppStateService.StateChanged -= OnStateChanged;
        }
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }
}
