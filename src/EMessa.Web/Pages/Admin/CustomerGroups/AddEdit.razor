@page "/admin/customerGroups/edit/{CustomerGroupId:int?}"
@attribute [Authorize(Roles = Role.Administrator)]

@using EMessa.Base.Enums
@using EMessa.Core.Features.CustomerGroups.Commands.AddEditCustomerGroup
@using EMessa.Core.Features.CustomerGroups.Queries.GetById
@using EMessa.Core.Features.Customers.Commands.AddEdit
@using EMessa.Core.Features.Customers.Queries.Get
@using EMessa.Web.Components.Editors
@using EMessa.Web.Pages.Admin.Customers.Parts
@using Radzen
@using Radzen.Blazor

@inject NavigationManager Navigation
@inject ILogger<AddEdit> Logger
@inject DialogService DialogService

<IndexCard Title=@(_isEditMode ? "Edytuj Grupę Klientów".Tr().AddText(" [ " + _addEditCustomer.Name + " ] ") : "Dodaj Grupę Klientów".Tr())>
    <ProgressBar IsLoading="@_isLoading" Visible="@_isLoading" />
    <RadzenTemplateForm TItem="AddEditCustomerGroupCommand"
                        Data="@_addEditCustomer"
                        Submit="@OnSubmit">
        <FluentValidationForRadzenComponent Validator="@(new AddEditCustomerGroupValidator())" />

        <RadzenTabs RenderMode="TabRenderMode.Server">
            <Tabs>
                <RadzenTabsItem Text="@("Dane podstawowe".Tr())">
                    <RadzenRow AlignItems="AlignItems.Start" RowGap="0.5rem" Gap="1rem">

                        <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                            <RadzenLabel Text=@("Kod".Tr().AddText('*')) />
                            <RadzenTextBox @bind-Value="_addEditCustomer.Code" class="w-full" />
                            <ValidationMessage For="@(() => _addEditCustomer.Code)" class="validation-message" />
                        </RadzenColumn>

                        <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                            <RadzenLabel Text=@("Typ".Tr().AddText('*')) />
                            <RadzenDropDown @bind-Value="@_addEditCustomer.Type"
                                            Data="@_allArticleTypeFilter"
                                            TValue="CustomerGroupType"
                                            AllowClear="true">
                                <Template Context="type">
                                    @GetArticleType(type)
                                </Template>
                                <ValueTemplate Context="type">
                                    @GetArticleType(type)
                                </ValueTemplate>
                            </RadzenDropDown>
                            <ValidationMessage For="@(() => _addEditCustomer.Type)" class="validation-message" />
                        </RadzenColumn>

                        <RadzenColumn Size="12" SizeMD="12" SizeLG="12">
                            <RadzenLabel Text=@("Nazwa".Tr().AddText('*')) />
                            <RadzenTextBox @bind-Value="_addEditCustomer.Name" class="w-full" />
                            <ValidationMessage For="@(() => _addEditCustomer.Name)" class="validation-message" />
                        </RadzenColumn>

                        <RadzenColumn Size="12" SizeMD="12" SizeLG="12">
                            <RadzenLabel Text=@("Opis".Tr()) />
                            <RadzenTextArea Rows="2" @bind-Value="_addEditCustomer.Description" class="w-full" />
                            <ValidationMessage For="@(() => _addEditCustomer.Description)" class="validation-message" />
                        </RadzenColumn>

                    </RadzenRow>
                </RadzenTabsItem>
                <RadzenTabsItem Text="@("Członkowie grupy".Tr().AddText(" (" + _groupMembers + ")"))" Disabled=@(!_isEditMode || _isLoading)>
                    <EMessa.Web.Pages.Admin.CustomerGroups.Parts.CustomerGroupMembersGrid CustomerGroupId="@CustomerGroupId!.Value"
                                                                                          GroupMembers="@((arg) => _groupMembers = arg)" />
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>

        <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" class="my-2 mx-4">
            <SaveRadzenButton Click="@OnSubmit" Disabled=@_isLoading />
            <CancelRadzenButton Click="@OnCancel" Disabled=@_isLoading />
        </RadzenStack>
    </RadzenTemplateForm>
</IndexCard>

@code {
    [Parameter] public int? CustomerGroupId { get; set; }

    private AddEditCustomerGroupCommand _addEditCustomer = new();
    private List<CustomerGroupType> _allArticleTypeFilter = Enum.GetValues<CustomerGroupType>().ToList();
    private bool _isLoading;
    private int _groupMembers = 0;

    private bool _isEditMode => (CustomerGroupId is > 0);

    public static string GetArticleType(dynamic type)
    {
        if (type is CustomerGroupType articleType)
        {
            return articleType.GetDisplayName().Tr();
        }

        if (type is string typeStr && Enum.TryParse<CustomerGroupType>(typeStr, ignoreCase: true, out var parsedStatus))
        {
            return parsedStatus.GetDisplayName().Tr();
        }

        return "";
    }

    protected override async Task OnInitializedAsync()
    {
        if (_isEditMode)
        {
            _isLoading = true;

            var response = await Mediator.Send(new GetCustomerGroupByIdQuery(CustomerGroupId!.Value));

            if (response.Succeeded)
            {
                _addEditCustomer = Mapper.Map<AddEditCustomerGroupCommand>(response.Data);
                _groupMembers = response.Data.Customers.Count;
            }
            else
            {
                ToastService.Show(ToastType.LoadDataError, string.Join(',', response.Messages));
            }

            _isLoading = false;
        }
    }

    private async Task OnSubmit()
    {
        try
        {
            _isLoading = true;

            var result = await Mediator.Send(_addEditCustomer);

            if (result.Succeeded)
            {
                ToastService.Show(ToastType.SaveSuccess);
                CustomerGroupId = result.Data;
                await InvokeAsync(StateHasChanged);
            }
            else
            {
                ToastService.Show(ToastType.SaveError, string.Join(',', result.Messages));
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas zapisywania grupy klientów");
            ToastService.Show(ToastType.SaveError);
        }
        finally
        {
            _isLoading = false;
        }
    }

    private void OnCancel()
    {
        Navigation.NavigateTo("/admin/customerGroups/index");
    }
}
