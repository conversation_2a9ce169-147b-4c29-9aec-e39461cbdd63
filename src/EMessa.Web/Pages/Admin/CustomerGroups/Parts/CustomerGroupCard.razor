@using EMessa.Core.Features.CustomerGroups.Queries.GetAll
@using EMessa.Core.Features.Customers.Queries
@using EMessa.Core.Features.Customers.Queries.GetAll
@using Radzen.Blazor

<div class="p-3 bg-white shadow-md rounded-lg border border-gray-200 text-wrap">
    <div class="mb-2 text-xl font-semibold text-gray-800">
        @CustomerGroup.Code
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Nazwa".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@CustomerGroup.Name</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Opis".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@CustomerGroup.Description</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Typ".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@CustomerGroup.Type</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Liczba klientów".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@CustomerGroup.CustomersCount</span>
    </div>
</div>

@code {
    [Parameter] public GetAllCustomerGroupsResponse CustomerGroup { get; set; } = new();
}