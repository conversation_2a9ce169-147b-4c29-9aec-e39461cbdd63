@page "/admin/factories/edit/{FactoryId:int?}"
@attribute [Authorize(Roles = Role.Administrator)]

@using EMessa.Core.Features.Factories.Commands.AddEdit
@using EMessa.Core.Features.Factories.Queries.Get
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor

@inject NavigationManager Navigation
@inject ILogger<AddEdit> Logger

<IndexCard Title=@(FactoryId is null ? "Dodaj <PERSON>".Tr() : "Edytuj Zakład".Tr())>
    <RadzenTemplateForm TItem="AddEditFactoryRequest"
                        Data="@_addEditFactory"
                        Submit="@OnSubmit">
        <FluentValidationForRadzenComponent Validator="@(new AddEditFactoryValidator())" />

        <RadzenRow AlignItems="AlignItems.Start" RowGap="1.5rem" Gap="1rem" class="rz-p-sm-12">

            <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                <RadzenLabel Text=@("Kod".Tr().AddText('*')) />
                <RadzenTextBox @bind-Value="_addEditFactory.Code" class="w-full" />
                <ValidationMessage For="@(() => _addEditFactory.Code)" class="validation-message" />
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                <RadzenLabel Text=@("Nazwa".Tr().AddText('*')) Component="Name" />
                <RadzenTextBox @bind-Value="_addEditFactory.Name" Name="Name" class="w-full" />
                <ValidationMessage For="@(() => _addEditFactory.Name)" class="validation-message" />
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                <RadzenLabel Text=@("Miasto".Tr()) Component="City" />
                <RadzenTextBox @bind-Value="_addEditFactory.City" Name="City" class="w-full" />
                <ValidationMessage For="@(() => _addEditFactory.City)" class="validation-message" />
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                <RadzenLabel Text=@("Kod pocztowy".Tr()) Component="PostCode" />
                <RadzenTextBox @bind-Value="_addEditFactory.PostCode" Name="PostCode" class="w-full" />
                <ValidationMessage For="@(() => _addEditFactory.PostCode)" class="validation-message" />
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                <RadzenLabel Text=@("Adres".Tr()) Component="Address" />
                <RadzenTextBox @bind-Value="_addEditFactory.Address" Name="Address" class="w-full" />
                <ValidationMessage For="@(() => _addEditFactory.Address)" class="validation-message" />
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                <RadzenLabel Text=@("Telefon".Tr()) Component="Phone" />
                <RadzenTextBox @bind-Value="_addEditFactory.Phone" Name="Phone" class="w-full" />
                <ValidationMessage For="@(() => _addEditFactory.Phone)" class="validation-message" />
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
                <RadzenLabel Text=@("Email".Tr()) Component="Email" />
                <RadzenTextBox @bind-Value="_addEditFactory.Email" Name="Email" class="w-full" />
                <ValidationMessage For="@(() => _addEditFactory.Email)" class="validation-message" />
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="12" SizeLG="6" class="flex items-center min-h-[90px] gap-4">
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="0.5rem">
                    <RadzenSwitch @bind-Value="_addEditFactory.IsActive" Name="IsActive" />
                    <RadzenLabel Text=@("Aktywny".Tr()) Component="IsActive" />
                    <ValidationMessage For="@(() => _addEditFactory.IsActive)" class="validation-message" />
                </RadzenStack>
            </RadzenColumn>

        </RadzenRow>
        <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.End" class="rz-m-4">
            <RadzenButton ButtonType="ButtonType.Submit" Text="@("Zapisz".Tr())" Icon="save" ButtonStyle="ButtonStyle.Success" />
            <RadzenButton Text="@("Anuluj".Tr())" Icon="cancel" ButtonStyle="ButtonStyle.Light" Click="@OnCancel" />
        </RadzenStack>
    </RadzenTemplateForm>
</IndexCard>

@code {
    [Parameter]
    public int? FactoryId { get; set; }

    private AddEditFactoryRequest _addEditFactory = new();

    protected override async Task OnInitializedAsync()
    {
        if (FactoryId is > 0)
        {
            var response = await Mediator.Send(new GetFactoryQuery(FactoryId.Value));

            if (response.Succeeded)
            {
                _addEditFactory = Mapper.Map<AddEditFactoryRequest>(response.Data);
            }
            else
            {
                ToastService.Show(ToastType.LoadDataError, response.Messages);
            }
        }
    }

    private async Task OnSubmit()
    {
        try
        {
            var result = await Mediator.Send(new AddEditFactoryCommand(Mapper.Map<AddEditFactoryRequest>(_addEditFactory)));

            if (result.Succeeded)
            {
                ToastService.Show(ToastType.SaveSuccess);
                Navigation.NavigateTo("/admin/factories/index");
            }
            else
            {
                ToastService.Show(ToastType.SaveError, result.Messages);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas zapisywania fabryki");
            ToastService.Show(ToastType.SaveError);
        }
    }

    private void OnCancel()
    {
        Navigation.NavigateTo("/admin/factories/index");
    }
}
