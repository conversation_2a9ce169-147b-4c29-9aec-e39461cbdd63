@page "/admin/pages/index"
@attribute [Authorize(Roles = Role.Administrator)]

@using EMessa.Core.Features.Pages.Commands.Delete
@using EMessa.Core.Features.Pages.Queries.GetAll
@using EMessa.Web.Constants
@using EMessa.Web.Pages.Admin.Pages.Parts
@using <PERSON><PERSON>zen
@using Radzen.Blazor

@inject ILogger<Index> Logger
@inject IModalDialogService ModalDialogService
@inject NavigationManager Navigation
@implements IDisposable

<IndexCard Title=@("Strony".Tr())>
    <ChildContent>

        <GridToolbar Items="@( [ GridToolbar.Add, GridToolbar.Search])"
                     OnAction="@HandleAction"
                     SearchTextChanged="@HandleSearch">
            <ChildContent>
                <RadzenButton Click="@(_ => _grid.ExpandRows(_gridData))" Text=@("Rozwiń".Tr()) Icon="keyboard_arrow_down" ButtonStyle="ButtonStyle.Secondary" />
                <RadzenButton Click="@_grid.CollapseAll" Text=@("Zwiń".Tr()) Icon="keyboard_arrow_up" ButtonStyle="ButtonStyle.Secondary" />
            </ChildContent>
        </GridToolbar>

        <ProgressBar IsLoading="@_isLoading" />

        <RadzenDataGrid @ref="_grid" TItem="GetAllPagesResponse"
                        Data="@_gridData" Count="@_count" LoadData="@LoadData" IsLoading="@_isLoading"
                        AllowFiltering FilterMode="FilterMode.Simple"
                        FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                        AllowPaging PageSize="@PageSize.DefaultPageSize" PageSizeOptions="@PageSize.Pages20To200" PageSizeText="@PageSize.FormatPaginationInfo(_grid)"
                        AllowSorting AllowMultiColumnSorting
                        AllowColumnResize
                        RowDoubleClick="@((args) => EditPage(args.Data))"
                        Density="Density.Compact"
                        EmptyText="@RadzenDataGridConstants.EmptyText"
                        ExpandMode="DataGridExpandMode.Multiple">
            <LoadingTemplate />

            <Template Context="data">
                <RadzenTabs>
                    <Tabs>
                        @foreach (var lang in SystemConstants.SupportedCulturesList)
                        {
                            var translation = data.Translations.FirstOrDefault(x => x.LanguageCode == lang);
                            <RadzenTabsItem Text="@lang">
                                <RadzenStack Orientation="Orientation.Vertical" Gap="0.2rem">
                                    <RadzenStack Orientation="Orientation.Vertical" Gap="0.2rem">
                                        <RadzenLabel Text="@("Temat".Tr())" />
                                        @if (lang == "pl")
                                        {
                                            <RadzenTextBox Value="@data.Title" ReadOnly />
                                        }
                                        else
                                        {
                                            <RadzenTextBox Value="@translation?.Title"
                                                           ReadOnly />
                                        }
                                    </RadzenStack>
                                    <RadzenStack Orientation="Orientation.Vertical" Gap="0.2rem">
                                        <RadzenLabel Text="@("Treść".Tr())" />
                                        @if (lang == "pl")
                                        {
                                            <RadzenHtmlEditor Value="@data.Content" ShowToolbar="false" Disabled class="min-h-[50px] max-h-[400px]" />
                                        }
                                        else
                                        {
                                            <RadzenHtmlEditor Value="@translation?.Content"
                                                              ShowToolbar="false"
                                                              Disabled
                                                              class="min-h-[50px] max-h-[400px]" />
                                        }
                                    </RadzenStack>
                                </RadzenStack>
                            </RadzenTabsItem>
                        }
                    </Tabs>
                </RadzenTabs>
            </Template>

            <Columns>
                <GridActionsColumn TItem="GetAllPagesResponse"
                                   IsLargeScreen="@AppStateService.IsLargeScreen"
                                   Width="@(AppStateService.IsLargeScreen ? "55px" : "30px")">
                    <ContentTemplate Context="data">
                        <EditRadzenButton IsAction Click="@(_ => EditPage(data))" />
                        <DeleteRadzenButton IsAction Click="@(_ => DeletePage(data))" />
                    </ContentTemplate>
                </GridActionsColumn>

                <RadzenDataGridColumn Property="@nameof(GetAllPagesResponse.Title)" Title=@("Tytuł".Tr())
                                      Width="300px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable WhiteSpace="WhiteSpace.Wrap" />

                <RadzenDataGridColumn Property="@nameof(GetAllPagesResponse.Description)" Title=@("Opis".Tr())
                                      Width="300px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable WhiteSpace="WhiteSpace.Wrap"/>

                <RadzenDataGridColumn Property="@nameof(GetAllPagesResponse.Link)" Title=@("Link".Tr())
                                      Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable WhiteSpace="WhiteSpace.Wrap" />

                <RadzenDataGridColumn Property="@nameof(GetAllPagesResponse.CreatedByFullName)" Title=@("Utworzył".Tr())
                                      Width="220px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterOperator="FilterOperator.Contains" FilterProperty="CreatedBy" WhiteSpace="WhiteSpace.Wrap" />

                <RadzenDataGridColumn Property="@nameof(GetAllPagesResponse.IsDefault)"
                                      Title=@("Domyślna".Tr()) Width="120px"
                                      TextAlign="TextAlign.Center" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable>
                    <Template Context="data">
                        <RadzenCheckBox @bind-Value="data.IsDefault" Disabled TValue="bool" />
                    </Template>
                </RadzenDataGridColumn>


                <RadzenDataGridColumn Property="@nameof(GetAllPagesResponse.IsPublic)"
                                      Title=@("Publiczna".Tr()) Width="120px"
                                      TextAlign="TextAlign.Center" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable>
                    <Template Context="data">
                        <RadzenCheckBox @bind-Value="data.IsPublic" Disabled TValue="bool" />
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Property="@nameof(GetAllPagesResponse.IsPublished)"
                                      Title=@("Opublikowana".Tr()) Width="140px"
                                      TextAlign="TextAlign.Center" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable>
                    <Template Context="data">
                        <RadzenCheckBox @bind-Value="data.IsPublished" Disabled TValue="bool" />
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Visible="@(!AppStateService.IsLargeScreen)" Filterable="false" Sortable="false">
                    <Template Context="data">
                        <PageCard Page="@data" />
                    </Template>
                </RadzenDataGridColumn>
            </Columns>
        </RadzenDataGrid>
    </ChildContent>
</IndexCard>

@code {
    private RadzenDataGrid<GetAllPagesResponse> _grid = new();
    private IEnumerable<GetAllPagesResponse> _gridData = [];
    private int _count;
    private bool _isLoading;
    private string? _searchString;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await _grid.Reload();
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    private async Task LoadData(LoadDataArgs args)
    {
        _isLoading = true;

        try
        {
            var sieveModel = args.ToSieveModel().ApplyGlobalSearchFilter(_searchString);
            var response = await Mediator.Send(new GetAllPagesQuery(sieveModel));

            if (response.Succeeded)
            {
                _gridData = response.Data;
                _count = response.TotalCount;
            }
            else
            {
                ToastService.Show(ToastType.LoadDataError);
                _gridData = [];
                _count = 0;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas ładowania stron");
            ToastService.Show(ToastType.LoadDataError);
            _gridData = [];
            _count = 0;
        }
        finally
        {
            _isLoading = false;
            await InvokeAsync(StateHasChanged);
        }
    }

    private void AddPage() => Navigation.NavigateTo("/admin/pages/edit");

    private void EditPage(GetAllPagesResponse page) => Navigation.NavigateTo($"/admin/pages/edit/{page.Id}");

    private async Task DeletePage(GetAllPagesResponse page)
    {
        try
        {
            var confirmed = await ModalDialogService.DeleteConfirmationByName(page.Title);

            if (confirmed)
            {
                _isLoading = true;

                var response = await Mediator.Send(new DeletePageCommand(page.Id));

                if (response.Succeeded)
                    ToastService.Show(ToastType.DeleteSuccess);
                else
                    ToastService.Show(ToastType.DeleteError, string.Join(',', response.Messages));

                await _grid.Reload();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, $"Błąd podczas usuwania strony ID: {page.Id}");
            ToastService.Show(ToastType.DeleteError);
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private void HandleAction(string item)
    {
        switch (item)
        {
            case GridToolbar.Add:
                AddPage();
                break;
        }
    }

    private void HandleSearch(string searchString)
    {
        _searchString = searchString;
        _grid.Reload();
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        if (AppStateService != null)
        {
            AppStateService.StateChanged += OnStateChanged;
        }
    }

    void IDisposable.Dispose()
    {
        if (AppStateService != null)
        {
            AppStateService.StateChanged -= OnStateChanged;
        }
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }
}
