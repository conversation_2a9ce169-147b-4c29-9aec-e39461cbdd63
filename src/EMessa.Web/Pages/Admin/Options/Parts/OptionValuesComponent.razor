@using EMessa.Core.DataAdapters
@using EMessa.Core.Exceptions
@using EMessa.Core.Expections
@using EMessa.Core.Features.OptionValues.Queries.GetAllValueForOption
@using EMessa.Core.Features.Options.Queries.GetAll
@using Syncfusion.Blazor.Data
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs;
@using EMessa.Web.Components.Grids;
@using Syncfusion.Blazor.RichTextEditor

@inject IMediator _mediator
@inject IMapper _mapper
@inject ILogger<OptionValuesComponent> _logger
@implements IDisposable


<SfGrid @ref="_optionVaulesGrid" TValue="GetAllValuesForOptionResponse" Query="@VaulesForOptionGridQuery" AdaptiveUIMode="@(AppStateService.GridState.AdaptiveMode)"
        EnableAdaptiveUI="@(AppStateService.GridState.EnableAdaptiveUI)"
        RowRenderingMode="@(AppStateService.GridState.RowDirection)"
        AllowFiltering="@(AppStateService.AppScreenSize == AppScreenSize.Large)"
        AllowPaging="true" AllowSorting="true" AllowSelection="true" Toolbar="@(SfGridToolbar.FullToolbar)">
    <GridPageSettings PageSize="20" PageSizes="@(new string[] { "20", "50", "100", "200" })"/>
    <GridFilterSettings Mode="FilterBarMode.Immediate" ShowFilterBarStatus="true" ImmediateModeDelay="500"/>
    <GridEvents OnActionFailure="ActionFailureHandler" OnActionBegin="OnActionHandler" TValue="GetAllValuesForOptionResponse"/>
    <SfDataManager AdaptorInstance="@typeof(OptionValuesDataAdaptor)" Adaptor="Adaptors.CustomAdaptor"/>
    <GridSelectionSettings Mode="@Syncfusion.Blazor.Grids.SelectionMode.Row" Type="Syncfusion.Blazor.Grids.SelectionType.Single"/>
    <GridEditSettings ShowDeleteConfirmDialog="true" AllowEditing="true" AllowAdding="true" AllowDeleting="true" Mode="EditMode.Normal">
        <Validator>
            <EMessa.Web.Components.FluentValidator TValidator="GetAllValuesForOptionValidator" />
        </Validator>
        <Template>
            @{
                var optionValue = (GetAllValuesForOptionResponse)context;
                <div class="py-4 px-2 bg-green-50 min-h-full flex flex-wrap">

                    <div class="px-2 w-full md:w-2/12">
                        <label class="">@("Kod".Tr())</label>
                        <SfTextBox ID="Code" @bind-Value="@(optionValue.Code)"></SfTextBox>
                        <ValidationMessage For="@(() => optionValue.Code)" />
                    </div>
                    <div class="px-2 w-full md:w-6/12">
                        <label class="">@("Wartość".Tr())</label>
                        <SfTextBox ID="Value" @bind-Value="@(optionValue.Value)"></SfTextBox>
                        <ValidationMessage For="@(() => optionValue.Value)" />
                    </div>
                    <div class="px-2 w-full md:w-6/12">
                        <label class="">@("Mnożnik".Tr())</label>
                        <SfNumericTextBox ID="WeightFactor" @bind-Value="@(optionValue.WeightFactor)"></SfNumericTextBox>
                        <ValidationMessage For="@(() => optionValue.WeightFactor)" />
                    </div>
                    <div class="px-2 w-full md:w-6/12">
                        <label class="">@("Dodatkowa strefa przetłoczenia".Tr())</label>
                        <SfNumericTextBox ID="EmbossZoneAddition" @bind-Value="@(optionValue.EmbossZoneAddition)"></SfNumericTextBox>
                        <ValidationMessage For="@(() => optionValue.EmbossZoneAddition)" />
                    </div>
                    <SfCheckBox TChecked="bool" CssClass="m-2 e-label-gap-5"
                                Label="@("Jest domyślną".Tr())"
                                @bind-Checked="@optionValue.IsDefault"/>
                    @* <div class="mr-3"> *@
                    @*     <SfCheckBox TChecked="bool" @bind-Checked="@(optionValue.AutoSetValue)" CssClass="mb-2" /> *@
                    @*     <label class="control-label">@("Automatyczna wartość".Tr())</label> *@
                    @* </div> *@
                    <div class="px-2 w-full ">
                        <label class="">@("Informacja dla użytkownika".Tr())</label>
                         <SfRichTextEditor @bind-Value="@(optionValue.ValueInfo)" Height="300" EditorMode="EditorMode.HTML" EnableHtmlSanitizer="true" EnableResize="true">
                                    <RichTextEditorToolbarSettings Items="@RichTextToolbarHelper.FullToolbar" Type="ToolbarType.Expand" />
                                    <RichTextEditorImageSettings SaveUrl="api/Image/Save" Path="/images/editor/" />
                                </SfRichTextEditor>
                        <ValidationMessage For="@(() => optionValue.ValueInfo)" />
                    </div>
                    <div class="pt-2 w-full">
                        <EMessa.Web.Components.Grids.GridSaveActions TItem="GetAllValuesForOptionResponse" DefaultGrid="@_optionVaulesGrid"></EMessa.Web.Components.Grids.GridSaveActions>
                    </div>
                </div>
            }
        </Template>
    </GridEditSettings>
    <GridColumns>
        <GridColumn Field=@nameof(GetAllValuesForOptionResponse.Id) Visible="false" IsPrimaryKey="true"></GridColumn>
        <GridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllValuesForOptionResponse.Code) HeaderText="@("Kod".Tr())" FilterSettings="@GridFilters.BarContains"></GridColumn>
        <GridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllValuesForOptionResponse.Value) HeaderText="@("Wartość".Tr())" FilterSettings="@GridFilters.BarContains"></GridColumn>
        <GridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllValuesForOptionResponse.WeightFactor) HeaderText="@("Mnożnik".Tr())" FilterSettings="@GridFilters.BarContains"></GridColumn>
        <GridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllValuesForOptionResponse.EmbossZoneAddition) HeaderText="@("Dodatkowa strefa przetłoczenia".Tr())" FilterSettings="@GridFilters.BarContains"></GridColumn>
        <GridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllValuesForOptionResponse.ValueInfo) HeaderText="@("Informacja dla użytkownika".Tr())" FilterSettings="@GridFilters.BarContains"></GridColumn>
        <GridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")"
                    Field=@nameof(GetAllValuesForOptionResponse.IsDefault) TextAlign="TextAlign.Center" AutoFit="true"
                    HeaderText="@("Jest domyślną".Tr())" DisplayAsCheckBox="true"></GridColumn>
        @* <GridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllValuesForOptionResponse.AutoSetValue) TextAlign="TextAlign.Center" AutoFit="true" HeaderText="@("Wartość automatyczna".Tr())" DisplayAsCheckBox="true"></GridColumn> *@
        <GridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Width="60" AllowFiltering="false" TextAlign="TextAlign.Center">
            <GridColumn HideAtMedia="@($"(max-width: {(int)AppScreenSize.Medium}px)")" HeaderText="@("Wartości opcji".Tr())">
                <Template>
                    <OptionValueCardComponent Value="@((GetAllValuesForOptionResponse)context)"/>
                </Template>
            </GridColumn>
        </GridColumn>
    </GridColumns>
</SfGrid>

@code {
    [Parameter]
    public GetAllOptionsResponse Option { get; set; } = new();
    [CascadingParameter]
    public AppToast _toast { get; set; } = new();
    
    public Query VaulesForOptionGridQuery { get; set; } = new();
    private SfGrid<GetAllValuesForOptionResponse> _optionVaulesGrid = new();

    protected override async Task OnInitializedAsync()
    {
        AppStateService.StateChanged += OnStateChanged;
        await base.OnInitializedAsync();
    }

    private async void ActionFailureHandler(Syncfusion.Blazor.Grids.FailureEventArgs e)
    {
        if (e.Error is ListExceptions listExceptions)
        {
            await _toast!.ShowError(listExceptions.Messages.String());
        }
        else
        {
            await _toast!.ShowError("Nieokreślony błąd operacji".Tr());
        }
    }

    protected override void OnParametersSet()
    {
        if (Option != null)
        {
            VaulesForOptionGridQuery.AddParams("optionIdParameter", Option.Id);
            _optionVaulesGrid.Refresh();
        }
        base.OnParametersSet();
    }

    private async void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            await InvokeAsync(StateHasChanged);
        }
    }

    private void OnActionHandler(ActionEventArgs<GetAllValuesForOptionResponse> args)
    {
        var action = args.Action;
        if (action == "Add")
        {
            args.Data.OptionId = Option.Id;
        }
    }

    void IDisposable.Dispose()
    {
        AppStateService.StateChanged -= OnStateChanged;
    }
}
