@page "/admin/options/index"
@attribute [Authorize(Roles = Role.Administrator)]

@using EMessa.Core.Expections
@using EMessa.Core.Features.Options.Queries.GetAll
@using EMessa.Web.Pages.Admin.Options.Parts
@using Syncfusion.Blazor.Data
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Navigations;
@using EMessa.Core.DataAdapters
@using EMessa.Core.Exceptions
@using Syncfusion.Blazor.Inputs;
@using EMessa.Web.Components.Grids;
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.RichTextEditor

@implements IDisposable

<IndexCard Title=@("Opcje produktów".Tr())>
    <ChildContent>
        <SfTab>
            <TabItems>
                <TabItem>
                    <HeaderTemplate>@("Opcje".Tr())</HeaderTemplate>
                    <ContentTemplate>
                        <div class="mt-2">
                            <SfGrid @ref="_defaultGrid" TValue="GetAllOptionsResponse"
                                    AdaptiveUIMode="@(AppStateService.GridState.AdaptiveMode)"
                                    EnableAdaptiveUI="@(AppStateService.GridState.EnableAdaptiveUI)"
                                    RowRenderingMode="@(AppStateService.GridState.RowDirection)"
                                    AllowFiltering="@(AppStateService.AppScreenSize == AppScreenSize.Large)"
                                    AllowPaging="true" AllowSorting="true" AllowSelection="true"
                                    Toolbar="@SfGridToolbar.FullToolbar">
                                <SfDataManager AdaptorInstance="@typeof(OptionsDataAdaptor)"
                                               Adaptor="Adaptors.CustomAdaptor"/>
                                <GridFilterSettings Mode="FilterBarMode.Immediate" ShowFilterBarStatus="true" ImmediateModeDelay="500"/>
                                <GridPageSettings PageSize="20" PageSizes="@(new string[] { "20", "50", "100", "200" })"/>
                                <GridEvents OnActionFailure="@ActionFailureHandler" RowSelected="@OnRowSelected" TValue="GetAllOptionsResponse"/>
                                <GridEditSettings ShowDeleteConfirmDialog="true" AllowEditing="true" AllowAdding="true" AllowDeleting="true" Mode="EditMode.Normal">
                                    <Validator>
                                        <FluentValidator TValidator="GetAllOptionsResponseValidator"/>
                                    </Validator>
                                    <Template>
                                        @{
                                            var option = (GetAllOptionsResponse)context;
                                            <div class="py-4 px-2 bg-green-50 min-h-full flex flex-wrap">
                                                <div class="px-2 w-full md:w-2/12">
                                                    <label class="">@("Lp".Tr())</label>
                                                    <SfNumericTextBox ID="Code" @bind-Value="@(option.No)"/>
                                                </div>
                                                <div class="px-2 w-full md:w-2/12">
                                                    <label class="">@("Kod".Tr())</label>
                                                    <SfTextBox ID="Code" @bind-Value="@(option.Code)"/>
                                                    <ValidationMessage For="@(() => option.Code)"/>
                                                </div>
                                                <div class="px-2 w-full md:w-2/12">
                                                    <label class="">@("Nazwa".Tr())</label>
                                                    <SfTextBox ID="Name" @bind-Value="@(option.Name)"/>
                                                    <ValidationMessage For="@(() => option.Name)"/>
                                                </div>
                                                <div class="px-2 w-full ">
                                                    <label class="">@("Opis".Tr())</label>
                                                    <SfRichTextEditor @bind-Value="@(option.Description)" Height="300" EditorMode="EditorMode.HTML" EnableHtmlSanitizer="true" EnableResize="true">
                                                        <RichTextEditorToolbarSettings Items="@RichTextToolbarHelper.FullToolbar" Type="ToolbarType.Expand"/>
                                                        <RichTextEditorImageSettings SaveUrl="api/Image/Save" Path="/images/editor/"/>
                                                    </SfRichTextEditor>
                                                    <ValidationMessage For="@(() => option.Description)"/>
                                                </div>
                                                <div class="flex flex-col justify-start">
                                                    <SfCheckBox TChecked="bool" CssClass="mx-2 my-1"
                                                                Label="@("Użyj wartości domyslnej".Tr())"
                                                                @bind-Checked="@(option.UseDefaultValue)"/>
                                                    <SfCheckBox TChecked="bool" CssClass="mx-2 my-1"
                                                                Label="@("Użyj w indeksie".Tr())"
                                                                @bind-Checked="@(option.InIndex)"/>
                                                    <SfCheckBox TChecked="bool" CssClass="mx-2 my-1"
                                                                Label="@("Ukryj edytor".Tr())"
                                                                @bind-Checked="@(option.HideEditor)"/>
                                                </div>
                                                <div class="pt-2 w-full">
                                                    <GridSaveActions TItem="GetAllOptionsResponse" DefaultGrid="@_defaultGrid"/>
                                                </div>
                                            </div>
                                        }
                                    </Template>
                                </GridEditSettings>
                                <GridColumns>
                                    @{
                                        var hideAtMin = $"(min-width: {(int)AppScreenSize.Medium}px)";
                                        var hideAtMax = $"(max-width: {(int)AppScreenSize.Medium}px)";
                                    }
                                    <GridColumn Field=@nameof(GetAllOptionsResponse.Id) Visible="false" IsPrimaryKey="true"/>
                                    <GridColumn HideAtMedia="@hideAtMin" Width="60"
                                                Field=@nameof(GetAllOptionsResponse.No) AllowEditing="false"
                                                HeaderText="@("Lp".Tr())" FilterSettings="@GridFilters.BarContains"/>
                                    <GridColumn HideAtMedia="@hideAtMin" Field=@nameof(GetAllOptionsResponse.Code)
                                                HeaderText="@("Kod".Tr())" FilterSettings="@GridFilters.BarContains"/>
                                    <GridColumn HideAtMedia="@hideAtMin" Field=@nameof(GetAllOptionsResponse.Name)
                                                HeaderText="@("Nazwa".Tr())" FilterSettings="@GridFilters.BarContains"/>
                                    <GridColumn HideAtMedia="@hideAtMin"
                                                Field=@nameof(GetAllOptionsResponse.Description)
                                                HeaderText="@("Opis".Tr())" FilterSettings="@GridFilters.BarContains"/>
                                    <GridColumn HideAtMedia="@hideAtMin"
                                                Field=@nameof(GetAllOptionsResponse.UseDefaultValue)
                                                TextAlign="TextAlign.Center" AutoFit="true"
                                                HeaderTextAlign="TextAlign.Left"
                                                HeaderText="@("Użyj wart. domyślnej".Tr())"
                                                DisplayAsCheckBox="true"/>
                                    <GridColumn HideAtMedia="@hideAtMin" Field=@nameof(GetAllOptionsResponse.InIndex)
                                                TextAlign="TextAlign.Center" AutoFit="true"
                                                HeaderTextAlign="TextAlign.Left" HeaderText="@("Użyj w indeksie".Tr())"
                                                DisplayAsCheckBox="true"/>
                                    <GridColumn HideAtMedia="@hideAtMin" Field=@nameof(GetAllOptionsResponse.HideEditor)
                                                TextAlign="TextAlign.Center" HeaderTextAlign="TextAlign.Left"
                                                AutoFit="true" HeaderText="@("Ukryj edytor".Tr())"
                                                DisplayAsCheckBox="true"/>
                                    <GridColumn HideAtMedia="@hideAtMax" HeaderText="@("Opcje produktów".Tr())"
                                                Field=@nameof(GetAllOptionsResponse.Id)>
                                        <Template>
                                            <OptionCardComponent Option="@((GetAllOptionsResponse)context)"/>
                                        </Template>
                                    </GridColumn>
                                </GridColumns>
                            </SfGrid>
                            <div class="mt-4">
                                <OptionsTabPageComponent Option="@_selectedOption"/>
                            </div>
                        </div>
                    </ContentTemplate>
                </TabItem>
                <TabItem>
                    <HeaderTemplate>@("Tłumaczenia wartości opcji".Tr())</HeaderTemplate>
                    <ContentTemplate>
                        <div class="mt-2">
                            <OptionsValuesTranslationsComponent/>
                        </div>
                    </ContentTemplate>
                </TabItem>
            </TabItems>
        </SfTab>
    </ChildContent>
</IndexCard>
<div class="row">
    <div class="col-lg-12">
        <SfDialog @ref="_dialog" Width="250px" Visible="false" ShowCloseIcon="true" IsModal="true">
            <DialogTemplates>
                <Header>@("Usuń opcję".Tr())</Header>
                <Content>@("Czy na pewno chcesz usunąć tą opcję".Tr())</Content>
            </DialogTemplates>
        </SfDialog>
    </div>
</div>

@code {
    [CascadingParameter]
    public AppToast Toast { get; set; }

    private SfDialog _dialog = new();
    private SfGrid<GetAllOptionsResponse> _defaultGrid = new();
    private GetAllOptionsResponse _selectedOption = new();
    private readonly FilterSettings _barContainsFilterSettings = new() { Operator = Operator.Contains, Type = Syncfusion.Blazor.Grids.FilterType.FilterBar };

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        AppStateService.StateChanged += OnStateChanged;
    }

    void IDisposable.Dispose()
    {
        AppStateService.StateChanged -= OnStateChanged;
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }

    private async Task ActionFailureHandler(Syncfusion.Blazor.Grids.FailureEventArgs e)
    {
        if (e.Error is ListExceptions listExceptions)
        {
            await Toast.ShowError(listExceptions.Messages.String());
        }
        else
        {
            await Toast.ShowError("Nieokreślony błąd operacji".Tr());
        }
    }

    private void OnRowSelected(RowSelectEventArgs<GetAllOptionsResponse> args)
    {
        _selectedOption = args.Data;
    }
}
