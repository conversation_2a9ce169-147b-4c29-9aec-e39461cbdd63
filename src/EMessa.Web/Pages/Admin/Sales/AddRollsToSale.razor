@page "/admin/sales/addrolls/{SaleId:int}"
@attribute [Authorize(Roles = $"{Role.Administrator},{Role.SaleManager}")]

@using EMessa.Core.Common.Results
@using EMessa.Core.Features.Rolls.Queries
@using EMessa.Core.Features.Rolls.Queries.GetRollsByOptions
@using EMessa.Core.Features.Rolls.Queries.GetRollsByNos
@using EMessa.Core.Features.Sales.Commands.AddEditSaleRoll
@using EMessa.Core.Features.Sales.Queries.GetSaleArticles
@using EMessa.Core.Features.Sales.Queries.GetSaleRolls
@using EMessa.Web.Components.Editors
@using EMessa.Web.Components.Grids
@using EMessa.Web.Components.RadzenComponents.Icons
@using EMessa.Web.Constants
@using EMessa.Web.Pages.Admin.Sales.Parts
@using EMessa.Web.Pages.Admin.Sales.Parts.Cards
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor

@inject ILogger<AddRollsToSale> Logger
@inject NavigationManager Navigation
@implements IDisposable

<RowHighlightedAlertInfo Text="@("Kolorem oznaczono kręgi, które są już dodane do tej promocji.".Tr())" />

<div class="w-full">
    <RadzenFieldset Text="@("Kręgi do wybrania".Tr())" class="p-1!">

        <RadzenRow class="mb-3 w-full">
            <RadzenColumn Size="12" SizeMD="4" SizeLG="3">
                <RadzenFormField AllowFloatingLabel="false" Text=@("Powłoka".Tr()) class="w-full">
                    <Start>
                        <RadzenIcon Icon="filter_alt"
                                    class="@(_rollNo is null && _coatId.HasValue && _colorId.HasValue && _thickId.HasValue ? "material-icons" : "material-icons-outlined")"
                                    IconColor="@(_rollNo is null && _coatId.HasValue && _colorId.HasValue && _thickId.HasValue ? "green" : "var(--rz-base-500)")" />
                    </Start>
                    <ChildContent>
                        <OptionValueSelector OptionCode="@OptionCode.Coat" CanSetNull
                                             TValue="int?" Value="_coatId" ValueChanged="@(async (value) => { _coatId = value; await GetRollsDataAsync(); })" />
                    </ChildContent>
                    <End>
                        <RadzenIcon Icon="filter_alt_off" IconColor="var(--rz-base-500)"
                                    class="cursor-pointer" Visible="@(_coatId is not null)"
                                    @onclick="@(async () => { _coatId = null; await InvokeAsync(StateHasChanged); })" />
                    </End>

                </RadzenFormField>
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="4" SizeLG="3">
                <RadzenFormField AllowFloatingLabel="false" Text=@("Kolor".Tr()) class="w-full">
                    <Start>
                        <RadzenIcon Icon="filter_alt"
                                    class="@(_rollNo is null && _coatId.HasValue && _colorId.HasValue && _thickId.HasValue ? "material-icons" : "material-icons-outlined")"
                                    IconColor="@(_rollNo is null && _coatId.HasValue && _colorId.HasValue && _thickId.HasValue ? "green" : "var(--rz-base-500)")" />
                    </Start>
                    <ChildContent>
                        <OptionValueSelector OptionCode="@OptionCode.Color" CanSetNull
                                             TValue="int?" Value="_colorId" ValueChanged="@(async (value) => { _colorId = value; await GetRollsDataAsync(); })" />
                    </ChildContent>
                    <End>
                        <RadzenIcon Icon="filter_alt_off" IconColor="var(--rz-base-500)"
                                    class="cursor-pointer" Visible="@(_colorId is not null)"
                                    @onclick="@(async () => { _colorId = null; await InvokeAsync(StateHasChanged); })" />
                    </End>
                </RadzenFormField>
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="4" SizeLG="3">
                <RadzenFormField AllowFloatingLabel="false" Text=@("Grubość".Tr()) class="w-full">
                    <Start>
                        <RadzenIcon Icon="filter_alt"
                                    class="@(_rollNo is null && _coatId.HasValue && _colorId.HasValue && _thickId.HasValue ? "material-icons" : "material-icons-outlined")"
                                    IconColor="@(_rollNo is null && _coatId.HasValue && _colorId.HasValue && _thickId.HasValue ? "green" : "var(--rz-base-500)")" />
                    </Start>
                    <ChildContent>
                        <OptionValueSelector OptionCode="@OptionCode.Thickness" CanSetNull
                                             TValue="int?" Value="_thickId" ValueChanged="@(async (value) => { _thickId = value; await GetRollsDataAsync(); })" />
                    </ChildContent>
                    <End>
                        <RadzenIcon Icon="filter_alt_off" IconColor="var(--rz-base-500)"
                                    class="cursor-pointer" Visible="@(_thickId is not null)"
                                    @onclick="@(async () => { _thickId = null; await InvokeAsync(StateHasChanged); })" />
                    </End>
                </RadzenFormField>
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="4" SizeLG="2">
                <RadzenFormField AllowFloatingLabel="false" Text=@("Numer kręgu".Tr()) class="w-full">
                    <Start>
                        <RadzenIcon Icon="filter_alt"
                                    class="@(_rollNo is not null ? "material-icons" : "material-icons-outlined")"
                                    IconColor="@(_rollNo is not null ? "green" : "var(--rz-base-500)")" />
                    </Start>
                    <ChildContent>
                        <RadzenNumeric TValue="int?" Value="_rollNo" class="w-full" ShowUpDown="false"
                                       ValueChanged="@(async (value) => { _rollNo = value; await GetRollsDataAsync(); })" />
                    </ChildContent>
                    <End>
                        <RadzenIcon Icon="filter_alt_off" IconColor="var(--rz-base-500)"
                                    class="cursor-pointer" Visible="@(_rollNo is not null)"
                                    @onclick="@(async () => { _rollNo = null; await GetRollsDataAsync(); await InvokeAsync(StateHasChanged); })" />
                    </End>
                </RadzenFormField>
            </RadzenColumn>

            <RadzenColumn Size="12" SizeMD="2" SizeLG="1">
                <RadzenFormField AllowFloatingLabel="false" Text="" class="w-full" Variant="Variant.Text">
                    <RadzenButton Icon="search" Text="@("Szukaj".Tr())" Click="GetRollsDataAsync" class="w-full" />
                </RadzenFormField>
            </RadzenColumn>
        </RadzenRow>

        <RadzenStack Orientation="Orientation.Horizontal" Class="p-1 justify-start items-start gap-2 flex-wrap">
            <RadzenButton Text=@("Dodaj do listy wybranych".Tr().AddText($" ({_rollsToAdd?.Count ?? 0})")) Icon="group_add"
                          ButtonStyle="ButtonStyle.Primary" Click="@AddRolls"
                          Disabled="@(_isLoading || _rollsToAdd?.Any() != true)" />
        </RadzenStack>
        <ProgressBar IsLoading="@_isLoading" Visible="@_isLoading" />
        <RadzenDataGrid @ref="_availableRollsGrid" TItem="RollResponse"
                        Data="@_availableRolls" Count="@(_availableRolls.Count())" IsLoading="@_isLoading"
                        AllowFiltering FilterMode="FilterMode.Simple"
                        FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                        AllowPaging PageSize="@PageSize.DefaultPageSize" PageSizeOptions="@PageSize.Pages20To200" PageSizeText="@PageSize.FormatPaginationInfo(_availableRollsGrid)"
                        AllowSorting AllowMultiColumnSorting
                        AllowColumnResize
                        Density="Density.Compact"
                        EmptyText="@PageSize.NoDataText"
                        SelectionMode="DataGridSelectionMode.Multiple"
                        @bind-Value="@_rollsToAdd"
                        RowRender="@OnRowRender"
                        GridLines="@RadzenDataGridConstants.DefaultDataGridLines">

            <LoadingTemplate />

            <Columns>
                <RadzenDataGridColumn Width="35px" Sortable="false" Filterable="false">
                    <HeaderTemplate>
                        <RadzenCheckBox TabIndex="-1" TriState="false" TValue="bool?" InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "Select all items" } })"
                                        Value="@(_rollsToAdd == null || _rollsToAdd?.Any() != true ? false : !_availableRolls.All(i => _rollsToAdd.Contains(i)) ? null : _availableRolls.Any(i => _rollsToAdd.Contains(i)))"
                                        Change="@(args => _rollsToAdd = args == true ? _availableRolls.ToList() : null)" />
                    </HeaderTemplate>
                    <Template Context="data">
                        <RadzenCheckBox TabIndex="-1"
                                        TriState="false"
                                        Value="@(_rollsToAdd != null && _rollsToAdd.Contains(data))"
                                        InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "Select item" } })"
                                        TValue="bool" />
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Property="@nameof(SaleRollResponse.Status)"
                                      Width="50px" Visible="@AppStateService.IsLargeScreen" TextAlign="TextAlign.Center"
                                      Sortable="false" Filterable="false">
                    <Template Context="data">
                        <RadzenRollStatusIcon Status="@data.Status" />
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Property="@nameof(RollResponse.RollNo)" Title=@("Nr.".Tr())
                                      Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable />

                <RadzenDataGridColumn Property="@nameof(RollResponse.CoatCode)" Title=@("Powłoka".Tr())
                                      Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable />

                <RadzenDataGridColumn Property="@nameof(RollResponse.ColorCode)" Title=@("Kolor".Tr())
                                      Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable />

                <RadzenDataGridColumn Property="@nameof(RollResponse.Thick)" Title=@("Grubość".Tr().AddText(" [mm]"))
                                      Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable />

                <RadzenDataGridColumn Property="@nameof(RollResponse.CurrentWeight)" Title=@("Waga akt.".Tr().AddText(" [kg]"))
                                      Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable />

                <RadzenDataGridColumn Property="@nameof(RollResponse.Weight)" Title=@("Waga nom.".Tr().AddText(" [kg]"))
                                      Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable />

                <RadzenDataGridColumn Visible="@(!AppStateService.IsLargeScreen)" Filterable="false" Sortable="false">
                    <Template Context="data">
                        <RollCard Roll="@data" />
                    </Template>
                </RadzenDataGridColumn>
            </Columns>
        </RadzenDataGrid>
    </RadzenFieldset>

    <RadzenFieldset Text="@("Wybrane kręgi".Tr())" class="mt-3">
        <RadzenStack Orientation="Orientation.Horizontal" Class="p-1 justify-start items-start gap-2 flex-wrap">
            <RadzenButton Text=@("Usuń z listy wybranych".Tr().AddText($" ({_rollsToRemove?.Count ?? 0})")) Icon="group_remove"
                          ButtonStyle="ButtonStyle.Primary" Click="@RemoveRolls"
                          Disabled="@(_isLoading || _rollsToRemove?.Any() != true)" />
        </RadzenStack>
        <RadzenDataGrid @ref="_pendingRollsGrid" TItem="SaleRollResponse"
                        Data="@_pendingRolls" Count="@(_pendingRolls.Count)" IsLoading="@_isLoading"
                        AllowFiltering FilterMode="FilterMode.Simple"
                        FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                        AllowPaging PageSize="@PageSize.DefaultPageSize" PageSizeOptions="@PageSize.Pages20To200" PageSizeText="@PageSize.FormatPaginationInfo(_pendingRollsGrid)"
                        AllowSorting AllowMultiColumnSorting
                        AllowColumnResize
                        Density="Density.Compact"
                        EmptyText="@PageSize.NoDataText"
                        SelectionMode="DataGridSelectionMode.Multiple"
                        @bind-Value="@_rollsToRemove"
                        RowRender="@OnRowRender"
                        GridLines="@RadzenDataGridConstants.DefaultDataGridLines">

            <LoadingTemplate />

            <Columns>
                <RadzenDataGridColumn Width="35px" Sortable="false" Filterable="false">
                    <HeaderTemplate>
                        <RadzenCheckBox TabIndex="-1" TriState="false" TValue="bool?" InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "Select all items" } })"
                                        Value="@(_rollsToRemove == null || _rollsToRemove?.Any() != true ? false : !_pendingRolls.All(i => _rollsToRemove.Contains(i)) ? null : _pendingRolls.Any(i => _rollsToRemove.Contains(i)))"
                                        Change="@(args => _rollsToRemove = args == true ? _pendingRolls.ToList() : null)" />
                    </HeaderTemplate>
                    <Template Context="data">
                        <RadzenCheckBox TabIndex="-1"
                                        TriState="false"
                                        Value="@(_rollsToRemove != null && _rollsToRemove.Contains(data))"
                                        InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "Select item" } })"
                                        TValue="bool" />
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Property="@nameof(SaleRollResponse.Status)"
                                      Width="50px" Visible="@AppStateService.IsLargeScreen" TextAlign="TextAlign.Center"
                                      Sortable="false" Filterable="false">
                    <Template Context="data">
                        <RadzenRollStatusIcon Status="@data.Status" />
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Property="@nameof(SaleRollResponse.RollNo)" Title=@("Nr.".Tr())
                                      Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable />

                <RadzenDataGridColumn Property="@nameof(SaleRollResponse.CoatCode)" Title=@("Powłoka".Tr())
                                      Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable />

                <RadzenDataGridColumn Property="@nameof(SaleRollResponse.ColorCode)" Title=@("Kolor".Tr())
                                      Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable />

                <RadzenDataGridColumn Property="@nameof(SaleRollResponse.Thick)" Title=@("Grubość".Tr().AddText(" [mm]"))
                                      Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable />

                <RadzenDataGridColumn Property="@nameof(SaleRollResponse.CurrentWeight)" Title=@("Waga akt.".Tr().AddText(" [kg]"))
                                      Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable />

                <RadzenDataGridColumn Property="@nameof(SaleRollResponse.SaleWeight)" Title=@("Waga promocji".Tr().AddText(" [kg]"))
                                      Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable>

                    <Template Context="data">
                        <RadzenNumeric @bind-Value="data.SaleWeight"
                                       Min="0"
                                       Max="data.CurrentWeight"
                                       class="bg-esa-edit w-full" />
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Property="@nameof(SaleRollResponse.Weight)" Title=@("Waga nom.".Tr().AddText(" [kg]"))
                                      Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable />

                <RadzenDataGridColumn Visible="@(!AppStateService.IsLargeScreen)" Filterable="false" Sortable="false">
                    <Template Context="data">
                        <RollCard Roll="@data" />
                        <RadzenNumeric @bind-Value="data.SaleWeight"
                                       Min="0"
                                       Max="data.CurrentWeight"
                                       class="bg-esa-edit w-full" />
                    </Template>
                </RadzenDataGridColumn>
            </Columns>
        </RadzenDataGrid>
    </RadzenFieldset>
    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" class="rz-m-4">
        <SaveRadzenButton Text="@("Dodaj produkty do promocji".Tr())" Click="@OnSubmit"
                          Disabled="@(_isLoading || _pendingRolls?.Any() != true)" />
        <CancelRadzenButton Click="@OnCancel" Disabled="@_isLoading" />
    </RadzenStack>
</div>

@code {
    [Parameter] public required int SaleId { get; set; }

    private int? _coatId;
    private int? _colorId;
    private int? _thickId;
    private int? _rollNo;

    private RadzenDataGrid<RollResponse> _availableRollsGrid = new();
    private IEnumerable<RollResponse> _availableRolls = [];
    private RadzenDataGrid<SaleRollResponse> _pendingRollsGrid = new();
    private List<SaleRollResponse> _pendingRolls = new();
    private bool _isLoading;
    private IList<RollResponse>? _rollsToAdd;
    private IList<SaleRollResponse>? _rollsToRemove;
    private List<SaleRollResponse> _rollsAlreadyInSale = new();

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await GetSaleRollsAsync();
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    private async Task GetSaleRollsAsync()
    {
        _isLoading = true;

        var response = await Mediator.Send(new GetSaleRollsQuery(SaleId));

        if (response.Succeeded)
        {
            _rollsAlreadyInSale = response.Data;
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError);
        }

        _isLoading = false;

        await InvokeAsync(StateHasChanged);
    }

    private async Task GetRollsDataAsync()
    {
        IRequest<ListResult<RollResponse>> request;

        if (_rollNo.HasValue)
        {
            request = new GetRollsByNosQuery(_rollNo.Value);
        }
        else if (_coatId.HasValue && _colorId.HasValue && _thickId.HasValue)
        {
            request = new GetRollsByOptionsQuery(
                        CoatId: (int)_coatId,
                        ColorId: (int)_colorId,
                        ThickId: (int)_thickId);
        }
        else
        {
            return;
        }

        _isLoading = true;

        var response = await Mediator.Send(request);

        if (response.Succeeded)
        {
            _availableRolls = response.Data.Where(x => _pendingRolls.All(y => y.RollId != x.RollId));
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError);
            _availableRolls = [];
        }

        _isLoading = false;
        await InvokeAsync(StateHasChanged);
    }

    void OnRowRender<T>(RowRenderEventArgs<T> args) where T : RollResponse
    {
        if (_rollsAlreadyInSale.Any(x => x.RollId == args.Data.RollId))
        {
            args.Attributes.Add("class", "highlighted-grid-row");
        }
    }

    private void AddRolls()
    {
        if (_rollsToAdd is not null && _rollsToAdd.Any())
        {
            _pendingRolls.AddRange(Mapper.Map<List<SaleRollResponse>>(_rollsToAdd));
            _pendingRolls = _pendingRolls.Distinct().ToList();

            foreach (var roll in _pendingRolls)
            {
                var saleRoll = _rollsAlreadyInSale.FirstOrDefault(x => x.RollId == roll.RollId);
                if (saleRoll is not null)
                {
                    roll.SaleWeight = saleRoll.SaleWeight; // Ustawienie wagi z istniejącego kręgu w promocji
                }
                else
                {
                    roll.SaleWeight = roll.CurrentWeight; // Domyślnie ustawia wage promocji na wagę aktualną
                }
            }
        }
        _rollsToAdd = null;

        _availableRollsGrid.Reload();
        _pendingRollsGrid.Reload();
    }

    private void RemoveRolls()
    {
        if (_rollsToRemove != null && _rollsToRemove.Any())
        {
            _pendingRolls.RemoveAll(c => _rollsToRemove.Contains(c));
        }
        _rollsToRemove = null;

        _availableRollsGrid.Reload();
        _pendingRollsGrid.Reload();
    }

    private void OnCancel() => Navigation.NavigateTo($"/admin/sales/details/{SaleId}");

    private async Task OnSubmit()
    {
        _isLoading = true;

        var command = new AddEditSaleRollCommand(SaleId, _pendingRolls.Select(c => new AddEditSaleRollRequest(c.RollId, c.SaleWeight)).ToList());
        var result = await Mediator.Send(command);

        if (result.Succeeded)
        {
            ToastService.Show(ToastType.SaveSuccess);
            Navigation.NavigateTo($"/admin/sales/details/{SaleId}");
        }
        else
        {
            ToastService.Show(ToastType.SaveError, string.Join(',', result.Messages));
        }

        _isLoading = false;
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        if (AppStateService != null)
        {
            AppStateService.StateChanged += OnStateChanged;
        }
    }

    void IDisposable.Dispose()
    {
        if (AppStateService != null)
        {
            AppStateService.StateChanged -= OnStateChanged;
        }
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }
}
