@page "/admin/sales/index"
@attribute [Authorize(Roles = $"{Role.Administrator},{Role.SaleManager}")]

@using EMessa.Core.Features.Sales.Commands.CancelSale
@using EMessa.Core.Features.Sales.Commands.DeleteSale
@using EMessa.Core.Features.Sales.Queries.GetAllSales
@using EMessa.Web.Components.Grids
@using EMessa.Web.Components.RadzenComponents.Badges
@using EMessa.Web.Constants
@using EMessa.Web.Pages.Admin.Factories.Parts;
@using EMessa.Web.Pages.Admin.Sales.Parts
@using EMessa.Web.Pages.Admin.Sales.Parts.Cards
@using Messa.Core.BL.eMessa.Sales.ViewModels
@using <PERSON><PERSON>zen
@using Ra<PERSON>zen.Blazor

@inject ILogger<EMessa.Web.Pages.Admin.Sales.Index> Logger
@inject IModalDialogService ModalDialogService
@inject NavigationManager Navigation
@inject TooltipService TooltipService
@implements IDisposable

<IndexCard Title=@("Promocje".Tr())>
    <ChildContent>
        <GridToolbar Items="@([GridToolbar.Add, GridToolbar.Search])"
                     OnAction="@HandleAction"
                     SearchTextChanged="@HandleSearch">

            <DateFromToFilter IsLoading="@_isLoading"
                              DateFrom="@_saleDateInFrom"
                              DateTo="@_saleDateInTo"
                              DateRangePreset="@DateFromToFilter.DateRangeOption.CurrentMonth"
                              OnChange="@(async (dates) => { _saleDateInFrom = dates.CreatedFrom; _saleDateInTo = dates.CreatedTo; await _grid.Reload(); })" />
        </GridToolbar>
        <ProgressBar IsLoading="@_isLoading" Visible="@_isLoading" />
        <RadzenDataGrid @ref="_grid" TItem="GetAllSalesResponse"
                        Data="@_gridData" Count="@_count" LoadData="@LoadData" IsLoading="@_isLoading"
                        AllowFiltering FilterMode="FilterMode.Simple"
                        FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                        AllowPaging PageSize="@PageSize.DefaultPageSize" PageSizeOptions="@PageSize.Pages20To200" PageSizeText="@PageSize.FormatPaginationInfo(_grid)"
                        AllowSorting AllowMultiColumnSorting
                        AllowColumnResize
                        RowDoubleClick="@OnRowDoubleClick"
                        Density="Density.Compact"
                        EmptyText="@RadzenDataGridConstants.EmptyText"
                        GridLines="@RadzenDataGridConstants.DefaultDataGridLines">
            <LoadingTemplate />

            <Columns>
                <RadzenDataGridColumn Context="sale "
                                      Title=@(AppStateService.IsLargeScreen ? "Akcje".Tr() : string.Empty)
                                      Filterable="false" Sortable="false"
                                      TextAlign="TextAlign.Center"
                                      Width="@(AppStateService.IsLargeScreen ? "55px" : "30px")"
                                      Frozen="true" FrozenPosition="FrozenColumnPosition.Right">
                    <Template Context="sale">
                        <div class="@(AppStateService.IsLargeScreen ? "flex flex-row gap-2 justify-center" : "flex flex-col gap-2 items-center")">
                            <DetailsNavRadzenButton Href="@($"/admin/sales/details/{sale.Id}")"
                                                    IsAction="true" />

                            <AuthorizeView Roles="@Role.Administrator">
                                <Authorized>
                                    <DeleteRadzenButton IsAction Click="@(_ => DeleteSale(sale))" Disabled=@(_isLoading || sale.Status != SaleStatus.Closed && sale.Status != SaleStatus.Canceled) />
                                </Authorized>
                            </AuthorizeView>
                        </div>
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Property="@nameof(GetAllSalesResponse.Id)" Title=@("Nr".Tr())
                                      Width="5%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                      Sortable Filterable />

                <RadzenDataGridColumn Property="@nameof(GetAllSalesResponse.Name)" Title=@("Nazwa".Tr())
                                      Width="30%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                      Sortable Filterable />

                <RadzenDataGridColumn Property="@nameof(GetAllSalesResponse.DateFromToActive)" Title=@("Akt. Dat.".Tr())
                                      Width="6%" Visible="@AppStateService.IsLargeScreen" TextAlign="TextAlign.Center"
                                      Sortable Filterable>
                    <Template Context="data">
                        <RadzenCheckBox Value="data.DateFromToActive" Disabled TValue="bool" />
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Property="@nameof(GetAllSalesResponse.DateFrom)" Title=@("Od".Tr())
                                      Width="120px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable=false
                                      FilterOperators="@RadzenDataGridFilterOptions.DateTimeFilters"
                                      FormatString="@DateFormatProvider.DisplayDateFormat">
                    <Template Context="data">
                        @data.DateFrom.ToDisplayLocalDate()
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Property="@nameof(GetAllSalesResponse.DateTo)" Title=@("Do".Tr())
                                      Width="120px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable=false
                                      FilterOperators="@RadzenDataGridFilterOptions.DateTimeFilters"
                                      FormatString="@DateFormatProvider.DisplayDateFormat">
                    <Template Context="data">
                        @data.DateTo.ToDisplayLocalDate()
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Property="@nameof(GetAllSalesResponse.Status)" Title=@("Status".Tr())
                                      Width="140px" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                      TextAlign="TextAlign.Center"
                                      Sortable FilterProperty="@nameof(GetAllSalesResponse.Status)" Filterable FilterOperator="FilterOperator.Equals" FilterValue="@_statusFilter">
                    <FilterTemplate>
                        <RadzenDropDown @bind-Value="@_statusFilter"
                                        TValue="SaleStatus?"
                                        Data="@_allStatusFilter"
                                        AllowClear>
                            <Template Context="data">
                                <SaleStatusBadge Status="@data" />
                            </Template>
                            <ValueTemplate Context="data">
                                <SaleStatusBadge Status="@data" />
                            </ValueTemplate>
                        </RadzenDropDown>
                    </FilterTemplate>
                    <Template Context="status">
                        <SaleStatusBadge Status="@status.Status" />
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn TItem="GetAllSalesResponse" Title=@("Powłoka".Tr())
                                      Width="120px" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                      Sortable SortProperty="@nameof(GetAllSalesResponse.Coat)"
                                      Filterable FilterProperty="@nameof(GetAllSalesResponse.Coat)" FilterOperator="FilterOperator.Contains">
                    <Template Context="data">
                        @data.Coat.Value
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn TItem="GetAllSalesResponse" Title=@("Kolor".Tr())
                                      Width="80px" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                      Sortable SortProperty="@nameof(GetAllSalesResponse.Color)"
                                      Filterable FilterProperty="@nameof(GetAllSalesResponse.Color)" FilterOperator="FilterOperator.Contains">
                    <Template Context="data">
                        @data.Color.Value
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn TItem="GetAllSalesResponse" Title=@("Grub.".Tr())
                                      Width="80px" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                      Sortable SortProperty="@nameof(GetAllSalesResponse.Thick)"
                                      Filterable FilterProperty="@nameof(GetAllSalesResponse.Thick)" FilterOperator="FilterOperator.Contains">
                    <Template Context="data">
                        @data.Thick.Value
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Property="@nameof(GetAllSalesResponse.DefinedWeight)" Title=@("Waga".Tr().AddText(" [kg]"))
                                      Width="120px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable>
                    <Template Context="data">
                        <RadzenRow RowGap="1px" Gap="1px">
                            <RadzenColumn>
                                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.Center" Gap="1px"
                                             MouseEnter="@(er => TooltipService.Open(er, "Dostępna waga promocji".Tr(), new TooltipOptions() { Position = TooltipPosition.Bottom }))">
                                    <RadzenText Text="@data.AvailableWeight.ToString("n0").AddText(" kg")" Style="@(data.AvailableWeight < 0 ? "color: red;" : "color: green;")" class="m-0 font-bold text-[14px]" />
                                </RadzenStack>
                            </RadzenColumn>
                        </RadzenRow>
                        <RadzenRow RowGap="1px" Gap="1px">
                            <RadzenColumn>
                                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.Center" Gap="1px">
                                    <RadzenText Text="@data.DefinedWeight.ToString("n0").AddText(" kg")" class="m-0 text-[14px]"
                                                MouseEnter="@(er => TooltipService.Open(er, "Zdefiniowana waga".Tr(), new TooltipOptions() { Position = TooltipPosition.Bottom }))" />
                                    <RadzenText Text=" | " class="m-0" />
                                    <RadzenText Text="@data.Rolls.Sum(x => x.SaleWeight).ToString("n0").AddText(" kg")" class="m-0 text-[14px]"
                                                MouseEnter="@(er => TooltipService.Open(er, "Zdefiniowana waga kręgów".Tr(), new TooltipOptions() { Position = TooltipPosition.Bottom }))" />
                                </RadzenStack>
                            </RadzenColumn>
                        </RadzenRow>
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Title=@("Info".Tr())
                                      Width="100px" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                      Sortable=false Filterable=false>
                    <Template Context="data">
                        <RadzenRow RowGap="1px" Gap="1px">
                            <RadzenColumn>
                                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.Center" Gap="1px"
                                             MouseEnter="@((er) => TooltipService.Open(er, "Przypisani klienci: {0}".Tr(string.Join(";\n", data.Customers.Select(x => x.ShortName))), new TooltipOptions() { Position = TooltipPosition.Left, Style = "white-space: normal; word-wrap: break-word;" }))">
                                    <RadzenIcon Icon="group" class="text-xl text-sky-600" />
                                    <RadzenText Text="@data.CustomersCount.ToString()" class="text-base m-0" />
                                </RadzenStack>
                            </RadzenColumn>
                            <RadzenColumn>
                                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.Center" Gap="1px"
                                             MouseEnter="@((er) => TooltipService.Open(er, "Przypisane produkty: {0}".Tr(string.Join(";\n", data.Articles.Select(x => x.Name))), new TooltipOptions() { Position = TooltipPosition.Left, Style = "white-space: normal; word-wrap: break-word;" }))">
                                    <RadzenIcon Icon="water" class="text-xl text-sky-600" />
                                    <RadzenText Text="@data.ArticlesCount.ToString()" class="text-base m-0" />
                                </RadzenStack>
                            </RadzenColumn>
                        </RadzenRow>
                        <RadzenRow RowGap="1px" Gap="1px">
                            <RadzenColumn>
                                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.Center" Gap="1px"
                                             MouseEnter="@((er) => TooltipService.Open(er, "Przypisane kręgi: {0}".Tr(data.RollsCount.ToString()), new TooltipOptions() { Position = TooltipPosition.Left, Style = "white-space: normal; word-wrap: break-word;" }))">
                                    <RadzenIcon Icon="@("\uf71e")" class="fas text-base mx-1 text-sky-600" />
                                    <RadzenText Text="@data.RollsCount.ToString()" class="text-base m-0" />
                                </RadzenStack>
                            </RadzenColumn>
                            <RadzenColumn>
                                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.Center" Gap="1px"
                                             MouseEnter="@((er) => TooltipService.Open(er, "Zamówienia z promocji: {0}".Tr(data.OrderItemsCount.ToString()), new TooltipOptions() { Position = TooltipPosition.Left, Style = "white-space: normal; word-wrap: break-word;" }))">
                                    <RadzenIcon Icon="shopping_cart" class="text-xl text-sky-600" />
                                    <RadzenText Text="@data.OrderItemsCount.ToString()" class="text-base m-0" />
                                </RadzenStack>
                            </RadzenColumn>
                        </RadzenRow>
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Visible="@(!AppStateService.IsLargeScreen)" Filterable="false" Sortable="false">
                    <Template Context="data">
                        <SaleCard Sale="@data" />
                    </Template>
                </RadzenDataGridColumn>
            </Columns>
        </RadzenDataGrid>
    </ChildContent>

</IndexCard>

@code {
    private RadzenDataGrid<GetAllSalesResponse> _grid = new();
    private IEnumerable<GetAllSalesResponse> _gridData = [];
    private int _count;
    private bool _isLoading;
    private string? _searchString;
    private IEnumerable<SaleStatus> _allStatusFilter = Enum.GetValues<SaleStatus>().ToList();
    private SaleStatus? _statusFilter;
    private DateTime? _saleDateInFrom;
    private DateTime? _saleDateInTo;

    private async Task LoadData(LoadDataArgs args)
    {
        _isLoading = true;

        var dateFromFilter = args.Filters.FirstOrDefault(f => f.Property == nameof(GetAllSalesResponse.DateFrom));
        var dateToFilter = args.Filters.FirstOrDefault(f => f.Property == nameof(GetAllSalesResponse.DateTo));

        if (dateFromFilter?.FilterValue is DateTime dateFromLocal)
            dateFromFilter.FilterValue = dateFromLocal.ToUtcDateTime();

        if (dateToFilter?.FilterValue is DateTime dateToLocal)
            dateToFilter.FilterValue = dateToLocal.AddDays(1).AddTicks(-1).ToUtcDateTime();

        var sieveModel = args.ToSieveModel().ApplyGlobalSearchFilter(_searchString);

        sieveModel.AddFilterWhenNoNull(
            "SaleSateInFrom",
            _saleDateInFrom.ToUtcDateTimeStringForSieveFilter(),
            ">=");

        sieveModel.AddFilterWhenNoNull(
            "SaleDateInTo",
            _saleDateInTo.ToUtcDateTimeStringForSieveFilter(),
            "<=");

        var response = await Mediator.Send(new GetAllSalesQuery(sieveModel));

        if (response.Succeeded)
        {
            _gridData = response.Data;
            _count = response.TotalCount;
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError);
            _gridData = [];
            _count = 0;
        }

        _isLoading = false;
        await InvokeAsync(StateHasChanged);
    }

    private void OnRowDoubleClick(DataGridRowMouseEventArgs<GetAllSalesResponse> args)
    {
        Editsale(args.Data);
    }

    private void AddSale() => Navigation.NavigateTo("/admin/sales/edit");

    private void Editsale(GetAllSalesResponse sale) => Navigation.NavigateTo($"/admin/sales/details/{sale.Id}");

    private async Task DeleteSale(GetAllSalesResponse sale)
    {
        var confirmed = await ModalDialogService.DeleteConfirmationByName(sale.Name);

        if (!confirmed)
            return;

        _isLoading = true;

        var response = await Mediator.Send(new DeleteSaleCommand(sale.Id));

        if (response.Succeeded)
            ToastService.Show(ToastType.DeleteSuccess);
        else
            ToastService.Show(ToastType.DeleteError, string.Join(',', response.Messages));

        await _grid.Reload();

        _isLoading = false;
    }

    private void HandleAction(string item)
    {
        switch (item)
        {
            case GridToolbar.Add:
                AddSale();
                break;
        }
    }

    private void HandleSearch(string searchString)
    {
        _searchString = searchString;
        _grid.Reload();
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        if (AppStateService != null)
        {
            AppStateService.StateChanged += OnStateChanged;
        }
    }

    void IDisposable.Dispose()
    {
        if (AppStateService != null)
        {
            AppStateService.StateChanged -= OnStateChanged;
        }
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }
}
