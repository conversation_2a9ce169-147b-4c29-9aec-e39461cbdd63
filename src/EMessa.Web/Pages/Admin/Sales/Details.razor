@page "/admin/sales/details/{SaleId:int}"
@attribute [Authorize(Roles = $"{Role.Administrator},{Role.SaleManager}")]

@using EMessa.Core.Features.Sales.Commands.ActivateSale
@using EMessa.Core.Features.Sales.Commands.CancelSale
@using EMessa.Core.Features.Sales.Commands.CloseSale
@using EMessa.Core.Features.Sales.Commands.DuplicateSale
@using EMessa.Core.Features.Sales.Queries.GetSaleById
@using EMessa.Web.Components.RadzenComponents.Badges
@using EMessa.Web.Pages.Admin.Sales.Parts
@using EMessa.Web.Pages.Admin.Sales.Parts.Grids
@using Messa.Core.BL.eMessa.Sales.ViewModels
@using Radzen
@using Radzen.Blazor

@inject NavigationManager Navigation
@inject TooltipService TooltipService

<IndexCard Title=@("Promocja".Tr())>

    <ProgressBar IsLoading="@_isLoading" Visible="@_isLoading" />

    <RadzenCard class="p-2">
        <RadzenStack Orientation="Orientation.Vertical" Gap="10px">
            @if (_isLoading)
            {
                <RadzenRow>
                    <RadzenStack Orientation="Orientation.Horizontal" Wrap="FlexWrap.Wrap" Gap="5px">
                        <BackRadzenButton Click="@OnReturn" />
                        <Shimmer Type="button" Width="80px" />
                        <Shimmer Type="button" Width="120px" />
                        <Shimmer Type="button" Width="120px" />
                    </RadzenStack>
                </RadzenRow>

                <RadzenRow>
                    <RadzenColumn Size="12" SizeMD="12" SizeLG="3">
                        <RadzenText TextStyle="TextStyle.H6" class="m-0">
                            <SaleStatusBadge Status="@(SaleStatus.New)" />
                            (@("nr".Tr()): @SaleId)
                        </RadzenText>
                        <Shimmer Type="card" Width="80%" Height="30px" />
                        <Shimmer Type="card" Width="80%" Height="30px" />
                        <Shimmer Type="card" Width="80%" Height="30px" />
                        <Shimmer Type="card" Width="80%" Height="30px" />
                    </RadzenColumn>
                </RadzenRow>
            }
            else
            {
                <RadzenRow>
                    <RadzenStack Orientation="Orientation.Horizontal" Wrap="FlexWrap.Wrap">
                        <BackRadzenButton Click="@OnReturn" />

                        <EditRadzenButton Click="@(_ => Navigation.NavigateTo($"/admin/sales/edit/{SaleId}"))"
                                          Disabled="@_isLoading" />

                        @if (_sale.Status == SaleStatus.New)
                        {
                            <RadzenButton Text="@("Aktywuj promocję".Tr())"
                                          Icon="flash_on"
                                          ButtonStyle="@ButtonStyle.Success"
                                          Disabled="@_isLoading"
                                          Click="@ActivateSaleAsync" />
                        }
                        else if (_sale.Status == SaleStatus.Active)
                        {
                            <RadzenButton Text="@("Zamknij promocję".Tr())"
                                          Icon="block"
                                          ButtonStyle="@ButtonStyle.Dark"
                                          Disabled="@_isLoading"
                                          Click="@CloseSaleAsync" />
                        }
                        else
                        {
                            <AuthorizeView Roles="@Role.Administrator">
                                <Authorized>
                                    <RadzenButton Text="@("Aktywuj promocję".Tr())"
                                                  Icon="flash_on"
                                                  ButtonStyle="@ButtonStyle.Success"
                                                  Disabled="@_isLoading"
                                                  Click="@ActivateSaleAsync" />
                                </Authorized>
                            </AuthorizeView>
                        }

                        <CloneRadzenButton Text="@("Klonuj promocję".Tr())" Click="@CloneSale" />

                        @if (_sale.Status == SaleStatus.New)
                        {
                            <DeleteRadzenButton Icon="block"
                                                Text="@("Anuluj promocję".Tr())"
                                                Click="@(_ => CancelSale())"
                                                Disabled="@(_isLoading || _sale.Status != SaleStatus.New)" />
                        }

                    </RadzenStack>
                </RadzenRow>

                <RadzenRow>
                    <RadzenColumn Size="12" SizeMD="12" SizeLG="9" SizeXL="10">
                        <RadzenStack Orientation="Orientation.Vertical" Gap="8px" AlignItems="AlignItems.Start">
                            <RadzenText TextStyle="TextStyle.H6"
                                        class="m-0">
                                <SaleStatusBadge Status="@_sale.Status" CssClass="text-base" />
                                @_sale.Name (@("nr".Tr()): @_sale.Id)
                            </RadzenText>

                            <RadzenStack Orientation="Orientation.Horizontal" Gap="5px" AlignItems="AlignItems.Center">
                                <RadzenIcon Icon="info" class="text-sky-600" />
                                <RadzenText TextStyle="TextStyle.Subtitle1" class="m-0 my-1">
                                    @($"{_sale.Coat.Value} - {_sale.Color.Value} - {_sale.Thick.Value}")
                                </RadzenText>
                            </RadzenStack>

                            <RadzenStack Orientation="Orientation.Horizontal"
                                         Gap="10px"
                                         AlignItems="AlignItems.Center"
                                         JustifyContent="JustifyContent.SpaceBetween"
                                         Wrap="FlexWrap.Wrap"
                                         class="w-full">
                                <RadzenStack Orientation="Orientation.Horizontal" Gap="5px"
                                             AlignItems="AlignItems.Center">
                                    <RadzenIcon Icon="calendar_month" class="text-sky-600" />
                                    <RadzenText class="m-0">
                                        @("Ważna od".Tr()):
                                        <strong>@_sale.DateFrom.ToDisplayLocalDate()</strong> @("do".Tr()):
                                        <strong>@_sale.DateTo.ToDisplayLocalDate()</strong>
                                    </RadzenText>
                                </RadzenStack>

                                <RadzenStack Orientation="Orientation.Horizontal" Gap="10px"
                                             AlignItems="AlignItems.Center" Wrap="FlexWrap.Wrap">
                                    <RadzenStack Orientation="Orientation.Horizontal" Gap="5px"
                                                 AlignItems="AlignItems.Center">
                                        <RadzenText Text=@(_sale.RollsCount.ToString()) class="m-0 font-bold"
                                                    MouseEnter="@(er => TooltipService.Open(er, "Ilość kręgów".Tr()))" />
                                        <RadzenIcon Icon="@("\uf71e")" class="fas text-[22px] mx-1 text-sky-600" />
                                        <RadzenText Text=@($"{_sale.Rolls.Sum(x => x.CurrentWeight):n0} kg")
                                                    class="m-0 font-bold"
                                                    MouseEnter="@(er => TooltipService.Open(er, "Aktualna waga kręgów - Messa".Tr()))" />
                                    </RadzenStack>
                                    <RadzenStack Orientation="Orientation.Horizontal" Gap="5px"
                                                 AlignItems="AlignItems.Center">
                                        <RadzenIcon Icon="balance" class="text-sky-600" />
                                        <RadzenText Text=@($"{_sale.AvailableWeight:n0} kg")
                                                    class="m-0 font-bold"
                                                    Style="@(_sale.AvailableWeight < 0 ? "color: red;" : "color: green;")"
                                                    MouseEnter="@(er => TooltipService.Open(er, "Waga pozostała w promocji".Tr()))" />
                                        <RadzenText Text=@($" ({_sale.DefinedWeight:n0} kg)")
                                                    class="m-0"
                                                    MouseEnter="@(er => TooltipService.Open(er, "Waga zdefiniowana w promocji".Tr()))" />

                                    </RadzenStack>
                                    <RadzenStack Orientation="Orientation.Horizontal" Gap="5px"
                                                 AlignItems="AlignItems.Center"
                                                 MouseEnter="@(er => TooltipService.Open(er, "Waga (ilość) zamówień".Tr()))">
                                        <RadzenIcon Icon="water" class="text-sky-600" />
                                        <RadzenText Text=@($"{_sale.OrderItems.Sum(x => x.OrderItemWeight):n0} kg ({_sale.OrderItemsCount})")
                                                    class="m-0" />
                                    </RadzenStack>
                                </RadzenStack>
                            </RadzenStack>

                            <RadzenTextArea Value="@(_sale.Description)"
                                            Rows="3"
                                            ReadOnly
                                            class="w-full" />

                            <RadzenText class="m-0 text-sm">
                                @("Utworzył".Tr()): @_sale.CreatedByFullName @_sale.CreatedDate.ToDisplayLocalDateTime()
                            </RadzenText>
                        </RadzenStack>
                    </RadzenColumn>

                    @* Ceny *@
                    <RadzenColumn Size="12" SizeMD="12" SizeLG="3" SizeXL="2">
                        <RadzenStack Orientation="Orientation.Vertical"
                                     AlignItems="AlignItems.Start"
                                     class="@(AppStateService.IsLargeScreen ? "pl-4" : "")"
                                     Gap=".2rem">
                            <span class="break-words break-all whitespace-normal max-w-full text-lg font-bold">
                                @("Ceny promocji".Tr())
                            </span>
                            @foreach (var price in _sale.Prices.OrderBy(x => x.CurrencyCode))
                            {
                                <PriceBadge Price="@price.Price" CurrencyCode="@price.CurrencyCode" />
                            }
                        </RadzenStack>
                    </RadzenColumn>
                </RadzenRow>
            }
        </RadzenStack>
    </RadzenCard>

    <RadzenTabs RenderMode="TabRenderMode.Server">
        <Tabs>
            <RadzenTabsItem Icon="group" Text="@("Klienci".Tr().AddText($" ({_sale.CustomersCount})"))"
                            Disabled=@(_isLoading)>
                <SaleCustomersGrid SaleId="@SaleId" ReloadSale="@GetSaleAsync" />
            </RadzenTabsItem>

            <RadzenTabsItem Icon="water" Text="@("Produkty".Tr().AddText($" ({_sale.ArticlesCount})"))"
                            Disabled=@(_isLoading)>
                <SaleArticlesGrid SaleId="@SaleId" ReloadSale="@GetSaleAsync" />
            </RadzenTabsItem>

            <RadzenTabsItem Icon="tune" Text="@("Ograniczenia opcji".Tr())" Disabled=@(_isLoading)>
                <SaleArticleOptionRestrictionGrid SaleId="@SaleId" />
            </RadzenTabsItem>

            <style>
                .roll .notranslate.rzi.rz-tabview-icon {
                    font-family: 'Font Awesome 6 Free' !important;
                    font-weight: 900;
                    font-style: normal;
                    font-size: 22px;
                    margin-right: 13px;
                    padding: 3px;
                }
            </style>
            <RadzenTabsItem class="roll" Icon="@("\uf71e")" Text="@("Kręgi".Tr().AddText($" ({_sale.RollsCount})"))"
                            Disabled=@(_isLoading)>
                <SaleRollsGrid SaleId="@SaleId" ReloadSale="@GetSaleAsync" />
            </RadzenTabsItem>

            <RadzenTabsItem Icon="photo_library" Text="@("Zdjęcia".Tr())" Disabled=@(_isLoading)>
                <SaleImagesGallery SaleId="@SaleId" />
            </RadzenTabsItem>

            <RadzenTabsItem Icon="translate" Text="@("Tłumaczenia".Tr())" Disabled=@(_isLoading)>
                <SaleTranslations SaleId="@SaleId" />
            </RadzenTabsItem>

            <RadzenTabsItem Icon="list_alt" Text="@("Zamówienia".Tr().AddText($" ({_sale.OrderItemsCount})"))"
                            Disabled=@(_isLoading)>
                <SaleOrderItemsGrid SaleId="@SaleId" />
            </RadzenTabsItem>
        </Tabs>
    </RadzenTabs>

</IndexCard>

@code {
    [Parameter] public required int SaleId { get; set; }

    private GetSaleByIdResponse _sale = new();
    private bool _isLoading;

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        await GetSaleAsync();
    }

    private async Task GetSaleAsync()
    {
        _isLoading = true;

        if (SaleId is > 0)
        {
            var response = await Mediator.Send(new GetSaleByIdQuery(SaleId));

            if (response.Succeeded && response.Data != null)
            {
                _sale = response.Data;
            }
            else
            {
                ToastService.Show(ToastType.LoadDataError, response.Messages);
                Navigation.NavigateTo("/admin/sales/index");
            }
        }

        await InvokeAsync(StateHasChanged);

        _isLoading = false;
    }

    private void OnReturn() => Navigation.NavigateTo("/admin/sales/index");

    private async Task ActivateSaleAsync()
    {
        _isLoading = true;

        var response = await Mediator.Send(new ActivateSaleCommand(SaleId));

        if (response.Succeeded)
        {
            await GetSaleAsync();
            ToastService.ShowSuccess("Promocja została aktywowana.".Tr());
        }
        else
        {
            ToastService.Show(ToastType.SaveError, response.Messages);
        }

        _isLoading = false;
    }

    private async Task CloseSaleAsync()
    {
        _isLoading = true;

        var response = await Mediator.Send(new CloseSaleCommand(SaleId));

        if (response.Succeeded)
        {
            await GetSaleAsync();
            ToastService.ShowSuccess("Promocja została zamknięta.".Tr());
        }
        else
        {
            ToastService.Show(ToastType.SaveError, string.Join(',', response.Messages));
        }

        _isLoading = false;
    }

    private async Task CloneSale()
    {
        var confirmed = await ModalDialogService.ConfirmActionWithValue(
            "Czy na pewno chcesz sklonować promocję '{0}'? Sklonowane zostaną parametry podstawowe, ceny, przypisani klienci oraz produkty promocji.",
            _sale.Name,
            "Klonowanie promocji");

        if (!confirmed)
            return;

        _isLoading = true;

        var response = await Mediator.Send(new DuplicateSaleCommand(SaleId));

        if (response.Succeeded)
        {
            ToastService.ShowSuccess("Promocja została sklonowana.".Tr());
            SaleId = response.Data;
            await GetSaleAsync();
        }
        else
        {
            ToastService.Show(ToastType.SaveError, response.Messages);
        }

        _isLoading = false;
    }

    private async Task CancelSale()
    {
        if (_sale.Status != SaleStatus.New)
        {
            ToastService.ShowWarning("Tylko promocje o statusie 'Nowe' mogą zostać anulowane.".Tr());
            return;
        }

        var confirmed = await ModalDialogService.ConfirmActionWithValue(
            "Czy na pewno chcesz anulować promocję '{0}'?",
            _sale.Name,
            "Anulowanie promocji");

        if (!confirmed)
            return;

        _isLoading = true;

        var response = await Mediator.Send(new CancelSaleCommand(_sale.Id));

        if (response.Succeeded)
            ToastService.Show(ToastType.SaveSuccess);
        else
            ToastService.Show(ToastType.SaveError, response.Messages);

        await GetSaleAsync();

        _isLoading = false;
    }
}
