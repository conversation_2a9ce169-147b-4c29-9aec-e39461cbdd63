@page "/admin/sales/addcustomers/{SaleId:int}"
@attribute [Authorize(Roles = $"{Role.Administrator},{Role.SaleManager}")]

@using EMessa.Base.Enums
@using EMessa.Core.Features.CustomerGroups.Queries.GetAll
@using EMessa.Core.Features.CustomerGroups.Queries.GetCustomerGroupMembers
@using EMessa.Core.Features.Customers.Commands.Delete
@using EMessa.Core.Features.Customers.Queries.GetAll
@using EMessa.Core.Features.Sales.Commands.AddSaleCustomer
@using EMessa.Core.Features.Sales.Queries.GetSaleCustomers
@using EMessa.Web.Components.Editors
@using EMessa.Web.Components.Grids
@using EMessa.Web.Constants
@using EMessa.Web.Pages.Admin.Customers.Parts
@using EMessa.Web.Pages.Admin.Sales.Parts
@using Radzen
@using Radzen.Blazor

@inject ILogger<AddCutomersToSale> <PERSON><PERSON>
@inject NavigationManager Navigation
@implements IDisposable

<RowHighlightedAlertInfo Text="@("Kolorem oznaczono klientów, którzy są już dodani do tej promocji.".Tr())" />

<div class="w-full">
    <RadzenFieldset Text="@("Klienci do wybrania".Tr())">

        <RadzenStack Orientation="Orientation.Horizontal" Class="p-1 justify-start items-start gap-2 flex-wrap">
            <RadzenButton Text=@("Dodaj zaznaczonych".Tr().AddText($" ({_customersToAdd?.Count ?? 0})")) Icon="group_add"
                          ButtonStyle="ButtonStyle.Primary" Click="@AddCustomers"
                          Disabled="@(_isLoading || _customersToAdd?.Any() != true)" />

            <RadzenButton Text=@("Dodaj widocznych".Tr().AddText($" ({_availableCustomersTotalCount})")) Icon="group_add"
                          ButtonStyle="ButtonStyle.Primary" Click="@AddAllCustomers"
                          Disabled="@(_isLoading || _availableCustomersTotalCount == 0)" />

            <RadzenDropDown TValue="GetAllCustomerGroupsResponse"
                            Data="@_filteredSaleCustomerGroups"
                            Change="@AssignCustomerGroupAsync"
                            AllowClear="true"
                            class="w-[400px]"
                            Placeholder="@("Dodaj klientów z grupy".Tr())"
                            AllowFiltering
                            LoadData="@OnCustomerGroupLoadData">
                <Template Context="data">
                    <RadzenStack Orientation="Orientation.Horizontal">
                        @((data as GetAllCustomerGroupsResponse)?.Name)
                    </RadzenStack>
                </Template>
                <ValueTemplate Context="data">
                    @((data as GetAllCustomerGroupsResponse)?.Name)
                </ValueTemplate>
            </RadzenDropDown>

            <BranchesSelector TValue="int"
                              ValueChanged="@AssignCustomersFromBranchAsync"
                              Clearable
                              ShowActiveOnly
                              CssClass="w-[400px]"
                              Placeholder="@("Dodaj klientów z oddziału".Tr())" />

            <GridSearch @ref="_gridSearch" ValueChanged="@_availableCustomersGrid.Reload" CssClass="ml-auto" />
        </RadzenStack>

        <ProgressBar IsLoading="@_isLoading" Visible="@_isLoading" />
        <RadzenDataGrid @ref="_availableCustomersGrid" TItem="GetAllCustomersResponse"
                        Data="@_availableCustomers" Count="@_availableCustomersTotalCount" LoadData="@LoadData" IsLoading="@_isLoading"
                        AllowFiltering FilterMode="FilterMode.Simple"
                        FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                        AllowPaging PageSize="@PageSize.DefaultPageSize" PageSizeOptions="@PageSize.Pages20To200" PageSizeText="@PageSize.FormatPaginationInfo(_availableCustomersGrid)"
                        AllowSorting AllowMultiColumnSorting
                        AllowColumnResize
                        Density="Density.Compact"
                        EmptyText="@PageSize.NoDataText"
                        SelectionMode="DataGridSelectionMode.Multiple"
                        @bind-Value="@_customersToAdd"
                        RowRender="@OnRowRender"
                        GridLines="@RadzenDataGridConstants.DefaultDataGridLines">

            <LoadingTemplate />

            <Columns>
                <RadzenDataGridColumn Width="35px" Sortable="false" Filterable="false">
                    <HeaderTemplate>
                        <RadzenCheckBox TabIndex="-1" TriState="false" TValue="bool?" InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "Select all items" } })"
                                        Value="@(_customersToAdd == null || _customersToAdd?.Any() != true ? false : !_availableCustomers.All(i => _customersToAdd.Contains(i)) ? null : _availableCustomers.Any(i => _customersToAdd.Contains(i)))"
                                        Change="@(args => _customersToAdd = args == true ? _availableCustomers.ToList() : null)" />
                    </HeaderTemplate>
                    <Template Context="data">
                        <RadzenCheckBox TabIndex="-1"
                                        TriState="false" Value="@(_customersToAdd != null && _customersToAdd.Contains(data))"
                                        InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "Select item" } })"
                                        TValue="bool" />
                    </Template>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.ShortName)" Title=@("Skrócona nazwa".Tr())
                                      Width="120px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.Name)" Title=@("Nazwa".Tr())
                                      Width="200px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.Address)" Title=@("Adres".Tr())
                                      Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.PostCode)" Title=@("Kod pocztowy".Tr())
                                      Width="120px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.City)" Title=@("Miasto".Tr())
                                      Width="150px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.TaxIdentificationNumber)" Title=@("NIP".Tr())
                                      Width="200px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.BranchName)" Title=@("Oddział".Tr())
                                      Width="200px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterProperty="@nameof(GetAllCustomersResponse.BranchId)" FilterOperator="FilterOperator.Equals" FilterValue="@_availableBranchFilter"
                                      WhiteSpace="WhiteSpace.Wrap">
                    <FilterTemplate>
                        <BranchesSelector @bind-Value="@_availableBranchFilter"
                                          ShowActiveOnly="false"
                                          Clearable="true" />
                    </FilterTemplate>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.IsActive)" Title=@("Aktywny".Tr()) Width="100px"
                                      TextAlign="TextAlign.Center" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable>
                    <Template Context="customer">
                        <RadzenCheckBox @bind-Value="customer.IsActive" Disabled TValue="bool" />
                    </Template>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.IsPrivate)"
                                      Title=@("Os. prywatna".Tr()) Width="120px"
                                      TextAlign="TextAlign.Center" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable>
                    <Template Context="customer">
                        <RadzenCheckBox @bind-Value="customer.IsPrivate" Disabled TValue="bool" />
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Visible="@(!AppStateService.IsLargeScreen)" Filterable="false" Sortable="false">
                    <Template Context="customer">
                        <CustomerCard Customer="@customer" />
                    </Template>
                </RadzenDataGridColumn>
            </Columns>
        </RadzenDataGrid>
    </RadzenFieldset>

    <RadzenFieldset Text="@("Wybrani klienci".Tr())">
        <RadzenStack Orientation="Orientation.Horizontal" Class="p-1 justify-start items-start gap-2 flex-wrap">
            <RadzenButton Text=@("Usuń z listy wybranych".Tr().AddText($" ({_customersToRemove?.Count ?? 0})")) Icon="group_remove"
                          ButtonStyle="ButtonStyle.Primary" Click="@RemoveCustomers"
                          Disabled="@(_isLoading || _customersToRemove?.Any() != true)" />
        </RadzenStack>
        <RadzenDataGrid @ref="_pendingCustomersGrid" TItem="GetAllCustomersResponse"
                        Data="@_pendingCustomers" Count="@(_pendingCustomers.Count)" IsLoading="@_isLoading"
                        AllowFiltering FilterMode="FilterMode.Simple"
                        FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                        AllowPaging PageSize="@PageSize.DefaultPageSize" PageSizeOptions="@PageSize.Pages20To200" PageSizeText="@PageSize.FormatPaginationInfo(_pendingCustomersGrid)"
                        AllowSorting AllowMultiColumnSorting
                        AllowColumnResize
                        Density="Density.Compact"
                        EmptyText="@PageSize.NoDataText"
                        SelectionMode="DataGridSelectionMode.Multiple"
                        @bind-Value="@_customersToRemove"
                        RowRender="@OnRowRender"
                        GridLines="@RadzenDataGridConstants.DefaultDataGridLines">

            <LoadingTemplate />

            <Columns>
                <RadzenDataGridColumn Width="35px" Sortable="false" Filterable="false">
                    <HeaderTemplate>
                        <RadzenCheckBox TabIndex="-1" TriState="false" TValue="bool?" InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "Select all items" } })"
                                        Value="@(_customersToRemove == null || _customersToRemove?.Any() != true ? false : !_pendingCustomers.All(i => _customersToRemove.Contains(i)) ? null : _pendingCustomers.Any(i => _customersToRemove.Contains(i)))"
                                        Change="@(args => _customersToRemove = args == true ? _pendingCustomers.ToList() : null)" />
                    </HeaderTemplate>
                    <Template Context="data">
                        <RadzenCheckBox TabIndex="-1"
                                        TriState="false"
                                        Value="@(_customersToRemove != null && _customersToRemove.Contains(data))"
                                        InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "Select item" } })"
                                        TValue="bool" />
                    </Template>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.ShortName)" Title=@("Skrócona nazwa".Tr())
                                      Width="120px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.Name)" Title=@("Nazwa".Tr())
                                      Width="200px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.Address)" Title=@("Adres".Tr())
                                      Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.PostCode)" Title=@("Kod pocztowy".Tr())
                                      Width="120px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.City)" Title=@("Miasto".Tr())
                                      Width="150px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.TaxIdentificationNumber)" Title=@("NIP".Tr())
                                      Width="200px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterOperators="@RadzenDataGridFilterOptions.TextFilters" />
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.BranchName)" Title=@("Oddział".Tr())
                                      Width="200px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable FilterProperty="@nameof(GetAllCustomersResponse.BranchId)" FilterOperator="FilterOperator.Equals" FilterValue="@_branchFilter"
                                      WhiteSpace="WhiteSpace.Wrap">
                    <FilterTemplate>
                        <BranchesSelector @bind-Value="@_branchFilter"
                                          ShowActiveOnly="false"
                                          Clearable="true" />
                    </FilterTemplate>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.IsActive)" Title=@("Aktywny".Tr()) Width="100px"
                                      TextAlign="TextAlign.Center" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable>
                    <Template Context="customer">
                        <RadzenCheckBox @bind-Value="customer.IsActive" Disabled TValue="bool" />
                    </Template>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.IsPrivate)"
                                      Title=@("Os. prywatna".Tr()) Width="120px"
                                      TextAlign="TextAlign.Center" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable>
                    <Template Context="customer">
                        <RadzenCheckBox @bind-Value="customer.IsPrivate" Disabled TValue="bool" />
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Visible="@(!AppStateService.IsLargeScreen)" Filterable="false" Sortable="false">
                    <Template Context="customer">
                        <CustomerCard Customer="@customer" />
                    </Template>
                </RadzenDataGridColumn>
            </Columns>
        </RadzenDataGrid>
    </RadzenFieldset>
    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" class="rz-m-4">
        <SaveRadzenButton Text="@("Dodaj klientów do promocji".Tr())" Click="@OnSubmit"
                          Disabled="@(_isLoading || _pendingCustomers?.Any() != true)" />
        <CancelRadzenButton Click="@OnCancel" Disabled="@_isLoading" />
    </RadzenStack>
</div>

@code {
    [Parameter] public required int SaleId { get; set; }
    private RadzenDataGrid<GetAllCustomersResponse> _availableCustomersGrid = new();
    private LoadDataArgs _availableCustomersGridDataArgs = new();
    private IEnumerable<GetAllCustomersResponse> _availableCustomers = [];
    private int _availableCustomersTotalCount;
    private RadzenDataGrid<GetAllCustomersResponse> _pendingCustomersGrid = new();
    private List<GetAllCustomersResponse> _pendingCustomers = new();
    private bool _isLoading;
    private GridSearch? _gridSearch;
    private int? _availableBranchFilter;
    private int? _branchFilter;
    private IList<GetAllCustomersResponse>? _customersToAdd;
    private IList<GetAllCustomersResponse>? _customersToRemove;
    private List<GetAllCustomersResponse> _customersAlreadyInSale = new();

    private IEnumerable<GetAllCustomerGroupsResponse> _saleCustomerGroups = [];
    private IEnumerable<GetAllCustomerGroupsResponse> _filteredSaleCustomerGroups = [];

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await _availableCustomersGrid.Reload();
            await GetCustomerGroupsAsync();
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    private async Task GetSaleCustomersAsync()
    {
        _isLoading = true;

        var response = await Mediator.Send(new GetSaleCustomersQuery(SaleId));

        if (response.Succeeded)
        {
            _customersAlreadyInSale = response.Data.ToList();
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError);
        }

        _isLoading = false;
    }

    private async Task LoadData(LoadDataArgs args)
    {
        _isLoading = true;

        _availableCustomersGridDataArgs = args;

        await GetSaleCustomersAsync();

        var sieveModel = args.ToSieveModel().ApplyGlobalSearchFilter(_gridSearch?.SearchValue);
        var response = await Mediator.Send(new GetAllCustomersQuery(sieveModel));

        if (response.Succeeded)
        {
            _availableCustomers = response.Data.Where(x => _pendingCustomers.All(y => y.Id != x.Id));
            _availableCustomersTotalCount = _availableCustomers.Count();
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError);
            _availableCustomers = [];
            _availableCustomersTotalCount = 0;
        }

        _isLoading = false;
        await InvokeAsync(StateHasChanged);
    }

    private void OnRowRender(RowRenderEventArgs<GetAllCustomersResponse> args)
    {
        if (_customersAlreadyInSale.Any(x => x.Id == args.Data.Id))
        {
            args.Attributes.Add("class", "highlighted-grid-row");
        }
    }

    private void AddCustomers()
    {
        if (_customersToAdd != null && _customersToAdd.Any())
        {
            _pendingCustomers.AddRange(_customersToAdd);
            _pendingCustomers = _pendingCustomers.DistinctBy(x => x.Id).ToList();
        }
        _customersToAdd = null;

        _availableCustomersGrid.Reload();
        _pendingCustomersGrid.Reload();
    }

    private void RemoveCustomers()
    {
        if (_customersToRemove != null && _customersToRemove.Any())
        {
            _pendingCustomers.RemoveAll(c => _customersToRemove.Contains(c));
        }
        _customersToRemove = null;

        _availableCustomersGrid.Reload();
        _pendingCustomersGrid.Reload();
    }

    private async Task AddAllCustomers()
    {
        _isLoading = true;

        _customersToAdd = null;

        var sieveModel = _availableCustomersGridDataArgs.ToSieveModel().ApplyGlobalSearchFilter(_gridSearch?.SearchValue);
        sieveModel.Page = 1;
        sieveModel.PageSize = int.MaxValue;
        var response = await Mediator.Send(new GetAllCustomersQuery(sieveModel));

        if (response.Succeeded)
        {
            _pendingCustomers.AddRange(response.Data);
            _pendingCustomers = _pendingCustomers.DistinctBy(x => x.Id).ToList();
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError);
        }

        await _availableCustomersGrid.Reload();
        await _pendingCustomersGrid.Reload();

        _isLoading = false;
    }

    private void OnCancel() => Navigation.NavigateTo($"/admin/sales/details/{SaleId}");

    private async Task OnSubmit()
    {
        if (_pendingCustomers.Count == 0)
        {
            ToastService.Show(ToastType.SaveError, "Nie wybrano żadnych klientów do dodania.".Tr());
            return;
        }

        if (_pendingCustomers.All(x => _customersAlreadyInSale.Any(y => y.Id == x.Id)))
        {
            ToastService.ShowWarning("Wszyscy wybrani klienci już istnieją w promocji.".Tr());
            return;
        }

        var command = new AddSaleCustomerCommand(SaleId, _pendingCustomers.Where(x => _customersAlreadyInSale.All(y => y.Id != x.Id)).Select(c => c.Id).ToList());
        var result = await Mediator.Send(command);

        if (result.Succeeded)
        {
            ToastService.Show(ToastType.SaveSuccess);
            Navigation.NavigateTo($"/admin/sales/details/{SaleId}");
        }
        else
        {
            ToastService.Show(ToastType.SaveError, string.Join(',', result.Messages));
        }
    }

    private async Task AssignCustomerGroupAsync(dynamic group)
    {
        var customerGroup = group as GetAllCustomerGroupsResponse;

        if (customerGroup is null)
            return;

        _isLoading = true;

        _customersToAdd = null;

        var response = await Mediator.Send(new GetCustomerGroupMembersQuery(customerGroup.Id));

        if (response.Succeeded)
        {
            _pendingCustomers.AddRange(response.Data);
            _pendingCustomers = _pendingCustomers.DistinctBy(x => x.Id).ToList();
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError);
        }

        await _availableCustomersGrid.Reload();
        await _pendingCustomersGrid.Reload();

        _isLoading = false;
    }

    private async Task AssignCustomersFromBranchAsync(int branchId)
    {
        _isLoading = true;

        _customersToAdd = null;

        var sieveModel = new Sieve.Models.SieveModel()
        {
            Filters = $"BranchId=={branchId}",
            Page = 1,
            PageSize = int.MaxValue
        };

        var response = await Mediator.Send(new GetAllCustomersQuery(sieveModel));

        if (response.Succeeded)
        {
            _pendingCustomers.AddRange(response.Data);
            _pendingCustomers = _pendingCustomers.DistinctBy(x => x.Id).ToList();
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError);
        }

        await _availableCustomersGrid.Reload();
        await _pendingCustomersGrid.Reload();

        _isLoading = false;
    }

    private async Task GetCustomerGroupsAsync()
    {
        _isLoading = true;

        var sieveModel = new Sieve.Models.SieveModel()
        {
            Page = 1,
            PageSize = int.MaxValue,
            Filters = $"Type=={CustomerGroupType.Sale}",
        };

        var response = await Mediator.Send(new GetAllCustomerGroupsQuery(sieveModel));

        if (response.Succeeded)
        {
            _saleCustomerGroups = response.Data.ToList();
            _filteredSaleCustomerGroups = _saleCustomerGroups.ToList();
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError);
        }

        _isLoading = false;

        await InvokeAsync(StateHasChanged);
    }

    private void OnCustomerGroupLoadData(LoadDataArgs args)
    {
        var filter = args.Filter?.ToLowerInvariant() ?? string.Empty;

        _filteredSaleCustomerGroups = string.IsNullOrEmpty(filter)
            ? _saleCustomerGroups.ToList()
            : _saleCustomerGroups.Where(x =>
                (x.Name?.ToLowerInvariant().Contains(filter) ?? false)
                || (x.Code?.ToLowerInvariant().Contains(filter) ?? false))
                .ToList();

        InvokeAsync(StateHasChanged);
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        if (AppStateService != null)
        {
            AppStateService.StateChanged += OnStateChanged;
        }
    }

    void IDisposable.Dispose()
    {
        if (AppStateService != null)
        {
            AppStateService.StateChanged -= OnStateChanged;
        }
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }
}
