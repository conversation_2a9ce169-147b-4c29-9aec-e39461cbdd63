@page "/admin/sales/edit/{SaleId:int?}"
@attribute [Authorize(Roles = $"{Role.Administrator},{Role.SaleManager}")]

@using EMessa.Core.Features.Countries.Queries.GetAllCountryCurrencies
@using EMessa.Core.Features.Sales.Commands.AddEditSale
@using EMessa.Core.Features.Sales.Commands.SalePrice
@using EMessa.Core.Features.Sales.Queries.GetSaleById
@using EMessa.Web.Components.RadzenComponents.Badges
@using Radzen
@using Radzen.Blazor

@inject NavigationManager Navigation
@inject ILogger<AddEdit> Logger

<IndexCard Title=@(SaleId is null ? "Dodaj Promocje".Tr() : "Edytuj Promocje".Tr())>

    <ProgressBar IsLoading="@_isLoading" Visible="@_isLoading" />

    <RadzenTemplateForm TItem="AddEditSaleCommand"
                        Data="@_addEditSale"
                        Submit="@OnSubmit">
        <FluentValidationForRadzenComponent Validator="@(new AddEditSaleValidatorBase())" />

        <RadzenRow AlignItems="AlignItems.Start" RowGap="1rem" Gap="1rem" class="p-3">

            <RadzenColumn SizeMD="12" SizeLG="8">
                <RadzenFieldset Text="@("Parametry promocji".Tr())">
                    <RadzenRow AlignItems="AlignItems.Start" RowGap="0.4rem" Gap="0.2rem" class="p-0">
                        <RadzenColumn Size="12" SizeMD="12" SizeLG="12">
                            <RadzenLabel Text=@("Nazwa".Tr().AddText('*')) />
                            <RadzenTextArea Rows="2" @bind-Value="_addEditSale.Name" class="w-full" />
                            <ValidationMessage For="@(() => _addEditSale.Name)" class="validation-message" />
                        </RadzenColumn>

                        <RadzenColumn Size="12" SizeMD="12" SizeLG="4">
                            <RadzenLabel Text=@("Powłoka".Tr().AddText('*')) />
                            <OptionValueSelector OptionCode="@OptionCode.Coat"
                                                 @bind-Value="@_addEditSale.CoatId"
                                                 For="@(() => _addEditSale.CoatId)" />
                            <ValidationMessage For="@(() => _addEditSale.CoatId)" class="validation-message" />
                        </RadzenColumn>

                        <RadzenColumn Size="12" SizeMD="12" SizeLG="4">
                            <RadzenLabel Text=@("Kolor".Tr().AddText('*')) />
                            <OptionValueSelector OptionCode="@OptionCode.Color"
                                                 @bind-Value="@_addEditSale.ColorId"
                                                 For="@(() => _addEditSale.ColorId)" />
                            <ValidationMessage For="@(() => _addEditSale.ColorId)" class="validation-message" />
                        </RadzenColumn>

                        <RadzenColumn Size="12" SizeMD="12" SizeLG="4">
                            <RadzenLabel Text=@("Grubość".Tr().AddText('*')) />
                            <OptionValueSelector OptionCode="@OptionCode.Thickness"
                                                 @bind-Value="@_addEditSale.ThickId"
                                                 For="@(() => _addEditSale.ThickId)" />
                            <ValidationMessage For="@(() => _addEditSale.ThickId)" class="validation-message" />
                        </RadzenColumn>

                        <RadzenColumn Size="12" SizeMD="4" SizeLG="4">
                            <RadzenLabel Text=@("Waga promocji".Tr().AddText(" [kg]")) />
                            <RadzenNumeric @bind-Value="_addEditSale.DefinedWeight" class="w-full" />
                            <ValidationMessage For="@(() => _addEditSale.DefinedWeight)" class="validation-message" />
                        </RadzenColumn>

                        <RadzenColumn Size="12" SizeMD="4" SizeLG="4" Style="min-height: 56.59px" class="flex items-center">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="0" class="w-full">
                                <RadzenLabel Text=@("Ważna od".Tr().AddText('*')) />
                                <RadzenDatePicker @bind-Value="@_addEditSale.DateFrom"
                                                  ShowCalendarWeek
                                                  DateFormat="@DateFormatProvider.DisplayDateFormat" />
                                <ValidationMessage For="@(() => _addEditSale.DateFrom)" class="validation-message" />
                            </RadzenStack>
                        </RadzenColumn>

                        <RadzenColumn Size="12" SizeMD="4" SizeLG="4" Style="min-height: 56.59px" class="flex items-center">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="0" class="w-full">
                                <RadzenLabel Text=@("Ważna do".Tr().AddText('*')) />
                                <RadzenDatePicker @bind-Value="@_addEditSale.DateTo"
                                                  ShowCalendarWeek
                                                  DateFormat="@DateFormatProvider.DisplayDateFormat" />
                                <ValidationMessage For="@(() => _addEditSale.DateTo)" class="validation-message" />
                            </RadzenStack>
                        </RadzenColumn>

                        <RadzenColumn Size="12" SizeMD="12" SizeLG="12">
                            <RadzenLabel Text=@("Uwagi/Opis".Tr()) />
                            <RadzenTextArea Rows="5" @bind-Value="_addEditSale.Description" class="w-full" />
                            <ValidationMessage For="@(() => _addEditSale.Description)" class="validation-message" />
                        </RadzenColumn>
                    </RadzenRow>

                </RadzenFieldset>
            </RadzenColumn>

            <RadzenColumn SizeMD="12" SizeLG="4">
                <RadzenFieldset Text="@("Ceny promocji".Tr())">

                    <RadzenStack Orientation="Orientation.Horizontal">

                        <RadzenColumn Size="4">
                            <RadzenLabel Text=@("Cena".Tr().AddText('*')) />
                            <RadzenNumeric @bind-Value="_addPrice.Price" class="w-full" />
                        </RadzenColumn>

                        <RadzenColumn Size="4">
                            <RadzenLabel Text=@("Waluta".Tr().AddText('*')) />
                            <RadzenDropDown AllowClear="false" TValue="string"
                                            Data="@_availableCurrencies.Where(x => !_addEditSale.Prices.Any(y => y.CurrencyCode == x))"
                                            @bind-Value="_addPrice.CurrencyCode" class="w-full" />
                        </RadzenColumn>
                        <RadzenColumn Size="4" class="pt-3">
                            <AddRadzenButton Click="@AddPrice" Disabled="@(!_addPrice.Price.HasValue || _addPrice.CurrencyCode is null)" />
                        </RadzenColumn>

                    </RadzenStack>

                    <RadzenStack Orientation="Orientation.Vertical"
                                 AlignItems="AlignItems.Start"
                                 class="mt-2"
                                 Gap=".5rem">
                        @foreach (var price in _addEditSale.Prices.OrderBy(x => x.CurrencyCode))
                        {
                            <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.SpaceBetween">
                                <PriceBadge Price="@price.Price!.Value" CurrencyCode="@price.CurrencyCode" />
                                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center">
                                    <DeleteRadzenButton IsAction Click="@(() => DeletePrice(price))" />
                                </RadzenStack>
                            </RadzenStack>
                        }
                        <ValidationMessage For="@(() => _addEditSale.Prices)" class="validation-message" />
                    </RadzenStack>

                </RadzenFieldset>
            </RadzenColumn>

        </RadzenRow>
        <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" class="rz-m-4">
            <SaveRadzenButton ButtonType="ButtonType.Submit" Disabled=@_isLoading />
            <CancelRadzenButton Click="@OnCancel" Disabled=@_isLoading />
        </RadzenStack>
    </RadzenTemplateForm>
</IndexCard>

@code {
    [Parameter] public int? SaleId { get; set; }

    private AddEditSaleCommand _addEditSale = new();
    private SalePriceCommand _addPrice = new();
    private bool _isLoading = false;
    private List<string> _availableCurrencies = new();

    protected override async Task OnInitializedAsync()
    {
        _isLoading = true;

        _addEditSale.Prices = new List<SalePriceCommand>();

        var currenciesResponse = await Mediator.Send(new GetAllCountryCurrenciesQuery());

        if (currenciesResponse.Succeeded)
        {
            _availableCurrencies = currenciesResponse.Data.ToList();
        }
        else
        {
            _availableCurrencies = ["PLN", "EUR", "HUF", "USD", "GBP"];
            ToastService.Show(ToastType.LoadDataError, string.Join(',', currenciesResponse.Messages));
        }

        if (SaleId is > 0)
        {
            var response = await Mediator.Send(new GetSaleByIdQuery(SaleId.Value));

            if (response.Succeeded && response.Data != null)
            {
                _addEditSale = Mapper.Map<AddEditSaleCommand>(response.Data);

                _addEditSale.DateFrom = _addEditSale.DateFrom.ToLocalDateTime();
                _addEditSale.DateTo =  new DateTime(
                    _addEditSale.DateTo.Year,
                    _addEditSale.DateTo.Month,
                    _addEditSale.DateTo.Day,
                    23, 59, 59);
                _addEditSale.DateTo = _addEditSale.DateTo.ToLocalDateTime();

                if (_addEditSale.Prices == null)
                {
                    _addEditSale.Prices = new List<SalePriceCommand>();
                }
            }
            else
            {
                Navigation.NavigateTo("/admin/sales/index");
                ToastService.Show(ToastType.LoadDataError, string.Join(',', response.Messages));
            }
        }

        _isLoading = false;
    }

    private async Task OnSubmit()
    {
        _isLoading = true;

        _addEditSale.DateFrom = _addEditSale.DateFrom.ToUtcDateTime();
        _addEditSale.DateTo = _addEditSale.DateTo.ToUtcDateTime();

        var result = await Mediator.Send(_addEditSale);

        if (result.Succeeded)
        {
            ToastService.Show(ToastType.SaveSuccess);
            Navigation.NavigateTo($"/admin/sales/details/{result.Data}");
        }
        else
        {
            ToastService.Show(ToastType.SaveError, string.Join(',', result.Messages));
        }

        _isLoading = false;
    }

    private void OnCancel()
    {
        if (SaleId is > 0)
            Navigation.NavigateTo($"/admin/sales/details/{SaleId}");
        else
            Navigation.NavigateTo("/admin/sales/index");
    }

    private void AddPrice()
    {
        _addEditSale.Prices.Add(_addPrice);
        _addPrice = new();
    }

    private void DeletePrice(SalePriceCommand priceToDelete)
    {
        _addEditSale.Prices.Remove(priceToDelete);
    }
}