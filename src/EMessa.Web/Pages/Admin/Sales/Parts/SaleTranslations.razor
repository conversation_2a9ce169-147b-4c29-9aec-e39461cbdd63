@using EMessa.Core.Features.MessageTranslations.Commands.AddEdit
@using EMessa.Core.Features.MessageTranslations.Queries.GetMessageTranslations
@using EMessa.Core.Features.Messages.Queries
@using EMessa.Core.Features.Messages.Queries.Get
@using EMessa.Core.Features.SaleTranslations.Commands.AddEdit
@using EMessa.Core.Features.Sales.Queries.GetSaleById
@using Radzen
@using Radzen.Blazor

@inject ILogger<SaleTranslations> Logger
@inject IMultiTranslationService MultiTranslationService
@inject NavigationManager Navigation

<RadzenTabs>
    <Tabs>
        @foreach (var lang in SystemConstants.SupportedCulturesDictionary.Where(x => x.Key != "pl"))
        {
            var saleTranslation = _addEditSaleTranslations.FirstOrDefault(x => x.LanguageCode == lang.Key);

            <RadzenTabsItem Text="@lang.Value">
                @if (saleTranslation != null)
                {
                    <RadzenTemplateForm TItem="AddEditSaleTranslationCommand"
                                        Data="@saleTranslation">
                        <RadzenRow AlignItems="AlignItems.Start" RowGap="0.5rem" Gap="1rem">

                            <FluentValidationForRadzenComponent Validator="@(new AddEditSaleTranslationValidatorBase())" />

                            <RadzenColumn Size="12" SizeMD="12" SizeLG="12">
                                <RadzenLabel Text=@("Nazwa".Tr()) />
                                <RadzenTextArea Rows="2" @bind-Value="saleTranslation.Name" class="w-full" />
                                <ValidationMessage For="@(() => saleTranslation.Name)" class="validation-message" />
                            </RadzenColumn>

                            <RadzenColumn Size="12" SizeMD="12" SizeLG="12">
                                <RadzenLabel Text=@("Opis".Tr()) />
                                <RadzenTextArea Rows="5" @bind-Value="saleTranslation.Description" class="w-full" />
                                <ValidationMessage For="@(() => saleTranslation.Description)" class="validation-message" />
                            </RadzenColumn>

                        </RadzenRow>
                        <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" Wrap="FlexWrap.Wrap" class="mt-2">
                            <RadzenButton ButtonType="ButtonType.Submit" Text="@("Zapisz".Tr())" Icon="save" ButtonStyle="ButtonStyle.Success" Click="@(async () => await OnSubmit(saleTranslation))" />
                            <RadzenButton Text="@("Tłumacz".Tr())" Icon="translate" ButtonStyle="ButtonStyle.Light" Click="@(async () => await TranslateMessage(lang.Key, saleTranslation))" />
                            <RadzenButton Text="@("Cofnij niezapisane zmiany".Tr())" Icon="undo" ButtonStyle="ButtonStyle.Light" Click="@GetDataAsync" />
                            <RadzenButton Text="@("Wyczyść pola".Tr())" Icon="cleaning_services" ButtonStyle="ButtonStyle.Light" Click="@(() => { saleTranslation.Name = ""; saleTranslation.Description = ""; })" />
                        </RadzenStack>
                    </RadzenTemplateForm>
                }
            </RadzenTabsItem>
        }
    </Tabs>
</RadzenTabs>


@code {
    [Parameter] public int? SaleId { get; set; }

    private GetSaleByIdResponse _sale = new();
    private List<AddEditSaleTranslationCommand> _addEditSaleTranslations = new();

    protected override async Task OnInitializedAsync()
    {
        await GetDataAsync();
    }

    public async Task GetDataAsync()
    {
        if (SaleId is > 0)
        {
            var response = await Mediator.Send(new GetSaleByIdQuery(SaleId.Value));

            if (response.Succeeded)
            {
                _addEditSaleTranslations = Mapper.Map<List<AddEditSaleTranslationCommand>>(response.Data.Translations);
                _sale = response.Data;

                await InvokeAsync(StateHasChanged);
            }
            else
            {
                ToastService.Show(ToastType.LoadDataError);
            }
        }
    }

    private async Task OnSubmit(AddEditSaleTranslationCommand saleTranslationCommand)
    {
        try
        {
            var result = await Mediator.Send(saleTranslationCommand);

            if (result.Succeeded)
            {
                ToastService.Show(ToastType.SaveSuccess);
            }
            else
            {
                ToastService.Show(ToastType.SaveError, string.Join(' ', result.Messages));
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas zapisywania tłumaczenia promocji.");
            ToastService.Show(ToastType.SaveError);
        }
    }

    private async Task TranslateMessage(string lang, AddEditSaleTranslationCommand model)
    {
        try
        {
            var translatedNameTask = MultiTranslationService.Translate(_sale.Name, [lang]);
            var translatedDescriptionTask = MultiTranslationService.Translate(_sale.Description ?? string.Empty, [lang]);

            await Task.WhenAll(translatedNameTask, translatedDescriptionTask);

            var translatedNameResult = await translatedNameTask;
            var translatedSubjectResult = await translatedDescriptionTask;

            if (translatedNameResult.Succeeded && translatedSubjectResult.Succeeded)
            {
                var target = _addEditSaleTranslations.First(x => x.Id == model.Id);

                target.Name = translatedNameResult.Data.FirstOrDefault().Value ?? "";
                target.Description = translatedSubjectResult.Data.FirstOrDefault().Value ?? "";
            }
            else
            {
                ToastService.ShowError("Tłumaczenie nie powiodło się.".Tr());
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas tłumaczenia promocji");
            ToastService.ShowError("Tłumaczenie nie powiodło się.".Tr());
        }
    }
}
