@using EMessa.Core.Features.Sales.Commands.AddSaleImages
@using EMessa.Core.Features.Sales.Commands.DeleteSaleImages
@using EMessa.Core.Features.Sales.Queries.GetSaleImages
@using EMessa.Core.Models
@using EMessa.Web.Models
@using Radzen
@using Radzen.Blazor

@inject TooltipService TooltipService
@inject ILogger<SaleImagesGallery> Logger
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<script>
    window.EMessa ??= {};
    window.EMessa.triggerInputClickById = (id) => {
        document.getElementById(id)?.click();
    };
</script>

<div class="w-full">
    <AddRadzenButton Text="@("Dodaj zdjęcia do promocji".Tr())"
                     Disabled="@_isLoading"
                     Click="@(() => JSRuntime.InvokeVoidAsync("EMessa.triggerInputClickById", "hiddenFileInput"))" />

    <InputFile id="hiddenFileInput"
               OnChange="@HandleFiles"
               multiple
               style="display: none;" />

    <ProgressBar IsLoading="@_isLoading" Visible="@_isLoading" />

    @if (_images.Any())
    {
        <div class="flex flex-wrap mt-4">
            @foreach (var img in _images)
            {
                <div style="position: relative; margin: 5px;">
                    <RadzenImage Path="@img.PreviewUrl" target="_blank"
                                 MouseEnter="@((er) => EmessaTooltipService.Open(er, img.FileName))"
                                 Style="width: 120px; height: 120px; object-fit: cover; border: 1px solid #ccc;" />
                    <DeleteRadzenButton style="position: absolute; top: -10px; right: -10px;" IsAction Click="@(() => DeleteSaleImage(img))" />
                </div>
            }
        </div>
    }
    else if (!_isLoading)
    {
        <RadzenAlert AlertStyle="AlertStyle.Light"
                     AllowClose="@false"
                     Style="margin-top: 10px;">
            @("Brak zdjęć dla tej promocji.".Tr())
        </RadzenAlert>
    }
</div>

@code {
    [Parameter] public required int SaleId { get; set; }
    private bool _isLoading;
    private List<GalleryImage> _images = [];

    private async Task HandleFiles(InputFileChangeEventArgs args)
    {
        _isLoading = false;

        List<ImageModel> images = [];

        var files = args.GetMultipleFiles();

        if (files.Count > 3)
        {
            ToastService.ShowWarning("Możesz dodać maksymalnie 3 zdjęcia jednocześnie.".Tr());
            return;
        }

        foreach (var file in files)
        {
            await using var stream = file.OpenReadStream(10 * 1024 * 1024);
            using var ms = new MemoryStream();
            await stream.CopyToAsync(ms);
            var bytes = ms.ToArray();

            images.Add(new ImageModel(bytes, Path.GetFileNameWithoutExtension(file.Name), Path.GetExtension(file.Name).TrimStart('.').ToLowerInvariant()));
        }

        var command = new AddSaleImagesCommand(SaleId, images);
        var result = await Mediator.Send(command);

        _isLoading = false;

        if (result.Succeeded)
        {
            ToastService.Show(ToastType.SaveSuccess);
            await LoadImages();
        }
        else
        {
            ToastService.Show(ToastType.SaveError, string.Join(", ", result.Messages));
            Logger.LogError("Failed to add sale images: {Messages}", string.Join(", ", result.Messages));
        }
    }

    private void AddImagesToSale() => Navigation.NavigateTo($"/admin/sales/addsaleimages/{SaleId}");

    protected override async Task OnInitializedAsync()
    {
        await LoadImages();
    }

    private async Task LoadImages()
    {
        _isLoading = true;

        await InvokeAsync(StateHasChanged);

        var result = await Mediator.Send(new GetSaleImagesQuery(SaleId));

        if (result.Succeeded && result.Data is not null) // mogą być null? chyba nie. zwraca []
        {
            _images = result.Data
                .Where(x => x.FileContent is not null)
                .Select(x => new GalleryImage
                {
                    Id = x.Id,
                    FileName = x.FileName,
                    PreviewUrl = $"data:image/{x.FileExtension};base64,{Convert.ToBase64String(x.FileContent!)}"
                }).ToList();
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError, string.Join(", ", result.Messages));
            Logger.LogError("Failed to add sale images: {Messages}", string.Join(", ", result.Messages));
        }

        _isLoading = false;
    }

    private async Task DeleteSaleImage(GalleryImage image)
    {
        var confirmed = await ModalDialogService.DeleteConfirmationByName(string.Join(" ,", image.FileName));

        if (confirmed == false)
            return;

        _isLoading = true;
        StateHasChanged();

        var command = new DeleteSaleImagesCommand([image.Id]);
        var result = await Mediator.Send(command);
        if (result.Succeeded)
        {
            ToastService.Show(ToastType.DeleteSuccess);
            _images.Remove(image);
        }
        else
        {
            ToastService.Show(ToastType.DeleteError, string.Join(", ", result.Messages));
        }

        _isLoading = false;
        StateHasChanged();
    }
}
