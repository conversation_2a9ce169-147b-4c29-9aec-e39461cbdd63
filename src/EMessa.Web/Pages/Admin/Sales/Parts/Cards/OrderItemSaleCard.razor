@using EMessa.Base.Enums
@using EMessa.Core.Features.Sales.Queries.CommonResponses
@using EMessa.Core.Features.Sales.Queries.GetAllSales
@using EMessa.Web.Helpers
@using Radzen.Blazor

<div class="p-3 bg-white shadow-md rounded-lg border border-gray-200 text-wrap">
    <div class="mb-2 text-xl font-semibold text-gray-800">
        @("Szczegóły pozycji zamówienia".Tr())
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Id".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@OrderItemSale.OrderItemId</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("MessaNo".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@OrderItemSale.OrderMessaNo</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Klient".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@OrderItemSale.OrderCustomerShortName</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Lokalizacja".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@OrderItemSale.OrderCustomerLocalizationName</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Status".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">
            @(EnumDisplayHelper.GetEnumTrDisplayName<OrderStatus>(OrderItemSale.OrderStatus))
        </span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Index".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@OrderItemSale.OrderItemIndex</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Produkt".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@OrderItemSale.OrderItemArticleName</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Ilość".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@OrderItemSale.OrderItemQuantity</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Waga".Tr().AddText(" [kg]")):</span>
        <span class="ml-1 text-sm text-gray-800">@OrderItemSale.OrderItemWeight:N2</span>
    </div>
</div>

@code {
    [Parameter] public required OrderItemResponseForSale OrderItemSale { get; set; }
}
