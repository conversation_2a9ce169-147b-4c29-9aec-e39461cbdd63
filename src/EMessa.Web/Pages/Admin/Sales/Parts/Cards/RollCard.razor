@using EMessa.Core.Features.Rolls.Queries
@using EMessa.Core.Features.Sales.Queries.GetSaleRolls
@using EMessa.Web.Components.RadzenComponents.Icons
@using EMessa.Web.Constants
@using Radzen.Blazor

<div class="p-3 bg-white shadow-md rounded-lg border border-gray-200 text-wrap">
    <div class="mb-2 text-xl font-semibold text-gray-800">
        <RadzenRollStatusIcon Status="@Roll.Status" Class="me-2" />
        @Roll.RollNo
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Powłoka".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@(Roll.CoatCode)</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Kolor".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@(Roll.ColorCode)</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Grubość".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@(Roll.Thick)</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Waga nom.".Tr().AddText(" [kg]")):</span>
        <span class="ml-1 text-sm text-gray-800">@(Roll.Weight)</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Waga akt.".Tr().AddText(" [kg]")):</span>
        <span class="ml-1 text-sm text-gray-800">@(Roll.CurrentWeight)</span>
    </div>

    @if (Roll is SaleRollResponse saleRoll)
    {
        <div class="mb-1">
            <span class="text-sm font-bold text-gray-600">@("Waga promocji".Tr().AddText(" [kg]")):</span>
            <span class="ml-1 text-sm text-gray-800">@(saleRoll.SaleWeight)</span>
        </div>
    }
</div>

@code {
    [Parameter] public required RollResponse Roll { get; set; }
}
