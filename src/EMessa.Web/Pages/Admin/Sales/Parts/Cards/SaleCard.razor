@using EMessa.Core.Features.Sales.Queries.GetAllSales
@using EMessa.Web.Components.RadzenComponents.Badges
@using EMessa.Web.Constants
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor

@inject TooltipService TooltipService

<div class="p-3 bg-white shadow-md rounded-lg border border-gray-200 text-wrap">
    <RadzenStack Orientation="Orientation.Horizontal"
                 JustifyContent="JustifyContent.SpaceBetween"
                 class="mb-1 text-xl font-semibold text-gray-800">
        <RadzenStack>
            @Sale.Name
        </RadzenStack>
        <RadzenStack>
            <SaleStatusBadge Status="@Sale.Status" />
        </RadzenStack>
    </RadzenStack>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Nr".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@Sale.Id.ToString()</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Od".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@(Sale.DateFrom.ToDisplayLocalDate())</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Do".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@(Sale.DateTo.ToDisplayLocalDate())</span>
    </div>

    <div class="flex items-center gap-2 mb-1">
        <RadzenCheckBox Value="@Sale.DateFromToActive" Disabled />
        <span class="text-sm text-gray-700">@("Aktywna data".Tr())</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Powłoka".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@(Sale.Coat.Value)</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Kolor".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@(Sale.Color.Value)</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Grubość".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@(Sale.Thick.Value)</span>
    </div>

    <div class="flex items-center gap-2 mb-1">
        <span class="text-sm font-bold text-gray-600">@("Dostępna waga".Tr()):</span>
        <RadzenText Text="@Sale.AvailableWeight.ToString("n0").AddText(" kg")" Style="@(Sale.AvailableWeight < 0 ? "color: red;" : "color: green;")" class="m-0 font-bold text-[14px]" />
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Zdef. waga".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@Sale.DefinedWeight.ToString("n0").AddText(" kg")</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Zdef. waga kręgów".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@Sale.Rolls.Sum(x => x.SaleWeight).ToString("n0").AddText(" kg")</span>
    </div>

    <div class="flex items-center gap-2">
        <span class="text-sm font-bold text-gray-600">@("Info".Tr()):</span>
        <RadzenRow RowGap="0px" Gap="3px">
            <RadzenColumn>
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.Center" Gap="1px"
                             MouseEnter="@((er) => TooltipService.Open(er, "Przypisani klienci: {0}".Tr(string.Join(";\n", Sale.Customers.Select(x => x.ShortName))), new TooltipOptions() { Position = TooltipPosition.Bottom, Style = "white-space: normal; word-wrap: break-word;" }))">
                    <RadzenIcon Icon="group" class="text-xl text-sky-600" />
                    <RadzenText Text="@Sale.CustomersCount.ToString()" class="text-base m-0" />
                </RadzenStack>
            </RadzenColumn>
            <RadzenColumn>
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.Center" Gap="1px"
                             MouseEnter="@((er) => TooltipService.Open(er, "Przypisane produkty: {0}".Tr(string.Join(";\n", Sale.Articles.Select(x => x.Name))), new TooltipOptions() { Position = TooltipPosition.Bottom, Style = "white-space: normal; word-wrap: break-word;" }))">
                    <RadzenIcon Icon="water" class="text-xl text-sky-600" />
                    <RadzenText Text="@Sale.ArticlesCount.ToString()" class="text-base m-0" />
                </RadzenStack>
            </RadzenColumn>
            <RadzenColumn>
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.Center" Gap="1px"
                             MouseEnter="@((er) => TooltipService.Open(er, "Przypisane kręgi: {0}".Tr(Sale.RollsCount.ToString()), new TooltipOptions() { Position = TooltipPosition.Bottom, Style = "white-space: normal; word-wrap: break-word;" }))">
                    <RadzenIcon Icon="@("\uf71e")" class="fas text-base mx-1 text-sky-600" />
                    <RadzenText Text="@Sale.RollsCount.ToString()" class="text-base m-0" />
                </RadzenStack>
            </RadzenColumn>
            <RadzenColumn>
                <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.Center" Gap="1px"
                             MouseEnter="@((er) => TooltipService.Open(er, "Zamówienia z promocji: {0}".Tr(Sale.OrderItemsCount.ToString()), new TooltipOptions() { Position = TooltipPosition.Bottom, Style = "white-space: normal; word-wrap: break-word;" }))">
                    <RadzenIcon Icon="shopping_cart" class="text-xl text-sky-600" />
                    <RadzenText Text="@Sale.OrderItemsCount.ToString()" class="text-base m-0" />
                </RadzenStack>
            </RadzenColumn>
        </RadzenRow>
    </div>
</div>

@code {
    [Parameter] public required GetAllSalesResponse Sale { get; set; }
}
