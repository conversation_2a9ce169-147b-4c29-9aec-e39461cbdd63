@using EMessa.Core.Features.ArticleOptionValuesRestrictions.Queries.GetOptionsRestrictionsForEdit
@using EMessa.Web.Helpers
@using Radzen.Blazor

<div class="p-3 bg-white shadow-md rounded-lg border border-gray-200 text-wrap">
    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Produkt".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@ArticleOptionRestriction.ArticleName</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Powłoka".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@ArticleOptionRestriction.Coat</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Kolor".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@ArticleOptionRestriction.Color</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Grubość".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@ArticleOptionRestriction.Thick</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Moduł".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@ArticleOptionRestriction.Module</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Folia".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@ArticleOptionRestriction.Foil</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Filc".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@ArticleOptionRestriction.Felt</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Wys. Przet.".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@ArticleOptionRestriction.EmbossHeight</span>
    </div>

    <div class="mb-1 flex items-center">
        <span class="text-sm font-bold text-gray-600">@("Aktywna".Tr()):</span>
        <RadzenCheckBox class="ml-2" Value="@ArticleOptionRestriction.IsActive" ReadOnly />
    </div>

    <div class="mb-1 flex items-center">
        <span class="text-sm font-bold text-gray-600">@("Edyt.".Tr()):</span>
        <RadzenCheckBox class="ml-2" Value="@ArticleOptionRestriction.IsEdited" ReadOnly />
    </div>

    <div class="mb-1 flex items-center">
        <span class="text-sm font-bold text-gray-600">@("Prom.".Tr()):</span>
        <RadzenCheckBox class="ml-2" Value="@ArticleOptionRestriction.OnlyInSale" ReadOnly />
    </div>
</div>

@code {
    [Parameter] public required EditArticleOptionRestrictionResponse ArticleOptionRestriction { get; set; }
}
