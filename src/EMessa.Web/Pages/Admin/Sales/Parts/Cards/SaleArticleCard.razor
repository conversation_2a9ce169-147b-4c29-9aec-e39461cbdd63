@using EMessa.Core.Features.Sales.Queries.CommonResponses
@using Radzen.Blazor

<div class="p-3 bg-white shadow-md rounded-lg border border-gray-200 text-wrap">
    <div class="mb-2 text-xl font-semibold text-gray-800">
        @Article.Code
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Nazwa".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@(Article.Name)</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Typ".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@Article.Type.ToString().Tr()</span>
    </div>

    <div class="flex items-center gap-2 mt-2">
        <RadzenCheckBox Value="@Article.IsActive" Disabled TValue="bool" />
        <span class="text-sm text-gray-700">@("Aktywny".Tr())</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Domyślna szerokość".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@Article.DefaultWidth.ToString("N2")</span>
    </div>

    <div class="mb-1">
        <span class="text-sm font-bold text-gray-600">@("Szerokość promocji".Tr()):</span>
        <span class="ml-1 text-sm text-gray-800">@Article.SaleWidth.ToString("N2")</span>
    </div>
</div>

@code {
    [Parameter] public required ArticleResponseForSale Article { get; set; }
}
