@using EMessa.Base.Enums
@using EMessa.Core.Features.Sales.Commands.AddSaleArticle
@using EMessa.Core.Features.Sales.Commands.RemoveSaleArticle
@using EMessa.Core.Features.Sales.Queries.CommonResponses
@using EMessa.Core.Features.Sales.Queries.GetSaleArticles
@using EMessa.DAL.Entities.Articles
@using EMessa.Web.Constants
@using EMessa.Web.Pages.Admin.Sales.Parts.Cards
@using Ra<PERSON>zen
@using Radzen.Blazor

@inject NavigationManager Navigation
@implements IDisposable

<div class="w-full">
    <RadzenStack Orientation="Orientation.Horizontal" Class="p-1 justify-start items-start gap-2 flex-wrap">
        <AddRadzenButton Text="@("Dodaj produkty do promocji".Tr())" Click="@AddArticlesToSale" Disabled="@_isLoading" />
        <DeleteRadzenButton Text="@("Usuń produkty z promocji".Tr().AddText($" ({_selectedArticlesToRemove?.Count ?? 0})"))" Click="@RemoveArticlesFromSale" Icon="group_remove" Disabled="@(_isLoading || _selectedArticlesToRemove?.Any() != true)" />
        <GridSearch @ref="_gridSearch" ValueChanged="@_grid.Reload" CssClass="ml-auto" />
    </RadzenStack>
    <ProgressBar IsLoading="@_isLoading" Visible="@_isLoading" />
    <RadzenDataGrid @ref="_grid" TItem="ArticleResponseForSale"
                    Data="@_gridData" Count="@_count" LoadData="@LoadData" IsLoading="@_isLoading"
                    AllowFiltering FilterMode="FilterMode.Simple"
                    FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                    AllowPaging PageSize="@PageSize.DefaultPageSize" PageSizeOptions="@PageSize.Pages20To200" PageSizeText="@PageSize.FormatPaginationInfo(_grid)"
                    AllowSorting AllowMultiColumnSorting
                    AllowColumnResize
                    Density="Density.Compact"
                    EmptyText="@PageSize.NoDataText"
                    SelectionMode="DataGridSelectionMode.Multiple"
                    @bind-Value="@_selectedArticlesToRemove"
                    EditMode="DataGridEditMode.Single"
                    RowEdit="@SetEditSaleArticle"
                    RowDoubleClick="@((args) => EditSaleArticle(args.Data))"
                    GridLines="@RadzenDataGridConstants.DefaultDataGridLines">

        <LoadingTemplate />

        <Columns>
            <RadzenDataGridColumn Width="35px" TextAlign="TextAlign.Center" Sortable="false" Filterable="false">
                <HeaderTemplate>
                    <RadzenCheckBox TabIndex="-1" TriState="false" TValue="bool?" InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "Select all items" } })"
                                    Value="@(_selectedArticlesToRemove == null || _selectedArticlesToRemove?.Any() != true ? false : !_gridData.All(i => _selectedArticlesToRemove.Contains(i)) ? null : _gridData.Any(i => _selectedArticlesToRemove.Contains(i)))"
                                    Change="@(args => _selectedArticlesToRemove = args == true ? _gridData.ToList() : null)" />
                </HeaderTemplate>
                <Template Context="data">
                    <RadzenCheckBox TabIndex="-1"
                                    TriState="false"
                                    Value="@(_selectedArticlesToRemove != null && _selectedArticlesToRemove.Contains(data))"
                                    InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "Select item" } })"
                                    TValue="bool" />
                </Template>
            </RadzenDataGridColumn>

            <RadzenDataGridColumn Title=@(AppStateService.IsLargeScreen ? "Akcje".Tr() : string.Empty) Filterable="false" Sortable="false"
                                  Width="@(AppStateService.IsLargeScreen ? "55px" : "30px")"
                                  Frozen="true" FrozenPosition="FrozenColumnPosition.Right">
                <Template Context="data">
                    <div class="@(AppStateService.IsLargeScreen ? "flex flex-row gap-2 justify-center" : "flex flex-col gap-2 items-center")">
                        <EditRadzenButton IsAction Click="@(_ => EditSaleArticle(data))" />
                        <RadzenButton ButtonStyle="ButtonStyle.Info" Icon="visibility"
                                      Size="ButtonSize.ExtraSmall" Click="@(_ => Navigation.NavigateTo($"/admin/articles/view/{data.Id}"))" />
                    </div>
                </Template>
                <EditTemplate Context="data">
                    <div class="@(AppStateService.IsLargeScreen ? "flex flex-row gap-2 justify-center" : "flex flex-col gap-2 items-center")">
                        <SaveRadzenButton IsAction Click="@(_ => SaveEditSaleArticle(data))" />
                        <CancelRadzenButton IsAction Click="@(_ => CancelEditSaleArticle(data))" />
                    </div>
                </EditTemplate>
            </RadzenDataGridColumn>

            <RadzenDataGridColumn Property="@nameof(ArticleResponseForSale.Code)" Title=@("Kod".Tr())
                                  Width="15%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable />
            <RadzenDataGridColumn Property="@nameof(ArticleResponseForSale.Name)" Title=@("Nazwa".Tr())
                                  Width="35%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable />
            <RadzenDataGridColumn Property="@nameof(ArticleResponseForSale.Type)" Title=@("Typ".Tr())
                                  Width="11%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable FilterProperty="@nameof(ArticleResponseForSale.Type)"
                                  FilterOperator="FilterOperator.Equals" FilterValue="@_articleTypeFilter">
                <FilterTemplate>
                    <RadzenDropDown @bind-Value="@_articleTypeFilter"
                                    Data="@_allArticleTypeFilter"
                                    TValue="ArticleType?"
                                    AllowClear="true">
                        <Template Context="type">
                            @GetArticleType(type)
                        </Template>
                        <ValueTemplate Context="type">
                            @GetArticleType(type)
                        </ValueTemplate>
                    </RadzenDropDown>
                </FilterTemplate>
                <Template Context="article">
                    @GetArticleType(article.Type)
                </Template>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn Property="@nameof(ArticleResponseForSale.IsActive)" Title=@("Aktywny".Tr()) TextAlign="TextAlign.Center"
                                  Width="8%" Visible="@AppStateService.IsLargeScreen"
                                  Sortable Filterable>
                <Template Context="data">
                    <RadzenCheckBox @bind-Value="data.IsActive" Disabled TValue="bool" />
                </Template>
            </RadzenDataGridColumn>

            <RadzenDataGridColumn Property="@nameof(ArticleResponseForSale.DefaultWidth)" Title=@("Domyślna szer.".Tr().AddText(" [mm]"))
                                  Width="15%" Visible="@AppStateService.IsLargeScreen"
                                  Sortable Filterable>
                <Template Context="data">
                    @data.DefaultWidth.ToString("N2")
                </Template>
            </RadzenDataGridColumn>

            <RadzenDataGridColumn Property="@nameof(ArticleResponseForSale.SaleWidth)" Title=@("Szer. promocji".Tr().AddText(" [mm]"))
                                  Width="15%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable>
                <Template Context="data">
                    @data.SaleWidth.ToString("N2")
                </Template>
                <EditTemplate Context="data">
                    <RadzenNumeric @bind-Value="data.SaleWidth"
                                   Min="0"
                                   class="bg-esa-edit"
                                   Style="width: 100%; " />
                </EditTemplate>
            </RadzenDataGridColumn>

            <RadzenDataGridColumn Visible="@(!AppStateService.IsLargeScreen)" Filterable="false" Sortable="false">
                <Template Context="data">
                    <SaleArticleCard Article="@data" />
                </Template>
                <EditTemplate Context="data">
                    <RadzenStack Orientation="Orientation.Vertical" AlignItems="AlignItems.Start" class="w-full">
                        <RadzenLabel Text=@("Szerokość promocji".Tr()) />
                        <RadzenNumeric @bind-Value="data.SaleWidth"
                                       Min="0"
                                       class="bg-esa-edit"
                                       Style="width: 100%;" />
                    </RadzenStack>
                </EditTemplate>
            </RadzenDataGridColumn>
        </Columns>
    </RadzenDataGrid>
</div>

@code {
    [Parameter] public required int SaleId { get; set; }
    [Parameter] public EventCallback ReloadSale { get; set; }

    private RadzenDataGrid<ArticleResponseForSale> _grid = new();
    private IEnumerable<ArticleResponseForSale> _gridData = [];
    private int _count;
    private bool _isLoading;
    private GridSearch? _gridSearch;
    private IList<ArticleResponseForSale>? _selectedArticlesToRemove;
    private List<ArticleType> _allArticleTypeFilter = Enum.GetValues<ArticleType>().ToList();
    private ArticleType? _articleTypeFilter;
    private ArticleResponseForSale _editSaleArticle = new();

    public static string GetArticleType(dynamic type)
    {
        if (type is ArticleType articleType)
        {
            return articleType.GetDisplayName().Tr();
        }

        if (type is string typeStr && Enum.TryParse<ArticleType>(typeStr, ignoreCase: true, out var parsedStatus))
        {
            return parsedStatus.GetDisplayName().Tr();
        }

        return "";
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await _grid.Reload();
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    private async Task LoadData(LoadDataArgs args)
    {
        _isLoading = true;

        var sieveModel = args.ToSieveModel().ApplyGlobalSearchFilter(_gridSearch?.SearchValue);
        var response = await Mediator.Send(new GetSaleArticlesQuery(SaleId, sieveModel));

        if (response.Succeeded)
        {
            _gridData = response.Data.ToList();
            _count = response.TotalCount;
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError);
            _gridData = [];
            _count = 0;
        }

        _isLoading = false;
        await InvokeAsync(StateHasChanged);
    }

    private void AddArticlesToSale() => Navigation.NavigateTo($"/admin/sales/addarticles/{SaleId}");

    private async Task RemoveArticlesFromSale()
    {
        if (_selectedArticlesToRemove == null || !_selectedArticlesToRemove.Any())
        {
            ToastService.ShowWarning("Nie wybrano żadnego produktu do usunięcia.".Tr());
            return;
        }

        var confirmed = await ModalDialogService.DeleteConfirmationByName(string.Join(" ,", _selectedArticlesToRemove.Select(x => x.Code)));

        if (confirmed == true)
        {
            _isLoading = true;

            var response = await Mediator.Send(new RemoveSaleArticleCommand(SaleId, _selectedArticlesToRemove.Select(x => x.Id)));

            if (response.Succeeded)
                ToastService.Show(ToastType.DeleteSuccess);
            else
                ToastService.Show(ToastType.DeleteError, string.Join(',', response.Messages));

            await _grid.Reload();
        }

        await ReloadSale.InvokeAsync();

        _selectedArticlesToRemove = null;
        _isLoading = false;
        StateHasChanged();
    }

    private void SetEditSaleArticle(ArticleResponseForSale saleArticleEdit)
    {
        if (_editSaleArticle?.Id != saleArticleEdit.Id)
        {
            _editSaleArticle = saleArticleEdit;
        }
    }

    private async Task EditSaleArticle(ArticleResponseForSale saleArticleEdit)
    {
        if (!_grid.IsValid) return;
        await _grid.EditRow(saleArticleEdit);
    }

    private async Task CancelEditSaleArticle(ArticleResponseForSale saleArticleEdit)
    {
        _grid.CancelEditRow(saleArticleEdit);
        _editSaleArticle = new();
        await _grid.Reload();
    }

    private async Task SaveEditSaleArticle(ArticleResponseForSale saleArticleEdit)
    {
        var command = new AddSaleArticleCommand(SaleId, [new(saleArticleEdit.Id, saleArticleEdit.SaleWidth)]);

        var validator = new AddSaleArticleValidatorBase();
        var result = await validator.ValidateAsync(command);

        if (!result.IsValid)
        {
            ToastService.Show(ToastType.SaveError, string.Join('\n', result.Errors));
            return;
        }

        var response = await Mediator.Send(command);

        if (response.Succeeded)
        {
            ToastService.Show(ToastType.SaveSuccess);
            _editSaleArticle = new();
            _grid.CancelEditRow(saleArticleEdit);
            await _grid.Reload();
        }
        else
        {
            ToastService.Show(ToastType.SaveError);
        }
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        if (AppStateService != null)
        {
            AppStateService.StateChanged += OnStateChanged;
        }
    }

    void IDisposable.Dispose()
    {
        if (AppStateService != null)
        {
            AppStateService.StateChanged -= OnStateChanged;
        }
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }
}
