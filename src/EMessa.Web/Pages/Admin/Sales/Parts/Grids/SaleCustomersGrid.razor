@using EMessa.Base.Enums
@using EMessa.Core.Features.CustomerGroups.Queries.GetAll
@using EMessa.Core.Features.Customers.Commands.Delete
@using EMessa.Core.Features.Customers.Queries.GetAll
@using EMessa.Core.Features.Sales.Commands.AddSaleCustomerViaGroup
@using EMessa.Core.Features.Sales.Commands.RemoveSaleCustomer
@using EMessa.Core.Features.Sales.Queries.GetSaleCustomers
@using EMessa.Web.Components.Editors
@using EMessa.Web.Components.Grids
@using EMessa.Web.Constants
@using EMessa.Web.Pages.Admin.Customers.Parts
@using Radzen
@using Radzen.Blazor

@inject ILogger<SaleCustomersGrid> Logger
@inject NavigationManager Navigation
@implements IDisposable

<div class="w-full">
    <RadzenStack Orientation="Orientation.Horizontal" Class="p-1 justify-start items-start gap-2 flex-wrap">
        <AddRadzenButton Text="@("Dodaj klientów do promocji".Tr())" Click="@AddCustomersToSale" Disabled="@_isLoading" />
        <DeleteRadzenButton Text="@("Usuń klientów z promocji".Tr().AddText($" ({_selectedCustomersToRemove?.Count ?? 0})"))" Click="@RemoveCustomersFromSale" Icon="group_remove" Disabled="@(_isLoading || _selectedCustomersToRemove?.Any() != true)" />
        <GridSearch @ref="_gridSearch" ValueChanged="@_grid.Reload" CssClass="ml-auto" />
    </RadzenStack>
    <ProgressBar IsLoading="@_isLoading" Visible="@_isLoading" />
    <RadzenDataGrid @ref="_grid" TItem="GetAllCustomersResponse"
                    Data="@_gridData" Count="@_count" LoadData="@LoadData" IsLoading="@_isLoading"
                    AllowFiltering FilterMode="FilterMode.Simple"
                    FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                    AllowPaging PageSize="@PageSize.DefaultPageSize" PageSizeOptions="@PageSize.Pages20To200" PageSizeText="@PageSize.FormatPaginationInfo(_grid)"
                    AllowSorting AllowMultiColumnSorting
                    AllowColumnResize
                    Density="Density.Compact"
                    EmptyText="@PageSize.NoDataText"
                    SelectionMode="DataGridSelectionMode.Multiple"
                    @bind-Value="@_selectedCustomersToRemove"
                    GridLines="@RadzenDataGridConstants.DefaultDataGridLines">

        <LoadingTemplate />

        <Columns>
            <RadzenDataGridColumn Width="35px" TextAlign="TextAlign.Center" Sortable="false" Filterable="false">
                <HeaderTemplate>
                    <RadzenCheckBox TabIndex="-1" TriState="false" TValue="bool?" InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "Select all items" } })"
                                    Value="@(_selectedCustomersToRemove == null || _selectedCustomersToRemove?.Any() != true ? false : !_gridData.All(i => _selectedCustomersToRemove.Contains(i)) ? null : _gridData.Any(i => _selectedCustomersToRemove.Contains(i)))"
                                    Change="@(args => _selectedCustomersToRemove = args == true ? _gridData.ToList() : null)" />
                </HeaderTemplate>
                <Template Context="data">
                    <RadzenCheckBox TabIndex="-1"
                                    TriState="false" Value="@(_selectedCustomersToRemove != null && _selectedCustomersToRemove.Contains(data))"
                                    InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "Select item" } })"
                                    TValue="bool" />
                </Template>
            </RadzenDataGridColumn>

            <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.ShortName)" Title=@("Skrócona nazwa".Tr())
                                  Width="140px" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable />
            <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.Name)" Title=@("Nazwa".Tr())
                                  Width="200px" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable />
            <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.Address)" Title=@("Adres".Tr())
                                  Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable />
            <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.PostCode)" Title=@("Kod pocztowy".Tr())
                                  Width="120px" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable />
            <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.City)" Title=@("Miasto".Tr())
                                  Width="150px" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable />
            <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.TaxIdentificationNumber)" Title=@("NIP".Tr())
                                  Width="150px" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable />
            <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.BranchName)" Title=@("Oddział".Tr())
                                  Width="200px" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable FilterProperty="@nameof(GetAllCustomersResponse.BranchId)"
                                  FilterOperator="FilterOperator.Equals" FilterValue="@_branchFilter">
                <FilterTemplate>
                    <BranchesSelector @bind-Value="@_branchFilter"
                                      ShowActiveOnly="false"
                                      Clearable="true" />
                </FilterTemplate>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.IsActive)" Title=@("Aktywny".Tr()) Width="100px"
                                  TextAlign="TextAlign.Center" Visible="@AppStateService.IsLargeScreen"
                                  Sortable Filterable>
                <Template Context="customer">
                    <RadzenCheckBox @bind-Value="customer.IsActive" Disabled TValue="bool" />
                </Template>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn Property="@nameof(GetAllCustomersResponse.IsPrivate)"
                                  Title=@("Os. prywatna".Tr()) Width="120px"
                                  TextAlign="TextAlign.Center" Visible="@AppStateService.IsLargeScreen"
                                  Sortable Filterable>
                <Template Context="customer">
                    <RadzenCheckBox @bind-Value="customer.IsPrivate" Disabled TValue="bool" />
                </Template>
            </RadzenDataGridColumn>

            <RadzenDataGridColumn Visible="@(!AppStateService.IsLargeScreen)" Filterable="false" Sortable="false">
                <Template Context="customer">
                    <CustomerCard Customer="@customer" />
                </Template>
            </RadzenDataGridColumn>
        </Columns>
    </RadzenDataGrid>
</div>

@code {
    [Parameter] public required int SaleId { get; set; }
    [Parameter] public EventCallback ReloadSale { get; set; }

    private RadzenDataGrid<GetAllCustomersResponse> _grid = new();
    private IEnumerable<GetAllCustomersResponse> _gridData = [];
    private int _count;
    private bool _isLoading;
    private GridSearch? _gridSearch;
    private int? _branchFilter;
    private IList<GetAllCustomersResponse>? _selectedCustomersToRemove;
    private IEnumerable<GetAllCustomerGroupsResponse> _saleCustomerGroups = [];

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await _grid.Reload();
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    private async Task LoadData(LoadDataArgs args)
    {
        _isLoading = true;

        var sieveModel = args.ToSieveModel().ApplyGlobalSearchFilter(_gridSearch?.SearchValue);
        var response = await Mediator.Send(new GetSaleCustomersQuery(SaleId, sieveModel));

        if (response.Succeeded)
        {
            _gridData = response.Data.ToList();
            _count = response.TotalCount;
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError);
            _gridData = [];
            _count = 0;
        }

        _isLoading = false;
        await InvokeAsync(StateHasChanged);
    }

    private void AddCustomersToSale() => Navigation.NavigateTo($"/admin/sales/addcustomers/{SaleId}");

    private async Task RemoveCustomersFromSale()
    {
        if (_selectedCustomersToRemove == null || !_selectedCustomersToRemove.Any())
        {
            ToastService.ShowWarning("Nie wybrano żadnego klienta do usunięcia.".Tr());
            return;
        }

        var confirmed = await ModalDialogService.DeleteConfirmationByName(string.Join(" ,", _selectedCustomersToRemove.Select(x => x.ShortName)));

        if (confirmed == true)
        {
            _isLoading = true;

            var response = await Mediator.Send(new RemoveSaleCustomerCommand(SaleId, _selectedCustomersToRemove.Select(x => x.Id)));

            if (response.Succeeded)
                ToastService.Show(ToastType.DeleteSuccess);
            else
                ToastService.Show(ToastType.DeleteError, string.Join(',', response.Messages));

            await _grid.Reload();
        }

        await ReloadSale.InvokeAsync();

        _selectedCustomersToRemove = null;
        _isLoading = false;
        StateHasChanged();
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        if (AppStateService != null)
        {
            AppStateService.StateChanged += OnStateChanged;
        }
    }

    void IDisposable.Dispose()
    {
        if (AppStateService != null)
        {
            AppStateService.StateChanged -= OnStateChanged;
        }
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }
}
