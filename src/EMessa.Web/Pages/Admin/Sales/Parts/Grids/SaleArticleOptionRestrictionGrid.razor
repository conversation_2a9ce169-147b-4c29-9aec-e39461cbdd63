@using EMessa.Core.Features.ArticleOptionValuesRestrictions.Commands.GenerateOptionsRestrictionsForArticle
@using EMessa.Core.Features.ArticleOptionValuesRestrictions.Commands.SaveArticleRestrictionActiveAndOnlyInSale
@using EMessa.Core.Features.ArticleOptionValuesRestrictions.Commands.SaveArticleRestrictionsActiveAndOnlyInSale
@using EMessa.Core.Features.ArticleOptionValuesRestrictions.Queries.GetArticleOptionsRestrictionsBySieve
@using EMessa.Core.Features.ArticleOptionValuesRestrictions.Queries.GetOptionsRestrictionsForEdit
@using EMessa.Core.Features.Sales.Queries.CommonResponses
@using EMessa.Core.Features.Sales.Queries.GetSaleArticles
@using EMessa.DAL.Entities.Articles
@using EMessa.Web.Constants
@using EMessa.Web.Pages.Admin.Sales.Parts.Cards
@using Radzen
@using Radzen.Blazor

@inject ILogger<SaleArticleOptionRestrictionGrid> Logger
@inject NavigationManager Navigation
@implements IDisposable

<div class="w-full">
    <RadzenStack Orientation="Orientation.Horizontal" Class="p-1 justify-start items-start gap-2 flex-wrap">
        <RadzenButton ButtonStyle=@ButtonStyle.Light Text="@("Generuj".Tr())" Click="@RegenerateArticleRestrictions" Icon="auto_fix" Disabled="@(_isLoading)" />
        <RadzenButton ButtonStyle=@ButtonStyle.Light Text="@("Włącz widoczne".Tr().AddText($" ({_count})"))" Click="@(() => SetAllPromoStatus(true))" Icon="check_circle" Disabled="@(_isLoading || _count == 0)" />
        <RadzenButton ButtonStyle=@ButtonStyle.Light Text="@("Wyłącz widoczne".Tr().AddText($" ({_count})"))" Click="@(() => SetAllPromoStatus(false))" Icon="block" Disabled="@(_isLoading || _count == 0)" />

        <GridSearch @ref="_gridSearch" ValueChanged="@_grid.Reload" CssClass="ml-auto" />
    </RadzenStack>
    <ProgressBar IsLoading="@_isLoading" Visible="@_isLoading" />
    <RadzenDataGrid @ref="_grid" TItem="EditArticleOptionRestrictionResponse"
                    Data="@_gridData" Count="@_count" LoadData="@LoadData" IsLoading="@_isLoading"
                    AllowFiltering FilterMode="FilterMode.Simple" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                    AllowPaging PageSize="@PageSize.DefaultPageSize" PageSizeOptions="@PageSize.Pages20To200" PageSizeText="@PageSize.FormatPaginationInfo(_grid)"
                    AllowSorting AllowMultiColumnSorting
                    AllowColumnResize
                    Density="Density.Compact"
                    EmptyText="@PageSize.NoDataText"
                    EditMode="DataGridEditMode.Single"
                    RowEdit="@SetEditSaleArticle"
                    RowDoubleClick="@((args) => EditSaleArticle(args.Data))"
                    PagerAlwaysVisible
                    PagerHorizontalAlign="Radzen.HorizontalAlign.Center"
                    PagerPosition="PagerPosition.Bottom"
                    GridLines="@RadzenDataGridConstants.DefaultDataGridLines">

        <LoadingTemplate />

        <Columns>
            <RadzenDataGridColumn Title=@(AppStateService.IsLargeScreen ? "Akcje".Tr() : string.Empty) Filterable="false" Sortable="false"
                                  Width="@(AppStateService.IsLargeScreen ? "55px" : "30px")"
                                  Frozen="true" FrozenPosition="FrozenColumnPosition.Right">
                <Template Context="data">
                    <div class="@(AppStateService.IsLargeScreen ? "flex flex-row gap-2 justify-center" : "flex flex-col gap-2 items-center")">
                        <EditRadzenButton IsAction Click="@(_ => EditSaleArticle(data))" />
                    </div>
                </Template>
                <EditTemplate Context="data">
                    <div class="@(AppStateService.IsLargeScreen ? "flex flex-row gap-2 justify-center" : "flex flex-col gap-2 items-center")">
                        <SaveRadzenButton IsAction Click="@(_ => SaveEditSaleArticle(data))" />
                        <CancelRadzenButton IsAction Click="@(_ => CancelEditSaleArticle(data))" />
                    </div>
                </EditTemplate>
            </RadzenDataGridColumn>

            <RadzenDataGridColumn Property="@nameof(EditArticleOptionRestrictionResponse.ArticleName)" Title=@("Produkt".Tr())
                                  Width="15%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable />

            <RadzenDataGridColumn Property="@nameof(EditArticleOptionRestrictionResponse.Coat)" Title=@("Powłoka".Tr())
                                  Width="10%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable />

            <RadzenDataGridColumn Property="@nameof(EditArticleOptionRestrictionResponse.Color)" Title=@("Kolor".Tr())
                                  Width="10%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable />

            <RadzenDataGridColumn Property="@nameof(EditArticleOptionRestrictionResponse.Thick)" Title=@("Grub.".Tr())
                                  Width="10%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable />

            <RadzenDataGridColumn Property="@nameof(EditArticleOptionRestrictionResponse.Module)" Title=@("Moduł".Tr())
                                  Width="10%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable />

            <RadzenDataGridColumn Property="@nameof(EditArticleOptionRestrictionResponse.Foil)" Title=@("Folia".Tr())
                                  Width="10%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable />

            <RadzenDataGridColumn Property="@nameof(EditArticleOptionRestrictionResponse.Felt)" Title=@("Filc".Tr())
                                  Width="10%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable />

            <RadzenDataGridColumn Property="@nameof(EditArticleOptionRestrictionResponse.EmbossHeight)" Title=@("Wys. Przet.".Tr())
                                  Width="10%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable />

            <RadzenDataGridColumn Property="@nameof(EditArticleOptionRestrictionResponse.IsActive)" Title=@("Aktywna".Tr())
                                  Width="7%" Visible="@AppStateService.IsLargeScreen"
                                  Sortable Filterable>
                <Template Context="data">
                    <RadzenCheckBox Value="data.IsActive" ReadOnly TValue="bool" />
                </Template>
                <EditTemplate Context="data">
                    <RadzenCheckBox Value="data.IsActive" Disabled TValue="bool" />
                </EditTemplate>
            </RadzenDataGridColumn>

            <RadzenDataGridColumn Property="@nameof(EditArticleOptionRestrictionResponse.IsEdited)" Title=@("Edyt.".Tr())
                                  Width="7%" Visible="@AppStateService.IsLargeScreen"
                                  Sortable Filterable>
                <Template Context="data">
                    <RadzenCheckBox Value="data.IsEdited" ReadOnly TValue="bool" />
                </Template>
                <EditTemplate Context="data">
                    <RadzenCheckBox Value="data.IsEdited" Disabled TValue="bool" />
                </EditTemplate>
            </RadzenDataGridColumn>

            <RadzenDataGridColumn Property="@nameof(EditArticleOptionRestrictionResponse.OnlyInSale)" Title=@("Prom.".Tr())
                                  Width="7%" Visible="@AppStateService.IsLargeScreen"
                                  Sortable Filterable>
                <Template Context="data">
                    <RadzenCheckBox Value="data.OnlyInSale" ReadOnly TValue="bool" />
                </Template>
                <EditTemplate Context="data">
                    <RadzenStack Orientation="Orientation.Vertical" AlignItems="AlignItems.Start" class="w-full">
                        <RadzenCheckBox @bind-Value="data.OnlyInSale"
                                        TValue="bool" class="bg-esa-edit" />
                    </RadzenStack>
                </EditTemplate>
            </RadzenDataGridColumn>

            <RadzenDataGridColumn Visible="@(!AppStateService.IsLargeScreen)" Filterable="false" Sortable="false">
                <Template Context="data">
                    <SaleArticleOptionRestrictionCard ArticleOptionRestriction="@data" />
                </Template>
                <EditTemplate Context="data">
                    <RadzenStack Orientation="Orientation.Vertical" AlignItems="AlignItems.Start" class="w-full">
                        <RadzenLabel Text=@("Prom.".Tr()) />
                        <RadzenCheckBox @bind-Value="data.OnlyInSale"
                                        TValue="bool" />
                    </RadzenStack>
                </EditTemplate>
            </RadzenDataGridColumn>
        </Columns>
    </RadzenDataGrid>
</div>

@code {
    [Parameter] public required int SaleId { get; set; }
    private RadzenDataGrid<EditArticleOptionRestrictionResponse> _grid = new();
    private IEnumerable<EditArticleOptionRestrictionResponse> _gridData = [];
    private LoadDataArgs _gridDataArgs = new();
    private int _count;
    private bool _isLoading;
    private GridSearch? _gridSearch;
    private EditArticleOptionRestrictionResponse _editSaleArticleOptionRestriction = new();
    private IEnumerable<ArticleResponseForSale> _saleArticle = [];

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await _grid.Reload();
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    private async Task LoadData(LoadDataArgs args)
    {
        _isLoading = true;

        _gridDataArgs = args;

        var saleArticleResponse = await Mediator.Send(new GetSaleArticlesQuery(SaleId));
        int[] articleIds = [];
        if (saleArticleResponse.Succeeded)
        {
            _saleArticle = saleArticleResponse.Data;
            articleIds = saleArticleResponse.Data.Select(x => x.Id).ToArray();
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError);
            return;
        }

        var sieveModel = args.ToSieveModel().ApplyGlobalSearchFilter(_gridSearch?.SearchValue);
        var response = await Mediator.Send(new GetArticleOptionsRestrictionsBySieveQuery(sieveModel, articleIds));

        if (response.Succeeded)
        {
            _gridData = response.Data.ToList();
            _count = response.TotalCount;
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError);
            _gridData = [];
            _count = 0;
        }

        _isLoading = false;
        await InvokeAsync(StateHasChanged);
    }

    private async Task RegenerateArticleRestrictions()
    {
        if (_saleArticle == null || !_saleArticle.Any())
        {
            ToastService.ShowWarning("Promocja nie posiada żadnego produktu.".Tr());
            return;
        }

        _isLoading = true;

        var response = await Mediator.Send(new GenerateOptionsRestrictionsForArticleCommand(_saleArticle.Select(x => x.Id).ToArray()));

        if (response.Succeeded)
            ToastService.Show(ToastType.UpdateSuccess);
        else
            ToastService.Show(ToastType.UpdateError, string.Join(',', response.Messages));

        await _grid.Reload();
        _isLoading = false;
        await InvokeAsync(StateHasChanged);
    }

    private async Task SetAllPromoStatus(bool enable)
    {
        var confirmed = await ModalDialogService.Confirmation(
            "Czy na pewno chcesz {0} opcję promocji dla wszystkich wyszukanych ograniczeń opcji ({1})?".Tr(enable ? "włączyć" : "wyłączyć", _count.ToString()),
            "Zmiana ograniczeń opcji produktu");

        if (!confirmed)
            return;

        _isLoading = true;

        var articleIds = _saleArticle.Select(x => x.Id).ToArray();

        var sieveModel = _gridDataArgs.ToSieveModel().ApplyGlobalSearchFilter(_gridSearch?.SearchValue);
        sieveModel.Page = 1;
        sieveModel.PageSize = int.MaxValue;
        var response = await Mediator.Send(new GetArticleOptionsRestrictionsBySieveQuery(sieveModel, articleIds));

        if (response.Succeeded)
        {
            var response2 = await Mediator.Send(new SaveArticleRestrictionsActiveAndOnlyInSaleCommand(
                response.Data.Select(x => new SaveArticleRestrictionsActiveAndOnlyInSaleRequest(x.Id, x.IsActive, enable)).ToArray()));

            if (response2.Succeeded)
                ToastService.Show(ToastType.SaveSuccess);
            else
                ToastService.Show(ToastType.SaveError, response2.Messages);
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError, response.Messages);
        }

        await _grid.Reload();
        _isLoading = false;
        await InvokeAsync(StateHasChanged);
    }

    private void SetEditSaleArticle(EditArticleOptionRestrictionResponse saleArticleOptionRestrictionEdit)
    {
        if (_editSaleArticleOptionRestriction?.Id != saleArticleOptionRestrictionEdit.Id)
        {
            _editSaleArticleOptionRestriction = saleArticleOptionRestrictionEdit;
        }
    }

    private async Task EditSaleArticle(EditArticleOptionRestrictionResponse saleArticleOptionRestrictionEdit)
    {
        if (!_grid.IsValid) return;
        await _grid.EditRow(saleArticleOptionRestrictionEdit);
    }

    private async Task CancelEditSaleArticle(EditArticleOptionRestrictionResponse saleArticleOptionRestrictionEdit)
    {
        _grid.CancelEditRow(saleArticleOptionRestrictionEdit);
        _editSaleArticleOptionRestriction = new();
        await _grid.Reload();
    }

    private async Task SaveEditSaleArticle(EditArticleOptionRestrictionResponse saleArticleOptionRestrictionEdit)
    {
        _isLoading = true;

        var command = new SaveArticleRestrictionsActiveAndOnlyInSaleCommand(
            new SaveArticleRestrictionsActiveAndOnlyInSaleRequest(saleArticleOptionRestrictionEdit.Id, saleArticleOptionRestrictionEdit.IsActive, saleArticleOptionRestrictionEdit.OnlyInSale));

        var response = await Mediator.Send(command);

        if (response.Succeeded)
        {
            ToastService.Show(ToastType.SaveSuccess);
            _editSaleArticleOptionRestriction = new();
            _grid.CancelEditRow(saleArticleOptionRestrictionEdit);
            await _grid.Reload();
        }
        else
        {
            ToastService.Show(ToastType.SaveError);
        }

        _isLoading = false;
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        if (AppStateService != null)
        {
            AppStateService.StateChanged += OnStateChanged;
        }
    }

    void IDisposable.Dispose()
    {
        if (AppStateService != null)
        {
            AppStateService.StateChanged -= OnStateChanged;
        }
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }
}
