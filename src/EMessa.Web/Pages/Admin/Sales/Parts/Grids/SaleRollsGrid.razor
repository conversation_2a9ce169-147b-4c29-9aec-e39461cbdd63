@using EMessa.Core.Features.Rolls.Queries
@using EMessa.Core.Features.Sales.Commands.AddEditSaleRoll
@using EMessa.Core.Features.Sales.Commands.RemoveSaleRoll
@using EMessa.Core.Features.Sales.Queries.GetSaleRolls
@using EMessa.Web.Components.Editors
@using EMessa.Web.Components.Grids
@using EMessa.Web.Components.RadzenComponents.Icons
@using EMessa.Web.Constants
@using EMessa.Web.Pages.Admin.Customers.Parts
@using EMessa.Web.Pages.Admin.Sales.Parts.Cards
@using Radzen
@using Radzen.Blazor

@inject ILogger<SaleRollsGrid> Logger
@inject NavigationManager Navigation
@implements IDisposable

<div class="w-full">
    <RadzenStack Orientation="Orientation.Horizontal" Class="p-1 justify-start items-start gap-2 flex-wrap">
        <AddRadzenButton Text="@("Dodaj kręgi do promocji".Tr())" Click="@AddArticlesToSale" Disabled="@_isLoading" />
        <DeleteRadzenButton Text="@("Usuń kręgi z promocji".Tr().AddText($" ({_selectedRollsToRemove?.Count ?? 0})"))" Click="@RemoveArticlesFromSale" Icon="group_remove" Disabled="@(_isLoading || _selectedRollsToRemove?.Any() != true)" />
        <GridSearch @ref="_gridSearch" ValueChanged="@_grid.Reload" CssClass="ml-auto" />
    </RadzenStack>
    <ProgressBar IsLoading="@_isLoading" Visible="@_isLoading" />
    <RadzenDataGrid @ref="_grid" TItem="SaleRollResponse"
                    Data="@_gridData" Count="@_count" LoadData="@LoadData" IsLoading="@_isLoading"
                    AllowFiltering FilterMode="FilterMode.Simple"
                    FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                    AllowPaging PageSize="@PageSize.DefaultPageSize" PageSizeOptions="@PageSize.Pages20To200" PageSizeText="@PageSize.FormatPaginationInfo(_grid)"
                    AllowSorting AllowMultiColumnSorting
                    AllowColumnResize
                    Density="Density.Compact"
                    EmptyText="@PageSize.NoDataText"
                    SelectionMode="DataGridSelectionMode.Multiple"
                    @bind-Value="@_selectedRollsToRemove"
                    EditMode="DataGridEditMode.Single"
                    RowEdit="@SetEditSaleArticle"
                    RowDoubleClick="@((args) => EditSaleArticle(args.Data))"
                    GridLines="@RadzenDataGridConstants.DefaultDataGridLines">

        <LoadingTemplate />

        <Columns>
            <RadzenDataGridColumn Width="35px" TextAlign="TextAlign.Center" Sortable="false" Filterable="false">
                <HeaderTemplate>
                    <RadzenCheckBox TabIndex="-1" TriState="false" TValue="bool?" InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "Select all items" } })"
                                    Value="@(_selectedRollsToRemove == null || _selectedRollsToRemove?.Any() != true ? false : !_gridData.All(i => _selectedRollsToRemove.Contains(i)) ? null : _gridData.Any(i => _selectedRollsToRemove.Contains(i)))"
                                    Change="@(args => _selectedRollsToRemove = args == true ? _gridData.ToList() : null)" />
                </HeaderTemplate>
                <Template Context="data">
                    <RadzenCheckBox TabIndex="-1"
                                    TriState="false"
                                    Value="@(_selectedRollsToRemove != null && _selectedRollsToRemove.Contains(data))"
                                    InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "Select item" } })"
                                    TValue="bool" />
                </Template>
            </RadzenDataGridColumn>

            <RadzenDataGridColumn Title=@(AppStateService.IsLargeScreen ? "Akcje".Tr() : string.Empty) Filterable="false" Sortable="false"
                                  Width="@(AppStateService.IsLargeScreen ? "55px" : "30px")"
                                  Frozen="true" FrozenPosition="FrozenColumnPosition.Right">
                <Template Context="data">
                    <div class="@(AppStateService.IsLargeScreen ? "flex flex-row gap-2 justify-center" : "flex flex-col gap-2 items-center")">
                        <EditRadzenButton IsAction Click="@(_ => EditSaleArticle(data))" />
                    </div>
                </Template>
                <EditTemplate Context="data">
                    <div class="@(AppStateService.IsLargeScreen ? "flex flex-row gap-2 justify-center" : "flex flex-col gap-2 items-center")">
                        <SaveRadzenButton IsAction Click="@(_ => SaveEditSaleArticle(data))" />
                        <CancelRadzenButton IsAction Click="@(_ => CancelEditSaleArticle(data))" />
                    </div>
                </EditTemplate>
            </RadzenDataGridColumn>

            <RadzenDataGridColumn Property="@nameof(SaleRollResponse.Status)"
                                  Width="50px" Visible="@AppStateService.IsLargeScreen" TextAlign="TextAlign.Center"
                                  Sortable="false" Filterable="false">
                <Template Context="data">
                    <RadzenRollStatusIcon Status="@data.Status" />
                </Template>
            </RadzenDataGridColumn>

            <RadzenDataGridColumn Property="@nameof(SaleRollResponse.RollNo)" Title=@("Nr.".Tr())
                                  Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable />
            <RadzenDataGridColumn Property="@nameof(SaleRollResponse.CoatCode)" Title=@("Powłoka".Tr())
                                  Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable />
            <RadzenDataGridColumn Property="@nameof(SaleRollResponse.ColorCode)" Title=@("Kolor".Tr())
                                  Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable />
            <RadzenDataGridColumn Property="@nameof(SaleRollResponse.Thick)" Title=@("Grubość".Tr().AddText(" [mm]"))
                                  Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable />
            <RadzenDataGridColumn Property="@nameof(SaleRollResponse.CurrentWeight)" Title=@("Waga akt.".Tr().AddText(" [kg]"))
                                  Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable />
            <RadzenDataGridColumn Property="@nameof(SaleRollResponse.SaleWeight)" Title=@("Waga promocji".Tr().AddText(" [kg]"))
                                  Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable>
                <Template Context="data">
                    @data.SaleWeight.ToString("N2")
                </Template>
                <EditTemplate Context="data">
                    <RadzenNumeric @bind-Value="data.SaleWeight"
                                   Min="0"
                                   class="bg-esa-edit"
                                   Style="width: 100%; " />
                </EditTemplate>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn Property="@nameof(SaleRollResponse.Weight)" Title=@("Waga nom.".Tr().AddText(" [kg]"))
                                  Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable />

            <RadzenDataGridColumn Visible="@(!AppStateService.IsLargeScreen)" Filterable="false" Sortable="false">
                <Template Context="data">
                    <RollCard Roll="@data" />
                </Template>
                <EditTemplate Context="data">
                    <RadzenStack Orientation="Orientation.Vertical" AlignItems="AlignItems.Start" class="w-full">
                        <RadzenLabel Text=@("Waga promocji".Tr()) />
                        <RadzenNumeric @bind-Value="data.SaleWeight"
                                       Min="0"
                                       Max="data.CurrentWeight"
                                       class="bg-esa-edit w-full" />
                    </RadzenStack>
                </EditTemplate>
            </RadzenDataGridColumn>
        </Columns>
    </RadzenDataGrid>
</div>

@code {
    [Parameter] public required int SaleId { get; set; }
    [Parameter] public EventCallback ReloadSale { get; set; }

    private RadzenDataGrid<SaleRollResponse> _grid = new();
    private IEnumerable<SaleRollResponse> _gridData = [];
    private int _count;
    private bool _isLoading;
    private GridSearch? _gridSearch;
    private IList<SaleRollResponse>? _selectedRollsToRemove;
    private SaleRollResponse _editSaleArticle = new();

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await _grid.Reload();
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    private async Task LoadData(LoadDataArgs args)
    {
        _isLoading = true;

        var sieveModel = args.ToSieveModel().ApplyGlobalSearchFilter(_gridSearch?.SearchValue);
        var response = await Mediator.Send(new GetSaleRollsQuery(SaleId, sieveModel));

        if (response.Succeeded)
        {
            _gridData = response.Data.ToList();
            _count = response.TotalCount;
        }
        else
        {
            ToastService.ShowError(details: "Błąd pobierania danych kręgów promocji".Tr());
            _gridData = [];
            _count = 0;
        }

        _isLoading = false;
        await InvokeAsync(StateHasChanged);
    }

    private void AddArticlesToSale() => Navigation.NavigateTo($"/admin/sales/addrolls/{SaleId}");

    private async Task RemoveArticlesFromSale()
    {
        if (_selectedRollsToRemove == null || !_selectedRollsToRemove.Any())
        {
            ToastService.ShowWarning(details: "Nie wybrano żadnego kręgu do usunięcia.".Tr());
            return;
        }

        var confirmed = await ModalDialogService.DeleteConfirmationByName(string.Join(" ,", _selectedRollsToRemove.Select(x => x.RollNo)));

        if (confirmed == true)
        {
            _isLoading = true;

            var response = await Mediator.Send(new RemoveSaleRollCommand(_selectedRollsToRemove.Select(x => x.Id)));

            if (response.Succeeded)
                ToastService.Show(ToastType.DeleteSuccess);
            else
                ToastService.Show(ToastType.DeleteError, string.Join(',', response.Messages));

            await _grid.Reload();
        }

        await ReloadSale.InvokeAsync();

        _selectedRollsToRemove = null;
        _isLoading = false;
        await InvokeAsync(StateHasChanged);
    }

    private void SetEditSaleArticle(SaleRollResponse saleArticleEdit)
    {
        if (_editSaleArticle?.Id != saleArticleEdit.Id)
        {
            _editSaleArticle = saleArticleEdit;
        }
    }

    private async Task EditSaleArticle(SaleRollResponse saleArticleEdit)
    {
        if (!_grid.IsValid) return;
        await _grid.EditRow(saleArticleEdit);
    }

    private async Task CancelEditSaleArticle(SaleRollResponse saleArticleEdit)
    {
        _grid.CancelEditRow(saleArticleEdit);
        _editSaleArticle = new();
        await _grid.Reload();
    }

    private async Task SaveEditSaleArticle(SaleRollResponse saleArticleEdit)
    {
        var command = new AddEditSaleRollCommand(SaleId, [new(saleArticleEdit.RollId, saleArticleEdit.SaleWeight)]);

        var validator = new AddSaleRollValidatorBase();
        var result = await validator.ValidateAsync(command);

        if (!result.IsValid)
        {
            ToastService.Show(ToastType.SaveError, string.Join('\n', result.Errors));
            return;
        }

        var response = await Mediator.Send(command);

        if (response.Succeeded)
        {
            ToastService.Show(ToastType.SaveSuccess);
            _editSaleArticle = new();
            _grid.CancelEditRow(saleArticleEdit);
            await _grid.Reload();
        }
        else
        {
            ToastService.Show(ToastType.SaveError);
        }
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        if (AppStateService != null)
        {
            AppStateService.StateChanged += OnStateChanged;
        }
    }

    void IDisposable.Dispose()
    {
        if (AppStateService != null)
        {
            AppStateService.StateChanged -= OnStateChanged;
        }
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }
}
