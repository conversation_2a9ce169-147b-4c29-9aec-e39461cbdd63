@using EMessa.Base.Enums
@using EMessa.Core.Features.Articles.Queries.GetAllBySieve
@using EMessa.Core.Features.Customers.Commands.Delete
@using EMessa.Core.Features.Customers.Queries.GetAll
@using EMessa.Core.Features.Sales.Commands.AddSaleArticle
@using EMessa.Core.Features.Sales.Commands.RemoveSaleArticle
@using EMessa.Core.Features.Sales.Commands.RemoveSaleCustomer
@using EMessa.Core.Features.Sales.Queries.CommonResponses
@using EMessa.Core.Features.Sales.Queries.GetSaleArticles
@using EMessa.Core.Features.Sales.Queries.GetSaleOrderItems
@using EMessa.DAL.Entities.Articles
@using EMessa.Web.Components.Editors
@using EMessa.Web.Components.Grids
@using EMessa.Web.Constants
@using EMessa.Web.Helpers
@using EMessa.Web.Pages.Admin.Customers.Parts
@using EMessa.Web.Pages.Admin.Sales.Parts.Cards
@using Radzen
@using Radzen.Blazor

@inject ILogger<SaleOrderItemsGrid> Logger
@inject NavigationManager Navigation
@implements IDisposable

<div class="w-full">
    <RadzenStack Orientation="Orientation.Horizontal" Class="p-1 justify-start items-start gap-2 flex-wrap">
        <GridSearch @ref="_gridSearch" ValueChanged="@_grid.Reload" CssClass="ml-auto" />
    </RadzenStack>
    <ProgressBar IsLoading="@_isLoading" Visible="@_isLoading" />
    <RadzenDataGrid @ref="_grid" TItem="OrderItemResponseForSale"
                    Data="@_gridData" Count="@_count" LoadData="@LoadData" IsLoading="@_isLoading"
                    AllowFiltering FilterMode="FilterMode.Simple"
                    FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                    AllowPaging PageSize="@PageSize.DefaultPageSize" PageSizeOptions="@PageSize.Pages20To200" PageSizeText="@PageSize.FormatPaginationInfo(_grid)"
                    AllowSorting AllowMultiColumnSorting
                    AllowColumnResize
                    Density="Density.Compact"
                    EmptyText="@PageSize.NoDataText"
                    GridLines="@RadzenDataGridConstants.DefaultDataGridLines">

        <LoadingTemplate />

        <Columns>
@*             <RadzenDataGridColumn Title=@(AppStateService.IsLargeScreen ? "Akcje".Tr() : string.Empty) Filterable="false" Sortable="false"
                                  Width="@(AppStateService.IsLargeScreen ? "55px" : "30px")"
                                  Frozen="true" FrozenPosition="FrozenColumnPosition.Right">
                <Template Context="data">
                    <div class="@(AppStateService.IsLargeScreen ? "flex flex-row gap-2 justify-center" : "flex flex-col gap-2 items-center")">
                        <RadzenButton ButtonStyle="ButtonStyle.Info" Icon="visibility"
                                      Size="ButtonSize.ExtraSmall" Click="@(_ => Navigation.NavigateTo($"/orders/details/{data.OrderId}"))" />
                    </div>
                </Template>
            </RadzenDataGridColumn> *@

            <RadzenDataGridColumn Property="@nameof(OrderItemResponseForSale.OrderItemId)" Title=@("Id".Tr())
                                  Width="5%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable />

            <RadzenDataGridColumn Property="@nameof(OrderItemResponseForSale.OrderMessaNo)" Title=@("MessaNo".Tr())
                                  Width="5%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable />

            <RadzenDataGridColumn Property="@nameof(OrderItemResponseForSale.OrderCustomerShortName)" Title=@("Klient".Tr())
                                  Width="20%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable />

            <RadzenDataGridColumn Property="@nameof(OrderItemResponseForSale.OrderCustomerLocalizationName)" Title=@("Lokalizacja".Tr())
                                  Width="20%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable />

            <RadzenDataGridColumn Property="@nameof(OrderItemResponseForSale.OrderStatus)" Title=@("Status".Tr())
                                  Width="10%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable FilterProperty="OrderStatus"
                                  FilterOperator="FilterOperator.Equals" FilterValue="@_orderStatusFilter">
                <FilterTemplate>
                    <RadzenDropDown @bind-Value="@_orderStatusFilter"
                                    Data="@(EnumDisplayHelper.GetAllEnumValues<OrderStatus>())"
                                    TValue="OrderStatus?"
                                    AllowClear>
                        <Template Context="data">
                            @(EnumDisplayHelper.GetEnumTrDisplayName<OrderStatus>(data))
                        </Template>
                        <ValueTemplate Context="data">
                            @(EnumDisplayHelper.GetEnumTrDisplayName<OrderStatus>(data))
                        </ValueTemplate>
                    </RadzenDropDown>
                </FilterTemplate>
                <Template Context="data">
                    @(EnumDisplayHelper.GetEnumTrDisplayName<OrderStatus>(data.OrderStatus))
                </Template>
            </RadzenDataGridColumn>

            <RadzenDataGridColumn Property="@nameof(OrderItemResponseForSale.OrderItemIndex)" Title=@("Index".Tr())
                                  Width="20%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable />

            <RadzenDataGridColumn Property="@nameof(OrderItemResponseForSale.OrderItemArticleName)" Title=@("Produkt".Tr())
                                  Width="20%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable />

            <RadzenDataGridColumn Property="@nameof(OrderItemResponseForSale.OrderItemQuantity)" Title=@("Ilość".Tr())
                                  Width="10%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable FilterMode="FilterMode.SimpleWithMenu" />

            <RadzenDataGridColumn Property="@nameof(OrderItemResponseForSale.OrderItemWeight)" Title=@("Waga".Tr())
                                  Width="10%" Visible="@AppStateService.IsLargeScreen" WhiteSpace="WhiteSpace.Wrap"
                                  Sortable Filterable FilterMode="FilterMode.SimpleWithMenu" />

            <RadzenDataGridColumn Visible="@(!AppStateService.IsLargeScreen)" Filterable="false" Sortable="false">
                <Template Context="data">
                    <OrderItemSaleCard OrderItemSale="@data" />
                </Template>
            </RadzenDataGridColumn>
        </Columns>
    </RadzenDataGrid>
</div>

@code {
    [Parameter] public required int SaleId { get; set; }
    private RadzenDataGrid<OrderItemResponseForSale> _grid = new();
    private IEnumerable<OrderItemResponseForSale> _gridData = [];
    private int _count;
    private bool _isLoading;
    private GridSearch? _gridSearch;
    private OrderStatus? _orderStatusFilter;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await _grid.Reload();
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    private async Task LoadData(LoadDataArgs args)
    {
        _isLoading = true;

        var sieveModel = args.ToSieveModel().ApplyGlobalSearchFilter(_gridSearch?.SearchValue);
        var response = await Mediator.Send(new GetSaleOrderItemsQuery(SaleId, sieveModel));

        if (response.Succeeded)
        {
            _gridData = response.Data.ToList();
            _count = response.TotalCount;
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError);
            _gridData = [];
            _count = 0;
        }

        _isLoading = false;
        await InvokeAsync(StateHasChanged);
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        if (AppStateService != null)
        {
            AppStateService.StateChanged += OnStateChanged;
        }
    }

    void IDisposable.Dispose()
    {
        if (AppStateService != null)
        {
            AppStateService.StateChanged -= OnStateChanged;
        }
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }
}
