@namespace EMessa.Web.Components.Editors
@using System.Linq.Expressions;
@using EMessa.Core.Features.OptionValues.Queries.GetAllValueForOption
@using EMessa.Core.Features.OptionValues.Queries.GetAllValuesForOptionByCode
@using Ra<PERSON>zen
@using Radzen.Blazor
@typeparam TValue

<RadzenDropDown TValue="GetAllValuesForOptionResponse"
                Placeholder="@("Wybierz".Tr())"
                @bind-SearchText=@_searchText
                Value="@_selectedValue"
                ValueChanged="@(async (value) => await SetSelectedValueAsync(value as GetAllValuesForOptionResponse))"
                Data=@_optionValuesFiltered
                LoadData=@OnLoadData
                AllowFiltering
                class="w-full">
    <Template>
        @($"[{(context as GetAllValuesForOptionResponse)?.Code}] {(context as GetAllValuesForOptionResponse)?.Value}")
    </Template>
    <ValueTemplate>
        @($"[{(context as GetAllValuesForOptionResponse)?.Code}] {(context as GetAllValuesForOptionResponse)?.Value}")
    </ValueTemplate>
</RadzenDropDown>

@code {
    [CascadingParameter] public EditContext? EditContext { get; set; }
    [Parameter] public Expression<Func<object>>? For { get; set; }
    [Parameter] public TValue? Value { get; set; }
    [Parameter] public EventCallback<TValue?> ValueChanged { get; set; }
    [Parameter] public required string OptionCode { get; set; }
    [Parameter] public bool CanSetNull { get; set; } = false;

    private GetAllValuesForOptionResponse? _selectedValue;
    private string _searchText = string.Empty;
    private IEnumerable<GetAllValuesForOptionResponse> _optionValues = [];
    private IEnumerable<GetAllValuesForOptionResponse> _optionValuesFiltered = [];
    private TValue? _value;

    private void OnLoadData(LoadDataArgs args)
    {
        if (string.IsNullOrEmpty(args.Filter))
            _optionValuesFiltered = _optionValues.ToList();

        _optionValuesFiltered = _optionValues
            .Where(x =>
                   x.Code.ToLower().Contains(args.Filter.ToLower())
                || x.Value.ToLower().Contains(args.Filter.ToLower())
                || x.No.ToString().ToLower().Contains(args.Filter.ToLower()))
                .ToList();

        InvokeAsync(StateHasChanged);
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        var response = await Mediator.Send(new GetAllValuesForOptionByCodeCommand(OptionCode));
        if (response.Succeeded)
        {
            _optionValues = response.Data;
            SetDropDownItems();

            await SetSelectedValueAsync();
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError, "Błąd pobrania wartości opcji".Tr());
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        if (!EqualityComparer<TValue?>.Default.Equals(_value, Value))
        {
            _value = Value;
            await SetSelectedValueAsync();
        }
        else if (_selectedValue == null && _optionValues.Any() && Value is not null)
        {
            await SetSelectedValueAsync();
        }
    }

    private async Task SetSelectedValueAsync()
    {
        if(!CanSetNull)
        {
            if (_optionValues == null || !_optionValues.Any() || _value == null)
                return;
        }

        var targetType = Nullable.GetUnderlyingType(typeof(TValue)) ?? typeof(TValue);

        if (targetType == typeof(int))
        {
            var intValue = Convert.ToInt32(_value);
            var selected = _optionValues.FirstOrDefault(x => x.Id == intValue);

            if (selected != null || CanSetNull)
            {
                await SetSelectedValueAsync(selected);
            }
        }
    }

    private async Task SetSelectedValueAsync(GetAllValuesForOptionResponse? value)
    {
        _selectedValue = value;
        _value = _selectedValue != null ? (TValue)(object)_selectedValue.Id : default;
        _searchText = string.Empty;

        SetDropDownItems();

        await ValueChanged.InvokeAsync(_value);

        if (EditContext != null && For != null)
        {
            EditContext.NotifyFieldChanged(FieldIdentifier.Create(For));
        }

        await InvokeAsync(StateHasChanged);
    }

    private void SetDropDownItems()
    {
        _optionValuesFiltered = _optionValues.ToList();
    }
}
