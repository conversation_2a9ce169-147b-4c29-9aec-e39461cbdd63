@page "/admin/sales/addarticles/{SaleId:int}"
@attribute [Authorize(Roles = $"{Role.Administrator},{Role.SaleManager}")]

@using EMessa.Core.Features.Articles.Queries.GetAllBySieve
@using EMessa.Core.Features.Customers.Commands.Delete
@using EMessa.Core.Features.Customers.Queries.GetAll
@using EMessa.Core.Features.Sales.Commands.AddSaleArticle
@using EMessa.Core.Features.Sales.Commands.AddSaleCustomer
@using EMessa.Core.Features.Sales.Queries.CommonResponses
@using EMessa.Core.Features.Sales.Queries.GetSaleArticles
@using EMessa.Web.Components.Editors
@using EMessa.Web.Components.Grids
@using EMessa.Web.Constants
@using EMessa.Web.Pages.Admin.Customers.Parts
@using EMessa.Web.Pages.Admin.Sales.Parts
@using EMessa.Web.Pages.Admin.Sales.Parts.Cards
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor

@inject ILogger<AddArticlesToSale> <PERSON><PERSON>
@inject NavigationManager Navigation
@implements IDisposable

<RowHighlightedAlertInfo Text="@("Kolorem oznaczono produkty, które są już dodane do tej promocji.".Tr())" />

<div class="w-full">
    <RadzenFieldset Text="@("Produkty do wybrania".Tr())">

        <RadzenStack Orientation="Orientation.Horizontal" Class="p-1 justify-start items-start gap-2 flex-wrap">
            <RadzenButton Text=@("Dodaj do listy wybranych".Tr().AddText($" ({_articlesToAdd?.Count ?? 0})")) Icon="group_add"
                          ButtonStyle="ButtonStyle.Primary" Click="@AddArticles"
                          Disabled="@(_isLoading || _articlesToAdd?.Any() != true)" />
            <GridSearch @ref="_gridSearch" ValueChanged="@_availableArticlesGrid.Reload" CssClass="ml-auto" />
        </RadzenStack>
        <ProgressBar IsLoading="@_isLoading" Visible="@_isLoading" />
        <RadzenDataGrid @ref="_availableArticlesGrid" TItem="ArticleResponseForSale"
                        Data="@_availableArticles" Count="@_availableArticlesTotalCount" LoadData="@LoadData" IsLoading="@_isLoading"
                        AllowFiltering FilterMode="FilterMode.Simple"
                        FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                        AllowPaging PageSize="@PageSize.DefaultPageSize" PageSizeOptions="@PageSize.Pages20To200" PageSizeText="@PageSize.FormatPaginationInfo(_availableArticlesGrid)"
                        AllowSorting AllowMultiColumnSorting
                        AllowColumnResize
                        Density="Density.Compact"
                        EmptyText="@PageSize.NoDataText"
                        SelectionMode="DataGridSelectionMode.Multiple"
                        @bind-Value="@_articlesToAdd"
                        RowRender="@OnRowRender"
                        GridLines="@RadzenDataGridConstants.DefaultDataGridLines">

            <LoadingTemplate />

            <Columns>
                <RadzenDataGridColumn Width="35px" Sortable="false" Filterable="false">
                    <HeaderTemplate>
                        <RadzenCheckBox TabIndex="-1" TriState="false" TValue="bool?" InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "Select all items" } })"
                                        Value="@(_articlesToAdd == null || _articlesToAdd?.Any() != true ? false : !_availableArticles.All(i => _articlesToAdd.Contains(i)) ? null : _availableArticles.Any(i => _articlesToAdd.Contains(i)))"
                                        Change="@(args => _articlesToAdd = args == true ? _availableArticles.ToList() : null)" />
                    </HeaderTemplate>
                    <Template Context="data">
                        <RadzenCheckBox TabIndex="-1"
                                        TriState="false"
                                        Value="@(_articlesToAdd != null && _articlesToAdd.Contains(data))"
                                        InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "Select item" } })"
                                        TValue="bool" />
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Property="@nameof(ArticleResponseForSale.Code)" Title=@("Kod".Tr())
                                      Width="120px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable />
                <RadzenDataGridColumn Property="@nameof(ArticleResponseForSale.Name)" Title=@("Nazwa".Tr())
                                      Width="200px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable />
                <RadzenDataGridColumn Property="@nameof(ArticleResponseForSale.Type)" Title=@("Typ".Tr())
                                      Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable />
                <RadzenDataGridColumn Property="@nameof(ArticleResponseForSale.IsActive)" Title=@("Aktywny".Tr()) Width="100px"
                                      TextAlign="TextAlign.Center" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable>
                    <Template Context="data">
                        <RadzenCheckBox @bind-Value="data.IsActive" Disabled TValue="bool" />
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Property="@nameof(ArticleResponseForSale.DefaultWidth)" Title=@("Domyślna szer.".Tr().AddText(" [mm]"))
                                      Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable>
                    <Template Context="data">
                        @data.DefaultWidth.ToString("N2")
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Property="@nameof(ArticleResponseForSale.SaleWidth)" Title=@("Szer. promocji".Tr().AddText(" [mm]"))
                                      Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable>
                    <Template Context="data">
                        @data.SaleWidth.ToString("N2")
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Visible="@(!AppStateService.IsLargeScreen)" Filterable="false" Sortable="false">
                    <Template Context="data">
                        <SaleArticleCard Article="@data" />
                    </Template>
                </RadzenDataGridColumn>
            </Columns>
        </RadzenDataGrid>
    </RadzenFieldset>

    <RadzenFieldset Text="@("Wybrane produkty".Tr())">
        <RadzenStack Orientation="Orientation.Horizontal" Class="p-1 justify-start items-start gap-2 flex-wrap">
            <RadzenButton Text=@("Usuń z listy wybranych".Tr().AddText($" ({_articlesToRemove?.Count ?? 0})")) Icon="group_remove"
                          ButtonStyle="ButtonStyle.Primary" Click="@RemoveArticles"
                          Disabled="@(_isLoading || _articlesToRemove?.Any() != true)" />
        </RadzenStack>
        <RadzenDataGrid @ref="_pendingArticlesGrid" TItem="ArticleResponseForSale"
                        Data="@_pendingArticles" Count="@(_pendingArticles.Count)" IsLoading="@_isLoading"
                        AllowFiltering FilterMode="FilterMode.Simple"
                        FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                        AllowPaging PageSize="@PageSize.DefaultPageSize" PageSizeOptions="@PageSize.Pages20To200" PageSizeText="@PageSize.FormatPaginationInfo(_pendingArticlesGrid)"
                        AllowSorting AllowMultiColumnSorting
                        AllowColumnResize
                        Density="Density.Compact"
                        EmptyText="@PageSize.NoDataText"
                        SelectionMode="DataGridSelectionMode.Multiple"
                        @bind-Value="@_articlesToRemove"
                        RowRender="@OnRowRender"
                        GridLines="@RadzenDataGridConstants.DefaultDataGridLines">

            <LoadingTemplate />

            <Columns>
                <RadzenDataGridColumn Width="35px" Sortable="false" Filterable="false">
                    <HeaderTemplate>
                        <RadzenCheckBox TabIndex="-1" TriState="false" TValue="bool?" InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "Select all items" } })"
                                        Value="@(_articlesToRemove == null || _articlesToRemove?.Any() != true ? false : !_pendingArticles.All(i => _articlesToRemove.Contains(i)) ? null : _pendingArticles.Any(i => _articlesToRemove.Contains(i)))"
                                        Change="@(args => _articlesToRemove = args == true ? _pendingArticles.ToList() : null)" />
                    </HeaderTemplate>
                    <Template Context="data">
                        <RadzenCheckBox TabIndex="-1"
                                        TriState="false"
                                        Value="@(_articlesToRemove != null && _articlesToRemove.Contains(data))"
                                        InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "Select item" } })"
                                        TValue="bool" />
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Property="@nameof(ArticleResponseForSale.Code)" Title=@("Kod".Tr())
                                      Width="120px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable />

                <RadzenDataGridColumn Property="@nameof(ArticleResponseForSale.Name)" Title=@("Nazwa".Tr())
                                      Width="200px" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable />

                <RadzenDataGridColumn Property="@nameof(ArticleResponseForSale.Type)" Title=@("Typ".Tr())
                                      Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable />

                <RadzenDataGridColumn Property="@nameof(ArticleResponseForSale.IsActive)" Title=@("Aktywny".Tr()) Width="100px"
                                      TextAlign="TextAlign.Center" Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable>
                    <Template Context="data">
                        <RadzenCheckBox @bind-Value="data.IsActive" Disabled TValue="bool" />
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Property="@nameof(ArticleResponseForSale.DefaultWidth)" Title=@("Domyślna szer.".Tr().AddText(" [mm]"))
                                      Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable>
                    <Template Context="data">
                        @data.DefaultWidth.ToString("N2")
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Property="@nameof(ArticleResponseForSale.SaleWidth)" Title=@("Szer. promocji".Tr().AddText(" [mm]"))
                                      Visible="@AppStateService.IsLargeScreen"
                                      Sortable Filterable>
                    <Template Context="data">
                        <RadzenNumeric @bind-Value="data.SaleWidth"
                                       Min="0"
                                       Format="N2"
                                       class="bg-esa-edit w-full" />
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Visible="@(!AppStateService.IsLargeScreen)" Filterable="false" Sortable="false">
                    <Template Context="data">
                        <SaleArticleCard Article="@data" />
                        <RadzenNumeric @bind-Value="data.SaleWidth"
                                       Min="0"
                                       Format="N2"
                                       class="bg-esa-edit w-full" />
                    </Template>
                </RadzenDataGridColumn>
            </Columns>
        </RadzenDataGrid>
    </RadzenFieldset>
    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" class="rz-m-4">
        <SaveRadzenButton Text="@("Dodaj produkty do promocji".Tr())" Click="@OnSubmit"
                          Disabled="@(_isLoading || _pendingArticles?.Any() != true)" />
        <CancelRadzenButton Click="@OnCancel" Disabled="@_isLoading" />
    </RadzenStack>
</div>

@code {
    [Parameter] public required int SaleId { get; set; }
    private RadzenDataGrid<ArticleResponseForSale> _availableArticlesGrid = new();
    private IEnumerable<ArticleResponseForSale> _availableArticles = [];
    private int _availableArticlesTotalCount;
    private RadzenDataGrid<ArticleResponseForSale> _pendingArticlesGrid = new();
    private List<ArticleResponseForSale> _pendingArticles = new();
    private bool _isLoading;
    private GridSearch? _gridSearch;
    private IList<ArticleResponseForSale>? _articlesToAdd;
    private IList<ArticleResponseForSale>? _articlesToRemove;
    private List<ArticleResponseForSale> _articlesAlreadyInSale = new();

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await _availableArticlesGrid.Reload();
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    private async Task GetSaleArticlesAsync()
    {
        _isLoading = true;

        try
        {
            var response = await Mediator.Send(new GetSaleArticlesQuery(SaleId));

            if (response.Succeeded)
            {
                _articlesAlreadyInSale = response.Data.ToList();
            }
            else
            {
                ToastService.Show(ToastType.LoadDataError);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas ładowania danych produktów.");
            ToastService.Show(ToastType.LoadDataError);
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task LoadData(LoadDataArgs args)
    {
        _isLoading = true;

        await GetSaleArticlesAsync();

        var sieveModel = args.ToSieveModel().ApplyGlobalSearchFilter(_gridSearch?.SearchValue);
        var response = await Mediator.Send(new GetAllArticlesBySieveQuery(sieveModel));

        if (response.Succeeded)
        {
            _availableArticles = Mapper.Map<IEnumerable<ArticleResponseForSale>>(response.Data.Where(x => _pendingArticles.All(y => y.Id != x.Id)));

            foreach (var article in _availableArticles)
            {
                article.SaleWidth = _articlesAlreadyInSale.FirstOrDefault(x => x.Id == article.Id)?.SaleWidth ?? article.DefaultWidth;
            }

            _availableArticlesTotalCount = response.TotalCount;
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError);
            _availableArticles = [];
        }

        _isLoading = false;
        await InvokeAsync(StateHasChanged);
    }

    void OnRowRender(RowRenderEventArgs<ArticleResponseForSale> args)
    {
        if (_articlesAlreadyInSale.Any(x => x.Id == args.Data.Id))
        {
            args.Attributes.Add("class", "highlighted-grid-row");
        }
    }

    private void AddArticles()
    {
        if (_articlesToAdd != null && _articlesToAdd.Any())
        {
            _pendingArticles.AddRange(_articlesToAdd);
            _pendingArticles = _pendingArticles.Distinct().ToList();
        }
        _articlesToAdd = null;

        _availableArticlesGrid.Reload();
        _pendingArticlesGrid.Reload();
    }

    private void RemoveArticles()
    {
        if (_articlesToRemove != null && _articlesToRemove.Any())
        {
            _pendingArticles.RemoveAll(c => _articlesToRemove.Contains(c));
        }
        _articlesToRemove = null;

        _availableArticlesGrid.Reload();
        _pendingArticlesGrid.Reload();
    }

    private void OnCancel() => Navigation.NavigateTo($"/admin/sales/details/{SaleId}");

    private async Task OnSubmit()
    {
        if (_pendingArticles.Count == 0)
        {
            ToastService.Show(ToastType.SaveError, "Nie wybrano żadnych produktów do dodania.".Tr());
            return;
        }

        var command = new AddSaleArticleCommand(SaleId, _pendingArticles.Select(c => new SaleArticleRequest(c.Id, c.SaleWidth)).ToList());
        var result = await Mediator.Send(command);

        if (result.Succeeded)
        {
            ToastService.Show(ToastType.SaveSuccess);
            Navigation.NavigateTo($"/admin/sales/details/{SaleId}");
        }
        else
        {
            ToastService.Show(ToastType.SaveError, string.Join(',', result.Messages));
        }
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        if (AppStateService != null)
        {
            AppStateService.StateChanged += OnStateChanged;
        }
    }

    void IDisposable.Dispose()
    {
        if (AppStateService != null)
        {
            AppStateService.StateChanged -= OnStateChanged;
        }
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }
}
