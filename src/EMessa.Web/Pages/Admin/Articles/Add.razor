@page "/admin/articles/add"
@attribute [Authorize(Roles = $"{Role.Administrator},{Role.TradeManager},{Role.Trade}")]

@using Core.Features.Articles.Commands.Add
@using EMessa.Base.Enums
@using EMessa.Core.Features.Articles.Commands.AddArticelUnit
@using EMessa.Core.Features.Articles.Queries.GetAllArticleUnits
@using EMessa.DAL.Entities.Articles
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Navigations
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.RichTextEditor
@using Syncfusion.Blazor.Spinner

@inject ILogger<Add> Logger
@inject NavigationManager NavigationManager
@inject IMediator Mediator

<EditForm Model="@AddArticleRequest"
          OnValidSubmit="SubmitAsync"
          OnInvalidSubmit="InvalidSubmitAsync">
    <FluentValidator TValidator="AddArticleRequestValidator" />
    <div>
        @* class="max-h-[calc(100dvh-100px)] overflow-y-auto" *@
        <div class="card mt-0">
            <div class="card-header font-bold">
                @("Nowy produkt".Tr())
            </div>
            <div class="card-body">
                <div class="w-full">
                    <div class="flex flex-wrap">
                        <div class="mr-3 max-w-[200px]">
                            <label class="control-label">@("Typ produktu".Tr())</label>
                            <SfDropDownList TItem="KeyValuePair<ArticleType, string>" TValue="ArticleType"
                                            @bind-Value="@AddArticleRequest.Type" Placeholder="Wybierz typ"
                                            CssClass="mb-1" DataSource="ArticleTypes">
                                <DropDownListFieldSettings Value="Key" Text="Value"></DropDownListFieldSettings>
                            </SfDropDownList>
                            <ValidationMessage For="@(() => AddArticleRequest.Type)"/>
                        </div>
                    </div>
                    <div class="text-sm italic mt-2">@("Dane podstawowe".Tr())</div>
                    <div class="p-2 border-1 border-gray-300">
                        <div class="flex flex-wrap my-1">
                            <div class="mr-5 w-full md:w-auto mb-2">
                                <SfCheckBox TChecked="bool"
                                            @bind-Checked="AddArticleRequest.IsActive"
                                            Label="@("Aktywny".Tr())"
                                            CssClass="mr-0"/>
                                <ValidationMessage For="@(() => AddArticleRequest.IsActive)"/>
                            </div>
                            <div class="mr-5 w-full md:w-auto mb-2">
                                <SfCheckBox TChecked="bool"
                                            @bind-Checked="AddArticleRequest.EditOrderCommentsEnable"
                                            Label="@("Edycja uwag pozycji zamówienia".Tr())"
                                            CssClass=""/>
                                <ValidationMessage For="@(() => AddArticleRequest.EditOrderCommentsEnable)"/>
                            </div>
                            @if (AddArticleRequest.Type == ArticleType.Complex)
                            {
                                <div class="mr-5 w-full md:w-auto mb-2">
                                    <SfCheckBox TChecked="bool"
                                                ValueChange="@ProfileEditableChanged"
                                                @bind-Checked="AddArticleRequest.ProfileEditable"
                                                Label="@("Edycja profilu. Szkic".Tr())"/>
                                    <ValidationMessage For="@(() => AddArticleRequest.ProfileEditable)"/>
                                </div>
                                <div class="mr-5 w-full md:w-auto mb-2">
                                    <SfCheckBox TChecked="bool"
                                                Disabled="@(!AddArticleRequest.ProfileEditable)"
                                                @bind-Checked="AddArticleRequest.RequireProfile"
                                                Label="@("Wymagaj szkic profilu.".Tr())"/>
                                    <ValidationMessage For="@(() => AddArticleRequest.RequireProfile)"/>
                                </div>
                                <div class="mr-5 w-full md:w-auto mb-2">
                                    <SfCheckBox TChecked="bool"
                                                @bind-Checked="AddArticleRequest.IsSplittableNParts"
                                                Label="@("Dowolny podział na N".Tr())"/>
                                    <ValidationMessage For="@(() => AddArticleRequest.IsSplittableNParts)"/>
                                </div>
                            }
                        </div>

                        <div class="flex flex-wrap">
                            <div class="w-full md:w-4/12 pr-3">
                                <label class="control-label">@("Kod".Tr())</label>
                                <SfTextBox @bind-Value="AddArticleRequest.Code" CssClass="mb-1"/>
                                <ValidationMessage For="@(() => AddArticleRequest.Code)"/>
                            </div>
                            <div class="w-full md:w-8/12 pr-3">
                                <label class="control-label">@("Nazwa".Tr())</label>
                                <SfTextBox @bind-Value="AddArticleRequest.Name" CssClass="mb-1"/>
                                <ValidationMessage For="@(() => AddArticleRequest.Name)"/>
                            </div>
                        </div>

                        <div class="flex flex-wrap mt-2">
                            <div class="e-fullrow md:w-4/12 pr-3">
                                <label class="control-label">@("Numer w katalogu".Tr())</label>
                                <SfNumericTextBox @bind-Value="AddArticleRequest.No" CssClass="mb-1"/>
                                <ValidationMessage For="@(() => AddArticleRequest.No)"/>
                            </div>
                            <div class="e-fullrow md:w-4/12 pr-3">
                                <label class="control-label">@("Waga jednostkowa".Tr())</label>
                                <SfNumericTextBox @bind-Value="AddArticleRequest.UnitWeight" CssClass="mb-1"/>
                                <ValidationMessage For="@(() => AddArticleRequest.UnitWeight)"/>
                            </div>

                            <div class="e-fullrow md:w-4/12 pr-3 items-center">
                                <div class="flex flex-nowrap">
                                    <div>
                                        <label class="control-label">@("Jednostka".Tr())</label>
                                        <div class="flex flex-nowrap">
                                            <SfComboBox TItem="GetAllArticleUnitsResponse" TValue="int?"
                                                        @bind-Value="@AddArticleRequest.ArticleUnitId"
                                                        Placeholder="Wybierz jednostkę"
                                                        DataSource="@ArticleUnits"
                                                        CssClass="mb-1">
                                                <ComboBoxFieldSettings Value="Id" Text="Name"></ComboBoxFieldSettings>
                                            </SfComboBox>
                                            <div class="mx-1">
                                                <SfButton OnClick="@ShowArticleUnitDialog" IconCss="e-icons e-plus"
                                                          CssClass="e-success e-round"></SfButton>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <ValidationMessage For="@(() => AddArticleRequest.ArticleUnitId)"/>
                            </div>
                        </div>

                        <div class="flex flex-wrap mt-2">
                            <div class="e-fullrow md:w-6/12 pr-3">
                                <label class="control-label">@("Kod obcy".Tr())</label>
                                <SfTextBox @bind-Value="AddArticleRequest.ForeignCode" CssClass="mb-1"/>
                                <ValidationMessage For="@(() => AddArticleRequest.ForeignCode)"/>
                            </div>
                            <div class="e-fullrow md:w-3/12 pr-3">
                                <label class="control-label">@("Cena bazowa".Tr())</label>
                                <SfNumericTextBox @bind-Value="AddArticleRequest.BasePrice" CssClass="mb-1"/>
                                <ValidationMessage For="@(() => AddArticleRequest.BasePrice)"/>
                            </div>
                            <div class="e-fullrow md:w-3/12 pr-3">
                                <label class="control-label">@("Ostrzeżenie ilościowe".Tr())</label>
                                <SfNumericTextBox @bind-Value="AddArticleRequest.QuantityWarning" CssClass="mb-1"/>
                                <ValidationMessage For="@(() => AddArticleRequest.QuantityWarning)"/>
                            </div>
                        </div>
                    </div>

                    @* Podstawa *@
                    @if (AddArticleRequest.Type == ArticleType.Complex)
                    {
                        <div class="text-sm italic mt-2">@("Długość arkuszy".Tr())</div>
                        <div class="p-2 border-1 border-gray-300 mb-1">
                            <div class="flex flex-wrap gap-x-3 mt-1">
                                <div class="e-fullrow md:w-2/12">
                                    <SfCheckBox TChecked="bool"
                                                @bind-Checked="AddArticleRequest.LengthEditable"
                                                Label="@("Edycja długości".Tr())"
                                                CssClass="mb-2"/>
                                    <ValidationMessage For="@(() => AddArticleRequest.LengthEditable)"/>
                                </div>
                                <div class="">
                                    <SfCheckBox TChecked="bool"
                                                @bind-Checked="AddArticleRequest.IsSplitteable"
                                                Label="@("Podział arkusza".Tr())"
                                                CssClass="mb-2"/>
                                    <ValidationMessage For="@(() => AddArticleRequest.IsSplitteable)"/>
                                </div>
                            </div>

                            <div class="flex flex-wrap mt-2">
                                <div class="e-fullrow md:w-4/12 pr-3">
                                    <label class="control-label">@("Długość domyślna".Tr())</label>
                                    <SfNumericTextBox @bind-Value="AddArticleRequest.DefaultLength" CssClass="mb-1"/>
                                    <ValidationMessage For="@(() => AddArticleRequest.DefaultLength)"/>
                                </div>
                                <div class="e-fullrow md:w-4/12 pr-3">
                                    <label class="control-label">@("Długość minimalna".Tr())</label>
                                    <SfNumericTextBox @bind-Value="AddArticleRequest.MinLength" CssClass="mb-1"/>
                                    <ValidationMessage For="@(() => AddArticleRequest.MinLength)"/>
                                </div>
                                <div class="e-fullrow md:w-4/12 pr-3">
                                    <label class="control-label">@("Długość maksymalna".Tr())</label>
                                    <SfNumericTextBox @bind-Value="AddArticleRequest.MaxLength" CssClass="mb-1"/>
                                    <ValidationMessage For="@(() => AddArticleRequest.MaxLength)"/>
                                </div>
                            </div>

                            <div class="flex flex-wrap mt-2">
                                <div class="e-fullrow md:w-4/12 pr-3">
                                    <label class="control-label">@("Całkowita długość zakładki".Tr())</label>
                                    <SfNumericTextBox @bind-Value="AddArticleRequest.OverlapLength" CssClass="mb-1"/>
                                    <ValidationMessage For="@(() => AddArticleRequest.OverlapLength)"/>
                                </div>
                                <div class="e-fullrow md:w-4/12 pr-3">
                                    <label
                                        class="control-label">@("Długość zakazanej strefy przetłoczenia".Tr())</label>
                                    <SfNumericTextBox @bind-Value="AddArticleRequest.EmbossZoneLength" CssClass="mb-1"/>
                                    <ValidationMessage For="@(() => AddArticleRequest.EmbossZoneLength)"/>
                                </div>
                                <div class="e-fullrow md:w-4/12 pr-3">
                                    <label class="control-label">@("Długość podziału opcjonalnego".Tr())</label>
                                    <SfNumericTextBox @bind-Value="AddArticleRequest.SplitLength" CssClass="mb-1"/>
                                    <ValidationMessage For="@(() => AddArticleRequest.SplitLength)"/>
                                </div>
                            </div>
                        </div>

                        <div class="text-sm italic mt-2">@("Szerokość arkuszy".Tr())</div>

                        <div class="p-2 border-1 border-gray-300 mb-1">
                            <div class="flex flex-wrap mt-2">
                                <div class="e-fullrow md:w-2/12">
                                    <SfCheckBox TChecked="bool"
                                                @bind-Checked="AddArticleRequest.WidthEditable"
                                                Label="@("Edycja szerokości".Tr())"
                                                CssClass="mb-2"/>
                                    <ValidationMessage For="@(() => AddArticleRequest.WidthEditable)"/>
                                </div>
                            </div>

                            <div class="flex flex-wrap mt-2">
                                <div class="e-fullrow md:w-4/12 pr-3">
                                    <label class="control-label">@("Szerokość domyślna".Tr())</label>
                                    <SfNumericTextBox @bind-Value="AddArticleRequest.DefaultWidth" CssClass="mb-1"/>
                                    <ValidationMessage For="@(() => AddArticleRequest.DefaultWidth)"/>
                                </div>

                                <div class="e-fullrow md:w-4/12 pr-3">
                                    <label class="control-label">@("Szerokość minimalna".Tr())</label>
                                    <SfNumericTextBox @bind-Value="AddArticleRequest.MinWidth" CssClass="mb-1"/>
                                    <ValidationMessage For="@(() => AddArticleRequest.MinWidth)"/>
                                </div>

                                <div class="e-fullrow md:w-4/12 pr-3">
                                    <label class="control-label">@("Szerokość maksymalna".Tr())</label>
                                    <SfNumericTextBox @bind-Value="AddArticleRequest.MaxWidth" CssClass="mb-1"/>
                                    <ValidationMessage For="@(() => AddArticleRequest.MaxWidth)"/>
                                </div>
                            </div>
                        </div>
                    }

                    <SfTab>
                        <TabAnimationSettings>
                            <TabAnimationPrevious Effect="AnimationEffect.None" Duration="50"></TabAnimationPrevious>
                            <TabAnimationNext Effect="AnimationEffect.None" Duration="50"></TabAnimationNext>
                        </TabAnimationSettings>
                        <TabItems>
                            <TabItem>
                                <HeaderTemplate>
                                    <div class="min-w-10">@("Opis".Tr())</div>
                                </HeaderTemplate>
                                <ContentTemplate>
                                    <SfRichTextEditor @bind-Value="AddArticleRequest.Description" Height="300"
                                                      EditorMode="EditorMode.HTML" EnableHtmlSanitizer="true"
                                                      EnableResize="true">
                                        <RichTextEditorToolbarSettings Items="@RichTextToolbarHelper.FullToolbar"
                                                                       Type="ToolbarType.Expand"/>
                                        <RichTextEditorImageSettings SaveUrl="api/Image/Save" Path="/images/editor/"/>
                                    </SfRichTextEditor>
                                    <ValidationMessage For="@(() => AddArticleRequest.Description)"/>
                                </ContentTemplate>
                            </TabItem>
                            <TabItem>
                                <HeaderTemplate>
                                    <div class="min-w-10">@("Błąd, zbyt długi arkusz".Tr())</div>
                                </HeaderTemplate>
                                <ContentTemplate>
                                    <SfRichTextEditor @bind-Value="AddArticleRequest.TooLongSheet" Height="300"
                                                      EditorMode="EditorMode.HTML" EnableHtmlSanitizer="true"
                                                      EnableResize="true">
                                        <RichTextEditorToolbarSettings Items="@RichTextToolbarHelper.FullToolbar"
                                                                       Type="ToolbarType.Expand"/>
                                        <RichTextEditorImageSettings SaveUrl="api/Image/Save" Path="/images/editor/"/>
                                    </SfRichTextEditor>
                                    <ValidationMessage For="@(() => AddArticleRequest.TooLongSheet)"/>
                                </ContentTemplate>
                            </TabItem>
                        </TabItems>
                    </SfTab>
                </div>
            </div>
        </div>
        <div class="card-footer">
            <div class="flex flex-nowrap gap-x-3 justify-between md:justify-end">
                <SfButton CssClass="e-success">@("Zapisz".Tr())</SfButton>
                <SfButton @onclick="@GoToIndexPage">@("Anuluj".Tr())</SfButton>
            </div>
        </div>
    </div>
</EditForm>
<SfSpinner @bind-Visible="@_spinnerVisible"></SfSpinner>
@* Add Article Unit Dialog *@
<SfDialog @ref="_addArticleUnitDialog" Width="500px" Visible="false" ShowCloseIcon="true" IsModal="true">
    <DialogTemplates>
        <Header>@("Dodaj jednostkę".Tr())</Header>
        <Content>
            <div class="row p-2">
                <div class="form-group">
                    <label class="control-label">@("Kod".Tr())</label>
                    <SfTextBox @bind-Value="_newArticleUnitRequest.Code" />
                </div>
                <div class="form-group">
                    <label class="control-label">@("Nazwa".Tr())</label>
                    <SfTextBox @bind-Value="_newArticleUnitRequest.Name"></SfTextBox>
                </div>
            </div>
        </Content>
    </DialogTemplates>
    <DialogButtons>
        <DialogButton OnClick="@AddNewArticleUnit" Content="@("Zapisz".Tr())" IsPrimary="true"/>
        <DialogButton OnClick="@CloseDialog" Content="@("Anuluj".Tr())"/>
    </DialogButtons>
</SfDialog>

@code {
    [CascadingParameter]
    private AppToast? Toast { get; set; }

    private SfDialog _addArticleUnitDialog = new();

    private bool _spinnerVisible = false;

    private AddArticleRequest AddArticleRequest { get; } = new();

    private List<KeyValuePair<ArticleType, string>> ArticleTypes { get; set; } = EnumExtensions.DictionaryValues<ArticleType>();

    private readonly AddArticleUnitRequest _newArticleUnitRequest = new();
    private List<GetAllArticleUnitsResponse> ArticleUnits { get; set; } = [];
    
    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        await GetAllArticleUnits();
    }

    private async Task InvalidSubmitAsync(EditContext context)
    {
        await Toast.ShowError("Błąd poprawności danych. Sprawdź dane artykułu".Tr());
    }

    private async Task SubmitAsync(EditContext context)
    {
        try
        {
            var result = await Mediator.Send(new AddArticleCommand(AddArticleRequest));
            if (result.Succeeded)
            {
                var articleAdded = result.Data;
                NavigationManager.NavigateTo($"admin/articles/view/{articleAdded.Id}");
            }
            else
            {
                await Toast!.ShowError(result.Messages.String("<br />"));
            }
        }
        catch (Exception e)
        {
            Logger.LogError(e, e.Message);
            await Toast!.ShowError("Błąd zapisu danych.".Tr());
        }
    }
    
    private async Task GetAllArticleUnits()
    {
        var result = await Mediator.Send(new GetAllArticleUnitsQuery());
        if (result.Succeeded)
        {
            ArticleUnits = result.Data;
        }
        else
        {
            await Toast!.ShowError(result.Messages.String("<br />"));
        }
    }

    private async Task AddNewArticleUnit()
    {
        var result = await Mediator.Send(new AddArticleUnitCommand(_newArticleUnitRequest));
        if (result.Succeeded)
        {
            await GetAllArticleUnits();
            await _addArticleUnitDialog.CloseDialog(null);
        }
        else
        {
            await Toast!.ShowError(result.Messages.String("<br />"));
        }
    }

    private async Task CloseDialog()
    {
        await _addArticleUnitDialog.CloseDialog(null);
    }

    private void GoToIndexPage()
    {
        NavigationManager.NavigateTo("/admin/articles/index");
    }

    private async Task ShowArticleUnitDialog()
    {
        await _addArticleUnitDialog.ShowAsync();
    }

    private void ProfileEditableChanged(Syncfusion.Blazor.Buttons.ChangeEventArgs<bool> args)
    {
        if (!args.Checked)
        {
            AddArticleRequest.RequireProfile = false;
        }
    }

}
