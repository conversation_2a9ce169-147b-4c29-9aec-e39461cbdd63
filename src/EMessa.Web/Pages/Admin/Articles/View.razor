@page "/admin/articles/view/{ArticleId:int}"
@attribute [Authorize(Roles = Role.Administrator)]

@using EMessa.Core.Features.Articles.Queries.Get
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Navigations
@using Syncfusion.Blazor.Spinner
@using EMessa.Web.Pages.Admin.Articles.Parts

@inject ILogger<Edit> Logger
@inject NavigationManager NavigationManager
@inject IGusService GusService
@implements IDisposable

@if (_article == null)
{
    <div class="p-2">
        <SfButton CssClass="e-success" @onclick="@GoToIndexPage">@("Lista".Tr())</SfButton>
    </div>
    <div class="p-2">
        <SfSpinner></SfSpinner>
    </div>
    return;
}

<div class="flex mb-2 justify-between">
    <h1 class="text-xl">@("Podgląd produktu".Tr())</h1>
    <div class="flex">
        <SfButton CssClass="mx-1 e-success" @onclick="@(()=>NavigationManager.NavigateTo($"admin/articles/index"))">@("Lista".Tr())</SfButton>
        <SfButton CssClass="mx-1 e-success" @onclick="@(()=>NavigationManager.NavigateTo($"admin/articles/edit/{ArticleId}"))">@("Edycja".Tr())</SfButton>
    </div>
</div>

<div class="border-1 p-2 bg-gray-50 rounded">
    <h1 class="text-lg">@_article.Name (@_article.Code)</h1>
    <div class="my-2 flex">
        <div class="mr-2">@("Typ produktu".Tr()): <strong>@_article.Type.GetDisplayName()</strong></div>
        <div class="mr-2">@("Aktywny".Tr()): <strong>@(_article.IsActive ? "Tak" : "Nie")</strong></div>
    </div>
</div>

<div class="mt-2">
    <SfTab>
        <TabItems>
            <TabItem>
                <ChildContent>
                    <TabHeader Text="@("Opcje".Tr())"/>
                </ChildContent>
                <ContentTemplate>
                    <ArticleOptionsComponent Article="@_article" />
                </ContentTemplate>
            </TabItem>
            <TabItem>
                <ChildContent>
                    <TabHeader Text="@("Tłumaczenia".Tr())"/>
                </ChildContent>
                <ContentTemplate><div class="p-2">
                        <ArticleTranslationsComponent Article="@_article" />
                </div></ContentTemplate>
            </TabItem>
            <TabItem>
                <ChildContent>
                    <TabHeader Text="@("Kategorie".Tr())"/>
                </ChildContent>
                <ContentTemplate>
                    <div class="p-2">
                        <ArticleCategoriesComponent Article="@_article" ShowCategoriesAdd="true" />
                    </div>
                </ContentTemplate>
            </TabItem>
            <TabItem>
                <ChildContent>
                    <TabHeader Text="@("Atrybuty filtrowania".Tr())"/>
                </ChildContent>
                <ContentTemplate>
                    <ArticlesAttributesValuesComponent Article="@_article" />
                </ContentTemplate>
            </TabItem>
            <TabItem>
                <ChildContent>
                    <TabHeader Text="@("Ograniczenia opcji".Tr())"/>
                </ChildContent>
                <ContentTemplate>
                    <ArticleOptionsRestrictionsComponent Article="@_article" />
                </ContentTemplate>
            </TabItem>
            <TabItem>
                <ChildContent>
                    <TabHeader Text="@("Walidatory".Tr())"/>
                </ChildContent>
                <ContentTemplate>
                    <AddConditionsToArticle ArticleId="@ArticleId"/>
                </ContentTemplate>
            </TabItem>
        </TabItems>
    </SfTab>
</div>

<SfSpinner @bind-Visible="@_spinnerVisible"/>

@code {
    [Parameter]
    public int ArticleId { get; set; }
    
    [CascadingParameter]
    public AppToast Toast { get; set; } = new();

    private GetArticleResponse _article = new();

    private bool _spinnerVisible;

    protected override async Task OnInitializedAsync()
    {
        AppStateService.StateChanged += OnStateChanged;

        try
        {
            var result = await Mediator.Send(new GetArticleQuery(ArticleId));

            if (result.Succeeded)
            {
                _article = result.Data;
            }
            else
            {
                await Toast.ShowError("Błąd określenia produktu".Tr());
            }
        }
        catch (Exception e)
        {
            Logger.LogError(e, e.Message);
            await Toast.ShowError("Błąd ładowania danych.".Tr());
        }
    }

    private void GoToIndexPage()
    {
        NavigationManager.NavigateTo("/admin/articles/index");
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }

    void IDisposable.Dispose()
    {
        AppStateService.StateChanged -= OnStateChanged;
    }

}
