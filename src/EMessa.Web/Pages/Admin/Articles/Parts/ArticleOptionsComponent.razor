@using EMessa.Core.Features.Articles.Queries.Get;
@using EMessa.Core.Features.Options.Queries.GetAllOptionsToArticle
@using Syncfusion.Blazor.Grids;
@using Syncfusion.Blazor.Buttons;
@using EMessa.Core.Features.ArticleOptions.Queries.GetAllOptionsForArticle;
@using EMessa.Core.Features.ArticleOptions.Queries.SetEnableAutoSetValueForArticle
@using static EMessa.Web.Pages.Admin.Articles.Parts.ArticleValuesInOptionComponent
@using static EMessa.Web.Pages.Admin.Articles.Parts.ArticleOptionValuesComponent

@inject IMediator _mediator;
@inject ILogger<ArticleOptionsComponent> _logger;

@* ArticleOptionsComponent.razor *@
<div class="row">
    <div class="flex flex-nowrap px-2 w-full md:w-6/12">
        <div class="mx-1">
            <h1 class="text-lg my-2">@("Opcje w produkcie".Tr())</h1>
            <SfGrid @ref="AllArticleOptionsGrid" DataSource="AllArticleOptions" TValue="GetAllOptionsForArticleResponse"
                    AdaptiveUIMode="@(AppStateService.GridState.AdaptiveMode)"
                    EnableAdaptiveUI="@(AppStateService.GridState.EnableAdaptiveUI)"
                    RowRenderingMode="@(AppStateService.GridState.RowDirection)">
                <GridEvents TValue="GetAllOptionsForArticleResponse"
                            DataBound="ArticleOptionsGridDataBound"
                            OnRecordClick="@ArticleOptionsGridRecordClick"/>
                <GridColumns>
                    <GridTemplates>
                        <DetailTemplate>
                            @{ var articleOption = (GetAllOptionsForArticleResponse)context; }
                            <SfCheckBox TChecked="bool" CssClass="m-2 mb-3"
                                        Label="@("Użyj wartości domyślnej".Tr())"
                                        Checked="@articleOption.UseDefaultValue"
                                        ValueChange="@(e => OnUseDefaultValueChange(e, articleOption))"/>
                            <ArticleValuesInOptionComponent
                                Option="articleOption"
                                Article="@Article"
                                OnAllOptionValuesDeleted="@RefreshAllArticleOptions"
                                OnOptionValueDeleted="@RefreshArticleOptionsDeleted">
                            </ArticleValuesInOptionComponent>
                        </DetailTemplate>
                    </GridTemplates>
                    <GridColumn Field=@nameof(GetAllOptionsForArticleResponse.Id) Visible="false"
                                IsPrimaryKey="true"/>
                    <GridColumn Field=@nameof(GetAllOptionsForArticleResponse.No) AutoFit="true"
                                HeaderText="@("Lp".Tr())"/>
                    <GridColumn Field=@nameof(GetAllOptionsForArticleResponse.Code) AutoFit="true"
                                HeaderText="@("Kod".Tr())"/>
                    <GridColumn Field=@nameof(GetAllOptionsForArticleResponse.Name) Width="auto"
                                HeaderText="@("Nazwa".Tr())"/>
                    <GridColumn Field=@nameof(GetAllOptionsForArticleResponse.UseDefaultValue) AutoFit="true"
                                HeaderText="@("Użyj wart. domyślnej".Tr())" TextAlign="TextAlign.Center"
                                DisplayAsCheckBox="true"/>
                </GridColumns>
            </SfGrid>
        </div>
    </div>
    <div class="flex flex-nowrap px-2 w-full md:w-6/12">
        <div class="mx-1">
            <h1 class="text-lg my-2">@("Wszystkie opcje".Tr())</h1>
            <SfGrid @ref="AllOptionsGrid" DataSource="AllOptions" TValue="GetAllOptionsArticleResponse"
                    AdaptiveUIMode="@(AppStateService.GridState.AdaptiveMode)"
                    EnableAdaptiveUI="@(AppStateService.GridState.EnableAdaptiveUI)"
                    RowRenderingMode="@(AppStateService.GridState.RowDirection)">
                <GridEvents TValue="GetAllOptionsArticleResponse"
                            OnRecordClick="@AllOptionsGridRecordClick"/>
                <GridColumns>
                    <GridTemplates>
                        <DetailTemplate>
                            <ArticleOptionValuesComponent Option="@((GetAllOptionsArticleResponse)context)"
                                                          Article="@Article"
                                                          OnValueAdded="@RefreshArticleOptionsAdded">
                            </ArticleOptionValuesComponent>
                        </DetailTemplate>
                    </GridTemplates>
                    <GridColumn Field=@nameof(GetAllOptionsArticleResponse.Id) Visible="false" IsPrimaryKey="true"/>
                    <GridColumn Field=@nameof(GetAllOptionsArticleResponse.No) AutoFit="true"
                                HeaderText="@("Lp".Tr())"/>
                    <GridColumn Field=@nameof(GetAllOptionsArticleResponse.Code) AutoFit="true"
                                HeaderText="@("Kod".Tr())"/>
                    <GridColumn Field=@nameof(GetAllOptionsArticleResponse.Name) HeaderText="@("Nazwa".Tr())"/>
                    <GridColumn Field=@nameof(GetAllOptionsForArticleResponse.UseDefaultValue) AutoFit="true"
                                HeaderText="@("Użyj wart. domyślnej".Tr())" TextAlign="TextAlign.Center"
                                DisplayAsCheckBox="true"/>
                </GridColumns>
            </SfGrid>
        </div>
    </div>
</div>

@code {
    [Parameter]
    public GetArticleResponse Article { get; set; } = new();

    [CascadingParameter]
    public  AppToast _toast { get; set; } = new();

    private GetAllOptionsArticleResponse? _toAddOption;

    private SfGrid<GetAllOptionsArticleResponse> AllOptionsGrid = new();
    private List<GetAllOptionsArticleResponse> AllOptions { get; set; } = [];

    private SfGrid<GetAllOptionsForArticleResponse> AllArticleOptionsGrid = new();
    private List<GetAllOptionsForArticleResponse> AllArticleOptions { get; set; } = [];

    private ArticleOptionValuesComponent AllOptionValues = new();
    private int LastOptionId;

    protected override async Task OnParametersSetAsync()
    {
        await LoadAllOtpions();
        await LoadOptionsForArticle();
        await base.OnParametersSetAsync();
    }

    private async Task LoadAllOtpions()
    {
        // GetAllOptions
        AllOptions = await _mediator.Send(new GetAllOptionsArticleQuery());
    }

    private async Task LoadOptionsForArticle()
    {
        if (Article == null || Article.Id <= 0) return;

        // GetAllArticleOptions
        var result =  await _mediator.Send(new GetAllOptionsForArticleQuery(Article.Id));
        if (result.Succeeded)
        {
            AllArticleOptions = result.Data;
        }
        else
        {
            await _toast.ShowError(result.Messages.String("<br />"));
        }
    }

    public async Task ArticleOptionsGridDataBound()
    {
        if (AllArticleOptionsGrid.DataSource.Any(x => x.Id == LastOptionId))
        {
            await AllArticleOptionsGrid.ExpandCollapseDetailRowAsync("Id", LastOptionId);
        }
    }

    public async Task RefreshAllArticleOptions()
    {
        await LoadOptionsForArticle();
    }

    public async Task RefreshArticleOptionsAdded(OnValueAddedEventArgs e)
    {
        await LoadOptionsForArticle();
        LastOptionId = e.OptionId;
    }

    public async Task RefreshArticleOptionsDeleted(OnValueDeletedEventArgs e)
    {
        await LoadOptionsForArticle();
        LastOptionId = e.OptionId;
    }

    private async Task ArticleOptionsGridRecordClick(RecordClickEventArgs<GetAllOptionsForArticleResponse> e)
    {
        if (!string.IsNullOrEmpty(e.Column.Field))
        {
            await AllArticleOptionsGrid.ExpandCollapseDetailRowAsync("Id", e.RowData.Id);
        }
    }

    private async Task AllOptionsGridRecordClick(RecordClickEventArgs<GetAllOptionsArticleResponse> e)
    {
        if (!string.IsNullOrEmpty(e.Column.Field))
        {
            await AllOptionsGrid.ExpandCollapseDetailRowAsync("Id", e.RowData.Id);
        }
    }

    private async Task OnUseDefaultValueChange(Syncfusion.Blazor.Buttons.ChangeEventArgs<bool> arg, GetAllOptionsForArticleResponse option)
    {
        var result = await _mediator.Send(new SetEnableAutoSetValueForArticleQuery(Article.Id, option.Id, arg.Checked));
        if (result.Succeeded)
        {
            option.UseDefaultValue = arg.Checked;
            await AllArticleOptionsGrid.SetRowDataAsync(option.Id, option);
        }
    }
}
