@using EMessa.Core.Exceptions
@using EMessa.Core.Expections
@using EMessa.Core.Features.ArticleFilterAttribute.Queries.GetAllFilterAttributesForArticle
@using EMessa.Core.Features.Articles.Queries.Get
@using EMessa.Web.Components.Grids
@using Syncfusion.Blazor.Grids
@inject IMediator _mediator;

<div class="row">
    <div class="col-lg-12">
        <SfGrid TValue="GetAllFilterAttributesForArticleResponse" DataSource="@AllAttributes"
                AdaptiveUIMode="@(AppStateService.GridState.AdaptiveMode)"
                EnableAdaptiveUI="@(AppStateService.GridState.EnableAdaptiveUI)"
                RowRenderingMode="@(AppStateService.GridState.RowDirection)"
                AllowFiltering="@(AppStateService.AppScreenSize == AppScreenSize.Large)"
                AllowSorting="true" AllowSelection="true">
            <GridFilterSettings Mode="FilterBarMode.Immediate" ShowFilterBarStatus="true" ImmediateModeDelay="500"></GridFilterSettings>
            <GridEvents TValue="GetAllFilterAttributesForArticleResponse"></GridEvents>
            <GridEditSettings ShowDeleteConfirmDialog="true" AllowEditing="true" Mode="EditMode.Normal">
            </GridEditSettings>
            <GridColumns>
                <GridColumn HideAtMedia="@($"(max-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllFilterAttributesForArticleResponse.Id)>
                    <Template>
                        @{
                            GetAllFilterAttributesForArticleResponse attribute = (GetAllFilterAttributesForArticleResponse)context;
                            <FilterAttributeCard FilterAttribute="attribute" >
                            </FilterAttributeCard>
                        }
                    </Template>
                </GridColumn>
                <GridTemplates>
                    <DetailTemplate>
                        @{
                            GetAllFilterAttributesForArticleResponse attribute = (GetAllFilterAttributesForArticleResponse)context;
                            <AddDeleteFilterAttributeValue Attribute="attribute" Article="@Article">
                            </AddDeleteFilterAttributeValue>
                        }
                    </DetailTemplate>
                </GridTemplates>   
                <GridColumn Field=@nameof(GetAllFilterAttributesForArticleResponse.Id) Visible="false" IsPrimaryKey="true"></GridColumn>
                <GridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllFilterAttributesForArticleResponse.OrdinaryNumber) HeaderText="@("Lp".Tr())" Width="80"></GridColumn>
                <GridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllFilterAttributesForArticleResponse.Code) HeaderText="@("Kod atrybutu".Tr())" FilterSettings="@GridFilters.BarContains"></GridColumn>
                <GridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllFilterAttributesForArticleResponse.Name) HeaderText="@("Nazwa".Tr())" FilterSettings="@GridFilters.BarContains"></GridColumn>
                <GridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllFilterAttributesForArticleResponse.DisplayName) HeaderText="@("Wyświetlana nazwa".Tr())" FilterSettings="@GridFilters.BarContains"></GridColumn>
            </GridColumns>
        </SfGrid>
    </div>
</div>

@code {
    [Parameter]
    public GetArticleResponse Article { get; set; } = new();
    [CascadingParameter]
    public Components.AppToast _toast { get; set; } = new();

    private SfGrid<GetAllFilterAttributesForArticleResponse> _defaultGrid = new();
    private List<GetAllFilterAttributesForArticleResponse> AllAttributes = [];
   
    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
    }
    private async void ActionFailureHandler(Syncfusion.Blazor.Grids.FailureEventArgs e)
    {
        var listExceptions = e.Error as ListExceptions;
        if (listExceptions != null)
        {
            await _toast!.ShowError(listExceptions.Messages.String());
        }
        else
        {
            await _toast!.ShowError("Nieokreślony błąd operacji".Tr());
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        await LoadAttributesForArticle();
        await base.OnParametersSetAsync();
    }
    
    private async Task LoadAttributesForArticle()
    {
        if (Article == null || Article.Id <= 0) return;

        var result = await _mediator.Send(new GetAllFilterAttributeForArticleQuery(Article.Id));

        AllAttributes = result;
    }
}
