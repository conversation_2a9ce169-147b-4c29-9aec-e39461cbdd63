@page "/admin/articles/edit/{ArticleId:int}"
@attribute [Authorize(Roles = Role.Administrator)]
@using EMessa.Base.Enums
@using EMessa.Core.Features.Articles.Commands.Update
@using EMessa.Core.Features.Articles.Queries.GetAllArticleUnits
@using EMessa.Core.Features.Articles.Queries.GetUpdateRequest
@using EMessa.DAL.Entities.Articles
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.DropDowns
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Navigations
@using Syncfusion.Blazor.RichTextEditor

@inject ILogger<Edit> Logger
@inject NavigationManager NavigationManager
@inject IMediator Mediator
@inject IMapper Mapper

<EditForm Model="@_updateArticleRequest" OnValidSubmit="@HandleValidSubmit" OnInvalidSubmit="@HandleInvalidSubmit">
    <FluentValidator TValidator="UpdateArticleRequestValidator" />
    <div class="card mt-0">
        <div class="card-header font-bold">
            @("Edycja produktu".Tr())
        </div>
        <div class="card-body">
            <div class="w-full">
                <div class="flex flex-wrap">
                    <div class="mr-3 max-w-[200px]">
                        <label class="control-label">@("Typ produktu".Tr())</label>
                        <SfDropDownList TItem="KeyValuePair<ArticleType, string>" TValue="ArticleType"
                                        @bind-Value="@_updateArticleRequest.Type" Placeholder="Wybierz typ"
                                        CssClass="mb-1" DataSource="ArticleTypes">
                            <DropDownListFieldSettings Value="Key" Text="Value"></DropDownListFieldSettings>
                        </SfDropDownList>
                        <ValidationMessage For="@(() => _updateArticleRequest.Type)"/>
                    </div>
                </div>
                <div class="text-sm italic mt-2">@("Dane podstawowe".Tr())</div>
                <div class="p-2 border-1 border-gray-300">
                    <div class="flex flex-wrap my-1">
                        <div class="mr-5 w-full md:w-auto mb-2">
                            <SfCheckBox TChecked="bool"
                                        @bind-Checked="_updateArticleRequest.IsActive"
                                        Label="@("Aktywny".Tr())"
                                        CssClass="mr-0" />
                            <ValidationMessage For="@(() => _updateArticleRequest.IsActive)"/>
                        </div>
                        <div class="mr-5 w-full md:w-auto mb-2">
                            <SfCheckBox TChecked="bool"
                                        @bind-Checked="_updateArticleRequest.EditOrderCommentsEnable"
                                        Label="@("Edycja uwag pozycji zamówienia".Tr())"
                                        CssClass=""/>
                            <ValidationMessage For="@(() => _updateArticleRequest.EditOrderCommentsEnable)"/>
                        </div>
                        @if (_updateArticleRequest.Type == ArticleType.Complex)
                        {
                            <div class="mr-5 w-full md:w-auto mb-2">
                                <SfCheckBox TChecked="bool"
                                            ValueChange="@ProfileEditableChanged"
                                            @bind-Checked="_updateArticleRequest.ProfileEditable"
                                            Label="@("Edycja profilu. Szkic".Tr())"/>
                                <ValidationMessage For="@(() => _updateArticleRequest.ProfileEditable)"/>
                            </div>
                            <div class="mr-5 w-full md:w-auto mb-2">
                                <SfCheckBox TChecked="bool"
                                            Disabled="@(!_updateArticleRequest.ProfileEditable)"
                                            @bind-Checked="_updateArticleRequest.RequireProfile"
                                            Label="@("Wymagaj szkic profilu.".Tr())"/>
                                <ValidationMessage For="@(() => _updateArticleRequest.RequireProfile)"/>
                            </div>
                            <div class="mr-5 w-full md:w-auto mb-2">
                                <SfCheckBox TChecked="bool"
                                            @bind-Checked="_updateArticleRequest.IsSplittableNParts"
                                            Label="@("Dowolny podział na N".Tr())"/>
                                <ValidationMessage For="@(() => _updateArticleRequest.IsSplittableNParts)"/>
                            </div>
                        }
                    </div>
                    <div>
                        <div class="flex flex-wrap">
                            <div class="w-full md:w-4/12 pr-3">
                                <label class="control-label">@("Kod".Tr())</label>
                                <SfTextBox @bind-Value="_updateArticleRequest.Code" CssClass="mb-1"/>
                                <ValidationMessage For="@(() => _updateArticleRequest.Code)"/>
                            </div>
                            <div class="w-full md:w-8/12 pr-3">
                                <label class="control-label">@("Nazwa".Tr())</label>
                                <SfTextBox @bind-Value="_updateArticleRequest.Name" CssClass="mb-1"/>
                                <ValidationMessage For="@(() => _updateArticleRequest.Name)"/>
                            </div>
                        </div>
                        <div class="flex flex-wrap mt-2">
                            <div class="e-fullrow md:w-4/12 pr-3">
                                <label class="control-label">@("Numer w katalogu".Tr())</label>
                                <SfNumericTextBox @bind-Value="_updateArticleRequest.No" CssClass="mb-1"/>
                                <ValidationMessage For="@(() => _updateArticleRequest.No)"/>
                            </div>
                            <div class="e-fullrow md:w-4/12 pr-3">
                                <label class="control-label">@("Waga jednostkowa".Tr())</label>
                                <SfNumericTextBox @bind-Value="_updateArticleRequest.UnitWeight" CssClass="mb-1"/>
                                <ValidationMessage For="@(() => _updateArticleRequest.UnitWeight)"/>
                            </div>
                            <div class="e-fullrow md:w-4/12 pr-3 items-center">
                                <div class="flex flex-nowrap">
                                    <div>
                                        <label class="control-label">@("Jednostka".Tr())</label>
                                        <div class="flex flex-nowrap">
                                            <SfComboBox TItem="GetAllArticleUnitsResponse" TValue="int?"
                                                        @bind-Value="@_updateArticleRequest.ArticleUnitId"
                                                        Placeholder="Wybierz jednostkę" DataSource="@ArticleUnits"
                                                        CssClass="mb-1">
                                                <ComboBoxFieldSettings Value="Id" Text="Name"></ComboBoxFieldSettings>
                                            </SfComboBox>
                                        </div>
                                    </div>
                                </div>
                                <ValidationMessage For="@(() => _updateArticleRequest.ArticleUnitId)"/>
                            </div>
                        </div>
                        <div class="flex flex-wrap mt-2">
                            <div class="e-fullrow md:w-6/12 pr-3">
                                <label class="control-label">@("Kod obcy".Tr())</label>
                                <SfTextBox @bind-Value="_updateArticleRequest.ForeignCode" CssClass="mb-1"/>
                                <ValidationMessage For="@(() => _updateArticleRequest.ForeignCode)"/>
                            </div>
                            <div class="e-fullrow md:w-3/12 pr-3">
                                <label class="control-label">@("Cena bazowa".Tr())</label>
                                <SfNumericTextBox @bind-Value="_updateArticleRequest.BasePrice" CssClass="mb-1"/>
                                <ValidationMessage For="@(() => _updateArticleRequest.BasePrice)"/>
                            </div>
                            <div class="e-fullrow md:w-3/12 pr-3">
                                <label class="control-label">@("Ostrzeżenie ilościowe".Tr())</label>
                                <SfNumericTextBox @bind-Value="_updateArticleRequest.QuantityWarning" CssClass="mb-1"/>
                                <ValidationMessage For="@(() => _updateArticleRequest.QuantityWarning)"/>
                            </div>
                        </div>
                    </div>
                </div>
                @{
                    if (_updateArticleRequest.Type == ArticleType.Complex)
                    {
                        <div class="text-sm italic mt-2">@("Długość arkuszy".Tr())</div>
                        <div class="p-2 border-1 border-gray-300 mb-1">
                            <div class="flex flex-wrap gap-x-3 mt-1">
                                <div class="e-fullrow md:w-2/12">
                                    <SfCheckBox TChecked="bool"
                                                @bind-Checked="_updateArticleRequest.LengthEditable"
                                                Label="@("Edycja długości".Tr())"
                                                CssClass="mb-2"/>
                                    <ValidationMessage For="@(() => _updateArticleRequest.LengthEditable)"/>
                                </div>
                                <div class="">
                                    <SfCheckBox TChecked="bool"
                                                @bind-Checked="_updateArticleRequest.IsSplitteable"
                                                Label="@("Podział arkusza".Tr())"
                                                CssClass="mb-2"/>
                                    <ValidationMessage For="@(() => _updateArticleRequest.IsSplitteable)"/>
                                </div>
                            </div>
                            <div class="flex flex-wrap mt-2">
                                <div class="e-fullrow md:w-4/12 pr-3">
                                    <label class="control-label">@("Długość domyślna".Tr())</label>
                                    <SfNumericTextBox @bind-Value="_updateArticleRequest.DefaultLength" CssClass="mb-1"/>
                                    <ValidationMessage For="@(() => _updateArticleRequest.DefaultLength)"/>
                                </div>
                                <div class="e-fullrow md:w-4/12 pr-3">
                                    <label class="control-label">@("Długość minimalna".Tr())</label>
                                    <SfNumericTextBox @bind-Value="_updateArticleRequest.MinLength" CssClass="mb-1"/>
                                    <ValidationMessage For="@(() => _updateArticleRequest.MinLength)"/>
                                </div>
                                <div class="e-fullrow md:w-4/12 pr-3">
                                    <label class="control-label">@("Długość maksymalna".Tr())</label>
                                    <SfNumericTextBox @bind-Value="_updateArticleRequest.MaxLength" CssClass="mb-1"/>
                                    <ValidationMessage For="@(() => _updateArticleRequest.MaxLength)"/>
                                </div>
                            </div>
                            <div class="flex flex-wrap mt-2">
                                <div class="e-fullrow md:w-4/12 pr-3">
                                    <label class="control-label">@("Całkowita długość zakładki".Tr())</label>
                                    <SfNumericTextBox @bind-Value="_updateArticleRequest.OverlapLength" CssClass="mb-1"/>
                                    <ValidationMessage For="@(() => _updateArticleRequest.OverlapLength)"/>
                                </div>
                                <div class="e-fullrow md:w-4/12 pr-3">
                                    <label
                                        class="control-label">@("Długość zakazanej strefy przetłoczenia".Tr())</label>
                                    <SfNumericTextBox @bind-Value="_updateArticleRequest.EmbossZoneLength"
                                                      CssClass="mb-1"/>
                                    <ValidationMessage For="@(() => _updateArticleRequest.EmbossZoneLength)"/>
                                </div>
                                <div class="e-fullrow md:w-4/12 pr-3">
                                    <label class="control-label">@("Długość podziału opcjonalnego".Tr())</label>
                                    <SfNumericTextBox @bind-Value="_updateArticleRequest.SplitLength" CssClass="mb-1"/>
                                    <ValidationMessage For="@(() => _updateArticleRequest.SplitLength)"/>
                                </div>
                            </div>
                        </div>

                        <div class="text-sm italic mt-2">@("Szerokość arkuszy".Tr())</div>
                        <div class="p-2 border-1 border-gray-300 mb-1">
                            <div class="flex flex-wrap mt-1">
                                <div class="w-full">
                                    <SfCheckBox TChecked="bool" @bind-Checked="_updateArticleRequest.WidthEditable"
                                                CssClass=""/>
                                    <label class="control-label">@("Edycja szerokości".Tr())</label>
                                    <ValidationMessage For="@(() => _updateArticleRequest.WidthEditable)"/>
                                </div>
                            </div>
                            <div class="flex flex-wrap mt-2">
                                <div class="e-fullrow md:w-4/12 pr-3">
                                    <label class="control-label">@("Szerokość domyślna".Tr())</label>
                                    <SfNumericTextBox @bind-Value="_updateArticleRequest.DefaultWidth" CssClass="mb-1"/>
                                    <ValidationMessage For="@(() => _updateArticleRequest.DefaultWidth)"/>
                                </div>
                                <div class="e-fullrow md:w-4/12 pr-3">
                                    <label class="control-label">@("Szerokość minimalna".Tr())</label>
                                    <SfNumericTextBox @bind-Value="_updateArticleRequest.MinWidth" CssClass="mb-1"/>
                                    <ValidationMessage For="@(() => _updateArticleRequest.MinWidth)"/>
                                </div>
                                <div class="e-fullrow md:w-4/12 pr-3">
                                    <label class="control-label">@("Szerokość maksymalna".Tr())</label>
                                    <SfNumericTextBox @bind-Value="_updateArticleRequest.MaxWidth" CssClass="mb-1"/>
                                    <ValidationMessage For="@(() => _updateArticleRequest.MaxWidth)"/>
                                </div>
                            </div>
                        </div>
                    }

                    <SfTab CssClass="mt-2">
                        <TabAnimationSettings>
                            <TabAnimationPrevious Effect="AnimationEffect.None" Duration="50"></TabAnimationPrevious>
                            <TabAnimationNext Effect="AnimationEffect.None" Duration="50"></TabAnimationNext>
                        </TabAnimationSettings>
                        <TabItems>
                            <TabItem>
                                <HeaderTemplate>
                                    <div class="min-w-10">@("Opis".Tr())</div>
                                </HeaderTemplate>
                                <ContentTemplate>
                                    <SfRichTextEditor @bind-Value="_updateArticleRequest.Description"
                                                      Height="300"
                                                      EditorMode="EditorMode.HTML"
                                                      EnableHtmlSanitizer="true"
                                                      EnableResize="true">
                                        <RichTextEditorToolbarSettings Items="@RichTextToolbarHelper.FullToolbar"
                                                                       Type="ToolbarType.Expand"/>
                                        <RichTextEditorImageSettings SaveUrl="api/Image/Save" Path="/images/editor/"/>
                                    </SfRichTextEditor>
                                    <ValidationMessage For="@(() => _updateArticleRequest.Description)"/>
                                </ContentTemplate>
                            </TabItem>
                            <TabItem>
                                <HeaderTemplate>
                                    <div class="min-w-10">@("Błąd, zbyt długi arkusz".Tr())</div>
                                </HeaderTemplate>
                                <ContentTemplate>
                                    <SfRichTextEditor @bind-Value="_updateArticleRequest.TooLongSheet"
                                                      Height="300"
                                                      EditorMode="EditorMode.HTML"
                                                      EnableHtmlSanitizer="true"
                                                      EnableResize="true">
                                        <RichTextEditorToolbarSettings Items="@RichTextToolbarHelper.FullToolbar"
                                                                       Type="ToolbarType.Expand"/>
                                        <RichTextEditorImageSettings SaveUrl="api/Image/Save" Path="/images/editor/"/>
                                    </SfRichTextEditor>
                                    <ValidationMessage For="@(() => _updateArticleRequest.TooLongSheet)"/>
                                </ContentTemplate>
                            </TabItem>
                        </TabItems>
                    </SfTab>
                }
            </div>
        </div>

        <div class="card-footer">
            <div class="flex flex-nowrap gap-x-3 justify-between md:justify-end">
                <SfButton CssClass="e-success">@("Zapisz".Tr())</SfButton>
                <SfButton HtmlAttributes="@ButtonAttrHelper.Button"
                          @onclick="@GoToIndexPage">@("Anuluj".Tr())</SfButton>
            </div>
        </div>
    </div>
</EditForm>

@code {

    [CascadingParameter]
    AppToast _toast { get; set; }
    [Parameter]
    public int ArticleId { get; set; }

    private UpdateArticleRequest _updateArticleRequest = new();

    private List<GetAllArticleUnitsResponse> ArticleUnits { get; set; } = new();
    private List<KeyValuePair<ArticleType, string>> ArticleTypes { get; set; } = EnumExtensions.DictionaryValues<ArticleType>();

    protected override async Task OnInitializedAsync()
    {
        await GetAllArticleUnits();

        var result = await Mediator.Send(new GetArticleUpdateRequestQuery(ArticleId));
        if (result.Succeeded)
        {
            _updateArticleRequest = result.Data;
        }
        else
        {
            await _toast.ShowError("Błąd pobrania danych artykułu".Tr());
        }
    }

    private async Task GetAllArticleUnits()
    {
        var result = await Mediator.Send(new GetAllArticleUnitsQuery());
        if (result.Succeeded)
        {
            ArticleUnits = result.Data;
        }
        else
        {
            await _toast.ShowError("Błąd pobrania jednostek produktów".Tr());
        }
    }

    private async Task HandleInvalidSubmit(EditContext editContext)
    {
        await _toast.ShowError("Błąd poprawności danych. Sprawdź dane artykułu".Tr());
    }

    private async Task HandleValidSubmit()
    {
        var result = await Mediator.Send(new UpdateArticleCommand(_updateArticleRequest));
        if (result.Succeeded)
        {
            NavigationManager.NavigateTo("admin/articles/index");
        }
        else
        {
            await _toast.ShowError("Błąd zapisania artykułu".Tr());
        }
    }

    private void GoToIndexPage()
    {
        Logger.LogInformation("GoToIndexPage");
        NavigationManager.NavigateTo($"/admin/articles/view/{ArticleId}");
    }

    private void ProfileEditableChanged(Syncfusion.Blazor.Buttons.ChangeEventArgs<bool> args)
    {
        if (!args.Checked)
        {
            _updateArticleRequest.RequireProfile = false;
        }
    }

}
