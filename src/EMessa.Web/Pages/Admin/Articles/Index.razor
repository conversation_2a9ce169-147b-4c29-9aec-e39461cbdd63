@page "/admin/articles/index"
@attribute [Authorize(Roles = Role.Administrator)]

@using EMessa.Core.Features.Articles.Commands.Delete
@using EMessa.Core.Features.Articles.Queries.GetAll
@using EMessa.Web.Components.Grids;
@using Syncfusion.Blazor.Grids
@using EMessa.Core.DataAdapters
@using Syncfusion.Blazor.Data
@using Syncfusion.Blazor.Popups
@using EMessa.Web.Constants
@using Action = Syncfusion.Blazor.Grids.Action

@inject ILogger<Index> Logger
@inject NavigationManager NavigationManager
@implements IDisposable

<IndexCard Title=@("Produkty".Tr())>
    <ChildContent>
        <div class="lg:w-full">
            @* Dodaj <PERSON>, Search -> Toolbar *@
            <SfGrid @ref="_defaultGrid" TValue="GetAllArticlesResponse" 
                    AdaptiveUIMode="@(AppStateService.GridState.AdaptiveMode)"
                    EnableAdaptiveUI="@(AppStateService.GridState.EnableAdaptiveUI)"
                    RowRenderingMode="@(AppStateService.GridState.RowDirection)"
                    AllowPaging="true" AllowSorting="true" AllowFiltering="true"
                    Toolbar="@SfGridToolbar.AddSearchToolbar">
                <GridEditSettings AllowAdding="true" AllowDeleting="true" Mode="EditMode.Normal"/>
                <GridFilterSettings Mode="FilterBarMode.Immediate" ShowFilterBarStatus="true" ImmediateModeDelay="500"/>
                <SfDataManager AdaptorInstance="@typeof(ArticlesDataAdaptor)" Adaptor="Adaptors.CustomAdaptor"/>
                <GridPageSettings PageSize="20" PageSizes="@(new[] { "20", "50", "100", "200" })"/>
                <GridEvents CommandClicked="CommandClickedHandler"
                            OnActionBegin="ActionBeginHandler"
                            OnRecordClick="RecordClickHandler"
                            OnActionFailure="ActionFailureHandler"
                            TValue="GetAllArticlesResponse"/>
                <GridColumns>
                    <GridColumn HideAtMedia="@($"(max-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllArticlesResponse.Id)>
                        <Template>
                            <EMessa.Web.Pages.Admin.Articles.Parts.ArticleCard Article="@(context as GetAllArticlesResponse)"></EMessa.Web.Pages.Admin.Articles.Parts.ArticleCard>
                        </Template>
                    </GridColumn>
                    <GridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllArticlesResponse.Id) Visible="false" IsPrimaryKey="true"></GridColumn>
                    <GridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllArticlesResponse.Code) HeaderText="@("Kod".Tr())" FilterSettings="@_barContainsFilterSettings"></GridColumn>
                    <GridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllArticlesResponse.Name) HeaderText="@("Nazwa".Tr())" FilterSettings="@_barContainsFilterSettings"></GridColumn>
                    <GridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllArticlesResponse.Type) HeaderText="@("Typ".Tr())" FilterSettings="@_barContainsFilterSettings"></GridColumn>
                    <GridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Field=@nameof(GetAllArticlesResponse.IsActive) TextAlign="TextAlign.Center" Width="80" HeaderText="@("Aktywny".Tr())" DisplayAsCheckBox="true"></GridColumn>
                    <GridColumn HideAtMedia="@($"(min-width: {(int)AppScreenSize.Medium}px)")" Width="150"
                                AllowFiltering="false" TextAlign="TextAlign.Center" HeaderText="@("Akcje".Tr())">
                        <GridCommandColumns>
                            <GridCommandColumn ID="ViewProduct" Type="CommandButtonType.None"
                                               ButtonOption="@ButtonOptions.View()"></GridCommandColumn>
                            <GridCommandColumn Type="CommandButtonType.Edit"
                                               ButtonOption="@ButtonOptions.Edit()"></GridCommandColumn>
                            <GridCommandColumn Type="CommandButtonType.Delete"
                                               ButtonOption="@ButtonOptions.Delete()"></GridCommandColumn>
                        </GridCommandColumns>
                    </GridColumn>
                </GridColumns>
            </SfGrid>
            @* Delete Article Dialog *@
            <SfDialog @ref="_dialogDelete" Width="250px" Visible="false" ShowCloseIcon="true" IsModal="true">
                <DialogTemplates>
                    <Header>@("Usuń artykuł".Tr())</Header>
                    <Content>@("Czy na pewno chcesz usunąć artykuł?".Tr())</Content>
                </DialogTemplates>
                <DialogButtons>
                    <DialogButton OnClick="@DeleteOkClick" Content="@("Usuń".Tr())" IsPrimary="true"></DialogButton>
                    <DialogButton OnClick="@DeleteCancelClick" Content="@("Anuluj".Tr())"></DialogButton>
                </DialogButtons>
            </SfDialog>
        </div>
    </ChildContent>
</IndexCard>

@code {
    [CascadingParameter]
    AppToast _toast { get; set; }

    private SfDialog _dialogDelete = new();
    private SfGrid<GetAllArticlesResponse> _defaultGrid = new();
    private readonly FilterSettings _barContainsFilterSettings = new() { Operator = Operator.Contains, Type = Syncfusion.Blazor.Grids.FilterType.FilterBar };
    private int? _toDeleteId;

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        AppStateService.StateChanged += OnStateChanged;
    }

    protected override void OnAfterRender(bool firstRender)
    {
        //_articlesGridQuery.AddParams("UserProfileId", AppStateService.UserData.UserProfileId);
    }

    private void GridAdd()
    {
        NavigationManager.NavigateTo("/admin/articles/add");
    }

    private async Task CommandClickedHandler(CommandClickEventArgs<GetAllArticlesResponse> args)
    {
        switch (args.CommandColumn.Type)
        {
            case CommandButtonType.Edit:
                NavigationManager.NavigateTo("/admin/articles/edit/" + args.RowData.Id);
                break;
            case CommandButtonType.Delete:
                _toDeleteId = args.RowData.Id;
                await _dialogDelete.ShowAsync();
                break;
            case CommandButtonType.None when args.CommandColumn.ID == "ViewProduct":
                NavigationManager.NavigateTo("/admin/articles/view/" + args.RowData.Id);
                break;
        }
    }

    private void ActionBeginHandler(ActionEventArgs<GetAllArticlesResponse> args)
    {
        if (args.RequestType != Action.Refresh)
        {
            args.Cancel = true;
        }
        
        if (args.RequestType == Action.Add)
        {
            NavigationManager.NavigateTo("/admin/articles/add");
        }
    }

    private async Task ActionFailureHandler(FailureEventArgs e)
    {
        Logger.LogError(e.Error, "Articles list action failed.");
        await _toast!.ShowError("Błąd operacji.".Tr());
    }

    private void RecordClickHandler(RecordClickEventArgs<GetAllArticlesResponse> args)
    {
        if (args.Column.Commands == null)
        {
            NavigationManager.NavigateTo("/admin/articles/view/" + args.RowData.Id);
        }
    }

    private async Task DeleteOkClick()
    {
        Logger.LogInformation("DeleteOkClick, has value {0}", _toDeleteId.HasValue);
        await _dialogDelete.HideAsync();
        
        if (_toDeleteId.HasValue)
        {
            try
            {
                var result = await Mediator.Send(new DeleteArticleCommand(_toDeleteId.Value));

                if (!result.Succeeded)
                {
                    await _toast.ShowError(result.Messages.String("<br />"));
                }
                else
                {
                    await _defaultGrid.Refresh();
                }
                _toDeleteId = null;

            }
            catch (Exception e)
            {
                Logger.LogError(e, e.Message);
                await _toast.ShowError("Błąd usuwania danych.".Tr());
            }
        }
    }

    private async Task DeleteCancelClick()
    {
        _toDeleteId = null;
        await _dialogDelete.HideAsync();
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }

    void IDisposable.Dispose()
    {
        AppStateService.StateChanged -= OnStateChanged;
    }
}
