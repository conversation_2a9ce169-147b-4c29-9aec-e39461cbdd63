@using EMessa.Base.Enums
@using EMessa.Core.Features.CustomerLocalizations.Queries.Get
@using EMessa.Core.Features.Customers.Queries.GetAll
@using EMessa.Core.Features.Customers.Queries.GetSelected
@using EMessa.Core.Features.Orders.Commands.CreateOrder
@using EMessa.Core.Features.Orders.Queries.Common
@using EMessa.Core.Features.ShoppingCart.Queries.GetShoppingCartItems
@using EMessa.Web.Interfaces
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor
@using EMessa.Web.Pages.Orders.Parts
@using EMessa.Web.Components.RadzenComponents.Badges

@inject ILogger<NewOrder> Logger
@inject IMediator Mediator
@inject ICheckoutStateService CheckoutStateService
@inject NavigationManager NavigationManager
@implements IDisposable


<RadzenCard class="p-1">
    <RadzenStack Orientation="Orientation.Vertical" Gap="10px" class="p-1">
        <RadzenStack Orientation="Orientation.Vertical" Gap=".5rem">
            <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center">
                <OrderStatusBadge Status="@Order.Status" CssClass="text-base" />
                <RadzenText TextStyle="TextStyle.H6"
                            class="m-0">
                    @("Zamówienie".Tr())
                    @if (!string.IsNullOrWhiteSpace(Order.No))
                    {
                        @(" ")
                        <strong>@Order.No</strong>
                    }
                    @if (!string.IsNullOrWhiteSpace(Order.CustomerNo))
                    {
                        @(" ")
                        <text> (@Order.CustomerNo)</text>
                    }
                </RadzenText>
            </RadzenStack>

            <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start"
                         class="mt-1">
                <RadzenFormField Variant="Variant.Outlined" Text="@("Klient".Tr())">
                    <ChildContent>
                        @if (_isOnlyClientOrClientManager)
                        {
                            if (_customer != null)
                            {
                                <RadzenTextBox Value="@_customer.ShortName" ReadOnly="true"
                                               class="w-full" />
                            }
                            else
                            {
                                <RadzenTextBox Value="@("Ładowanie...".Tr())" ReadOnly="true" class="w-full" />
                            }
                        }
                        else
                        {
                            <CustomerSelector TValue="int?"
                                              Value="@_customer?.Id"
                                              Selected="@OnCustomerSelected"
                                              AutoSelect
                                              ShowActiveOnly />
                        }
                    </ChildContent>
                </RadzenFormField>

                <RadzenFormField Variant="Variant.Outlined" Text="@("Lokalizacja".Tr())">
                    <ChildContent>
                        @if (_customer != null)
                        {
                            <LocalizationSelector TValue="int?"
                                                  CustomerId="@_customer.Id"
                                                  Value="@_mainLocalizationFromSelector?.Id"
                                                  Selected="@OnLocalizationSelected"
                                                  AutoSelect
                                                  ShowActiveOnly />
                        }
                        else
                        {
                            <RadzenTextBox Value="@("Wybierz klienta".Tr())" ReadOnly class="w-full" />
                        }
                    </ChildContent>
                </RadzenFormField>

                <RadzenFormField Variant="Variant.Outlined" Text="@("Nr. własny".Tr())">
                    <ChildContent>
                        <RadzenTextBox @bind-Value="@Order.CustomerNo" />
                    </ChildContent>
                </RadzenFormField>

                @if (Order.Status != OrderStatus.New)
                {
                    <RadzenFormField Variant="Variant.Outlined" Text="@("Nr. Messa".Tr())">
                        <ChildContent>
                            <RadzenTextBox Value="@Order.MessaNo.ToString()" ReadOnly="true" />
                        </ChildContent>
                    </RadzenFormField>
                }
            </RadzenStack>

            <RadzenRow Gap="1rem" class="">
                <RadzenColumn Size="12" SizeMD="6">
                    <RadzenFormField Variant="Variant.Outlined" Text="@("Uwagi do zamówienia".Tr())"
                                     class="w-full">
                        <ChildContent>
                            @* <RadzenTextBox @bind-Value="@_orderRequestTemp.Note"/> *@
                            <RadzenTextArea @bind-Value="@Order.Comments"
                                            Rows="3"
                                            class="w-full" />
                        </ChildContent>
                    </RadzenFormField>
                </RadzenColumn>
            </RadzenRow>


            <RadzenRow Gap="1rem" class="">
                <RadzenColumn Size="12" SizeMD="6">
                    <RadzenFormField Variant="Variant.Outlined" Text="@("Lokalizacja".Tr())"
                                     class="w-full">
                        <ChildContent>
                            <RadzenRow class="p-2">
                                <RadzenColumn>
                                    @if (_mainLocalizationFromSelector != null)
                                    {
                                        <LocalizationTemplate @ref="@_customerLocalizationTemplate"
                                                              @bind-Localization="_mainLocalizationFromSelector"
                                                              @bind-IsValid="_isValidMainLocalization"
                                                              ValidateOnInitialized
                                                              ReadOnly />
                                    }
                                    else
                                    {
                                        <RadzenTextBox Value="@("Wybierz lokalizację".Tr())" ReadOnly
                                                       class="w-full" />
                                    }
                                </RadzenColumn>
                            </RadzenRow>
                        </ChildContent>
                    </RadzenFormField>
                </RadzenColumn>

                <RadzenColumn Size="12" SizeMD="6">
                    <RadzenFormField Variant="Variant.Outlined" Text="@("Adres dostawy".Tr())"
                                     class="w-full">
                        <ChildContent>
                            <RadzenRow class="p-2">
                                <RadzenColumn>
                                    <div class="flex justify-between gap-2">
                                        <CheckBox Label="@("Dostawa na inny adres".Tr())"
                                                  Disabled="@(_mainLocalizationFromSelector == null)"
                                                  Value="@Order.DifferentDeliveryLocalization"
                                                  ValueChanged="@DeliveryChanged" />
                                        @if (Order.DifferentDeliveryLocalization && _mainLocalizationFromSelector != null)
                                        {
                                            <RadzenButton Text="@("Skopiuj".Tr())"
                                                          Icon="content_copy"
                                                          ButtonStyle="ButtonStyle.Light"
                                                          Size="ButtonSize.Small"
                                                          Click="@CopyDeliveryAddress" />
                                        }
                                    </div>
                                    @if (Order.DifferentDeliveryLocalization && _differentDeliveryLocalization != null)
                                    {
                                        <LocalizationTemplate @ref="@_differentDeliveryLocalizationTemplate"
                                                              @bind-Localization="_differentDeliveryLocalization"
                                                              @bind-IsValid="_isValidDeliveryLocalization" />
                                    }

                                </RadzenColumn>
                            </RadzenRow>
                        </ChildContent>
                    </RadzenFormField>
                </RadzenColumn>
            </RadzenRow>

            <OrderItemGrid OrderItems="_orderItems"
                           ShowDraftColumn
                           ShowSaleColumn
                           ShowOptionValuesTable
                           ShowSheetsTable />

        </RadzenStack>
    </RadzenStack>
    <GridToolbar>
        <SaveRadzenButton Click="@ValidateOrder" />
        <CancelRadzenButton Click="@GoBack" />
    </GridToolbar>
</RadzenCard>

@code {
    [Parameter] public required EditOrderResponse Order { get; set; }

    private bool _isOnlyClientOrClientManager;
    private List<OrderItemResponse> _orderItems = [];
    private GetAllCustomersResponse? _customer;
    private GetCustomerLocalizationsResponse? _mainLocalizationFromSelector;
    private GetCustomerLocalizationsResponse? _differentDeliveryLocalization;
    private bool _isValidMainLocalization;
    private bool _isValidDeliveryLocalization;
    private LocalizationTemplate? _customerLocalizationTemplate;
    private LocalizationTemplate? _differentDeliveryLocalizationTemplate;

    #region Initialize, Dispoze

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        _isOnlyClientOrClientManager = Role.HasAnyRole(AppStateService.UserData.Roles, Role.Client, Role.ClientManager);
        if (_isOnlyClientOrClientManager && AppStateService.UserData.CustomerId.HasValue)
        {
            await LoadCurrentCustomer(AppStateService.UserData.CustomerId.Value);
        }
    }

    public void Dispose()
    {
        CheckoutStateService.OnChange -= StateHasChanged;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            await CheckoutStateService.RestoreStateAsync();
            if (!CheckoutStateService.OrderItemIds.Any())
            {
                ToastService.ShowWarning( "Brak elementów do zamówienia".Tr());
                NavigationManager.NavigateTo("/shoppingcart");
            }
            CheckoutStateService.OnChange += StateHasChanged;
            await LoadOrderItems();
        }
    }

    #endregion

    private void GoBack() => NavigationManager.NavigateTo("/shoppingcart");

    private async Task LoadCurrentCustomer(int customerId)
    {
        var response = await Mediator.Send(new GetSelectedCustomerQuery(customerId));

        if (response.Succeeded)
        {
            _customer = response.Data;
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError, "Błąd pobrania danych klienta".Tr());
        }
    }

    private async Task ValidateOrder()
    {
        _customerLocalizationTemplate?.ValidateModel();
        _differentDeliveryLocalizationTemplate?.ValidateModel();

        var isInvalid = false;
        if (_customer == null)
        {
            ToastService.ShowWarning("Wybierz klienta".Tr());
            isInvalid = true;
        }

        if (_mainLocalizationFromSelector == null || _customerLocalizationTemplate == null)
        {
            ToastService.ShowWarning("Wybierz lokalizację".Tr());
            isInvalid = true;
        }

        if (!_isValidMainLocalization)
        {
            ToastService.ShowWarning("Popraw dane w lokalizacji".Tr());
            isInvalid = true;
        }

        if (Order.DifferentDeliveryLocalization && !_isValidDeliveryLocalization)
        {
            ToastService.ShowWarning("Uzupełnij dane w lokalizacji dostawy".Tr());
            isInvalid = true;
        }

        if (Order.DifferentDeliveryLocalization && _differentDeliveryLocalization == null)
        {
            ToastService.ShowError("Brak lokalizacji dostawy".Tr());
            isInvalid = true;
        }

        //todo sprawdzenie czy nadal jest dość materiału itp

        if (isInvalid)
            return;

        await SaveNewOrder(Order);
    }

    private async Task SaveNewOrder(EditOrderResponse order)
    {
        try
        {
            var request = new CreateOrderRequest
            {
                UserProfileId = AppStateService.UserData.UserProfileId,
                CustomerId = _customer!.Id,
                CustomerLocalizationId = _mainLocalizationFromSelector!.Id,
                BranchId = AppStateService.UserData.BranchIds.First(),
                CustomerNo = order.CustomerNo,
                Comments = order.Comments,
                DifferentDeliveryLocalization = order.DifferentDeliveryLocalization,
                OrderItemIds = CheckoutStateService.OrderItemIds,
                Localizations =
                [
                    // Główna lokalizacja
                    new CreateLocalizationRequest
                    {
                        Type = LocalizationType.Main,
                        Address = _mainLocalizationFromSelector.Address,
                        City = _mainLocalizationFromSelector.City,
                        PostCode = _mainLocalizationFromSelector.PostCode
                    }
                ]
            };

            // Dodaj lokalizację dostawy jeśli jest inna
            if (order.DifferentDeliveryLocalization && _differentDeliveryLocalization != null)
            {
                request.Localizations.Add(new CreateLocalizationRequest
                {
                    Type = LocalizationType.Delivery,
                    Address = _differentDeliveryLocalization.Address,
                    City = _differentDeliveryLocalization.City,
                    PostCode = _differentDeliveryLocalization.PostCode
                });
            }

            var response = await Mediator.Send(new CreateOrderCommand(request));

            if (response.Succeeded)
            {
                var newOrder = response.Data;
                ToastService.ShowSuccess($"Złożono zamówienie {newOrder.OrderNo}".Tr());

                // Wyczyść zamówienie
                await CheckoutStateService.ClearStateAsync();
                // Powiadom o zmianach w koszyku
                AppStateService.SetShoppingCart(this);

                // Przekieruj do koszyka
                NavigationManager.NavigateTo("/shoppingcart");
            }
            else
            {
                if (response.Data is { OrderItemIds: var availableItemIds } && availableItemIds.Any())
                {
                    // Pokaż użytkownikowi które elementy są dostępne
                    // Może zaproponować utworzenie zamówienia tylko z dostępnych elementów
                    // Może dialog ostrzegawczy tutaj?
                    await CheckoutStateService.SetOrderItemsIds(availableItemIds);
                    ToastService.ShowWarning("Niektóre elementy są niedostępne. Zamówienie zostało zaktualizowane.".Tr());
                    return;
                }

                ToastService.Show(ToastType.SaveError, response.Messages);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas składania zamówienia");
            ToastService.Show(ToastType.SaveError, "Wystąpił błąd podczas składania zamówienia".Tr());
        }
    }

    private void OnCustomerSelected(GetAllCustomersResponse? customer)
    {
        _customer = customer;
        _mainLocalizationFromSelector = null;
    }

    private void OnLocalizationSelected(GetCustomerLocalizationsResponse? localization)
    {
        _mainLocalizationFromSelector = localization;
    }

    private void DeliveryChanged(bool differentDeliveryAddress)
    {
        Order.DifferentDeliveryLocalization = differentDeliveryAddress;
        if (differentDeliveryAddress && _mainLocalizationFromSelector != null)
        {
            _differentDeliveryLocalization = new GetCustomerLocalizationsResponse
            {
                Address = "",
                City = "",
                PostCode = "",
            };
        }
        else
        {
            _differentDeliveryLocalization = null;
        }
    }

    private void CopyDeliveryAddress()
    {
        if (_mainLocalizationFromSelector != null && _differentDeliveryLocalization != null)
        {
            _differentDeliveryLocalization = new GetCustomerLocalizationsResponse
            {
                Address = _mainLocalizationFromSelector.Address,
                City = _mainLocalizationFromSelector.City,
                PostCode = _mainLocalizationFromSelector.PostCode,
            };
        }
    }

    private async Task LoadOrderItems()
    {
        if (CheckoutStateService.OrderItemIds.Any())
        {
            var response = await Mediator.Send(new GetShoppingCartItemsQueryByIds(CheckoutStateService.OrderItemIds));

            if (response.Succeeded)
            {
                _orderItems = response.Data;
                var ids = _orderItems.Select(x => x.Id).ToList();
                await CheckoutStateService.SetOrderItemsIds(ids);
                StateHasChanged();
            }
            else
            {
                ToastService.Show(ToastType.LoadDataError, "Błąd pobrania produktów z koszyka".Tr());
            }
        }
    }

}