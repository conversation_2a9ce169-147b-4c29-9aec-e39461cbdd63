using EMessa.Core.Features.Accounts.Commands.Login;
using EMessa.Web.Extensions;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace EMessa.Web.Pages.Account
{
    public class LoginModel(IMediator mediator, ILogger<LoginModel> logger) : PageModel
    {
        [BindProperty]
        public string Email { get; set; } = null!;

        [BindProperty]
        public string Password { get; set; } = null!;

        [BindProperty]
        public bool RememberMe { get; set; }

        public string Message { get; set; } = null!;

        public void OnGet()
        {
            HttpContext.SetLanguageByBrowser();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            try
            {
                var loginIp = HttpContext.Connection.RemoteIpAddress?.ToString() ?? string.Empty;

                var request = new AccountLoginRequest()
                {
                    Email = Email,
                    Password = Password,
                    RememberMe = RememberMe,
                    LoginIp = loginIp
                };

                var validator = new AccountLoginValidator();
                var validateResult = await validator.ValidateAsync(request);

                if (!validateResult.IsValid)
                {
                    ModelState.Clear();
                    validateResult.AddToModelState(ModelState);
                    return Page();
                }

                var result = await mediator.Send(new AccountLoginCommand(request));

                if (result.Succeeded)
                {
                    return LocalRedirect("/");
                }

                Message = string.Join(' ', result.Messages);
            }
            catch (Exception e)
            {
                logger.LogError(e, e.Message);
                Message = "Wystąpił błąd. Spróbuj ponownie.";
            }

            return Page();
        }
    }
}
