@attribute [Authorize]

@using EMessa.Core.Features.Categories.Queries.GetAllCategoriesWithRoot
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor

@implements IDisposable

<RadzenTree @ref="_tree" class="bg-white"
            Data="@_treeData"
            Change="@OnTreeSelectionChanged"
            Style="@(string.Concat("width: 100%; height: ", Height))">
    <RadzenTreeLevel TextProperty="@nameof(TreeItem.Name)"
                     ChildrenProperty="@nameof(TreeItem.Children)"
                     Selected="@(item => ((TreeItem)item).Id == SelectedCategoryId)"
                     Expanded="@(item => ((TreeItem)item).HasChildren && ((TreeItem)item).Expanded)"
                     HasChildren="@((item) => ((TreeItem)item).HasChildren)" />
</RadzenTree>

@code {

    [CascadingParameter]
    AppToast Toast { get; set; } = new();

    [Parameter]
    public int? SelectedCategoryId { get; set; }

    [Parameter]
    public required List<GetAllCategoriesWithRootResponse> CategoriesTree { get; set; }

    [Parameter]
    public EventCallback<int?> OnCategorySelected { get; set; }

    [Parameter]
    public GetAllCategoriesWithRootResponse? RootCategory { get; set; }

    [Parameter]
    public string Height { get; set; } = "100%";

    private RadzenTree _tree = new();
    private List<TreeItem> _treeData = [];

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
        InitializeTreeData();
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        AppStateService.StateChanged += OnStateChanged;
    }

    void IDisposable.Dispose()
    {
        AppStateService.StateChanged -= OnStateChanged;
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }

    private TreeItem GetTreeItem(GetAllCategoriesWithRootResponse category)
    {
        return new TreeItem
        {
            Id = category.Id,
            Code = category.Code,
            Name = category.TranslatedName,
            ParentId = category.ParentId,
            Expanded = AppStateService.GetCategoryExpanded(category.Id),
            HasChildren = category.SubCategories.Any(),
            Children = category.SubCategories.Select(GetTreeItem).ToList(),
            Category = category
        };
    }

    private void InitializeTreeData()
    {
        _treeData = CategoriesTree.Select(GetTreeItem).ToList();
    }

    private async Task OnTreeSelectionChanged(TreeEventArgs args)
    {
        if (args.Value is TreeItem treeItem)
        {
            // Jeśli kliknięty element ma dzieci, przełącz jego stan rozwinięcia/zwinięcia
            if (treeItem.HasChildren)
            {
                treeItem.Expanded = !treeItem.Expanded;
                AppStateService.SetCategoryExpanded(treeItem.Id, treeItem.Expanded);
            }

            if (treeItem.Id != SelectedCategoryId)
            {
                await OnCategorySelected.InvokeAsync(treeItem.Id);
            }
        }
    }

    internal class TreeItem
    {
        public int Id { get; set; }
        public string Code { get; set; } = "";
        public string Name { get; set; } = "";
        public int? ParentId { get; set; }
        public bool Expanded { get; set; }
        public bool HasChildren { get; set; }
        public List<TreeItem> Children { get; set; } = [];
        public GetAllCategoriesWithRootResponse Category { get; set; } = new();
    }

}