@using EMessa.Base.Enums
@using EMessa.Core.Features.ArticleCategory.Queries.GetArticlesForCategoryAndDependent
@using EMessa.Core.Features.ArticleFilterAttribute.Queries.GetFilterAttributesForCategory
@using EMessa.Core.Features.Categories.Queries.GetAllCategoriesWithRoot
@using EMessa.Core.Features.Sales.Queries.CommonResponses
@using EMessa.Core.Features.Sales.Queries.GetSalesAvailabilityByArticleIds
@using EMessa.DAL.Entities.Articles
@using EMessa.Web.Pages.Admin.Categories.Parts
@using EMessa.Web.Pages.CommonComponents
@using Rad<PERSON>
@using Radzen.Blazor

@inject ILogger<ShoppingCartList> Logger
@inject IMediator Mediator
@inject IAppStateService AppStateService
@implements IDisposable

<div>
    <div class="w-full flex flex-col gap-2 p-2">
        <CategoryBreadcrumb CategoriesTree="@_categoriesTree"
                            RootCategory="@_rootCategory"
                            SelectedCategoryId="@_selectedCategoryId"
                            OnCategorySelected="@OnCategorySelected" />
    </div>
    <div class="w-full flex flex-col border">
        @* <div class="w-full md:w-3/12"> *@
        @*     <CategoriesComponent CategorySelected="@((id)=>AppStateService.CategoryChanged(this, id))" /> *@
        @* </div>  *@
        @* <div class="pl-0 w-full md:w-9/12"> *@

        @* OrderItems *@
        <RadzenStack Orientation="Orientation.Vertical"
                     JustifyContent="JustifyContent.Start"
                     Class="w-full p-2 gap-2">
            <RadzenStack Orientation="Orientation.Horizontal"
                         JustifyContent="JustifyContent.SpaceBetween"
                         Class="w-full gap-2">
                <GridSearch ValueChanged="@OnSearchTextChanged" CssClass="" />
                <RadzenPager @ref="_pager1"
                             Count="@_totalCount"
                             PageSize="@_pageSize"
                             PageNumbersCount="5"
                             PageChanged="@PageChangedHandler" />
            </RadzenStack>
            <RadzenStack Orientation="Orientation.Horizontal"
                         AlignItems="AlignItems.Start"
                         JustifyContent="JustifyContent.Start"
                         Wrap="FlexWrap.Wrap"
                         Class="p-1 pl-2">
                <div class="text-md mr-2">@("Filtruj wg:".Tr())</div>
                <RadzenStack Orientation="Orientation.Vertical"
                             JustifyContent="JustifyContent.Start"
                             Class="gap-2">
                    @foreach (var attribute in _attributes)
                    {
                        <RadzenDropDown TValue="FilterAttributeValueForCategory"
                                        Placeholder="@(attribute.TranslatedDisplayName)"
                                        AllowClear="true"
                                        Data="@(attribute.Values)"
                                        TextProperty="TranslatedDisplayValue"
                                        ValueProperty="Id"
                                        Change="@(args => AttributeValueChanged(attribute, args))">
                        </RadzenDropDown>
                    }
                </RadzenStack>
            </RadzenStack>

            <ProgressBar Value="@(_loadingData ? 50 : 0)" />

            <RadzenStack Orientation="Orientation.Horizontal" Class="w-full h-full gap-2">
                <div class="w-56 h-full p-1 border border-gray-500">
                    <CategoriesTreeComponent CategoriesTree="@_categoriesTree"
                                             RootCategory="@_rootCategory"
                                             SelectedCategoryId="@_selectedCategoryId"
                                             OnCategorySelected="@OnCategorySelected"
                                             Height="550px" />
                </div>

                <div class="p-2 w-full border border-gray-500">
                    <div class="flex flex-wrap gap-4">
                        @{ var articleKey = 0; }
                        @foreach (var article in _articles) // Cards to lista z danymi kart
                        {
                            switch (article.Type)
                            {
                                case ArticleType.Complex:
                                case ArticleType.Trade:
                                    <ArticleCardComponent @key="@(articleKey++)"
                                                          Article="@article"
                                                          HasAvailableSales="@article.HasAvailableSales"
                                                          OnSelectedArticle="@SelectedArticleHandle"
                                                          OnSelectedSale="@SelectedSaleArticleHandle" />
                                    break;
                                @* TODO *@
                                @* case ArticleType.Service: *@
                                @* @*     <ServiceArticleComponent @key="@(articleKey++)" *@
                                @*                             Article="@article" *@
                                @*                             OnSelectedArticle="@SelectedArticleHandle"/> *@
                                @*     break; *@
                            }
                        }
                    </div>
                </div>
            </RadzenStack>

            <RadzenStack Orientation="Orientation.Horizontal"
                         JustifyContent="JustifyContent.End"
                         Class="w-full gap-2">
                <RadzenPager @ref="_pager2"
                             Count="@_totalCount"
                             PageSize="@_pageSize"
                             PageNumbersCount="5"
                             PageChanged="@PageChangedHandler" />
            </RadzenStack>
        </RadzenStack>
    </div>
</div>

@code {
    #region Parameters

    [Parameter] public EventCallback<GetArticlesForCategoryAndDependentsQueryResponse> OnSelectedArticle { get; set; }
    [Parameter] public EventCallback<SaleArticleResponse> OnSelectedSaleArticle { get; set; }

    #endregion

    #region General

    private string _searchText = "";
    private bool _loadingData;
    private int _currentPageIndex = 0;
    private int _totalCount = 0;
    private int _pageSize = 20;

    private RadzenPager _pager1 = new();
    private RadzenPager _pager2 = new();

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        AppStateService.StateChanged += OnStateChanged;
        AppStateService.SetCatalogVisibility(this, true);
        await InitializeAllCategoriesWithRoot();
    }

    void IDisposable.Dispose()
    {
        AppStateService.StateChanged -= OnStateChanged;
        AppStateService.SetCatalogVisibility(this, false);
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);
    }

    private async Task SelectedArticleHandle(GetArticlesForCategoryAndDependentsQueryResponse article)
    {
        await OnSelectedArticle.InvokeAsync(article);
    }

    private async Task SelectedSaleArticleHandle(SaleArticleResponse selectedSaleArticle)
    {
        await OnSelectedSaleArticle.InvokeAsync(selectedSaleArticle);
    }

    private async Task OnCategoryChanged()
    {
        await LoadArticles();
        await LoadAttributes();
        await InvokeAsync(StateHasChanged);
    }

    #endregion

    #region Categories

    private int? _selectedCategoryId = -1;

    private List<GetAllCategoriesWithRootResponse> _categoriesTree = [];

    private GetAllCategoriesWithRootResponse? _rootCategory;

    private async Task InitializeAllCategoriesWithRoot()
    {
        var result = await Mediator.Send(new GetAllCategoriesWithRootQuery());
        if (result.Succeeded)
        {
            _categoriesTree = result.Data;
            _rootCategory = _categoriesTree.FirstOrDefault(x => x.ParentId == null);
            _selectedCategoryId = _rootCategory?.Id;
            await OnCategoryChanged();
        }
        else
        {
            ToastService.ShowError(result.Messages.String("<br />"));
        }

        StateHasChanged();
    }

    private async Task OnCategorySelected(int? selectedCategoryId)
    {
        _selectedCategoryId = selectedCategoryId;
        await OnCategoryChanged();
        await InvokeAsync(StateHasChanged);
    }

    #endregion

    #region Articles

    private List<GetArticlesForCategoryAndDependentsQueryResponse> _articles = [];

    private async Task LoadArticles()
    {
        _loadingData = true;

        var attributeValues = _attributeValues
            .Select(x => new SearchFilterAttributeValue(x.Key, x.Value))
            .ToList();

        var articlesResult = await Mediator.Send(new GetArticlesForCategoryAndDependentsQuery(
            _selectedCategoryId ?? -1,
            _currentPageIndex,
            _pageSize,
            attributeValues,
            _searchText));
        if (articlesResult.Succeeded)
        {
            _totalCount = articlesResult.Data.TotalCount;
            _articles = articlesResult.Data.Data;

            await LoadSales();
        }
        else
        {
            _articles = [];
        }

        _loadingData = false;
    }

    private async Task OnSearchTextChanged(string value)
    {
        _searchText = value;
        await LoadArticles();
    }

    private async Task PageChangedHandler(PagerEventArgs args)
    {
        var toLoad = false;
        if (args.PageIndex != _pager1.CurrentPage)
        {
            await _pager1.GoToPage(args.PageIndex);
            toLoad = true;
        }

        if (args.PageIndex != _pager2.CurrentPage)
        {
            await _pager2.GoToPage(args.PageIndex);
            toLoad = true;
        }

        if (toLoad)
        {
            _currentPageIndex = args.PageIndex;
            await LoadArticles();
        }
    }

    #endregion

    #region Attributes

    private List<GetFilterAttributesForCategoryResponse> _attributes = [];
    private Dictionary<int, int> _attributeValues = new();

    private async Task LoadAttributes()
    {
        var result = await Mediator.Send(new GetFilterAttributesForCategoryQuery(_selectedCategoryId ?? -1));
        if (result.Succeeded)
        {
            _attributes = result.Data;
        }
        else
        {
            Logger.LogError(result.Messages.String());
            ToastService.Show(ToastType.LoadDataError, result.Messages);
        }
    }

    private async Task AttributeValueChanged(GetFilterAttributesForCategoryResponse attribute, object? value)
    {
        if (value is null)
        {
            _attributeValues.Remove(attribute.Id);
        }
        else
        {
            _attributeValues[attribute.Id] = (int)value;
        }

        await LoadArticles();
    }

    #endregion

    #region Sales

    private async Task LoadSales()
    {
        var loggedUser = AppStateService.UserData;
        if (!loggedUser.HasAnyRole(Role.Administrator, Role.Client))
            return;

        var articleIds = _articles.Select(x => x.Id).ToList();
        if (articleIds.Any())
        {
            var result = await Mediator.Send(new GetSalesAvailabilityByArticleIdsQuery(articleIds));
            if (result.Succeeded)
            {
                foreach (var article in _articles)
                {
                    article.HasAvailableSales = result.Data.Contains(article.Id);
                }
            }
            else
            {
                Logger.LogError(result.Messages.String());
                _articles.ForEach(x => x.HasAvailableSales = false);
                ToastService.Show(ToastType.LoadSaleError, result.Messages);
            }
        }
    }

    #endregion
}
