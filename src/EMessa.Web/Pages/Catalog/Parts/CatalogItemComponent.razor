@using EMessa.Core.Features.ArticleCategory.Queries.GetArticlesForCategoryAndDependent
@using EMessa.Core.Features.ShoppingCart.Commands.AddItem

@inject IMediator Mediator
@inject ILogger<CatalogItemComponent> Logger

<div class="shadow-md hover:shadow-lg my-2 p-1 border-1 ">
    <div class="flex justify-between">
        <div class="md:flex">
            <div class="text-black">
                <strong>@Article.TranslatedName</strong>
                @if (AppStateService.UserData.HasAdminRole)
                {
                    <br />
                    <span>@(Article.Id + " - ")</span>
                    <span class="text-sm">@Article.Name</span>
                }
            </div>
            <div class="flex py-1  md:mx-3">
                <span
                    class="mr-2 text-[18pt] text-teal-600 hover:text-red-600 hover:cursor-pointer material-symbols-outlined"
                    translate="no">info</span>
                <span
                    class="mr-2 text-[18pt] text-teal-600 hover:text-red-600 hover:cursor-pointer material-symbols-outlined"
                    translate="no">image</span>
                <span
                    class="mr-2 text-[18pt] text-teal-600 hover:text-red-600 hover:cursor-pointer material-symbols-outlined"
                    translate="no">paid</span>
            </div>
        </div>
        <div class="flex h-parent hover:border-1">
            <div class="flex  hover:border rounded-2 justify-around">
                <i @onclick="@(async (e) => await AddToCart(Article))"
                   class="align-middle p-1 text-[28pt] text-teal-600 hover:text-red-600 hover:cursor-pointer material-symbols-outlined">shopping_cart</i>
            </div>
        </div>

    </div>
    <div id="edit@(Article.Id)" class="collapse edit block">bl</div>
</div>

@code {

    [Parameter]
    public GetArticlesForCategoryAndDependentsQueryResponse Article { get; set; } = new();

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
    }

    private async Task AddToCart(GetArticlesForCategoryAndDependentsQueryResponse article)
    {
        var result = await Mediator.Send(new AddItemToShoppingCartCommand(article.Id, AppStateService.UserData.CustomerId ?? 0, AppStateService.UserData.UserProfileId));
        if (result.Succeeded)
        {
            ToastService.Show(ToastType.AddCartItemSuccess);
        }
        else
        {
            Logger.LogError(result.Messages.String());
            ToastService.Show(ToastType.AddCartItemError);
        }
    }

}
