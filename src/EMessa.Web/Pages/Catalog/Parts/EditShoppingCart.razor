@using EMessa.Base.Enums
@using EMessa.Core.Features.OrderItems.Commands.AddOrderItemToOrder
@using EMessa.Core.Features.OrderItems.Commands.AddOrderItemToShoppingCart
@using EMessa.Core.Features.OrderItems.Commands.SavePng
@using EMessa.Core.Features.OrderItems.Exceptions
@using EMessa.Core.Features.OrderItems.Models
@using EMessa.Core.Features.ShoppingCart.Models
@using EMessa.Core.Features.ShoppingCart.Queries.GetAddNewItemModel
@using EMessa.Web.Pages.CommonComponents

@inject ILogger<EditShoppingCart> Logger
@inject IRandomGeneratorService RandomGeneratorService
@* @inject ICartService CartService *@


<HeadContent>
    <script src="js/draftEditor/DraftEditor.js"></script>
</HeadContent>

<div class="w-full flex flex-col">
    @* <div class="flex flex-row gap-2 justify-start items-center"> *@
    @*     <div>@EditingItem.ArticleName</div> *@
    @*     <div>@EditingItem.ArticleType</div> *@
    @*     <div>@_isDraftEditorVisible</div> *@
    @*     <strong @onclick="@CancelEditingHandle" class="p-2 border-1 border-gray-500 cursor-pointer">Close</strong> *@
    @* </div> *@
    @if (_isDraftEditorVisible)
    {
        <DraftEditorWrapper EditItem="@EditingItem"
                            OnSave="@OnDraftEditorSave"
                            OnCancel="@OnDraftEditorCancel"
                            GenerateFileName="@GenerateRandomFileName" />
    }
    else
    {
        switch (EditingItem.ArticleType)
        {
            case ArticleType.Complex:
                @* ArticleDefinition="@ArticleDefinition" *@
                @* SelectedOptionAndOptionValue="@SelectedOptionAndOptionValue" *@
                <ArticleComplexEditComponent
                    EditingItem="@EditingItem"
                    NewRequestedSheet="@NewRequestedSheet"
                    OnCancel="@CancelEditingHandle"
                    OnSave="@SaveArticleHandle"
                    OpenDraftEditor="@OpenDraftEditorHandle" />
                break;
            case ArticleType.Trade:
                @* ArticleDefinition="@ArticleDefinition" *@
                <ArticleTradeEditComponent
                    EditingItem="@EditingItem"
                    OnCancel="@CancelEditingHandle"
                    OnSave="@SaveArticleHandle" />
                break;
            case ArticleType.Service:
            default:
                <div>TODO ArticleType</div>
                // TODO
                break;
        }
    }
</div>

@code {

    #region Parameters

    [Parameter] public required ShoppingCartItemEditModel EditingItem { get; set; }
    [Parameter] public required RequestedSheetEditModel NewRequestedSheet { get; set; }

    [Parameter] public EventCallback OnCancelEditing { get; set; }
    // [Parameter]
    // public SelectedOptionAndOptionValue? SelectedOptionAndOptionValue
    // {
    //     get => _selectedOptionAndOptionValue;
    //     set
    //     {
    //         _selectedOptionAndOptionValue = value;
    //         Print.WriteInline("Edit", value);
    //     }
    // }
    //
    // SelectedOptionAndOptionValue? _selectedOptionAndOptionValue;
    // public SelectedOptionAndOptionValue? SelectedOptionAndOptionValue { get; set; }

    #endregion

    #region General

    private EditContext _editContext = null!;

    protected override void OnInitialized()
    {
        base.OnInitializedAsync();
        _editContext = new EditContext(_model);
    }

    private string GenerateRandomFileName()
    {
        var random = RandomGeneratorService
            .GenerateRandomString("abcdefghijklmnopqrstuvwxyz0123456789", 6);
        return Path.GetFileName($@"{"Szkic".Tr()}-{random}");
    }

    private async Task CancelEditingHandle()
    {
        await OnCancelEditing.InvokeAsync();
    }

    private async Task SaveArticleHandle(BaseOrderItemEditModel editingItem)
    {
        if (AppStateService.OwnLockedOrder?.IsLockedByCurrentUser is true)
        {
            await AddOrderItemToOrder(AppStateService.OwnLockedOrder.OrderId, editingItem);
        }
        else
        {
            await AddArticle(editingItem);
        }

        await OnCancelEditing.InvokeAsync();
    }

    private async Task AddOrderItemToOrder(int orderId, BaseOrderItemEditModel editingItem)
    {
        var newOrderItem = Mapper.Map<OrderItemEditModel>(editingItem);

        var result = await Mediator.Send(new AddOrderItemToOrderCommand(orderId, newOrderItem));
        if (result.Succeeded)
        {
            ToastService.Show(ToastType.AddOrderItemSuccess);
        }
        else
        {
            Logger.LogError(result.Messages.String());
            ToastService.Show(ToastType.AddOrderItemError, result.Messages);
        }
    }

    private async Task AddArticle(BaseOrderItemEditModel editingItem)
    {
        var newOrderItem = Mapper.Map<OrderItemEditModel>(editingItem);

        var result = await Mediator.Send(new AddOrderItemToShoppingCartCommand(newOrderItem));
        if (result.Succeeded)
        {
            ToastService.Show(ToastType.AddCartItemSuccess);
            AppStateService.SetShoppingCart(this);
        }
        else
        {
            Logger.LogError(result.Messages.String());
            ToastService.Show(ToastType.AddCartItemError, result.Messages.String());
        }
    }

    #endregion

    #region DraftEditor

    private readonly FileNameModel _model = new();
    private bool _isDraftEditorVisible;
    private string DraftContent { get; set; } = "";

    private void OpenDraftEditorHandle()
    {
        _model.FileName = EditingItem.DraftOriginalFileName ?? GenerateRandomFileName();
        DraftContent = EditingItem.DraftDrawingSource ?? "";
        _isDraftEditorVisible = true;
    }

    private void OnDraftEditorCancel()
    {
        _isDraftEditorVisible = false;
    }

    private async Task OnDraftEditorSave(SaveRecord args)
    {
        // Validate the form
        var isValid = _editContext.Validate();
        if (!isValid)
        {
            ToastService.ShowError("Podaj nazwę szkicu".Tr());
            return;
        }

        DraftContent = args.DraftContent;
        try
        {
            var result = await Mediator.Send(new SavePngCommand
            {
                PngFile = args.PngContent,
                FileName = _model.FileName
            });

            if (result.Succeeded)
            {
                EditingItem.DraftEditable = true;
                EditingItem.DraftOriginalFileName = result.Data.OriginalFileName;
                EditingItem.DraftHashedFileName = result.Data.HashFileName;
                EditingItem.DraftDrawingSource = DraftContent;
            }
            else
            {
                throw new SavePngException(result.Messages);
            }
        }
        catch (Exception e)
        {
            Logger.LogError("Error during saving file", e.Message);
            ToastService.ShowError("Błąd podczas zapisu pliku".Tr());
        }
        finally
        {
            _isDraftEditorVisible = false;
        }
    }

    #endregion

}