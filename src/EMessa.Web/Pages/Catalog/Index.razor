@page "/catalog"
@attribute [Authorize]

@using EMessa.Base.Enums
@using EMessa.Base.Models
@using EMessa.Core.Features.Sales.Queries.CommonResponses
@using EMessa.Core.Features.ShoppingCart.Models
@using EMessa.Core.Features.ShoppingCart.Queries.GetAddNewItemModel
@using EMessa.Core.Models
@using EMessa.Services
@using EMessa.Services.Interfaces
@using EMessa.Web.Interfaces
@using EMessa.Web.Pages.Catalog.Parts
@using EMessa.Web.StateServices

@* @inject ILogger<Index> Logger *@
@inject IMediator Mediator
@inject IShoppingCartStateService ShoppingCartStateService
@implements IDisposable

<IndexCard Title=@("Katalog produktów".Tr())>
    <ChildContent>
        <div class="w-full flex flex-col gap-2 p-2">
            @if (_editingItem == null)
            {
                <ShoppingCartList OnSelectedArticle="@SelectedEditingItemHandler" 
                                  OnSelectedSaleArticle="@SelectedSaleEditingItemHandler"/>
            }
            else
            {
                <EditShoppingCart EditingItem="@_editingItem"
                                  NewRequestedSheet="@_newRequestedSheet"
                                  OnCancelEditing="@CancelEditingHandler" />
            }
        </div>
    </ChildContent>
</IndexCard>

@code {

    private ShoppingCartItemEditModel? _editingItem;
    
    private RequestedSheetEditModel? _newRequestedSheet;

    public SelectedOptionAndOptionValue? SelectedOptionAndOptionValue
    {
        get => ShoppingCartStateService.SelectedOptionAndOptionValue;
        set
        {
            ShoppingCartStateService.SelectedOptionAndOptionValue = value;
            StateHasChanged();
        }
    }

    private List<CartOrderItem> _cartItems = [];

    private int? _orderItemId;

    private readonly List<DropDownListItem<SelectedOptionItem>> _predefinedList =
    [
        CopyOptionsMethod.Disabled.ToDropDownListItem(0),
        CopyOptionsMethod.First.ToDropDownListItem(0),
        CopyOptionsMethod.Last.ToDropDownListItem(0),
    ];
    
    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        ShoppingCartStateService.OptionItemChanged += OnSelectedOptionItem;
        ShoppingCartStateService.SelectedOptionItem = _predefinedList.First(x => x.Value.Method == CopyOptionsMethod.First).Value;
    }

    void IDisposable.Dispose()
    {
        ShoppingCartStateService.OptionItemChanged -= OnSelectedOptionItem;
    }

    private void OnSelectedOptionItem(SelectedOptionItem arg)
    {
        ShoppingCartStateService.SelectedOptionItem = arg;
        _ = UpdateOrderItemId();
    }

    private async Task UpdateOrderItemId()
    {
        // Set null, first, last, selected
        switch (ShoppingCartStateService.SelectedOptionItem.Method)
        {
            default:
            case CopyOptionsMethod.Disabled:
                await ChangedOrderItemId(null);
                break;
            case CopyOptionsMethod.First:
                await ChangedOrderItemId(_cartItems.FirstOrDefault()?.OrderItemId);
                break;
            case CopyOptionsMethod.Last:
                await ChangedOrderItemId(_cartItems.LastOrDefault()?.OrderItemId);
                break;
            case CopyOptionsMethod.Selected:
                await ChangedOrderItemId(ShoppingCartStateService.SelectedOptionItem.OrderItemId);
                break;
        }
    }

    private async Task ChangedOrderItemId(int? orderItemId)
    {
        _orderItemId = orderItemId;
        await LoadOptionValues();
    }

    private async Task SetOrderItemId()
    {
        _cartItems = await ShoppingCartStateService.GetOrderItems(AppStateService.UserData.UserProfileId); // List<{ArticleName, OrderItemId}>;

        if (_cartItems.Count == 0)
        {
            ShoppingCartStateService.SelectedOptionItem = _predefinedList.First(x => x.Value.Method == CopyOptionsMethod.First).Value;
        }

        await UpdateOrderItemId();
    }

    private void PrepareDropdownList()
    {
        ShoppingCartStateService.OptionsList = _predefinedList.ToList();
        ShoppingCartStateService.OptionsList.AddRange(_cartItems
            .Select((x, i) => CopyOptionsMethod.Selected.ToDropDownListItem(x.OrderItemId, $"{i + 1}. {x.ArticleName}"))
        );
    }

    private void CancelEditingHandler()
    {
        _editingItem = null;
        _newRequestedSheet = null;
        SelectedOptionAndOptionValue = null;
    }

    private async Task SelectedEditingItemHandler(OrderItemEditArticleModel article)
    {
        await SetOrderItemId();
        PrepareDropdownList();
        await LoadEditingItem(article);
        StateHasChanged();
    }

    private async Task LoadEditingItem(OrderItemEditArticleModel article)
    {
        var response = await Mediator.Send(new GetShoppingCartItemEditModelQuery(article.Id));
        if (response.Succeeded)
        {
            _editingItem = response.Data;
            _editingItem.Article = article;
            PresetNewRequestedSheet(_editingItem.Article);
            await LoadOptionValues();
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError, response.Messages);
            _editingItem = null;
            _newRequestedSheet = null;
        }
    }

    private async Task SelectedSaleEditingItemHandler(SaleArticleResponse saleArticle)
    {
        await SetOrderItemId();
        PrepareDropdownList();
        await LoadSaleEditingItem(saleArticle);
        StateHasChanged();
    }

    private async Task LoadSaleEditingItem(SaleArticleResponse saleArticle)
    {
        // Pobierz podstawowy model dla artykułu
        var response = await Mediator.Send(new GetShoppingCartItemEditModelQuery(saleArticle.ArticleId));
        if (response.Succeeded)
        {
            _editingItem = response.Data;

            // Oznacz jako promocyjny artykuł
            _editingItem.IsSaleArticle = true;
            // Sale
            _editingItem.SaleArticle = saleArticle;
            _editingItem.SaleAvailableWeight = saleArticle.AvailableWeight;
            _editingItem.SaleAvailableLength = SaleWeightCalculatorBase.CalculateAvailableLength(
                saleArticle.AvailableWeight,
                saleArticle.Efficiency,
                saleArticle.Rolls);
            // Article
            _editingItem.Article.HasAvailableSales = true;
            _editingItem.Article.IsActive = true;
            _editingItem.Article.IsDeleted = false;
            if (saleArticle.Width > 0)
            {
                _editingItem.Article.DefaultWidth = saleArticle.Width;
            }

            PresetNewRequestedSheet(_editingItem.Article);
            await LoadSaleOptionValues(saleArticle);
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError, response.Messages);
            _editingItem = null;
            _newRequestedSheet = null;
        }
    }

    private async Task LoadSaleOptionValues(SaleArticleResponse saleArticle)
    {
        if (_editingItem == null)
            return;

        // Pobierz opcje artykułu
        var response = await Mediator.Send(new GetRestrictedArticleOptionsQuery(
            _editingItem.ArticleId,
            _editingItem.OrderItemId,
            _editingItem.IsSaleArticle,
            -1,
            null,
            []));

        if (response.Succeeded)
        {
            _editingItem.OptionValues = response.Data;

            // Ustaw wartości promocyjne i przygotuj do zablokowania edycji
            foreach (var option in _editingItem.OptionValues)
            {
                switch (option.OptionCode.ToUpper())
                {
                    case OptionCode.Coat: // Kod opcji powłoki
                        option.OptionValueId = saleArticle.CoatId;
                        option.UserSelectedOptionValueId = saleArticle.CoatId;
                        break;

                    case OptionCode.Color: // Kod opcji koloru
                        option.OptionValueId = saleArticle.ColorId;
                        option.UserSelectedOptionValueId = saleArticle.ColorId;
                        break;

                    case OptionCode.Thickness: // Kod opcji grubości
                        option.OptionValueId = saleArticle.ThickId;
                        option.UserSelectedOptionValueId = saleArticle.ThickId;
                        break;
                }
            }

            SelectedOptionAndOptionValue = await ShoppingCartStateService.PresetOptionValues(_editingItem.OptionValues, _orderItemId);
            PresetNewRequestedSheet(_editingItem.Article);
        }
        else
        {
            ToastService.ShowError("Nieokreślony błąd zawężania opcji produktu promocyjnego".Tr());
        }
    }

    private void PresetNewRequestedSheet(OrderItemEditArticleModel article)
    {
        _newRequestedSheet = new RequestedSheetEditModel
        {
            Quantity = article.DefaultQuantity,
            Length = article.LengthEditable ? 0m : article.DefaultLength,
            Width = article.WidthEditable ? 0m : article.DefaultWidth
        };
    }

    private async Task LoadOptionValues()
    {
        if (_editingItem == null)
            return;

        var response = await Mediator.Send(new GetRestrictedArticleOptionsQuery(_editingItem.ArticleId, _editingItem.OrderItemId, false, -1, null, []));
        if (response.Succeeded)
        {
            _editingItem.OptionValues = response.Data;
            SelectedOptionAndOptionValue = await ShoppingCartStateService.PresetOptionValues(_editingItem.OptionValues, _orderItemId);
            PresetNewRequestedSheet(_editingItem.Article);
            // SetSheetEditorsEnabled();
            // await GetTotalOverlapLength();
        }
        else
        {
            ToastService.ShowError("Nieokreślony błąd zawężania opcji produktu".Tr());
        }
    }
}