@using EMessa.Base.Enums
@using EMessa.Base.Models
@using EMessa.Core.Features.ArticleValidators.Models
@using EMessa.Core.Features.ArticleValidators.Queries.GetAllValidatorsForArticle
@using EMessa.Core.Features.Articles.Queries.GetArticleForValidation
@using EMessa.Core.Features.OrderItems.Commands.ValidateSheet
@using EMessa.Core.Features.ShoppingCart.Models
@using EMessa.Core.Features.ShoppingCart.Queries.GetAddNewItemModel
@using EMessa.Core.Models
@using EMessa.Core.Services.Calculators
@using EMessa.Services.Interfaces
@using EMessa.Web.Helpers
@using EMessa.Web.Interfaces
@using <PERSON><PERSON><PERSON>
@using Ra<PERSON>zen.Blazor

@inject IMediator Mediator
@inject ILogger<ArticleComplexEditComponent> Logger
@inject ISumsCalculatorService SumsCalculatorService
@inject IMapper Mapper
@inject IArticleValidatorService ArticleValidatorService
@inject ISheetValidationService SheetValidationService
@inject IShoppingCartStateService ShoppingCartStateService
@inject IEmessaTooltipService EmessaTooltipService
@implements IDisposable

<style>
    /* Własne style dla niepoprawnych wierszy */
    .rz-grid-table-striped tbody tr.bg-row-incorrect td {
        background-color: #fff7ed !important; /* bg-orange-50 */
    }

    /* Własne style dla niepoprawnych parzystych wierszy */
    .rz-grid-table-striped tbody tr.bg-row-incorrect:nth-child(even) td {
        background-color: #ffedd5 !important; /* bg-orange-100 */
    }

</style>

<div class="flex justify-content-between emessa">
    <RadzenStack Orientation="Orientation.Vertical" class="w-full p-2 border-1 gap-y-2"
                 id="@($"edit-{EditingItem.ArticleId}")">
        <RadzenText Text="@("Edycja pozycji zamówienia".Tr())" TextStyle="TextStyle.Body2" class="m-0" />
        <RadzenText Text="@Article.TranslatedName" TextStyle="TextStyle.Subtitle2" class="m-0" />
        <div class="w-full flex flex-col md:flex-row gap-x-2 gap-y-1">
            <div class="w-full md:w-1/3 md:min-w-80 flex flex-col gap-2 relative">
                <div class="w-full flex flex-col gap-2 p-2 border-1">
                    <h1 class="font-bold">@("Opcje produktu".Tr())</h1>

                    @if (EditingItem.IsSaleArticle)
                    {
                        // todo buttons SaleButton view_sale_button CancelButton
                        <RadzenStack Orientation="Orientation.Vertical" JustifyContent="JustifyContent.Start"
                                     AlignItems="AlignItems.Start" Gap=".125rem">
                            <RadzenText Text="@("Promocja".Tr())" TextStyle="TextStyle.Subtitle2" class="m-0" />
                            <RadzenTextArea Value="@(EditingItem.SaleArticle?.Name ?? "")" class="m-0 w-full bg-esa-disabled"
                                            Rows="2" ReadOnly />
                        </RadzenStack>
                    }

                    @if (ShowPreselectOptions)
                    {
                        <div class="flex flex-col gap-1">
                            <span>@("Przypisz wartości opcji z".Tr())</span>
                            <RadzenDropDown TValue="SelectedOptionItem"
                                            Data="@(GetOptionsList())"
                                            Value="@(GetOptionValue())"
                                            ValueChanged="@SetOptionItem"
                                            TextProperty="Text"
                                            ValueProperty="Value"
                                            Disabled="@EditingItem.IsSaleArticle"
                                            Style="width: 100%;" />
                        </div>
                    }
                    <div class="flex flex-col gap-2">
                        <ArticleOptionsEditorsComponent
                            EditingOptions="@EditingItem.OptionValues"
                            ArticleId="@Article.Id"
                            IsSaleArticle="@EditingItem.IsSaleArticle"
                            OnOptionValueChanged="@OnOptionValueChanged" />
                        <div><strong>@EditingItem.NewIndex</strong></div>
                    </div>
                </div>
                @if (EditingItem.ProfileEditable)
                {
                    <div class="w-full p-2 border-1">
                        <div class="min-h-full">
                            <ArticleDraftComponent EditItem="@EditingItem"
                                                   OpenDraftEditor="@OpenDraftEditorHandle" />
                        </div>
                    </div>
                }
                @* Overlay to block Product options during showing validation proposals *@
                @if (_showState != ShowState.Default)
                {
                    <div
                        class="absolute inset-0 bg-black opacity-10 hover:opacity-50 transition-opacity duration-200 z-50"></div>
                }
            </div>
            <div class="w-full md:w-2/3 md:max-w-[800px] p-2 border-1">
                @switch (_showState)
                {
                    case ShowState.Default:
                    default:
                        <div class="w-full flex flex-col gap-2">
                            <h1 class="font-bold">@("Wymiary arkuszy".Tr())</h1>
                            <div class="my-1 flex flex-col md:flex-row md:items-center justify-start gap-2">
                                <div class="whitespace-nowrap">
                                    @("Ilość".Tr())
                                    <RadzenNumeric TValue="int" @ref="_numericQuantity"
                                                   @bind-Value="@NewRequestedSheet.Quantity"
                                                   Min="0"
                                                   Change="@SetSplitNEnabled"
                                                   ReadOnly="@(!Article.QuantityEditable)"
                                                   Disabled="@(!_txtQuantityEnable)"
                                                   class="w-14 h-7"
                                                   ShowUpDown />
                                </div>
                                <div class="whitespace-nowrap">
                                    @("Długość".Tr())
                                    <RadzenNumeric TValue="decimal"
                                                   @bind-Value="@NewRequestedSheet.Length"
                                                   Change="@SetSplitNEnabled"
                                                   ReadOnly="@(!Article.LengthEditable)"
                                                   Disabled="@(!_txtLengthEnable)"
                                                   class="w-16 h-7"
                                                   ShowUpDown="false" />

                                </div>
                                <div class="whitespace-nowrap">
                                    @("Szerokość".Tr())
                                    <RadzenNumeric TValue="decimal"
                                                   @bind-Value="@NewRequestedSheet.Width"
                                                   Change="@SetSplitNEnabled"
                                                   ReadOnly="@(!Article.WidthEditable)"
                                                   Disabled="@(!_txtWidthEnable)"
                                                   class="w-16 h-7"
                                                   ShowUpDown="false" />
                                </div>
                                @* <div class="whitespace-nowrap" *@
                                @*      title="Całkowita długość strefy przetłoczenia"> *@
                                @*     @{ var embossZoneLength = 1000m * (_sheetValidationModel?.EmbossZoneLength ?? 0m); } *@
                                @*     @("Strefa".Tr()) *@
                                @*     <strong>@embossZoneLength.ToString("n0")</strong> *@
                                @*     @(" " + "mm".Tr()) *@
                                @* </div> *@
                                @* Dodaj arkusz *@
                                <AddRadzenButton Text="@("Dodaj arkusze".Tr())"
                                                 Disabled="@_btnAddSheetDisabled"
                                                 Click="@AddSheetHandler" />
                                @* Dodaj arkusz z podziałem na N *@
                                @if (Article is { IsSplitteable: true, IsSplittableNParts: true })
                                {
                                    <AddRadzenButton Text="@("Podziel na N".Tr())"
                                                     Disabled="@_btnAddSheetDisabled"
                                                     Click="@(_ => AddSheetWithSplitNHandler(null))"
                                                     MouseEnter="@(args => EmessaTooltipService.Open(args, "Dodaj arkusze z podziałem na N".Tr()))" />
                                }
                            </div>

                            <RadzenDataGrid @ref="@_sheetsGrid" TItem="ArticleSheetEditModel"
                                            Data="@EditingItem.ViewOrderItemSheets"
                                            AllowPaging PageSize="10"
                                            EditMode="DataGridEditMode.Single"
                                            AllowColumnResize="true"
                                            RowRender="@OnSheetRowRender"
                                            EmptyText="@("Brak danych".Tr())">
                                <Columns>
                                    <GridActionsColumn TItem="ArticleSheetEditModel"
                                                       IsLargeScreen="@AppStateService.IsLargeScreen"
                                                       Width="@(AppStateService.IsLargeScreen ? "90px" : "30px")">
                                        <ContentTemplate Context="sheet">
                                            <DeleteRadzenButton IsAction
                                                                Click="@(_ => OnClickDelete(sheet))"
                                                                MouseEnter="@(args => EmessaTooltipService.Open(
                                                                                args, "Usuń arkusze".Tr()))" />
                                            <RefreshRadzenButton IsAction Click="@(_ => OnClickRecalculate(sheet))"
                                                                 Visible="@sheet.RequestedSheet.IsIncorrect"
                                                                 MouseEnter="@(args => EmessaTooltipService.Open(
                                                                                 args, "Przelicz ponownie".Tr()))" />
                                        </ContentTemplate>
                                    </GridActionsColumn>

                                    <RadzenDataGridColumn TItem="ArticleSheetEditModel" Title="@("Informacje".Tr())"
                                                          Width="80px" TextAlign="TextAlign.Center" Sortable="false"
                                                          Filterable="false">
                                        <Template Context="sheet">
                                            <RadzenStack Orientation="Orientation.Horizontal"
                                                         JustifyContent="JustifyContent.Center" Gap=".5rem">
                                                @{
                                                    RenderFragment<TooltipService> tooltipContent = (context) =>
                                                        @<div class="flex flex-col gap-1">
                                                            <div class="text-base">@("Oryginalne wymagania".Tr())</div>
                                                            <div class="flex justify-between text-sm">
                                                                <span>@("Ilość".Tr()):</span>
                                                                <span>
                                                                    @sheet.RequestedSheet.Quantity @("szt.".Tr())
                                                                </span>
                                                            </div>
                                                            <div class="flex justify-between text-sm">
                                                                <span>@("Długość".Tr()):</span>
                                                                <span>
                                                                    @sheet.RequestedSheet.Length.ToString("n2") @("m".Tr())
                                                                </span>
                                                            </div>
                                                            <div class="flex justify-between text-sm">
                                                                <span>@("Szerokość".Tr()):</span>
                                                                <span>
                                                                    @sheet.RequestedSheet.Width.ToString("n2") @("m".Tr())
                                                                </span>
                                                            </div>
                                                            @if (sheet.RequestedSheet.SplitN.HasValue)
                                                            {
                                                                <div class="flex justify-between text-sm">
                                                                    <span>@("Podział na N".Tr()):</span>
                                                                    <span>
                                                                        @sheet.RequestedSheet.SplitN.Value.ToString("n0") @("części".Tr())
                                                                    </span>
                                                                </div>
                                                            }
                                                        </div>;
                                                }
                                                <RadzenButton Icon="info" class="help-icon esa-button-size"
                                                              Size="ButtonSize.ExtraSmall"
                                                              MouseEnter="@(args => EmessaTooltipService.OpenHtml(
                                                                              args, tooltipContent))" />
                                                <DeleteRadzenButton IsAction Icon="error"
                                                                    Visible="@sheet.RequestedSheet.IsIncorrect"
                                                                    MouseEnter="@(args => EmessaTooltipService.Open(
                                                                                    args, "Arkusz nie spełnia wymagań".Tr()))" />
                                            </RadzenStack>
                                        </Template>
                                    </RadzenDataGridColumn>

                                    <RadzenDataGridColumn TItem="ArticleSheetEditModel"
                                                          Property="@nameof(ArticleSheetEditModel.Id)"
                                                          Title="@("Id".Tr())"
                                                          Visible="false" />

                                    @* <RadzenDataGridColumn TItem="ArticleSheetEditModel" *@
                                    @*                       Title="Debug" *@
                                    @*                       Width="auto" TextAlign="TextAlign.Left"> *@
                                    @*     <Template Context="sheet"> *@
                                    @*         @($"{sheet.RequestedSheet.Id}.{sheet.Id}.{(sheet.RequestedSheet.IsIncorrect ? "Incorrect" : "Correct")}") *@
                                    @*     </Template> *@
                                    @* </RadzenDataGridColumn> *@

                                    <RadzenDataGridColumn TItem="ArticleSheetEditModel"
                                                          Property="@nameof(ArticleSheetEditModel.Quantity)"
                                                          Title="@("Ilość".Tr())"
                                                          Width="auto" TextAlign="TextAlign.Left">
                                        <Template Context="sheet">
                                            @sheet.Quantity
                                        </Template>
                                    </RadzenDataGridColumn>

                                    <RadzenDataGridColumn TItem="ArticleSheetEditModel"
                                                          Property="@nameof(ArticleSheetEditModel.Length)"
                                                          Title="@("Długość".Tr())"
                                                          Width="auto" TextAlign="TextAlign.Left">
                                        <Template Context="sheet">
                                            @sheet.Length.ToString("n2")
                                        </Template>
                                    </RadzenDataGridColumn>

                                    <RadzenDataGridColumn TItem="ArticleSheetEditModel"
                                                          Property="@nameof(ArticleSheetEditModel.Width)"
                                                          Title="@("Szerokość".Tr())"
                                                          Width="auto" TextAlign="TextAlign.Left"
                                                          Sortable="false"
                                                          Filterable="false">
                                        <Template Context="sheet">
                                            @sheet.Width.ToString("n2")
                                        </Template>
                                    </RadzenDataGridColumn>
                                </Columns>
                            </RadzenDataGrid>

                            <div class="flex flex-col md:flex-row justify-content-between">
                                <div>
                                    @("Suma".Tr()) <strong>@EditingItem.SumM2.ToString("n2")</strong> @("m2".Tr())
                                </div>
                                <div>
                                    @("Suma".Tr()) <strong>@EditingItem.SumMb.ToString("n2")</strong> @("mb".Tr())
                                </div>
                                <div>
                                    @("Suma".Tr()) <strong>@EditingItem.SumQuantity.ToString("n0")</strong> @("szt.".Tr())
                                </div>
                                <div>
                                    @("Waga".Tr()) <strong>@EditingItem.Weight.ToString("n2")</strong> @("kg".Tr())
                                </div>
                            </div>
                            <div class="mt-2">
                                <h1 class="font-bold">@("Uwagi".Tr())</h1>
                                <RadzenTextArea Placeholder="@("Uwagi do pozycji zamówienia".Tr())"
                                                @bind-Value="@EditingItem.Comments"
                                                Rows="3" class="w-full" />
                            </div>
                            @if (EditingItem.IsSaleArticle)
                            {
                                <RadzenStack Orientation="Orientation.Vertical" JustifyContent="JustifyContent.Start"
                                             AlignItems="AlignItems.Start" Gap=".125rem">
                                    <RadzenText Text="@("Promocja".Tr())" TextStyle="TextStyle.Subtitle2" class="m-0" />
                                    <span>
                                        <RadzenText Text="@("Ilość pozostała".Tr() + ": ")" TextStyle="TextStyle.Body2"
                                                    class="m-0 inline" />
                                        <RadzenText Text="@($"~{EditingItem.SaleAvailableLength:n0} m ")"
                                                    TextStyle="TextStyle.Body2"
                                                    class="m-0 font-bold inline" />
                                        <RadzenText Text="@($"({EditingItem.SaleAvailableWeight:n2} kg)")"
                                                    TextStyle="TextStyle.Body2"
                                                    class="m-0 inline" />
                                    </span>
                                    <RadzenText Text="@("Opis".Tr())" TextStyle="TextStyle.Subtitle2" class="m-0" />
                                    <RadzenTextArea Value="@(EditingItem.SaleArticle?.Description ?? "")"
                                                    class="m-0 w-full bg-esa-disabled"
                                                    Rows="5" ReadOnly />
                                </RadzenStack>
                            }
                        </div>
                        break;
                    case ShowState.NSplitSheet:
                        <NSplitSheets
                            Title="@("Podział na (N) arkuszy".Tr())"
                            AddSheetsDisabled="@_btnAddSheetDisabled"
                            CssClass="md:max-w-[500px] md:min-w-80"
                            MinSplitN="@_minSplitN"
                            MaxSplitN="@_maxSplitN"
                            SplitN="@_splitN"
                            OnAddSplitSheet="@AddSplitSheetHandler"
                            OnCancel="@(() => CancelAddSplitSheetHandler())" />
                        <div class="w-auto"></div>
                        break;
                    case ShowState.NSplitSheetProposals:
                        <NSplitSheetsProposals
                            Title="@("Podział arkuszy (propozycje)".Tr())"
                            CssClass="md:max-w-[500px] md:min-w-80"
                            ValidationSplits="@_addNSplits"
                            OnAddProposedSheets="@(async proposedTuple => await AddSheetsHandler(proposedTuple.Sheets, proposedTuple.SplitN))"
                            OnCancel="@(() => CancelAddNSplitsHandler())" />
                        <div class="w-auto"></div>
                        break;
                    case ShowState.WrongSheetLength:
                        <SheetLengthValidator
                            Title="@("Nieprawidłowy wymiar arkusza".Tr())"
                            CssClass="md:max-w-[500px] md:min-w-80"
                            Article="@Article"
                            SheetValidationModel="@_sheetValidationModel"
                            OriginalSize="@_addOriginalSize"
                            ValidationErrors="@_addErrors"
                            ValidationWarnings="@_addWarnings"
                            OnAddProposedSheets="@(async proposedSheets => await AddSheetsHandler(proposedSheets))"
                            OnCancel="@(() => CancelSheetLengthDialog())" />
                        <div class="w-auto"></div>
                        break;
                    case ShowState.SheetValidator:
                        <ValidatorComponent
                            Title="@("Walidacja arkuszy (błędy i ostrzeżenia)".Tr())"
                            CssClass="md:max-w-[500px] md:min-w-80"
                            ValidationErrors="@_sheetValidationErrors"
                            ValidationWarnings="@_sheetValidationWarnings"
                            OnConfirmed="@(async () => await ConfirmSheetValidatorHandler())"
                            OnCancel="@(() => CancelSheetValidatorHandler())" />
                        <div class="w-auto"></div>
                        break;
                    case ShowState.ArticleValidator:
                        <ValidatorComponent
                            Title="@("Walidacja produktu (błędy i ostrzeżenia)".Tr())"
                            CssClass="md:max-w-[500px] md:min-w-80"
                            ValidationErrors="@_articleValidationErrors"
                            ValidationWarnings="@_articleValidationWarnings"
                            OnConfirmed="@(async () => await ConfirmArticleValidatorHandler())"
                            OnCancel="@(() => CancelArticleValidatorHandler())" />
                        <div class="w-auto"></div>
                        break;
                }
            </div>
        </div>
        <GridToolbar>
            <AddRadzenButton Text="@("Dodaj produkt".Tr())"
                             Visible="@(!ShowSaveButton)"
                             Click="@AddOrSaveArticleHandler"
                             Disabled="@DisableBtnSave" />
            <SaveRadzenButton Click="@AddOrSaveArticleHandler"
                              Visible="@(ShowSaveButton)"
                              Disabled="@DisableBtnSave" />
            <CancelRadzenButton Click="@CancelAddArticleHandler" />
        </GridToolbar>
    </RadzenStack>
</div>

@code {

    [Parameter] public required BaseOrderItemEditModel EditingItem { get; set; }
    [Parameter] public required RequestedSheetEditModel NewRequestedSheet { get; set; }
    [Parameter] public EventCallback<BaseOrderItemEditModel> OnSave { get; set; }
    [Parameter] public EventCallback OnCancel { get; set; }
    [Parameter] public EventCallback OpenDraftEditor { get; set; }
    [Parameter] public bool ShowSaveButton { get; set; }
    // [Parameter] public GetArticleResponse ArticleDefinition { get; set; } = new();

    private OrderItemEditArticleModel Article => EditingItem.Article;

    private bool ShowPreselectOptions => EditingItem.OptionValues.Any() && ShoppingCartStateService.OptionsList.Count > 3;

    private RequestedSheetEditModel? _recalculateRequestedSheet;

    private RequestedSheetEditModel RequestedSheet => _recalculateRequestedSheet ?? NewRequestedSheet;

    ShowState _showState = ShowState.Default;

    RadzenDataGrid<ArticleSheetEditModel> _sheetsGrid = new();
    RadzenNumeric<int> _numericQuantity = new();

    bool _hasCompleteOptionSet;
    bool _txtQuantityEnable;
    bool _txtLengthEnable;
    bool _txtWidthEnable;
    bool _btnAddSheetDisabled = true;
    ArticleSheetValidationModel? _sheetValidationModel;

    // Podział arkusza na N
    int _minSplitN = 2;
    int _maxSplitN = 2;
    int _splitN = 2;
    bool _btnAddSheetWithSplitNDisabled;
    List<ArticleSheetValidationResult> _addNSplits = [];

    // Podział arkusza
    ArticleSheetValidationResult? _addOriginalSize;
    List<ArticleSheetValidationResult> _addErrors = [];
    List<ArticleSheetValidationResult> _addWarnings = [];

    // Walidacja arkuszy
    List<string> _sheetValidationErrors = [];
    List<string> _sheetValidationWarnings = [];
    List<ArticleSheetEditModel> _validatingSheets = [];

    // Walidacja produktu
    List<string> _articleValidationErrors = [];
    List<string> _articleValidationWarnings = [];

    bool AddProfile => EditingItem.RequireProfile && string.IsNullOrEmpty(EditingItem.DraftDrawingSource);

    bool DisableBtnSave => !EditingItem.OptionValues.All(x => x.OptionValueId.HasValue)
                           || EditingItem.RequestedSheets.Count == 0
                           || EditingItem.RequestedSheets.Any(x => x.IsIncorrect)
                           || AddProfile;

    #region Initialize, Dispoze

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        AppStateService.StateChanged += OnStateChanged;
    }

    void IDisposable.Dispose()
    {
        AppStateService.StateChanged -= OnStateChanged;
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source == this) return;
        if (property == AppStateNotifyProperties.ScreenSizeChanged)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();
        SetSheetEditorsEnabled();
        EditingItem.ViewOrderItemSheets = EditingItem.RequestedSheets.SelectMany(x => x.OrderItemSheets).ToList();
        await OnOptionValueChanged(ShoppingCartStateService.SelectedOptionAndOptionValue);
        _sheetValidationModel = await GetSheetValidationModel();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);
    }

    #endregion

    protected List<ArticleSheetEditModel> GetProposedSheets(
        List<decimal> proposedLengths,
        int quantity,
        decimal width)
    {
        return proposedLengths
            .Select(length => new ArticleSheetEditModel
            {
                Quantity = quantity,
                Length = length,
                Width = width
            })
            .ToList();
    }

    private async Task OnOptionValueChanged(SelectedOptionAndOptionValue? selectedOptionAndOptionValue)
    {
        _sheetValidationModel = await GetSheetValidationModel();

        if (selectedOptionAndOptionValue is null)
        {
            SetSheetEditorsEnabled();
            await RevalidateSheets();
            return;
        }

        var response = await Mediator.Send(new GetRestrictedArticleOptionsQuery(
            Article.Id,
            EditingItem.OrderItemId,
            EditingItem.IsSaleArticle,
            selectedOptionAndOptionValue.OptionId,
            selectedOptionAndOptionValue.OptionValueId,
            EditingItem.OptionValues
        ));
        if (response.Succeeded)
        {
            EditingItem.OptionValues = response.Data;
            SetSheetEditorsEnabled();
            await RevalidateSheets();
        }
        else
        {
            ToastService.ShowError("Nieokreślony błąd zawężania opcji produktu".Tr());
        }
    }

    private async Task AddOrSaveArticleHandler()
    {
        var ready = true;
        if (!EditingItem.OptionValues.All(x => x.OptionValueId.HasValue))
        {
            ToastService.ShowError("Wprowadź wszystkie wymagane opcje produktu".Tr());
            ready = false;
        }

        if (EditingItem.RequestedSheets.Count == 0)
        {
            ToastService.ShowError("Dodaj przynajmniej jeden arkusz".Tr());
            ready = false;
        }

        if (EditingItem.RequireProfile && string.IsNullOrEmpty(EditingItem.DraftDrawingSource))
        {
            ToastService.ShowError("Dodaj szkic obróbki".Tr());
            ready = false;
        }

        if (!ready) 
            return;

        // Walidacja produktu
        if (await ValidateWholeArticleWithArticleValidatorConditions(EditingItem))
        {
            await SaveArticle();
        }
    }

    private async Task SaveArticle()
    {
        await OnSave.InvokeAsync(EditingItem);
    }

    private async Task CancelAddArticleHandler()
    {
        await OnCancel.InvokeAsync();
    }

    private void SetSheetEditorsEnabled()
    {
        _hasCompleteOptionSet = EditingItem.OptionValues.All(x => x.OptionValueId.HasValue);
        _txtQuantityEnable = _hasCompleteOptionSet && Article.QuantityEditable;
        _txtLengthEnable = _hasCompleteOptionSet && Article.LengthEditable;
        _txtWidthEnable = _hasCompleteOptionSet && Article.WidthEditable;
        _btnAddSheetDisabled = !_hasCompleteOptionSet;
        SetSplitNEnabled();
    }

    private void SetSplitNEnabled()
    {
        _btnAddSheetWithSplitNDisabled =
            !_hasCompleteOptionSet
            || _sheetValidationModel is not { IsSplitteable: true }
            || NewRequestedSheet.Quantity <= 0
            || NewRequestedSheet.Width <= 0
            || NewRequestedSheet.Length < 2 * _sheetValidationModel.MinLength
            || NewRequestedSheet.Length > 5 * _sheetValidationModel.SplitLength;
    }

    private async Task AddSheetWithSplitNHandler(int? splitN = null)
    {
        if (!_hasCompleteOptionSet) return;

        _sheetValidationModel = await GetSheetValidationModel();
        if (_sheetValidationModel == null)
        {
            return;
        }

        if (!_sheetValidationModel.IsSplitteable || !_sheetValidationModel.IsSplittableNParts)
        {
            ToastService.ShowError("Ten produkt nie można podzielić".Tr());
            return;
        }

        if (RequestedSheet.Quantity <= 0 || RequestedSheet.Length <= 0 || RequestedSheet.Width <= 0)
        {
            ToastService.ShowError("Wprowadź wszystkie wymagane wymiary arkusza".Tr());
            return;
        }

        if (RequestedSheet.Length < 2 * _sheetValidationModel.MinLength)
        {
            ToastService.ShowError("Długość arkusza jest mniejsza od minimalnej długości * 2".Tr());
            return;
        }

        if (RequestedSheet.Length > 5 * _sheetValidationModel.SplitLength)
        {
            ToastService.ShowError("Długość arkusza przekracza zalecaną długość * 5".Tr());
            return;
        }

        _minSplitN = (int)Math.Max(2, Math.Ceiling(RequestedSheet.Length / _sheetValidationModel.SplitLength));
        _maxSplitN = (int)Math.Min(5, Math.Floor(RequestedSheet.Length / _sheetValidationModel.MinLength));
        _splitN = splitN ?? _minSplitN;
        _showState = ShowState.NSplitSheet;
    }

    private void CancelAddNSplitsHandler()
    {
        _recalculateRequestedSheet = null;
        _showState = ShowState.Default;
    }

    private async Task AddSplitSheetHandler(int splitN)
    {
        _showState = ShowState.Default;
        if (_btnAddSheetWithSplitNDisabled && _recalculateRequestedSheet == null)
            return;

        _splitN = splitN;
        await ValidateRequestedSheet(splitN);
    }

    private void CancelAddSplitSheetHandler()
    {
        _showState = ShowState.Default;
    }

    private async Task AddSheetHandler()
    {
        if (!_hasCompleteOptionSet) return;

        _recalculateRequestedSheet = null;
        await ValidateRequestedSheet();
    }

    private async Task RevalidateSheets()
    {
        if (!_hasCompleteOptionSet || EditingItem.RequestedSheets.Count == 0) return;

        _recalculateRequestedSheet = null;

        _sheetValidationModel = await GetSheetValidationModel();
        if (_sheetValidationModel == null)
        {
            return;
        }

        EditingItem.RequestedSheets.ForEach(requestedSheet =>
        {
            var orderItemSheets = requestedSheet.OrderItemSheets;

            var validationResults = requestedSheet.SplitN.HasValue
                ? SheetValidationService.ValidateRequestedSheetWithSplit(_sheetValidationModel, requestedSheet, requestedSheet.SplitN.Value)
                : SheetValidationService.ValidateRequestedSheet(_sheetValidationModel, requestedSheet);

            var validationSheets = validationResults
                .Select(x => x.ProposedSheets)
                .Where(proposedSheets => proposedSheets.Count == orderItemSheets.Count)
                .ToList();

            // Nie ma propozycji, to zaznacz, że arkusz jest poprawny
            if (validationSheets.Count == 0)
            {
                requestedSheet.IsIncorrect = false;
                return;
            }

            foreach (var validations in validationSheets)
            {
                var allMatch = orderItemSheets.All(orderSheet =>
                    validations.Any(validationSheet =>
                        validationSheet.Quantity == orderSheet.Quantity && validationSheet.Length == orderSheet.Length && validationSheet.Width == orderSheet.Width));
                // Jeśli wszystkie arkusze pasują do którejś propozycji, to arkusz jest poprawny
                if (allMatch)
                {
                    requestedSheet.IsIncorrect = false;
                    return;
                }
            }

            // Jeśli nie ma żadnej propozycji i nie ma dopasowania, to arkusz jest błędny
            requestedSheet.IsIncorrect = true;
        });

        await _sheetsGrid.Reload();
        try
        {
            await _numericQuantity.FocusAsync();
        }
        catch
        {
            // ignored
        }
    }

    private async Task ValidateRequestedSheet(int? splitN = null)
    {
        if (RequestedSheet.Quantity <= 0 || RequestedSheet.Length <= 0 || RequestedSheet.Width <= 0)
        {
            ToastService.ShowError("Wprowadź wszystkie wymagane wymiary arkusza".Tr());
            return;
        }

        _sheetValidationModel = await GetSheetValidationModel();
        if (_sheetValidationModel == null)
            return;

        await ValidateArticleRequestedSheetLength(_sheetValidationModel, splitN);
    }

    private async Task ValidateArticleRequestedSheetLength(ArticleSheetValidationModel sheetValidationModel, int? splitN = null)
    {
        var validationResults = splitN.HasValue
            ? SheetValidationService.ValidateRequestedSheetWithSplit(sheetValidationModel, RequestedSheet, splitN.Value)
            : SheetValidationService.ValidateRequestedSheet(sheetValidationModel, RequestedSheet);

        if (validationResults.Count == 0)
        {
            // Nie ma błędów, ostrzeżeń, jest ok, dodajemy arkusz
            await AddSheetsHandler([RequestedSheet.ToArticleSheet()]);
            return;
        }

        _addErrors = [];
        _addWarnings = [];
        _addNSplits = [];

        _addOriginalSize = validationResults.FirstOrDefault(x => x.Type == ArticleSheetValidationResultType.AllowOriginalSize);

        _addErrors = validationResults.Where(x => x.Type == ArticleSheetValidationResultType.Error).ToList();
        if (_addErrors.Count > 0)
        {
            _showState = ShowState.WrongSheetLength;
            return;
        }

        _addWarnings = validationResults.Where(x => x.Type == ArticleSheetValidationResultType.Warning).ToList();
        if (_addWarnings.Count > 0)
        {
            _showState = ShowState.WrongSheetLength;
        }

        _addNSplits = validationResults.Where(x => x.Type == ArticleSheetValidationResultType.NSplit).ToList();
        if (_addNSplits.Count > 0)
        {
            _showState = ShowState.NSplitSheetProposals;
        }
    }

    private async Task<bool> ValidateSheetsWithArticleValidatorConditions(List<ArticleSheetEditModel> sheets, List<ArticleOptionEditModel> optionValues)
    {
        _sheetValidationErrors = [];
        _sheetValidationWarnings = [];
        _validatingSheets = [];

        var response = await Mediator.Send(new GetAllValidatorConditionsForArticleQuery(Article.Id, ArticleValidatorType.Sheet));
        if (response.Succeeded)
        {
            var options = optionValues
                .Select(o => new ArticleValidationOptionValue(o.OptionCode, o.ValueCode ?? ""))
                .ToList();

            var validators = response.Data;

            sheets.ForEach(sheet =>
            {
                var sheetValidatorData = new SheetValidatorData
                {
                    ArticleId = Article.Id,
                    ArticleCode = Article.Code,
                    Sheet = new ArticleValidatorSheet(sheet),
                    Options = options,
                };

                // Walidator arkusza
                var result = ArticleValidatorService.Validate(sheetValidatorData, validators);

                if (result.HasErrors)
                {
                    result.ErrorMessages.ForEach(message => _sheetValidationErrors.Add(message));
                }

                if (result.HasConfirmations)
                {
                    result.ConfirmationMessages.ForEach(message => _sheetValidationWarnings.Add(message));
                }
            });

            if (_sheetValidationErrors.Count == 0 && _sheetValidationWarnings.Count == 0)
                return true;

            _validatingSheets = sheets;
            _showState = ShowState.SheetValidator;
        }
        else
        {
            ToastService.ShowError("Nieokreślony błąd walidatorów arkusza".Tr());
        }

        return false;
    }

    private async Task<bool> ValidateWholeArticleWithArticleValidatorConditions(BaseOrderItemEditModel cartItem)
    {
        _articleValidationErrors = [];
        _articleValidationWarnings = [];

        var response = await Mediator.Send(new GetAllValidatorConditionsForArticleQuery(Article.Id, ArticleValidatorType.Article));
        if (response.Succeeded)
        {
            var sheets = cartItem.RequestedSheets
                .SelectMany(x => x.OrderItemSheets.Select(sheet => new ArticleValidatorSheet(sheet)))
                .ToList();
            var articleValidatorData = new ArticleValidatorData
            {
                ArticleId = Article.Id,
                ArticleCode = Article.Code,
                Sheets = sheets,
                Weight = cartItem.Weight,
                SumM2 = cartItem.SumM2,
                SumMb = cartItem.SumMb,
                SumQuantity = cartItem.SumQuantity,
                Options = cartItem.OptionValues
                    .Select(o => new ArticleValidationOptionValue(o.OptionCode, o.ValueCode ?? ""))
                    .ToList(),
            };

            // Walidator produktu
            var result = ArticleValidatorService.Validate(articleValidatorData, response.Data);

            if (result.HasErrors)
            {
                result.ErrorMessages.ForEach(message => _articleValidationErrors.Add(message));
            }

            if (result.HasConfirmations)
            {
                result.ConfirmationMessages.ForEach(message => _articleValidationWarnings.Add(message));
            }

            if (_articleValidationErrors.Count == 0 && _articleValidationWarnings.Count == 0)
                return true;

            _showState = ShowState.ArticleValidator;
        }
        else
        {
            ToastService.ShowError("Nieokreślony błąd walidatorów arkusza".Tr());
        }

        return false;
    }

    private async Task ConfirmSheetValidatorHandler()
    {
        _showState = ShowState.Default;
        AddSheets(RequestedSheet, _validatingSheets);
        await RecalculateRefresh();
        _sheetValidationErrors = [];
        _sheetValidationWarnings = [];
        _validatingSheets = [];
        // SetSaveEnable();
    }

    private void CancelSheetValidatorHandler()
    {
        _showState = ShowState.Default;
        _sheetValidationErrors = [];
        _sheetValidationWarnings = [];
        _validatingSheets = [];
    }

    private async Task ConfirmArticleValidatorHandler()
    {
        _showState = ShowState.Default;
        _articleValidationErrors = [];
        _articleValidationWarnings = [];
        await SaveArticle();
    }

    private void CancelArticleValidatorHandler()
    {
        _showState = ShowState.Default;
        _articleValidationErrors = [];
        _articleValidationWarnings = [];
    }

    private async Task AddSheetsHandler(List<ArticleSheetEditModel>? sheets, int? splitN = null)
    {
        if (sheets == null) return;

        _showState = ShowState.Default;

        RequestedSheet.IsIncorrect = false;
        RequestedSheet.SplitN = splitN;

        // Walidacja arkuszy
        if (await ValidateSheetsWithArticleValidatorConditions(sheets, EditingItem.OptionValues))
        {
            AddSheets(RequestedSheet, sheets, splitN);
            await RecalculateRefresh();
            // SetSaveEnable();
        }
    }

    private async Task<ArticleSheetValidationModel?> GetSheetValidationModel()
    {
        // Get article/sheet validation model
        var response = await Mediator.Send(new GetArticleForValidationQuery(Article.Id));
        if (!response.Succeeded)
        {
            ToastService.ShowError("Nie można pobrać danych artykułu do kontroli poprawności danych".Tr());
            _sheetValidationModel = null;
            return null;
        }

        // Map to sheet validation model
        var sheetValidationModel = response.Data;

        // Get module length
        var moduleString = EditingItem.OptionValues.FirstOrDefault(x => x.OptionCode.Equals(OptionCode.Module, StringComparison.OrdinalIgnoreCase))?.ValueCode ?? "0";
        var module = int.TryParse(moduleString, out var moduleInt) ? moduleInt * 0.001m : 0m;
        sheetValidationModel.Module = module;

        // Get max emboss zone addition
        var additionalEmbossZones = EditingItem.OptionValues
            .SelectMany(ov => ov.Values.Where(v => v.OptionValueId == ov.OptionValueId)
                .Select(v => v.EmbossZoneAddition))
            .DefaultIfEmpty(0);
        var additionalEmbossZone = Math.Round(
            additionalEmbossZones.Max(),
            SystemConstants.RoundingLengthDecimals,
            MidpointRounding.AwayFromZero);

        // Set additional emboss zone
        sheetValidationModel.AdditionalEmbossLength = additionalEmbossZone;
        // Correct overlap and emboss zone length with additional emboss zone
        sheetValidationModel.OverlapLength += additionalEmbossZone;
        // Total emboss zone length
        sheetValidationModel.EmbossZoneLength += additionalEmbossZone;

        if (!EditingItem.IsSaleArticle)
            return sheetValidationModel;

        // Sale
        var saleArticle = EditingItem.SaleArticle;
        // Default width
        if (saleArticle is { Width: > 0m })
        {
            sheetValidationModel.DefaultWidth = saleArticle.Width;
        }

        return sheetValidationModel;
    }

    private async Task RecalculateSheet(ArticleSheetEditModel sheet)
    {
        if (!_hasCompleteOptionSet) return;

        _recalculateRequestedSheet = sheet.RequestedSheet;

        if (_recalculateRequestedSheet.SplitN.HasValue)
        {
            await AddSheetWithSplitNHandler(_recalculateRequestedSheet.SplitN);
            return;
        }

        await ValidateRequestedSheet();
    }

    private void DeleteSheet(ArticleSheetEditModel sheet)
    {
        var requestedSheet = sheet.RequestedSheet;
        EditingItem.RequestedSheets.Remove(requestedSheet);
        // Refresh No ex [1,3,4] -> [1,2,3]
        var newNo = 1;
        // EditingItem.RequestedSheets.ForEach(x => x.Id = newId++);
        EditingItem.RequestedSheets.ForEach(x => x.No = newNo++);
    }

    private void AddSheets(RequestedSheetEditModel requestedSheet, List<ArticleSheetEditModel> sheets, int? splitN = null)
    {
        // New requested sheet
        if (requestedSheet.No == 0)
        {
            requestedSheet.No = EditingItem.RequestedSheets.Count + 1;
            EditingItem.RequestedSheets.Add(requestedSheet);
        }

        // Always clear correct sheets and add new ones
        requestedSheet.OrderItemSheets.Clear();
        requestedSheet.SplitN ??= splitN;
        sheets.ForEach(sheet =>
        {
            sheet.No = requestedSheet.OrderItemSheets.Count + 1;
            sheet.RequestedSheet = requestedSheet;
            requestedSheet.OrderItemSheets.Add(sheet);
        });

        _recalculateRequestedSheet = null;
    }

    private async Task RecalculateRefresh()
    {
        EditingItem.ViewOrderItemSheets = EditingItem.RequestedSheets.SelectMany(x => x.OrderItemSheets).ToList();
        SumsCalculatorService.RecalculateOrderItem(EditingItem, EditingItem.ArticleWeight, EditingItem.ArticleUnit);
        ResetNewSheet();
        await _sheetsGrid.Reload();
        await FocusOnQualityInput();
    }

    private async Task FocusOnQualityInput()
    {
        await Task.Delay(50);
        try
        {
            await _numericQuantity.FocusAsync();
        }
        catch (Exception)
        {
            Logger.LogWarning("Focus on quality input failed");
        }
    }

    private void CancelSheetLengthDialog()
    {
        _showState = ShowState.Default;
    }

    private async Task OpenDraftEditorHandle()
    {
        await OpenDraftEditor.InvokeAsync();
    }

    private void OnSheetRowRender(RowRenderEventArgs<ArticleSheetEditModel> args)
    {
        if (args.Data is not { } articleSheet)
            return;

        var classes = new List<string>();
        var attributes = new Dictionary<string, object>();

        if (articleSheet.RequestedSheet.IsIncorrect)
        {
            // classes.Add("bg-row-incorrect");
            attributes["title"] = "Arkusz nie spełnia wymagań technicznych. Przelicz go.".Tr();
        }

        HtmlAttributesHelper.EnhanceElement(args.Attributes, classes, attributes);
    }

    private async Task OnClickRecalculate(ArticleSheetEditModel sheet)
    {
        await RecalculateSheet(sheet);
        await RecalculateRefresh();
    }

    private async Task OnClickDelete(ArticleSheetEditModel sheet)
    {
        DeleteSheet(sheet);
        await RecalculateRefresh();
    }

    private void ResetNewSheet()
    {
        NewRequestedSheet = new RequestedSheetEditModel
        {
            Quantity = Article.DefaultQuantity,
            Length = Article.LengthEditable ? 0m : Article.DefaultLength,
            Width = Article.WidthEditable ? 0m : Article.DefaultWidth
        };
    }

    enum ShowState
    {
        Default,
        WrongSheetLength,
        SheetValidator,
        ArticleValidator,
        NSplitSheet,
        NSplitSheetProposals,
    }

    private void OnOptionSelectedHandler(object args)
    {
        var value = args as SelectedOptionItem;
        ShoppingCartStateService.NotifyOptionItemChanged(value!);
    }

    private List<DropDownListItem<SelectedOptionItem>> GetOptionsList()
    {
        return !EditingItem.IsSaleArticle
            ? ShoppingCartStateService.OptionsList
            : ShoppingCartStateService.DisabledOptionsList;
    }
    
    private SelectedOptionItem GetOptionValue()
    {
        return !EditingItem.IsSaleArticle
            ? ShoppingCartStateService.SelectedOptionItem
            : ShoppingCartStateService.DisabledOptionItem;
    }
    
    private void SetOptionItem(SelectedOptionItem value)
    {
        if (EditingItem.IsSaleArticle)
            return;
        ShoppingCartStateService.SelectedOptionItem = value;
        OnOptionSelectedHandler(value);
    }


}
