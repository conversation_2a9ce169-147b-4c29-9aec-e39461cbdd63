@using EMessa.Core.Features.Articles.Queries.GetArticleWarehouseStateForCustomer
@using EMessa.Core.Features.Articles.Queries.GetArticleWarehouseStateForEmployee
@using EMessa.Core.Features.ShoppingCart.Models
@using EMessa.Core.Features.ShoppingCart.Queries.GetAddNewItemModel
@using EMessa.Web.Components.RadzenComponents.Badges
@using Radzen
@using Radzen.Blazor

<RadzenStack Orientation="Orientation.Vertical" Gap="0.5rem" Class="w-full p-2 border-1">

    <ProgressBar IsLoading="@_isLoading" Visible="@_isLoading" />

    <RadzenText Text="@("Edycja pozycji zamówienia".Tr())" TextStyle="TextStyle.Body2" />
    <RadzenText Text="@Article.TranslatedName" TextStyle="TextStyle.Subtitle2" />
    <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="0.5rem">
        <RadzenText Text="@("Ilość".Tr())" />
        <RadzenNumeric TValue="decimal"
                       Style="width: 80px;"
                       @bind-Value="@EditingItem.Quantity"
                       Min="Decimal.Zero"
                       ShowUpDown="true" />
        <RadzenText Text="@EditingItem.ArticleUnit" />
    </RadzenStack>
    @if (_warehouseStateForCustomerResponse is not null)
    {
        <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="0.5rem">
            <RadzenText Text="@("Stan magazynowy: ".Tr())" class="m-0" />
            <ArticleQuantityLevelBadge Status="@_warehouseStateForCustomerResponse.WarehouseQuantity" />
        </RadzenStack>
    }
    @if (_warehouseStateForEmployeeResponse is not null)
    {
        <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="0.5rem">
            <RadzenText Text="@("Stan magazynowy (główny): ".Tr())" class="m-0" />
            <ArticleQuantityLevelBadge Status="@_warehouseStateForEmployeeResponse.MainWarehouseQuantity" />
        </RadzenStack>
        <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" Gap="0.5rem" class="mt-1">
            <RadzenText Text="@("Stan magazynowy (oddziału): ".Tr())" class="m-0" />
            <ArticleQuantityLevelBadge Status="@_warehouseStateForEmployeeResponse.BranchWarehouseQuantity" />
        </RadzenStack>
    }
    <GridToolbar Items="@([GridToolbar.Add, GridToolbar.Cancel])"
                 DisabledAdd="@BtnSaveDisable"
                 OnAddClick="@ConfirmAddArticleHandler"
                 OnCancelClick="@CancelAddArticleHandler">
        <RadzenButton ButtonStyle="ButtonStyle.Light"
                      Icon="warehouse"
                      Text="@("Sprawdź stan magazynowy".Tr())"
                      Click="@CheckWarehouseStock"
                      Disabled=@_isLoading />
    </GridToolbar>
</RadzenStack>

@code {

    [Parameter] public required ShoppingCartItemEditModel EditingItem { get; set; }
    [Parameter] public EventCallback<ShoppingCartItemEditModel> OnSave { get; set; }
    [Parameter] public EventCallback OnCancel { get; set; }
    // [Parameter] public GetArticleResponse ArticleDefinition { get; set; } = new();

    private bool _isLoading;
    private GetArticleWarehouseStateForCustomerResponse? _warehouseStateForCustomerResponse;
    private GetArticleWarehouseStateForEmployeeResponse? _warehouseStateForEmployeeResponse;

    private OrderItemEditArticleModel Article => EditingItem.Article;

    private bool BtnSaveDisable => EditingItem.Quantity <= 0;

    private async Task ConfirmAddArticleHandler()
    {
        if (EditingItem.Quantity <= 0)
        {
            ToastService.ShowError("Ilość musi być większa od zera".Tr());
            return;
        }

        await SaveArticle();
    }

    private async Task SaveArticle()
    {
        await OnSave.InvokeAsync(EditingItem);
    }

    private async Task CancelAddArticleHandler()
    {
        await OnCancel.InvokeAsync();
    }

    private async Task CheckWarehouseStock()
    {
        if (EditingItem.ArticleType == Base.Enums.ArticleType.Trade)
        {
            _isLoading = true;
            if (AppStateService.UserData.HasAnyRole(Role.Administrator, Role.Trade, Role.TradeManager, Role.Production))
            {
                var response = await Mediator.Send(new GetArticleWarehouseStateForEmployeeQuery(EditingItem.NewIndex, AppStateService.UserData.BranchIds.ToArray()));

                if (response.Succeeded)
                {
                    _warehouseStateForEmployeeResponse = response.Data;
                    StateHasChanged();
                }
                else
                {
                    ToastService.ShowError("Nie udało się pobrać stanu magazynowego".Tr());
                }
            }
            else
            {
                var response = await Mediator.Send(new GetArticleWarehouseStateForCustomerQuery(EditingItem.NewIndex));

                if (response.Succeeded)
                {
                    _warehouseStateForCustomerResponse = response.Data;
                    StateHasChanged();
                }
                else
                {
                    ToastService.ShowError("Nie udało się pobrać stanu magazynowego".Tr());
                }
            }
            _isLoading = false;
        }
    }
}
