@using EMessa.Core.Features.OrderItems.Commands.RemoveHashFile
@using EMessa.Core.Features.OrderItems.Exceptions
@using EMessa.Core.Features.ShoppingCart.Models
@using EMessa.Core.Features.ShoppingCart.Queries.GetAddNewItemModel
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor

@inject ILogger<ArticleOptionsEditorsComponent> Logger
@inject IMediator Mediator
@inject IEmessaTooltipService EmessaTooltipService
@inject DialogService DialogService

<div class="f-col-2 @CssClass">
    <h1 class="font-bold">@("Szkic obróbki".Tr())</h1>
    @* NOTE Klient musi narysować 25-02-2025 *@
    @* Draft *@
    @if (IsUploading)
    {
        <RadzenProgressBarCircular ShowValue="false"
                                   Value="100"
                                   Size="ProgressBarCircularSize.Small" />
    }
    @if (AddProfile && !IsUploading)
    {
        <div class="flex flex-wrap gap-2">
            <RadzenRow class="w-full sm:w-auto flex justify-start items-center gap-2"
                       MouseEnter="@(args => EmessaTooltipService.Open(args, "Szkic obróbki jest wymagany".Tr()))"
                       MouseLeave="@(_ => EmessaTooltipService.Close())">
                <SpanIcon Icon="warning"
                          Color="#f59e0b"
                          Class="text-[20px] material-symbols-outlined" />
                <div class="text-sm sm:text-base text-break">@("Dodaj szkic obróbki.".Tr())</div>
            </RadzenRow>
        </div>
    }
    <div
        class="flex flex-row items-center gap-2 p-2.5 border-1 border-dashed border-gray-400 rounded-md">
        <RadzenButton Click="@OpenDraftEditorHandle"
                      ButtonStyle="ButtonStyle.Light"
                      Class="flex flex-row items-center px-2.5 py-0.5 h-8 border-1 border-gray-300">
            <SpanIcon Icon="draw" Class="material-symbols-outlined text-xl p-0 pr-1" />
            @(DraftEditable ? "Edytuj szkic".Tr() : "Rysuj szkic".Tr())
        </RadzenButton>
        @if (DraftEditable)
        {
            <RadzenButton Click="@RemoveHashedFileHandle"
                          ButtonStyle="ButtonStyle.Light"
                          Class="flex flex-row items-center px-2.5 py-0.5 h-8 border-1 border-gray-300">
                <SpanIcon Icon="delete" Class="material-symbols-outlined text-xl p-0 pr-1" />
                @("Usuń szkic".Tr())
            </RadzenButton>
        }
    </div>
    @* Image preview *@
    @if (HasHashedFileName)
    {
        <div class="relative size-16 hover:w-full">
            <img src="api/OrderItemImage/Get/@HashedFileName"
                 alt=@("Szkic produktu".Tr())
                 class="size-16 cursor-pointer object-cover hover:w-full hover:h-auto hover:object-contain absolute bottom-0 transition-all duration-100"
                 @onclick="@ShowInlineDialog">
        </div>
    }
</div>

@code {
    // TODO co z autoryzacjami?

    [Parameter]
    public required BaseOrderItemEditModel EditItem { get; set; }

    [Parameter]
    public string CssClass { get; set; } = "";

    [Parameter]
    public EventCallback OpenDraftEditor { get; set; }

    private bool AddProfile => EditItem.RequireProfile && string.IsNullOrEmpty(EditItem.DraftDrawingSource);
    private bool DraftEditable => EditItem.DraftEditable && !string.IsNullOrEmpty(EditItem.DraftHashedFileName);
    private string HashedFileName => EditItem.DraftHashedFileName ?? "";
    private string OriginalFileName => EditItem.DraftOriginalFileName ?? "";
    private bool HasHashedFileName => !string.IsNullOrEmpty(EditItem.DraftHashedFileName);
    private bool IsUploading { get; set; }

    private async Task ShowInlineDialog()
    {
        await DialogService.OpenAsync(OriginalFileName, ds =>
                @<RadzenImage Path=@($"api/OrderItemImage/Get/{HashedFileName}")
                              AlternateText=@("Szkic produktu".Tr())
                              Click="() => ds.Close()"
                              class="max-w-full max-h-full object-contain cursor-pointer" />,
            new DialogOptions { CloseDialogOnOverlayClick = true, });
    }

    private async Task OpenDraftEditorHandle()
    {
        await OpenDraftEditor.InvokeAsync();
    }

    private async Task RemoveHashedFileHandle()
    {
        if (!EditItem.DraftEditable || string.IsNullOrEmpty(EditItem.DraftHashedFileName))
            return;

        try
        {
            IsUploading = true;

            var result = await Mediator.Send(new RemoveHashFileCommand
            {
                HashFileName = EditItem.DraftHashedFileName
            });

            if (result.Succeeded)
            {
                EditItem.DraftEditable = false;
                EditItem.DraftHashedFileName = null;
                EditItem.DraftOriginalFileName = null;
                EditItem.DraftDrawingSource = null;
            }
            else
            {
                throw new RemoveFileHashException(result.Messages);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during removing file");
            ToastService.ShowError("Błąd podczas usuwania pliku".Tr());
        }
        finally
        {
            IsUploading = false;
        }
    }

}