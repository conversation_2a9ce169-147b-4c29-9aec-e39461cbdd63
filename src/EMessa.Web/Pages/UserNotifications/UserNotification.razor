@attribute [Authorize]
@page "/usermessages"

@using EMessa.Core.Features.Notifications.Queries.NextUserNotification
@using EMessa.Core.Features.Notifications.Queries.UserNotifications
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor
@using EMessa.Core.Features.Notifications.Commands.MarkAsRead

@inject ILogger<UserNotification> Logger
@inject NavigationManager NavigationManager

<NavigationLock OnBeforeInternalNavigation="PreventNavigationIfUnreadNotification" />

<IndexCard Title=@_notification.Title>
    <RadzenTemplateForm TItem="NextUserNotificationResponse"
                        Data="@_notification"
                        Submit="@SubmitAsync">

        <RadzenRow AlignItems="AlignItems.Start" RowGap="1.5rem" Gap="1rem" class="p-3">

            <RadzenHtmlEditor Value="@_notification.Message"
                              ShowToolbar="false"
                              Disabled
                              class="w-full min-h-[100px]" />

        </RadzenRow>
        <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" class="rz-m-4">
            <RadzenButton ButtonType="ButtonType.Submit" Text="@("Zapoznałem się".Tr())" Icon="check_circle" ButtonStyle="ButtonStyle.Secondary" />
        </RadzenStack>
    </RadzenTemplateForm>
</IndexCard>

@code {
    private NextUserNotificationResponse _notification = new();
    private bool _hasUnreadNotification = true;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            var userName = (await AuthenticationStateProvider.GetAuthenticationStateAsync()).User.GetUserName();

            var nextUserNotification = await Mediator.Send(new NextUserNotificationQuery(userName));
            if (nextUserNotification is { Succeeded: true, Data.Id: > 0 })
            {
                _notification = nextUserNotification.Data;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, ex.Message);
            ToastService.Show(ToastType.LoadDataError);
            _hasUnreadNotification = false;
        }
    }

    private async Task SubmitAsync()
    {
        try
        {
            var userName = (await AuthenticationStateProvider.GetAuthenticationStateAsync()).User.GetUserName();

            await Mediator.Send(new MarkAsReadNotificationCommand(_notification.Id, userName));

            var nextUserNotification = await Mediator.Send(new NextUserNotificationQuery(userName));
            if (nextUserNotification is { Succeeded: true, Data.Id: > 0 })
            {
                _notification = nextUserNotification.Data;
            }
            else
            {
                _hasUnreadNotification = false;
                ToastService.ClearAll();
                NavigationManager.NavigateTo("/");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, ex.Message);
            ToastService.Show(ToastType.SaveError);
            _hasUnreadNotification = false;
        }
    }

    private void PreventNavigationIfUnreadNotification(LocationChangingContext context)
    {
        var allowedPaths = new[] { "usermessages", "culture/setculture", "account/logout" };

        if (allowedPaths.Any(x => context.TargetLocation.Contains(x)))
            return;

        if (_hasUnreadNotification)
        {
            context.PreventNavigation();
            ToastService.ShowWarning("Proszę potwierdzić zapoznanie się z komunikatem, aby przejść dalej.".Tr());
        }
    }
}