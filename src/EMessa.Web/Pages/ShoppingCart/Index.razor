@page "/shoppingcart"
@attribute [Authorize]

@using EMessa.Base.Enums
@using EMessa.Core.Features.ShoppingCart.Commands.DeleteAllItems
@using EMessa.Core.Features.ShoppingCart.Commands.DeleteItem
@using EMessa.Core.Features.ShoppingCart.Queries.GetShoppingCartItems
@using EMessa.Web.Interfaces
@using EMessa.Web.Pages.ShoppingCart.Parts
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor

@inject IMediator Mediator
@inject DialogService DialogService
@inject ICheckoutStateService CheckoutStateService
@implements IDisposable

<IndexCard Title=@("Zawartość koszyka".Tr()) ChildClass="bg-white">
    <HeaderContent>
        <GridToolbar>
            <ChildContent>
                <AddRadzenButton Icon="check_circle" Text=@("Przejdź do zamówienia".Tr())
                                 Disabled="@(_orderItemIds.Count == 0)"
                                 Click="@GoToCheckout" />
                <DeleteRadzenButton Text=@("Wyczyść".Tr()) Click="@ClearShoppingCart" />
            </ChildContent>
        </GridToolbar>
    </HeaderContent>
    <ChildContent>
        @if (_cartOrderItems.Count == 0)
        {
            <div class="m-1">
                <RadzenText Text="@("Koszyk jest pusty".Tr())"
                            TextStyle="TextStyle.Subtitle2" />
            </div>
        }
        <div class="w-full p-2 f-col-2">
            @foreach (var orderItem in _cartOrderItems)
            {
                switch (orderItem.ArticleType)
                {
                    case ArticleType.Complex:
                        <ViewComplexOrderItem CartOrderOrderItem="@orderItem"
                                              CartOrderItemChanged="@LoadCarts"
                                              DeleteArticleItem="@DeleteArticleItem" />
                        break;
                    case ArticleType.Trade:
                        <ViewTradeOrderItem CartOrderItem="@orderItem"
                                            CartItemChanged="@LoadCarts"
                                            DeleteArticleItem="@DeleteArticleItem" />
                        break;
                    case ArticleType.Service:
                    default:
                        <div>TODO ArticleType</div>
                        // TODO
                        break;
                }
            }
        </div>
    </ChildContent>
</IndexCard>

@code {
    private List<GetShoppingCartOrderItemResponse> _cartOrderItems = [];
    private List<int> _orderItemIds = [];

    #region Initialize, Dispoze

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        AppStateService.StateChanged += OnStateChanged;
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source == this) return;

        switch (property)
        {
            case AppStateNotifyProperties.ScreenSizeChanged:
                _ = InvokeAsync(StateHasChanged);
                break;
            case AppStateNotifyProperties.ShoppingCartChanged:
                _ = LoadCarts();
                break;
        }
    }

    void IDisposable.Dispose()
    {
        AppStateService.StateChanged -= OnStateChanged;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);
        if (firstRender) 
            await LoadCarts();
    }

    #endregion

    private async Task LoadCarts()
    {
        var cartItems = await Mediator.Send(new GetShoppingCartOrderItemsQuery(AppStateService.UserData.UserProfileId));
        if (cartItems.Succeeded)
        {
            _cartOrderItems = cartItems.Data;
            _orderItemIds = _cartOrderItems.Where(x => x.SelectedForOrder).Select(x => x.Id).ToList();
            StateHasChanged();
        }

        await InvokeAsync(StateHasChanged);
    }

    private async Task DeleteArticleItem(int cartItemId)
    {
        var confirmed = await DialogService.Confirm(
            "Czy na pewno chcesz usunąć ten artykuł z koszyka?".Tr(),
            "Potwierdzenie usunięcia".Tr(),
            new ConfirmOptions { OkButtonText = "Tak".Tr(), CancelButtonText = "Nie".Tr() });

        if (confirmed == true)
        {
            var response = await Mediator.Send(new DeleteOrderItemFromShoppingCartCommand(cartItemId));

            if (response.Succeeded)
            {
                ToastService.Show(ToastType.DeleteSuccess);
                StateHasChanged();
            }
            else
            {
                ToastService.Show(ToastType.DeleteError, response.Messages);
            }
        }
    }

    private async Task ClearShoppingCart()
    {
        await Mediator.Send(new DeleteAllItemsFromShoppingCartCommand());
        await LoadCarts();
    }

    private void GoToCheckout()
    {
        if (_orderItemIds.Count == 0)
            return;
        CheckoutStateService.StartCheckout(_orderItemIds);
    }

}