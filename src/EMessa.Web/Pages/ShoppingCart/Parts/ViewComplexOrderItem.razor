@using EMessa.Core.Features.OrderItems.Queries.GetOrderItemQuery
@using EMessa.Core.Features.ShoppingCart.Queries.GetShoppingCartItem
@using EMessa.Core.Features.ShoppingCart.Queries.GetShoppingCartItems
@using EMessa.Core.Features.Sales.Queries.CommonResponses
@using EMessa.Core.Features.Sales.Queries.GetSaleArticlesByArticleId
@using EMessa.Core.Features.Sales.Queries.GetSaleArticlesByArticleIdSaleId
@using EMessa.Core.Features.ShoppingCart.Commands.UpdateOrderItem
@using EMessa.Core.Features.ShoppingCart.Commands.UpdateSelectedForOrder
@using EMessa.Core.Features.ShoppingCart.Models
@using EMessa.Core.Features.ShoppingCart.Queries.GetAddNewItemModel
@using Ra<PERSON>zen
@using Radzen.Blazor

@inject IMediator Mediator
@* @inject ILogger<ViewComplexOrderItem> Logger *@

<style>
    .rz-card {
        padding: 0.25rem;
    }

    .rz-stack .rz-text-subtitle1,
    .rz-stack .rz-text-body2 {
        margin-block-end: 0;
    }
</style>

<RadzenCard class="bg-gray-100">
    @* View *@
    <CollapseView Id="@($"view{CartOrderOrderItem.Id}")" Collapse="@IsEditingOrderItem">
        <RadzenStack Orientation="@(AppStateService.IsLargeScreen ? Orientation.Horizontal : Orientation.Vertical)"
                     JustifyContent="JustifyContent.SpaceBetween"
                     Gap="0.5rem" class="">
            <RadzenStack Orientation="Orientation.Vertical"
                         JustifyContent="JustifyContent.Start"
                         AlignItems="AlignItems.Start"
                         Wrap="FlexWrap.Wrap"
                         Gap="0.5rem"
                         Class="h-full p-2">
                <RadzenCheckBox TValue="bool"
                                Value="@CartOrderOrderItem.SelectedForOrder"
                                ValueChanged="@OnSelectedForOrderChanged" />
            </RadzenStack>

            <RadzenPanel Class="text-black flex-1 bg-transparent">
                <RadzenStack Gap="0">
                    <RadzenText TextStyle="TextStyle.Subtitle1" Class="font-bold">
                        @CartOrderOrderItem.ArticleName
                    </RadzenText>
                    <RadzenText TextStyle="TextStyle.Body2">
                        @("Ilość".Tr()): <strong>@CartOrderOrderItem.QuantityUnitString</strong>
                    </RadzenText>
                    <RadzenText TextStyle="TextStyle.Body2">
                        @("Waga".Tr()): <strong>@CartOrderOrderItem.WeightUnitString</strong>
                    </RadzenText>

                    <RadzenStack Orientation="Orientation.Horizontal" Gap="0.5rem" Class="mt-2">
                        <EditRadzenButton Text="@null" Size="ButtonSize.Medium" Click="@Edit" />
                        <DeleteRadzenButton Text="@null" Size="ButtonSize.Medium"
                                            Click="@(_ => DeleteArticleItem.InvokeAsync(CartOrderOrderItem.Id))" />
                        <InfoRadzenButton Size="ButtonSize.Medium" Click="@SelectedInfoHandle" />
                        <SaleRadzenButton Size="ButtonSize.Medium" Click="@SelectedPriceHandle" />
                    </RadzenStack>
                </RadzenStack>
            </RadzenPanel>

            <RadzenPanel Class="flex-1 bg-transparent">
                <details>
                    <summary>
                        <RadzenText TextStyle="TextStyle.Body1" TagName="TagName.Span">
                            @(CartOrderOrderItem.OptionValues.Any() ? "Opcje produktu".Tr() : "Brak opcji produktu".Tr())
                        </RadzenText>
                        @foreach (var option in CartOrderOrderItem.OptionValues.Take(3).ToList())
                        {
                            <RadzenText TextStyle="TextStyle.Body2">
                                @option.OptionName: <strong>@(option.Value ?? "brak wartości".Tr())</strong>
                            </RadzenText>
                        }
                    </summary>
                    @foreach (var option in CartOrderOrderItem.OptionValues.Skip(3).ToList())
                    {
                        <RadzenText TextStyle="TextStyle.Body2">
                            @option.OptionName: <strong>@(option.Value ?? "brak wartości".Tr())</strong>
                        </RadzenText>
                    }
                </details>
            </RadzenPanel>

            <RadzenPanel Class="flex-1 bg-transparent">
                <details>
                    <summary>
                        <RadzenText TextStyle="TextStyle.Body1" TagName="TagName.Span">
                            @(CartOrderOrderItem.RequestedSheets.Any() ? "Wymiary arkuszy".Tr() : "Brak wymiarów arkuszy".Tr())
                        </RadzenText>
                        @foreach (var sheet in CartOrderOrderItem.OrderItemSheetsCache.OrderByDescending(x => x.Length).Take(3).ToList())
                        {
                            <RadzenText TextStyle="TextStyle.Body2">
                                <strong>@sheet.Length.ToString("n3") x @sheet.Width.ToString("n3") - @sheet.Quantity @("szt.".Tr())</strong>
                            </RadzenText>
                        }
                    </summary>
                    @foreach (var sheet in CartOrderOrderItem.OrderItemSheetsCache.OrderByDescending(x => x.Length).Skip(3).ToList())
                    {
                        <RadzenText TextStyle="TextStyle.Body2">
                            <strong>@sheet.Length.ToString("n3") x @sheet.Width.ToString("n3") - @sheet.Quantity @("szt.".Tr())</strong>
                        </RadzenText>
                    }
                </details>
            </RadzenPanel>
        </RadzenStack>
    </CollapseView>

    @* Edit *@
    <CollapseView Id="@($"edit{CartOrderOrderItem.Id}")" Collapse="@(!IsEditingOrderItem)" CssClass="min-h-[300px] p-2">
        @if (_editingOrderItem != null && _newRequestedSheet != null)
        {
            <EditComplexOrderItem EditingItem="@_editingOrderItem"
                                  NewRequestedSheet="@_newRequestedSheet"
                                  OnSave="@SaveEdit"
                                  OnCancel="@CancelEdit" />
        }
    </CollapseView>
</RadzenCard>

@code {

    [Parameter] public required GetShoppingCartOrderItemResponse CartOrderOrderItem { get; set; }
    [Parameter] public EventCallback<GetShoppingCartOrderItemResponse> CartOrderItemChanged { get; set; }
    [Parameter] public EventCallback<int> DeleteArticleItem { get; set; }

    private ShoppingCartItemEditModel? _editingOrderItem;
    private RequestedSheetEditModel? _newRequestedSheet;
    private bool IsLoading { get; set; }
    private bool IsEditingOrderItem { get; set; }

    #region Buttons

    private async Task OnSelectedForOrderChanged(bool value)
    {
        IsLoading = true;
        var response = await Mediator.Send(new UpdateSelectedForOrderCommand(CartOrderOrderItem.Id, value));

        if (response.Succeeded)
        {
            CartOrderOrderItem.SelectedForOrder = value;
            IsLoading = false;
            return;
        }

        ToastService.Show(ToastType.DeleteError, response.Messages);
    }

    private async Task<ShoppingCartItemEditModel?> LoadEditingItem()
    {
        var response = await Mediator.Send(new GetShoppingCartItemEditModelByOrderItemIdQuery(CartOrderOrderItem.Id));
        if (response.Succeeded)
        {
            return response.Data;
        }

        ToastService.Show(ToastType.LoadDataError, response.Messages);
        return null;
    }

    private async Task<SaleArticleResponse?> LoadSaleArticle(int articleId, int saleId)
    {
        var response = await Mediator.Send(new GetSaleArticlesByArticleIdSaleIdQuery(articleId, saleId));
        if (response.Succeeded)
        {
            return response.Data;
        }

        ToastService.Show(ToastType.LoadDataError, response.Messages);
        return null;
    }

    private async Task Edit()
    {
        IsLoading = true;

        _editingOrderItem = await LoadEditingItem();
        if (_editingOrderItem != null)
        {
            if (CartOrderOrderItem.SaleId.HasValue)
            {
                var saleArticle = await LoadSaleArticle(CartOrderOrderItem.ArticleId, CartOrderOrderItem.SaleId.Value);
                if (saleArticle == null)
                {
                    _editingOrderItem = null;
                    IsLoading = false;
                    return;
                }

                _editingOrderItem.IsSaleArticle = true;
                // Sale
                _editingOrderItem.SaleArticle = saleArticle;
                _editingOrderItem.SaleAvailableWeight = saleArticle.AvailableWeight;
                _editingOrderItem.SaleAvailableLength = SaleWeightCalculatorBase.CalculateAvailableLength(
                    saleArticle.AvailableWeight,
                    saleArticle.Efficiency,
                    saleArticle.Rolls);
                // Article
                _editingOrderItem.Article.HasAvailableSales = true;
                _editingOrderItem.Article.IsActive = true;
                _editingOrderItem.Article.IsDeleted = false;
                if (saleArticle.Width > 0)
                {
                    _editingOrderItem.Article.DefaultWidth = saleArticle.Width;
                }
            }

            PresetNewRequestedSheet(_editingOrderItem.Article);
            IsEditingOrderItem = true;
        }
        else
        {
            _newRequestedSheet = null;
        }

        IsLoading = false;
    }

    private void PresetNewRequestedSheet(OrderItemEditArticleModel article)
    {
        _newRequestedSheet = new RequestedSheetEditModel
        {
            Quantity = article.DefaultQuantity,
            Length = article.LengthEditable ? 0m : article.DefaultLength,
            Width = article.WidthEditable ? 0m : article.DefaultWidth
        };
    }

    private void SelectedInfoHandle()
    {
        //TODO
    }

    private void SelectedPriceHandle()
    {
        //TODO
    }

    #endregion

    private async Task<GetShoppingCartOrderItemResponse> LoadCartOrderItem()
    {
        var result = await Mediator.Send(new GetShoppingCartOrderItemQuery(CartOrderOrderItem.Id));
        if (result.Succeeded)
        {
            return result.Data;
        }

        ToastService.Show(ToastType.LoadDataError, result.Messages);
        return null;
    }

    private async Task UpdateCartItem(ShoppingCartItemEditModel? editingItem)
    {
        if (editingItem is null)
        {
            ToastService.ShowError("Nie można zaktualizować pustego artykułu.".Tr());
            return;
        }

        var result = await Mediator.Send(new UpdateShoppingCartOrderItemCommand(CartOrderOrderItem.Id, editingItem));
        if (result.Succeeded)
        {
            ToastService.Show(ToastType.UpdateSuccess);
        }
        else
        {
            ToastService.Show(ToastType.UpdateError, result.Messages.String());
        }
    }

    private async Task SaveEdit()
    {
        await UpdateCartItem(_editingOrderItem);
        var cartOrderOrderItem = await LoadCartOrderItem();
        await CartOrderItemChanged.InvokeAsync(cartOrderOrderItem);
        IsEditingOrderItem = false;
        _editingOrderItem = null;
    }

    private void CancelEdit()
    {
        IsEditingOrderItem = false;
        _editingOrderItem = null;
        _newRequestedSheet = null;
    }

}