@using EMessa.Core.Features.ShoppingCart.Commands.UpdateQuantity
@using EMessa.Core.Features.ShoppingCart.Commands.UpdateSelectedForOrder
@using EMessa.Core.Features.ShoppingCart.Queries.GetShoppingCartItems
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor

@inject IMediator Mediator
@inject ILogger<ViewTradeOrderItem> Logger

<style>
    .rz-card {
        padding: 0.25rem;
    }

    .rz-stack .rz-text-subtitle1,
    .rz-stack .rz-text-body2 {
        margin-block-end: 0;
    }
</style>

<RadzenCard class="bg-gray-100">
    <RadzenStack Orientation="@(AppStateService.IsLargeScreen ? Orientation.Horizontal : Orientation.Vertical)"
                 JustifyContent="JustifyContent.SpaceBetween"
                 Gap="0.5rem" class="">
        <RadzenStack Orientation="Orientation.Vertical"
                     JustifyContent="JustifyContent.Start"
                     AlignItems="AlignItems.Start"
                     Wrap="FlexWrap.Wrap"
                     Gap="0.5rem"
                     Class="h-full p-2">
            <RadzenCheckBox TValue="bool"
                            Value="@CartOrderItem.SelectedForOrder"
                            ValueChanged="@OnSelectedForOrderChanged" />
        </RadzenStack>

        <RadzenPanel Class="text-black flex-1 bg-transparent">
            <RadzenStack Gap="0">
                <RadzenText TextStyle="TextStyle.Subtitle1" Class="font-bold">
                    @CartOrderItem.ArticleName
                </RadzenText>
                <RadzenText TextStyle="TextStyle.Body2">
                    @("Ilość".Tr()): <strong>@CartOrderItem.QuantityUnitString</strong>
                </RadzenText>

                <RadzenStack Orientation="Orientation.Horizontal" Gap="0.5rem" Class="mt-2">
                    <DeleteRadzenButton Text="@null" Size="ButtonSize.Medium"
                                        Click="@(_ => DeleteArticleItem.InvokeAsync(CartOrderItem.Id))" />
                    <RadzenButton Click="@SelectedInfoHandle"
                                  ButtonStyle="ButtonStyle.Primary"
                                  Size="ButtonSize.Medium"
                                  Icon="info"
                                  Class="" />
                    <RadzenButton Click="@SelectedPriceHandle"
                                  ButtonStyle="ButtonStyle.Dark"
                                  Size="ButtonSize.Medium"
                                  Icon="attach_money"
                                  Class="bg-esa-add" />
                </RadzenStack>
            </RadzenStack>
        </RadzenPanel>

        <RadzenPanel Class="text-black flex-1 bg-transparent">
            <RadzenStack Orientation="Orientation.Horizontal"
                         JustifyContent="JustifyContent.Center"
                         AlignItems="AlignItems.Center"
                         Wrap="FlexWrap.Wrap"
                         Gap="0.5rem">
                    <RadzenText Text="@("Ilość".Tr())" />
                    <RadzenNumeric TValue="decimal"
                                   Style="width: 80px;"
                                   @bind-Value="@CartOrderItem.Quantity"
                                   Min="@decimal.Zero"
                                   ShowUpDown="false" />
                    <RadzenText Text="@CartOrderItem.UnitName" />
                <SaveRadzenButton Click="@UpdateArticleItemQuantity" />
                </RadzenStack>
        </RadzenPanel>
    </RadzenStack>
</RadzenCard>

@code {
    [Parameter]
    public GetShoppingCartOrderItemResponse CartOrderItem { get; set; } = new();

    [Parameter]
    public EventCallback<GetShoppingCartOrderItemResponse> CartItemChanged { get; set; }

    [Parameter]
    public EventCallback<int> DeleteArticleItem { get; set; }

    private bool IsLoading { get; set; }

    #region Buttons

    private async Task OnSelectedForOrderChanged(bool value)
    {
        try
        {
            IsLoading = true;
            var response = await Mediator.Send(new UpdateSelectedForOrderCommand(CartOrderItem.Id, value));

            if (response.Succeeded)
            {
                CartOrderItem.SelectedForOrder = value;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, $"Błąd podczas zmiany statusu zamówienia ID: {CartOrderItem.Id}");
            ToastService.Show(ToastType.DeleteError, "Błąd podczas zmiany statusu artykułu.".Tr());
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task UpdateArticleItemQuantity()
    {
        try
        {
            IsLoading = true;
            var result = await Mediator.Send(new UpdateShoppingCartItemQuantityCommand(CartOrderItem.Id, CartOrderItem.Quantity));
            if (result.Succeeded)
            {
                ToastService.Show(ToastType.UpdateSuccess, "Zaktualizowano artykuł w koszyku.".Tr());
                await CartItemChanged.InvokeAsync(CartOrderItem);
            }
            else
            {
                ToastService.Show(ToastType.UpdateError, result.Messages);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, $"Błąd podczas aktualizacji artykułu ID: {CartOrderItem.Id}");
            ToastService.Show(ToastType.UpdateError);
        }
        finally
        {
            IsLoading = false;
            StateHasChanged();
        }
    }

    private void SelectedInfoHandle()
    {
        //TODO
    }

    private void SelectedPriceHandle()
    {
        //TODO
    }

    #endregion
}
