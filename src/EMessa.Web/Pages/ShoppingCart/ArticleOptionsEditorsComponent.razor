@using EMessa.Core.Features.OrderItems.Commands.UpdateOrderItemOptionValue
@using EMessa.Core.Features.OrderItems.Queries.GetOrderItemOptionsForEdit
@using Syncfusion.Blazor.DropDowns
@inject ILogger<ArticleOptionsEditorsComponent> _logger
@inject IMediator _mediator

<style>
    .e-listbox-container:not(.e-list-template) .e-list-item {
        padding: 5px 10px 5px 10px;
        height: 28px;
    }

    /* .e-ddl.e-input-group.e-control-wrapper {
        font-size: 20px;
        font-family: emoji;
        color: #ab3243;
        background: #32a5ab;
    } */

    .e-listbox-container .e-list-item.e-selected {
        background-color: #8f8;
    }
</style>

<div>
    @foreach (var itemOption in OptionEditing)
    {
        <div class="flex flex-col w-full  lg:w-12/12">
            <span>@itemOption.Name</span>
            @* != null ? itemOption.OptionValueId : (itemOption.Values.Count == 1 ? itemOption.Values.First().Id : null))" *@
            <SfDropDownList DataSource="@itemOption.Values" CssClass="@(itemOption.OptionValueId is null ? "e-warning" : "" )"
                            Value="@(itemOption.OptionValueId)"
                            ValueChanged=@((e) => OptionValueChanged(itemOption, e)) 
                TValue="int?" TItem="GetOrderItemOptionValuesEditResponse" Width="100%" PopupHeight="200px">
                <DropDownListFieldSettings Text="Value" Value="Id"></DropDownListFieldSettings>
            </SfDropDownList>
        </div>
    }
</div>

@code {
    [CascadingParameter]
    public AppToast _toast { get; set; } = new();
    [Parameter]
    public int ArticleId { get; set; }
    [Parameter]
    public int? OrderItemId { get; set; }
    private List<GetOrderItemOptionsEditResponse> OptionEditing = new();

    protected override async Task OnInitializedAsync()
    {
        _logger.LogInformation("OnInitializedAsync");
        await LoadOptionsForEdit();
        await base.OnInitializedAsync();
    }

    protected override void OnParametersSet()
    {
        base.OnParametersSet();
    }


    private async Task LoadOptionsForEdit()
    {
        var optionsResult = await _mediator.Send(new GetOrderItemOptionsEditQuery(OrderItemId ?? 0));
        if (optionsResult.Succeeded)
        {
            OptionEditing = optionsResult.Data;
        }
        else
        {
            await _toast.ShowError(optionsResult.Messages.String());
        }
    }

    

    protected async Task OptionValueChanged(GetOrderItemOptionsEditResponse orderItemOptionValue, int? optionValueId)
    {
        if (optionValueId is null || optionValueId.Value <= 0) return;
        _logger.LogInformation("OptionValueChanged: {0} {1}", orderItemOptionValue.Id, optionValueId);
        var result = await _mediator.Send(new UpdateOrderItemOptionValueCommand(optionValueId ?? 0, orderItemOptionValue.Id));
        if (result.Succeeded)
        {
            orderItemOptionValue.OptionValueId = optionValueId;
        }
        else
        {
            await _toast.ShowError(result.Messages.String());
        }
    }
}
