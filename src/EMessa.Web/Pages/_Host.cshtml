@page "/"

@using Microsoft.AspNetCore.Components.Web
@using System.Globalization
@using EMessa.Core.Localizer

@namespace EMessa.Web.Pages
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

@{
    var currentCulture = CultureInfo.CurrentCulture.Name;
}

<!DOCTYPE html>
<html lang="@currentCulture">
<head>
    <title>eMessa</title>
    <base href="~/" />

    <meta charset="utf-8" />
    <meta name="google" content="notranslate" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <link rel="stylesheet" href="css/bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="css/tailwind.min.css" />
    <link rel="stylesheet" href="css/app.min.css" />

    <link rel="stylesheet" href="_content/Radzen.Blazor/css/standard-base.css">
    @* <link rel="stylesheet" href="_content/Radzen.Blazor/css/material-base.css"> *@
    
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/lipis/flag-icons@7.2.3/css/flag-icons.min.css" />

    <!--Google-->
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">

    <link rel="stylesheet" href="css/syncfusion-blazor-icons.css" />

    <link rel="icon" type="image/png" href="favicon.png" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..400,0..1,-50..200" />

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Jura:wght@300;400;700&display=swap">
    <style>
        .e-icons.material-symbols-outlined {
            font-variation-settings: 'FILL' 0, 'wght' 300, 'GRAD' 0, 'opsz' 48;
            font-size: 28px;
        }
    </style>

    <style>
        /* Definicje fontów */
        @@font-face {
            font-family: 'Montserrat';
            src: url('css/fonts/Montserrat/Montserrat-Regular.ttf') format('truetype');
            font-weight: 400;
            font-style: normal;
        }

        @@font-face {
            font-family: 'Montserrat';
            src: url('css/fonts/Montserrat/Montserrat-Bold.ttf') format('truetype');
            font-weight: 700;
            font-style: normal;
        }

        @@font-face {
            font-family: 'Montserrat';
            src: url('css/fonts/Montserrat/Montserrat-Light.ttf') format('truetype');
            font-weight: 300;
            font-style: normal;
        }

        @@font-face {
            font-family: 'Montserrat';
            src: url('css/fonts/Montserrat/Montserrat-Medium.ttf') format('truetype');
            font-weight: 500;
            font-style: normal;
        }

        @@font-face {
            font-family: 'Montserrat';
            src: url('css/fonts/Montserrat/Montserrat-SemiBold.ttf') format('truetype');
            font-weight: 600;
            font-style: normal;
        }

        @@font-face {
            font-family: 'Montserrat';
            src: url('css/fonts/Montserrat/Montserrat-ExtraBold.ttf') format('truetype');
            font-weight: 800;
            font-style: normal;
        }

        @@font-face {
            font-family: 'Montserrat';
            src: url('css/fonts/Montserrat/Montserrat-Italic.ttf') format('truetype');
            font-weight: 400;
            font-style: italic;
        }

        /* Zastosowanie fontu do całej strony */
        body {
            font-family: 'Montserrat', sans-serif;
        }
    </style>

    <component type="typeof(HeadOutlet)" render-mode="ServerPrerendered" />

    @* To powinno być w body wg Blazor *@
    <script type="text/javascript" src="_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js"></script>
    @* <script type="text/javascript" src="https://cdn.tailwindcss.com/3.4.16.js"></script> *@
    <script type="text/javascript" src="js/libs/cdn.tailwindcss.com/3.4.16.js"></script>
    <link rel="stylesheet" href="css/site.css" />
    <link rel="stylesheet" href="css/emessa.css" />
    <link rel="stylesheet" href="css/syncfusion-custom.css" />
    <link rel="stylesheet" href="css/radzen.css" asp-append-version="true" />
    <link rel="stylesheet" href="css/app.min.css" />

    @* window.EMessa , wspólne miejsce do podłaczania JavaScript *@
    <script type="module" src="js/emessa.js"></script>
</head>
<body onresize="EMessa.windowResized()" style="background-color: #fcfdff">

    <component type="typeof(App)" render-mode="ServerPrerendered" />

    <div id="blazor-error-ui" style="z-index: 9999;" >
        <environment include="Staging,Production">
            @("Wystąpił błąd. Aplikacja może nie odpowiadać, dopóki nie zostanie ponownie załadowana.".Tr())
        </environment>
        <environment include="Development">
            @("Wystąpił nieobsługiwany wyjątek. Zobacz narzędzia deweloperskie przeglądarki w celu uzyskania szczegółów.".Tr())
        </environment>
        <a href="" class="reload">Reload</a>
        <a class="dismiss">🗙</a>
    </div>

    <script type="module">
        // Ten kod wykona się po załadowaniu wszystkich modułów
        console.log("Moduły `window.EMessa` załadowane", window.EMessa);
    </script>

    <div id="components-reconnect-modal" class="custom-reconnect-modal components-reconnect-hide">
        <div class="show">
            <div class="components-reconnect-overlay">
                <div class="components-reconnect-box">
                    <p class="components-reconnect-paragraf">
                        @("Utracono połączenie z serwerem...".Tr())<br>
                        @("Próba połączenia".Tr()) <span id="components-reconnect-current-attempt"></span> / <span id="components-reconnect-max-retries"></span>
                    </p>
                </div>
            </div>
        </div>
        <div class="failed">
            <div class="components-reconnect-overlay">
                <div class="components-reconnect-box">
                    <p class="components-reconnect-paragraf mb-2">
                        @("Nie udało się ponownie połączyć z serwerem.".Tr())<br>
                        @("Proszę odświeżyć stronę aby przywrócić funkcjonalność.".Tr())
                    </p>
                    <button onclick="window.location.reload()"
                            class="components-reconnect-button">
                        @("Odśwież stronę".Tr())
                    </button>
                </div>
            </div>
        </div>
        <div class="rejected">
            <div class="components-reconnect-overlay">
                <div class="components-reconnect-box">
                    <p class="components-reconnect-paragraf mb-2">
                        @("Połączenie zostało odrzucone.".Tr())<br>
                        @("Proszę odświeżyć stronę, aby spróbować ponownie.".Tr())
                    </p>
                    <button onclick="window.location.reload()"
                            class="components-reconnect-button">
                        @("Odśwież stronę".Tr())
                    </button>
                </div>
            </div>
        </div>
    </div>
    <script src="_framework/blazor.server.js"></script>
    <script src="_content/Radzen.Blazor/Radzen.Blazor.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/tw-elements/dist/js/tw-elements.umd.min.js"></script>
</body>
</html>
