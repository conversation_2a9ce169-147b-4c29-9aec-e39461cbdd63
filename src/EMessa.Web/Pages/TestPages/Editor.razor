@page "/test-edytor"
@* http://localhost:5020/lista-zamowien *@

@inject IRandomGeneratorService RandomGeneratorService

@* Editor.razoe *@
<HeadContent>
    <script type="text/javascript" src="js/draftEditor/DraftEditor.js"></script>
</HeadContent>

<div class="w-full flex flex-col">
    <div class="mt-4 w-full transition-all duration-300 ease-in-out">
        <DraftEditor @ref="_draftEditor"
                     DraftName="@GenerateRandomFileName()"
                     DraftContent="@_orderItem.DraftContent"
                     DraftContentChanged="@OrderItemChangedHandler"
                     OnState="@State"
                     OnSave="@SaveDraft"/>
    </div>
</div>

@code {
    private DraftEditor _draftEditor = new();
    private readonly OrderItem _orderItem = new() { Id = 1, DraftContent = "" };

    private string _currentState = "";
    private string _imageUrl = "";

    private string GenerateRandomFileName()
    {
        var random = RandomGeneratorService
            .GenerateRandomString("abcdefghijklmnopqrstuvwxyz0123456789", 6);
        return Path.GetFileName($@"{"Szkic".Tr()}-{random}");
    }

    private void OrderItemChangedHandler(string content)
    {
        _orderItem.DraftContent = content;
    }

    private void State(string state)
    {
        _currentState = state;
    }

    private void SaveDraft(SaveRecord args)
    {
        Print.WriteInline(new { png = args.PngContent?.Length ?? 0, json = args.DraftContent?.Length ?? 0 });
    }

    public record OrderItem
    {
        public int Id { get; set; }
        public string DraftContent { get; set; } = string.Empty;
    }

}