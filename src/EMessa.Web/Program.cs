using EMessa.Base.Constants;
using EMessa.Core.Common;
using EMessa.Core.DataAdapters;
using EMessa.Core.Features.ArticleOptionValuesRestrictions.Services;
using EMessa.Core.Features.ArticleValidators.Queries.GetAllArticleValidatorCondition;
using EMessa.Core.Features.ArticleValidators.Queries.GetSelectedArticleValidator;
using EMessa.Core.Features.ArticleValidators.Validators;
using EMessa.Core.Features.OrderItems.Services;
using EMessa.Core.Features.Orders;
using EMessa.Core.Features.ShoppingCart.DataAdapters;
using EMessa.Core.Features.Users.Services;
using EMessa.Core.Interfaces;
using EMessa.Core.Localizer;
using EMessa.Core.Services;
using EMessa.Core.Services.Calculators;
using EMessa.Core.Services.FileStorage;
using EMessa.Core.Services.Orders;
using EMessa.Core.Services.Sieve;
using EMessa.DAL.Entities.Identity;
using EMessa.Services;
using EMessa.Services.Interfaces;
using EMessa.Web.Extensions;
using EMessa.Web.Interfaces;
using EMessa.Web.Services;
using EMessa.Web.Shared;
using EMessa.Web.StateServices;
using FluentValidation;
using MediatR.NotificationPublishers;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using Radzen;
using Radzen.Blazor;
using Sieve.Services;
using Syncfusion.Blazor;
using TranslationService;


var builder = WebApplication.CreateBuilder(args);

// API
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Components
builder.Services.AddSyncfusionBlazor();
builder.Services.AddRadzenComponents();

builder.Services.AddDataAccess(builder.Configuration);

//TODO Trzeba by te strony odświeżyć kopią z .net 8, te są z .net 6
builder.Services.AddRazorPages(options =>
{
    options.Conventions.AllowAnonymousToPage("/Account/Login");
    options.Conventions.AllowAnonymousToPage("/Account/ForgotPassword");
    options.Conventions.AllowAnonymousToPage("/Account/ResetPassword");
});

builder.Services.AddServerSideBlazor();
builder.Services.AddHttpClient();
builder.Services.AddRollDataHttpClient(builder.Configuration);

builder.Services.AddMessaApiClient(builder.Configuration);
builder.Services.AddOptimaApiClient(builder.Configuration);

// Scoped. Per request
builder.Services.AddScoped<IAppStateService, AppStateService>();
builder.Services.AddScoped<AuthenticationStateProvider, RevalidatingIdentityAuthenticationStateProvider<ApplicationUser>>();
builder.Services.AddScoped<IToastService, ToastService>();
builder.Services.AddScoped<IModalDialogService, ModalDialogService>();
builder.Services.AddScoped<IEmessaTooltipService, EmessaTooltipService>();
builder.Services.AddScoped<ArticleOptionValueDataAdaptor>();
builder.Services.AddScoped<OptionsDataAdaptor>();
builder.Services.AddScoped<OptionValuesDataAdaptor>();
builder.Services.AddScoped<CategoryFilterAttributeDataAdaptor>();
builder.Services.AddScoped<CategoryWithoutFilterAttributeDataAdaptor>();
builder.Services.AddScoped<FilterAttributeWithoutCategoryDataAdaptor>();
builder.Services.AddScoped<FilterAttributeCategoryDataAdaptor>();
builder.Services.AddScoped<FilterAttributeValueDataAdaptor>();
builder.Services.AddScoped<FilterAttributeDataAdaptor>();
builder.Services.AddScoped<CategoryDataAdaptor>();
builder.Services.AddScoped<ArticlesDataAdaptor>();
builder.Services.AddScoped<OptionsValuesTranslationsDataAdaptor>();
builder.Services.AddScoped<SheetsEditDataAdaptor>();

builder.Services.AddScoped<IRandomGeneratorService, RandomGeneratorService>();
builder.Services.AddScoped<IEmailService, EmailService>();
builder.Services.AddScoped<IGusService, GusService>();
builder.Services.AddScoped<IOrderAccessService, OrderAccessService>();
builder.Services.AddScoped<IOrderResponseTranslationService, OrderResponseTranslationService>();
builder.Services.AddScoped<IUserTokenService, UserTokenService>();
builder.Services.AddScoped<IUrlGenerator, UrlGenerator>();
builder.Services.AddScoped<IArticleValidatorService, ArticleValidatorNCalcService>(); // ArticleValidatorMxParserService
builder.Services.AddScoped<IArticleValidatorDataService, ArticleValidatorDataService>();
builder.Services.AddScoped<IShoppingCartStateService, ShoppingCartStateService>();
builder.Services.AddScoped<ICheckoutStateService, CheckoutStateService>();
builder.Services.AddScoped<ISieveProcessor, ApplicationSieveProcessor>();
builder.Services.AddScoped<ISieveCustomSortMethods, SieveCustomSortMethods>();
builder.Services.AddScoped<ISieveCustomFilterMethods, SieveCustomFilterMethods>();
builder.Services.AddScoped<ISieveService, SieveService>();
builder.Services.AddScoped<ILocalStorageService, LocalStorageService>();
builder.Services.AddScoped<ISessionStorageService, SessionStorageService>();
builder.Services.AddScoped<DialogSettingsService>();
builder.Services.AddScoped<IRollDataService>(sp => sp.GetRequiredService<RollDataService>());
builder.Services.AddScoped<IFilePathProvider, FilePathProvider>();
builder.Services.AddScoped<IFileStorageService, LocalFileStorageService>();

builder.Services.AddScoped<IOrderWatchService, OrderWatchService>();
builder.Services.AddScoped<ILockOrderService, LockOrderService>();

// Singletons. Per application
builder.Services.AddSingleton<IOrderNotificationService, OrderNotificationService>();
builder.Services.AddSingleton<ISumsCalculatorService, SumCalculatorSumFirstService>();
builder.Services.AddSingleton<ISheetValidationService, SheetValidationService>();
builder.Services.AddSingleton<IOptionRestrictionsService, OptionRestrictionsService>();
builder.Services.AddSingleton<ArticleValidatorSettingsService>();
builder.Services.AddSingleton<ITranslationManagerService, TranslationManagerService>();
builder.Services.AddSingleton<IMultiTranslationService, GeminiTranslationService>();
builder.Services.AddSingleton(typeof(ISyncfusionStringLocalizer), typeof(SyncfusionLocalizer));
builder.Services.AddSingleton(typeof(IStringLocalizer), typeof(StringLocalizer));
builder.Services.AddSingleton<ITranslationExportImportService, TranslationExportImportService>();
builder.Services.AddSingleton<IFakeOrdersService, FakeOrdersService>();

// BackgroundService
builder.Services.AddHostedService<TranslationBackgroundService>();

builder.Services.AddTranslationService(builder.Configuration);

builder.Services.AddAutoMapper(AppDomain.CurrentDomain.GetAssemblies());

builder.Services.AddMediatR(config =>
{
    config.RegisterServicesFromAssemblies(AppDomain.CurrentDomain.GetAssemblies());
    config.NotificationPublisher = new TaskWhenAllPublisher();
});

builder.Services.AddLocalization();

// Validators
builder.Services.AddValidatorsFromAssembly(typeof(Program).Assembly);
builder.Services.AddScoped<IValidator<GetAllArticleValidatorConditionResponse>,
    GetArticleValidatorConditionValidator>();
builder.Services.AddScoped<IValidator<GetSelectedArticleValidatorResponse>,
    GetSelectedArticleValidatorResponseValidator>();

builder.SetupSerilog();

// Nadpisanie domyślnego komponentu Radzen, aby używać tłumaczonych tekstów w filtrach Radzen DataGrid
var activator = new RadzenComponentActivator();
activator.Override(typeof(RadzenDataGrid<>), typeof(RadzenDataFilterLocalized<>));
builder.Services.AddSingleton<IComponentActivator>(activator);

var app = builder.Build();

//Register Syncfusion license
var syncfusionLicenseKey = builder.Configuration["SyncfusionLicenseKey"];
Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense(syncfusionLicenseKey);

//Configure localization
var localizationOptions = new RequestLocalizationOptions()
    .SetDefaultCulture(SystemConstants.SupportedCultures[0].Code)
    .AddSupportedCultures(SystemConstants.SupportedCultures.Select(x => x.Code).ToArray())
    .AddSupportedUICultures(SystemConstants.SupportedCultures.Select(x => x.Code).ToArray());
localizationOptions.ApplyCurrentCultureToResponseHeaders = false;
//localizationOptions.
app.UseRequestLocalization(localizationOptions);

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseMigrationsEndPoint();
    
    app.UseSwagger();
    app.UseSwaggerUI();
}
else
{
    app.UseExceptionHandler("/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();

app.UseStaticFiles();

app.UseRouting();

app.UseAuthorization();

app.MapControllers();
app.MapBlazorHub();
app.MapFallbackToPage("/_Host");

using (var scope = app.Services.CreateScope())
{
    StringLocalizerExtension.Configure(scope.ServiceProvider.GetRequiredService<IStringLocalizer>());
}

//// uruhcomienie migracji dla DEBUG - zakomentowane
//#if DEBUG // Migrate database for DEBUG build configuration
//using (var scope = app.Services.CreateScope())
//{
//    var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();
//    logger.LogInformation("Run migrations");

//    var dbFactory = scope.ServiceProvider.GetRequiredService<IDbContextFactory<EMessa.Core.Data.ApplicationDbContext>>();
//    var dbContext = dbFactory.CreateDbContext();
//    dbContext.Database.Migrate();
//}
//#endif

//#if BDP_Develop // Migrate database for BDP_Develop build configuration
using (var scope = app.Services.CreateScope())
{
    var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();
    logger.LogInformation("Run migrations");

    var dbFactory = scope.ServiceProvider.GetRequiredService<IDbContextFactory<EMessa.DAL.Data.ApplicationDbContext>>();
    var dbContext = dbFactory.CreateDbContext();
    dbContext.Database.Migrate();
}
//#endif


await app.SeedDatabaseAsync();
await app.LoadSystemParameters();
await app.LoadRoleIds();
await app.LoadValidatorSettings();

app.Run();
