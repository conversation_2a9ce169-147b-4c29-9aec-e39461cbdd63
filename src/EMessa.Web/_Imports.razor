@using System.Net.Http
@using System.Text.Json
@using AutoMapper
@using MediatR
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using Microsoft.JSInterop
@using Syncfusion.Blazor

@using EMessa.Base.Constants
@using EMessa.Core.Common
@using EMessa.Core.Features.AppState
@using EMessa.Core.Interfaces
@using EMessa.Core.Localizer
@using EMessa.Web
@using EMessa.Web.Components
@using EMessa.Web.Components.Editors
@using EMessa.Web.Components.Grids
@using EMessa.Web.Components.RadzenComponents
@using EMessa.Web.Components.RadzenComponents.Buttons
@using EMessa.Web.Components.RadzenComponents.Controls
@using EMessa.Web.Components.TreeGrid
@using EMessa.Web.Components.Sale
@using EMessa.Web.Shared
@using EMessa.Web.Extensions
@using EMessa.Web.Services
@using EMessa.Web.Shared.DraftEditor

@inject IMediator Mediator
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IAppStateService AppStateService
@inject IMapper Mapper
@inject IToastService ToastService
@inject IEmessaTooltipService EmessaTooltipService
@inject IModalDialogService ModalDialogService
