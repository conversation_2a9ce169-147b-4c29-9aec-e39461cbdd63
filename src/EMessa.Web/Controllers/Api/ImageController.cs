using System.Net.Http.Headers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Http.Features;

namespace EMessa.Web.Controllers.Api
{
    [ApiController]
    public class ImageController : ControllerBase
    {
        private readonly IWebHostEnvironment hostingEnv;

        public ImageController(IWebHostEnvironment env)
        {
            this.hostingEnv = env;
        }

        //[HttpPost("[action]")]
        [HttpPost]
        [Route("api/Image/Save")]
        public void Save(IList<IFormFile> uploadFiles)
        {
            try
            {
                foreach (var file in uploadFiles)
                {
                    if (uploadFiles != null)
                    {
                        var targetPath = hostingEnv.ContentRootPath + @"\wwwroot\images\editor";
                        var filename = ContentDispositionHeaderValue.Parse(file.ContentDisposition).FileName?.Trim('"');

                        // Create a new directory, if it does not exists
                        if (!Directory.Exists(targetPath))
                        {
                            Directory.CreateDirectory(targetPath);
                        }

                        // Name which is used to save the image
                        filename = targetPath + $@"\{filename}";

                        if (!System.IO.File.Exists(filename))
                        {
                            // Upload a image, if the same file name does not exist in the directory
                            using (FileStream fs = System.IO.File.Create(filename))
                            {
                                file.CopyTo(fs);
                                fs.Flush();
                            }
                            Response.StatusCode = 200;
                        }
                        else
                        {
                            Response.StatusCode = 204;
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Response.Clear();
                Response.ContentType = "application/json; charset=utf-8";
                Response.HttpContext.Features.Get<IHttpResponseFeature>()!.ReasonPhrase = e.Message;
            }
        }
    }

}
