using EMessa.Core.Features.OrderItems.ApiCommands.UpdateOrderItemsStatus;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace EMessa.Web.Controllers.Api.Orders;

//[Authorize]
[ApiController]
[Route("api/[controller]")]
public class OrderItemsStatusController(
    ILogger<OrderItemsStatusController> logger,
    IMediator mediator)
    : ControllerBase
{
    [HttpPatch("{orderItemId}")]
    public async Task<IActionResult> UpdateOrderItemStatus(int orderItemId, [FromBody] UpdateOrderItemsStatusBase request)
    {
        logger.LogDebug("Updating OrderItem status for OrderItemId {OrderItemId} to {OrderItemStatus}", orderItemId, request.OrderItemStatus);

        var requestObj = new UpdateOrderItemsStatus
        {
            OrderItemId = orderItemId,
            OrderItemStatus = request.OrderItemStatus,
            TransportDateUtc = request.TransportDateUtc,
            ProductionDateUtc = request.ProductionDateUtc
        };

        var result = await mediator.Send(new ApiUpdateOrderItemsStatusCommand(new() { OrderItems = [requestObj] }));

        if (result.Succeeded)
        {
            logger.LogDebug("OrderItem status updated successfully for OrderItemId {OrderItemId}", orderItemId);
            return NoContent();
        }
        else
        {
            logger.LogWarning("Failed to update OrderItem status for OrderItemId {OrderItemId}: {ErrorMessages}", orderItemId, result.Messages);
            return BadRequest(result.Messages);
        }
    }

    [HttpPatch]
    public async Task<IActionResult> UpdateOrderItemsStatus([FromBody] UpdateOrderItemsStatusRequest request)
    {
        logger.LogDebug("Updating multiple OrderItems status ({OrderItemStatus})", request.OrderItems.Count);
        var result = await mediator.Send(new ApiUpdateOrderItemsStatusCommand(request));

        if (result.Succeeded)
        {
            logger.LogDebug("OrderItems status updated successfully for {Count} items", request.OrderItems.Count);
            return NoContent();
        }
        else
        {
            logger.LogWarning("Failed to update OrderItemss status: {ErrorMessages}", result.Messages);
            return BadRequest(result.Messages);
        }
    }
}
