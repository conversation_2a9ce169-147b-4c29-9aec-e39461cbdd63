using EMessa.Core.Features.Orders.ApiCommands.UpdateOrdersStatus;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace EMessa.Web.Controllers.Api.Orders;

//[Authorize]
[ApiController]
[Route("api/[controller]")]
public class OrdersStatusController(
    ILogger<OrdersStatusController> logger,
    IMediator mediator)
    : ControllerBase
{
    [HttpPatch("{orderId}")]
    public async Task<IActionResult> UpdateOrderStatus(int orderId, [FromBody] UpdateOrderStatusRequestBase request)
    {
        logger.LogDebug("Updating Order status for OrderId {OrderId} to {OrderItemStatus}", orderId, request.OrderStatus);

        var requestObj = new UpdateOrderStatusRequest
        {
            OrderId = orderId,
            OrderStatus = request.OrderStatus,
            ApplyStatusToOrderItems = request.ApplyStatusToOrderItems,
            MessaNo = request.MessaNo
        };

        var result = await mediator.Send(new ApiUpdateOrdersStatusCommand(new() { Orders = [requestObj] }));

        if (result.Succeeded)
        {
            logger.LogDebug("Order status updated successfully for OrderId {OrderId}", orderId);
            return NoContent();
        }
        else
        {
            logger.LogWarning("Failed to update Order status for OrderId {OrderId}: {ErrorMessages}", orderId, result.Messages);
            return BadRequest(result.Messages);
        }
    }

    [HttpPatch]
    public async Task<IActionResult> UpdateOrdersStatus([FromBody] UpdateOrdersStatusRequest request)
    {
        logger.LogDebug("Updating multiple Orders status ({OrderStatus})", request.Orders.Count);
        var result = await mediator.Send(new ApiUpdateOrdersStatusCommand(request));

        if (result.Succeeded)
        {
            logger.LogDebug("Orders status updated successfully for {Count} items", request.Orders.Count);
            return NoContent();
        }
        else
        {
            logger.LogWarning("Failed to update Orders status: {ErrorMessages}", result.Messages);
            return BadRequest(result.Messages);
        }
    }
}
