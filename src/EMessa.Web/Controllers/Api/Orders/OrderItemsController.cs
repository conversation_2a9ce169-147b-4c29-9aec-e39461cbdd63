using EMessa.Core.Features.OrderItems.ApiQueries.GetOrderItemByIdQuery;
using EMessa.Core.Features.Orders.ApiQueries.Common;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace EMessa.Web.Controllers.Api.Orders;

//[Authorize]
[ApiController]
[Route("api/[controller]")]
public class OrderItemsController(
    ILogger<OrderItemsController> logger,
    IMediator mediator)
    : ControllerBase
{
    [HttpGet("{orderItemId}")]
    public async Task<ActionResult<OrderItemDTO>> GetOrderItemById(int orderItemId)
    {
        logger.LogDebug("Retrieving Order for OrderItemId {OrderItemId}", orderItemId);

        var result = await mediator.Send(new ApiGetOrderItemByIdQuery(orderItemId));

        if (result.Succeeded)
        {
            logger.LogDebug("Order retrieved successfully for OrderItemId {OrderItemId}", orderItemId);
            return Ok(result.Data);
        }
        else
        {
            logger.LogWarning("Failed to retrieve Order for OrderItemId {OrderItemId}: {ErrorMessages}", orderItemId, result.Messages);
            return BadRequest(result.Messages);
        }
    }
}
