using EMessa.Core.Features.Orders.ApiQueries.Common;
using EMessa.Core.Features.Orders.ApiQueries.GetAcceptedOrdersQuery;
using EMessa.Core.Features.Orders.ApiQueries.GetOrderByIdQuery;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace EMessa.Web.Controllers.Api.Orders;

//[Authorize]
[ApiController]
[Route("api/[controller]")]
public class OrdersController(
    ILogger<OrdersController> logger,
    IMediator mediator)
    : ControllerBase
{
    [HttpGet("{orderId}")]
    public async Task<ActionResult<OrderDTO>> GetOrderById(int orderId)
    {
        logger.LogDebug("Retrieving Order for OrderId {OrderId}", orderId);

        var result = await mediator.Send(new ApiGetOrderByIdQuery(orderId));

        if (result.Succeeded)
        {
            logger.LogDebug("Order retrieved successfully for OrderId {OrderId}", orderId);
            return Ok(result.Data);
        }
        else
        {
            logger.LogWarning("Failed to retrieve Order for OrderId {OrderId}: {ErrorMessages}", orderId, result.Messages);
            return BadRequest(result.Messages);
        }
    }

    [HttpGet("accepted")]
    public async Task<ActionResult<List<OrderDTO>>> GetAcceptedOrders()
    {
        logger.LogDebug("Retrieving all accepted orders");

        var result = await mediator.Send(new ApiGetAcceptedOrdersQuery());

        if (result.Succeeded)
        {
            logger.LogDebug("Accepted orders retrieved successfully");
            return Ok(result.Data);
        }
        else
        {
            logger.LogWarning("Failed to retrieve accepted orders: {ErrorMessages}", result.Messages);
            return BadRequest(result.Messages);
        }
    }
}
