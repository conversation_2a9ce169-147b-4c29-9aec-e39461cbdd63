using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using EMessa.Base.Constants;
using EMessa.Core.Interfaces;
using EMessa.Core.Localizer;
using EMessa.Web.Models;

namespace EMessa.Web.Controllers.Api;

[Authorize]
[Route("api/[controller]/[action]")]
public class OrderItemImageController : Controller
{
    private readonly ILogger<OrderItemImageController> _logger;
    private readonly IOrderAccessService _orderService;
    private readonly IRandomGeneratorService _randomGeneratorService;
    private readonly string _draftsPath;

    public OrderItemImageController(
        IConfiguration configuration,
        ILogger<OrderItemImageController> logger,
        IOrderAccessService orderService,
        IRandomGeneratorService randomGeneratorService
    )
    {
        _logger = logger;
        _orderService = orderService;
        _randomGeneratorService = randomGeneratorService;

        var draftsPath = configuration["FileUploadSettings:DraftsPath"];

        if (draftsPath == null)
        {
            _logger.LogError("FileUploadSettings.DraftsPath is not set in appsettings.json");
            throw new InvalidOperationException(
                "FileUploadSettings.DraftsPath is not set in appsettings.json");
        }

        _draftsPath = draftsPath;

        if (Directory.Exists(_draftsPath))
        {
            return;
        }

        _logger.LogInformation(@"Creating directory: {DraftsPath}", _draftsPath);
        Directory.CreateDirectory(_draftsPath);
    }

    private int? GetUserId()
    {
        return int.TryParse(User.FindFirstValue(ClaimTypes.NameIdentifier), out var userId)
            ? userId
            : null;
    }

    [HttpPost]
    public async Task<IActionResult> Save(IList<IFormFile> uploadFiles)
    {
        try
        {
            if (uploadFiles.Count == 0)
            {
                _logger.LogError("Error saving file, missing file");
                return BadRequest(new UploadRemoveResponse
                {
                    Action = UploadRemoveAction.Save,
                    Status = false,
                    Message = "Brak pliku".Tr()
                });
            }
            
            var file = uploadFiles.First();
            if (file.Length <= 0)
            {
                _logger.LogError("Error saving file, file is empty");
                return StatusCode(422, new UploadRemoveResponse
                {
                    Action = UploadRemoveAction.Save,
                    Status = false,
                    Message = "Plik jest pusty".Tr()
                });
            }

            var originalFileName = Path.GetFileName(file.FileName);
            var hashFileName = _randomGeneratorService.GenerateHashFileName(originalFileName, _draftsPath);

            var physicalPath = Path.Combine(_draftsPath, hashFileName);
            var directoryPath = Path.GetDirectoryName(physicalPath);
            if (directoryPath == null)
            {
                _logger.LogError("Error saving file, directoryPath is null");
                return BadRequest(new UploadRemoveResponse
                {
                    Action = UploadRemoveAction.Save,
                    Status = false,
                    Message = "Folder docelowy nie istnieje".Tr()
                });
            }

            Directory.CreateDirectory(directoryPath);

            await using var stream = new FileStream(physicalPath, FileMode.Create);
            await file.CopyToAsync(stream);

            return Ok(new UploadRemoveResponse
            {
                Action = UploadRemoveAction.Save,
                Status = true,
                Message = "Plik zapisany".Tr(),
                OriginalFileName = originalFileName,
                HashFileName = hashFileName
            });

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving file");
            return StatusCode(500, new UploadRemoveResponse
            {
                Action = UploadRemoveAction.Save,
                Status = false,
                Message = "Plik nie zapisany".Tr()
            });
            
        }
        // Object reference not set to an instance of an object.
    }

    [HttpPost]
    public IActionResult Remove([FromForm] string uploadFiles, [FromForm] string hashFileName)
    {
        try
        {
            if (string.IsNullOrEmpty(hashFileName))
            {
                _logger.LogError("Error removing file, hashFileName is null or empty");
                return BadRequest(new UploadRemoveResponse
                {
                    Action = UploadRemoveAction.Remove,
                    Status = false,
                    Message = "Bląd nazwy pliku".Tr()
                });
            }

            var physicalPath = Path.Combine(_draftsPath, hashFileName);
            if (!System.IO.File.Exists(physicalPath))
            {
                _logger.LogError("Error removing file, file not found");
                return BadRequest(new UploadRemoveResponse
                {
                    Action = UploadRemoveAction.Remove,
                    Status = false,
                    Message = "Plik nie usunięty".Tr()
                });
            }

            System.IO.File.Delete(physicalPath);

            return Ok(new UploadRemoveResponse
            {
                Action = UploadRemoveAction.Remove,
                Status = true,
                Message = "Plik usunięty".Tr()
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing file");
            return BadRequest(new UploadRemoveResponse
            {
                Action = UploadRemoveAction.Remove,
                Status = false,
                Message = "Podczas usuwania pliku".Tr()
            });
        }
        // Object reference not set to an instance of an object.
    }

    //TODO W przyszłosci trzeba dodać endpoint który używa klucza do autoryzaji. Potrzebne dla innych serwisów
    [HttpGet("{**hashFileName}")]
    public async Task<IActionResult> Get(string hashFileName)
    {
        try
        {
            var isAdminOrProduction = User.IsInRole(Role.Administrator) || User.IsInRole(Role.Production);
            if (!isAdminOrProduction)
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                    return Unauthorized();

                var canAccess = await _orderService.CanUserAccessOrderItem(hashFileName, userId.Value);
                if (!canAccess)
                {
                    return Unauthorized();
                }
            }

            if (string.IsNullOrEmpty(hashFileName))
            {
                _logger.LogError("Error getting file, hashFileName is null or empty");
                return BadRequest(new UploadRemoveResponse
                {
                    Action = UploadRemoveAction.Get,
                    Status = false,
                    Message = "Nazwa pliku jest wymagana".Tr()
                });
            }

            var physicalPath = Path.Combine(_draftsPath, hashFileName);

            if (!System.IO.File.Exists(physicalPath))
            {
                _logger.LogError("Error getting file, file not found");
                return NotFound(new UploadRemoveResponse
                {
                    Action = UploadRemoveAction.Get,
                    Status = false,
                    Message = "Nie znaleziono pliku".Tr()
                });
            }

            var fileBytes = await System.IO.File.ReadAllBytesAsync(physicalPath);
            var contentType = GetContentType(hashFileName);

            return File(fileBytes, contentType);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting file");
            return BadRequest(new UploadRemoveResponse
            {
                Action = UploadRemoveAction.Get,
                Status = false,
                Message = "Błąd pobierania pliku".Tr()
            });
        }
    }

    private readonly FileExtensionContentTypeProvider _contentTypeProvider = new();

    private string GetContentType(string fileName)
    {
        _contentTypeProvider.TryGetContentType(fileName, out var contentType);
        return contentType ?? "application/octet-stream";
    }
}