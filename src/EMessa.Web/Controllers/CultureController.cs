using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;
using System.Globalization;

namespace EMessa.Web.Controllers
{
    [Route("[controller]/[action]")]
    public class CultureController : Controller
    {
        private ILogger<CultureController> _logger;

        public CultureController(ILogger<CultureController> logger)
        {
            _logger = logger;
        }

        [HttpGet]
        public IActionResult SetCulture(string culture, string redirectUri)
        {
            var newCulture = CultureInfo.GetCultureInfo(culture);
            System.Globalization.CultureInfo.CurrentCulture = newCulture;
            System.Globalization.CultureInfo.CurrentUICulture = newCulture;
            if (culture != null)
            {
                HttpContext.Response.Cookies.Delete(CookieRequestCultureProvider.DefaultCookieName);
                HttpContext.Response.Cookies.Append(
                    CookieRequestCultureProvider.DefaultCookieName,
                    CookieRequestCultureProvider.MakeCookieValue(
                        new RequestCulture(culture)));
            }

            return LocalRedirect(redirectUri);
        }
    }
}
