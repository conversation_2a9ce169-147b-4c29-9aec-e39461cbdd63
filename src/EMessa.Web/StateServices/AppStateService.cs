using EMessa.Core.Features.AppState;
using EMessa.Core.Features.Orders.Queries.Common;
using EMessa.Core.Interfaces;
using EMessa.Core.Services;
using Microsoft.AspNetCore.Components;
using Syncfusion.Blazor.Grids;

namespace EMessa.Web.StateServices;

public class AppStateService : IAppStateService, IAsyncDisposable
{
    private readonly ILockOrderService _lockOrderService;
    private readonly IOrderNotificationService _orderNotificationService;
    private readonly IOrderWatchService _orderWatchService;
    private readonly CancellationTokenSource _cts = new();

    public AppStateService(ILockOrderService lockOrderService, 
        IOrderNotificationService orderNotificationService, 
        IOrderWatchService orderWatchService)
    {
        _lockOrderService = lockOrderService;
        _orderNotificationService = orderNotificationService;
        _orderWatchService = orderWatchService;
        _orderNotificationService.OnOrderChangedAsync += OnOrderChanged;
    }

    public ValueTask DisposeAsync()
    {
        _orderNotificationService.OnOrderChangedAsync -= OnOrderChanged;
        _cts.Cancel();
        _cts.Dispose();
        return ValueTask.CompletedTask;
    }

    public OrdersState OrdersState { get; set; } = new();
    public CategoriesState CategoriesState { get; set; } = new();

    public GridUIState GridState { get; set; } = new();

    public int GetUserProfileId()
    {
        return UserData.UserProfileId;
    }

    public int? GetCustomerId()
    {
        return UserData.CustomerId;
    }

    public UserData UserData { get; set; } = new();

    #region Locked Order

    public LockOrderResponse? OwnLockedOrder { get; private set; }

    public void SetOwnLockedOrder(ComponentBase? source, LockOrderResponse? lockedOrder)
    {
        if (lockedOrder?.IsLockedByCurrentUser is true or null)
        {
            OwnLockedOrder = lockedOrder;
            NotifyStateChanged(source, AppStateNotifyProperties.OwnLockedOrderChanged);
        }
    }
    
    private async Task LoadUserLockAsync(ComponentBase? source, int userProfileId)
    {
        try
        {
            var lockResult = await _lockOrderService.GetUserLockAsync(userProfileId, _cts.Token);
            SetOwnLockedOrder(source, lockResult.Succeeded ? lockResult.Data : null);
        }
        catch (TaskCanceledException)
        {
            // ignored
        }
    }

    private async Task OnOrderChanged(int orderId)
    {
        await _orderWatchService.NotifyOrderChanged(orderId);
        
        // Otrzymujemy powiadomienie ze ktoś zablokował zamówienie
        // Jesli nie mamy blokady na to zamówienie to ładujemy tę blokadę
        if (OwnLockedOrder == null)
        {
            NotifyStateChanged(null, AppStateNotifyProperties.WatchedOrderChanged);
        }
    }

    #endregion

    #region Screen Size

    public AppScreenSize AppScreenSize { get; set; }
    public int ScreenWidth { get; set; }
    public int ScreenHeight { get; set; }
    public bool IsSmallScreen => AppScreenSize == AppScreenSize.Small;
    public bool IsMediumScreen => AppScreenSize == AppScreenSize.Medium;
    public bool IsLargeScreen => AppScreenSize == AppScreenSize.Large;

    #endregion

    public event Action<ComponentBase?, string>? StateChanged;
    private void NotifyStateChanged(ComponentBase? source, string property) => StateChanged?.Invoke(source, property);

    public void SetLoggedUser(
        ComponentBase? source, 
        int userProfileId, 
        string firstName, 
        string lastName,
        bool firstConfiguration,
        int? customerId, 
        List<string> roles, 
        List<int> branchIds, 
        int? factoryId,
        string email
        )
    {
        UserData.SetUserData(
            userProfileId, 
            firstName, 
            lastName, 
            firstConfiguration, 
            customerId, 
            roles, 
            branchIds, 
            factoryId,
            email);
        NotifyStateChanged(source, AppStateNotifyProperties.SetUserData);

        NotifyStateChanged(source, AppStateNotifyProperties.ShoppingCartChanged);
        _ = LoadUserLockAsync(source, userProfileId);
    }

    public void UserLoggedIn(ComponentBase source)
    {
        NotifyStateChanged(source, AppStateNotifyProperties.UserLoggedIn);
    }

    public AppScreenSize SetScreenSize(ComponentBase? source, int width, int height)
    {
        ScreenWidth = width;
        ScreenHeight = height;

        AppScreenSize = width switch
        {
            < (int)AppScreenSize.Small => AppScreenSize.Small,
            < (int)AppScreenSize.Medium => AppScreenSize.Medium,
            _ => AppScreenSize.Large
        };

        GridState.RowDirection =
            RowDirection
                .Horizontal; // AppScreenSize == AppScreenSize.Medium ? RowDirection.Vertical : RowDirection.Horizontal;
        GridState.AdaptiveMode = AppScreenSize <= AppScreenSize.Medium ? AdaptiveMode.Both : AdaptiveMode.Mobile;

        NotifyStateChanged(source, AppStateNotifyProperties.ScreenSizeChanged);
        return AppScreenSize;
    }


    public void SetLastOrderStatus(ComponentBase? source, Base.Enums.OrderStatus status)
    {
        OrdersState.LastOrderStatus = status;
        NotifyStateChanged(source, AppStateNotifyProperties.OrdersStateChanged);
    }

    public void SetCatalogVisibility(ComponentBase? source, bool visible)
    {
        CategoriesState.CatalogVisible = visible;
        NotifyStateChanged(source, AppStateNotifyProperties.CatalogVisibleChanged);
    }

    public void SetShoppingCart(ComponentBase source)
    {
        NotifyStateChanged(source, AppStateNotifyProperties.ShoppingCartChanged);
    }

    public bool GetCategoryExpanded(int categoryId)
    {
        return CategoriesState.CategoryExpanded.TryGetValue(categoryId, out var value) && value;
    }

    public void SetCategoryExpanded(int categoryId, bool expanded)
    {
        CategoriesState.CategoryExpanded[categoryId] = expanded;
    }
}