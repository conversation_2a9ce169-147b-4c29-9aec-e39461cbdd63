using EMessa.Services.Interfaces;
using EMessa.Web.Interfaces;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace EMessa.Web.StateServices;

public class CheckoutStateService(
    NavigationManager navigationManager,
    ISessionStorageService sessionStorageService,
    ILogger<CheckoutStateService> logger)

    : ICheckoutStateService
{
    public List<int> OrderItemIds { get; private set; } = [];

    public event Action? OnChange;

    private void NotifyStateChanged() => OnChange?.Invoke();

    private const string CheckoutStateKey = "checkout_state";

    public async Task StoreStateAsync()
    {
        try
        {
            await sessionStorageService.SetAsync(CheckoutStateKey, OrderItemIds);
        }
        catch (JSException ex)
        {
            logger.LogError(ex, "Failed to save checkout state.");
        }
    }

    public async Task RestoreStateAsync()
    {
        try
        {
            OrderItemIds = await sessionStorageService.GetAsync<List<int>>(CheckoutStateKey, []);
        }
        catch (JSException ex)
        {
            logger.LogError(ex, "Failed to restore checkout state.");
            OrderItemIds = [];
        }

        NotifyStateChanged();
    }

    public async Task StartCheckout(List<int> orderItemIds)
    {
        if (orderItemIds.Count == 0)
        {
            OrderItemIds = [];
            await StoreStateAsync();
            navigationManager.NavigateTo("/shoppingcart");
            return;
        }

        OrderItemIds = orderItemIds;
        await StoreStateAsync();
        NotifyStateChanged();
        navigationManager.NavigateTo("/checkout");
    }

    public async Task DropOrderItem(int orderItemId)
    {
        OrderItemIds = OrderItemIds.Where(i => i != orderItemId).ToList();
        await StoreStateAsync();
        NotifyStateChanged();
    }

    public async Task SetOrderItemsIds(List<int> orderItemIds)
    {
        if (new HashSet<int>(OrderItemIds).SetEquals(orderItemIds))
            return;

        OrderItemIds = orderItemIds;
        await StoreStateAsync();
        NotifyStateChanged();
    }

    public async Task ClearStateAsync()
    {
        OrderItemIds = [];
        await sessionStorageService.RemoveAsync(CheckoutStateKey);
        NotifyStateChanged();
    }
} 