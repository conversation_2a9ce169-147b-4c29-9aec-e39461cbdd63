using Microsoft.AspNetCore.Components;
using <PERSON><PERSON>zen;

namespace EMessa.Web.Services;

internal interface IEmessaTooltipService
{
    void Open(ElementReference elementReference, string text, TooltipOptions? options = null);

    void OpenHtml(ElementReference elementReference, RenderFragment<TooltipService>
        contentHtml, TooltipOptions? options = null);

    void Close();
}

internal class EmessaTooltipService(TooltipService tooltipService) : IEmessaTooltipService
{
    private static TooltipOptions DefaultOptions => new()
    {
        Duration = 2000,
        Position = TooltipPosition.Top
    };

    public void Open(ElementReference elementReference, string text, TooltipOptions? options = null) =>
        tooltipService.Open(elementReference, text, options ?? DefaultOptions);

    public void OpenHtml(ElementReference elementReference, RenderFragment<TooltipService>
        contentHtml, TooltipOptions? options = null) =>
        tooltipService.Open(elementReference, contentHtml,
            options ?? DefaultOptions);

    public void Close() => tooltipService.Close();

}