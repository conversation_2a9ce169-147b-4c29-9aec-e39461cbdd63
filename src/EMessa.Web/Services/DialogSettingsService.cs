using System.Drawing;
using EMessa.Services.Interfaces;

namespace EMessa.Web.Services;

public class DialogSettingsService(ILocalStorageService localStorage)
{
    public async Task<DialogSettingsManager> CreateManagerAsync(string storageKey, DialogSettings? defaultSettings = null)
    {
        var manager = new DialogSettingsManager(localStorage, storageKey, defaultSettings);
        await manager.LoadStateAsync();
        return manager;
    }
}

public class DialogSettingsManager(ILocalStorageService localStorage, string storageKey, DialogSettings? defaultSettings = null)
{
    private DialogSettings? _settings;

    private DialogSettings? Settings
    {
        get => _settings;
        set
        {
            if (_settings == value) return;
            _settings = value;
            _ = Task.Run(SaveStateAsync);
        }
    }

    public async Task LoadStateAsync()
    {
        _settings = await localStorage.GetAsync(storageKey, defaultSettings);
    }

    public async Task SaveStateAsync()
    {
        await localStorage.SetAsync(storageKey, Settings);
    }

    public void OnDrag(Point point)
    {
        Settings ??= defaultSettings ?? new DialogSettings
        {
            Height = "600px",
            Width = "1300px"
        };
        Settings.Left = $"{point.X}px";
        Settings.Top = $"{point.Y}px";
    }

    public void OnResize(Size size)
    {
        Settings ??= defaultSettings ?? new DialogSettings();
        Settings.Width = $"{size.Width}px";
        Settings.Height = $"{size.Height}px";
    }

    public string GetWidth() => Settings?.Width ?? defaultSettings?.Width ?? "1300px";
    public string GetHeight() => Settings?.Height ?? defaultSettings?.Height ?? "600px";
    public string? GetLeft() => Settings?.Left ?? defaultSettings?.Left;
    public string? GetTop() => Settings?.Top ?? defaultSettings?.Top;
}

public class DialogSettings
{
    public string? Left { get; set; }
    public string? Top { get; set; }
    public string? Width { get; set; }
    public string? Height { get; set; }
}
