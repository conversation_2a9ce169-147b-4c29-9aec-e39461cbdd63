using EMessa.Core.Localizer;
using Microsoft.AspNetCore.Components;
using <PERSON><PERSON>zen;

namespace EMessa.Web.Services;

internal interface IModalDialogService
{
    /// <summary>
    /// Wyświetla okno potwierdzenia usunięcia ze wzkazanym przetłumaczonym  komunikatem: 
    /// "Czy na pewno chcesz aktywować '{0}'?".
    /// </summary>
    /// <param name="messageTemplate">Szablon treści np. "Czy na pewno chcesz aktywować '{0}'?". Jest tłumaczona.</param>
    /// <param name="highlightedValue">Nazwa elementu do wyświetlenia w komunikacie, nie jest tłumaczona. Wartość jest pogrubiona poprzez znacznik HTML strong.</param>
    /// <param name="dialogTitle">Tytuł okna dialogowego, jest tłumaczony.</param>
    /// <returns>True, jeśli u<PERSON> potwierdzi, false w przeciwnym razie.</returns>
    Task<bool> ConfirmActionWithValue(string messageTemplate, string highlightedValue, string dialogTitle = "Potwierdzenie");

    /// <summary>
    /// Wyświetla okno potwierdzenia usunięcia z przetłumaczonym  komunikatem: 
    /// "Czy na pewno chcesz usunąć '{itemName}'?".Tr().
    /// </summary>
    /// <param name="itemName">Nazwa elementu do wyświetlenia w komunikacie, nie jest tłumaczona.</param>
    /// <returns>True, jeśli użytkownik potwierdzi, false w przeciwnym razie.</returns>
    Task<bool> DeleteConfirmationByName(string itemName = "");

    /// <summary>
    /// Wyświetla okno potwierdzenia typu tak lub nie.
    /// </summary>
    /// <param name="dialogMessage"></param>
    /// <param name="dialogTitle"></param>
    /// <returns>True, jeśli użytkownik potwierdzi, false w przeciwnym razie.</returns>
    Task<bool> Confirmation(string dialogMessage, string dialogTitle = "Potwierdzenie");

    /// <summary>
    /// Wyświetla okno potwierdzenia typu tak lub nie.
    /// </summary>
    /// <param name="dialogMessage"></param>
    /// <param name="dialogTitle"></param>
    /// <returns>True, jeśli użytkownik potwierdzi, false w przeciwnym razie.</returns>
    Task<bool> Confirmation(RenderFragment dialogContent, string dialogTitle = "Potwierdzenie");

}

internal class ModalDialogService(DialogService dialogService) : IModalDialogService
{
    private const string DefaultStyle = "z-index: 1002;";

    /// <inheritdoc/>
    public async Task<bool> ConfirmActionWithValue(string messageTemplate, string highlightedValue, string dialogTitle = "Potwierdzenie")
    {
        var translated = messageTemplate.Tr(); // np. "Czy na pewno chcesz aktywować promocję {0}?"
        var content = BuildTranslatedConfirmationContent(translated, highlightedValue);
        return await Confirmation(dialogContent: content, dialogTitle);
    }

    /// <inheritdoc/>
    public async Task<bool> DeleteConfirmationByName(string itemName = "")
    {
        return await ConfirmActionWithValue("Czy na pewno chcesz usunąć '{0}'?".Tr(), itemName);

        //return await Confirmation(dialogMessage: "Czy na pewno chcesz usunąć '{0}'?".Tr(itemName));
    }

    /// <inheritdoc/>
    public async Task<bool> Confirmation(string dialogMessage, string dialogTitle = "Potwierdzenie")
    {
        var confirmed = await dialogService.Confirm(
            dialogMessage,
            dialogTitle.Tr(),
            new ConfirmOptions { Style = DefaultStyle, AutoFocusFirstElement = true, OkButtonText = "Tak".Tr(), CancelButtonText = "Nie".Tr() });

        return confirmed ?? false;
    }

    /// <inheritdoc/>
    public async Task<bool> Confirmation(RenderFragment dialogContent, string dialogTitle = "Potwierdzenie")
    {
        var confirmed = await dialogService.Confirm(
            dialogContent,
            dialogTitle.Tr(),
            new ConfirmOptions { Style = DefaultStyle, AutoFocusFirstElement = true, OkButtonText = "Tak".Tr(), CancelButtonText = "Nie".Tr() });

        return confirmed ?? false;
    }

    private RenderFragment BuildTranslatedConfirmationContent(string template, string strongText)
    {
        var parts = template.Split("{0}");

        return builder =>
        {
            builder.OpenElement(0, "p");

            builder.AddContent(1, parts[0]);

            builder.OpenElement(2, "strong");
            builder.AddContent(3, strongText);
            builder.CloseElement();

            if (parts.Length > 1)
            {
                builder.AddContent(4, parts[1]);
            }

            builder.CloseElement();
        };
    }
}
