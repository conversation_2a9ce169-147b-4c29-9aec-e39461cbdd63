using EMessa.Core.Localizer;
using <PERSON><PERSON>zen;

namespace EMessa.Web.Services;

internal interface IToastService
{
    private const int DefaultDuration = 5000;

    /// <summary>
    /// Wyświetla powiadomienie na podstawie wskazanego typu, z predefiniowanym nagłówkiem oraz opcjonalnym opisem.
    /// </summary>
    /// <param name="toastType"></param>
    /// <param name="details"></param>
    /// <param name="duration"></param>
    void Show(ToastType toastType, string details = "", int duration = DefaultDuration);
    void Show(ToastType toastType, IEnumerable<string> details, int duration = DefaultDuration);
    void ShowSuccess(string summary = "", string details = "", int duration = DefaultDuration);
    void ShowInfo(string summary = "", string details = "", int duration = DefaultDuration);
    void ShowError(string summary = "", string details = "", int duration = DefaultDuration);
    void ShowWarning(string summary = "", string details = "", int duration = DefaultDuration);
    void ShowNotImplemented(string details = "");
    void ClearAll();
}

internal class ToastService(NotificationService notificationService) : IToastService
{
    private const int DefaultDuration = 5000;
    private const bool ShowProgress = false;
    private const string DefaultStyle = "";
    private const int MaxNotifications = 3;

    /// <inheritdoc />
    public void Show(ToastType toastType, string details = "", int duration = DefaultDuration) =>
        ShowNotification(GetSeverity(toastType), GetSummary(toastType), details, duration);

    public void Show(ToastType toastType, IEnumerable<string> details, int duration = DefaultDuration) =>
        ShowNotification(GetSeverity(toastType), GetSummary(toastType), string.Join(". ", details), duration);

    public void ShowSuccess(string summary = "", string details = "", int duration = DefaultDuration) =>
        ShowNotification(NotificationSeverity.Success, summary, details, duration);

    public void ShowInfo(string summary = "", string details = "", int duration = DefaultDuration) =>
        ShowNotification(NotificationSeverity.Info, summary, details, duration);

    public void ShowWarning(string summary = "", string details = "", int duration = DefaultDuration) =>
        ShowNotification(NotificationSeverity.Warning, summary, details, duration);

    public void ShowError(string summary = "", string details = "", int duration = DefaultDuration) =>
        ShowNotification(NotificationSeverity.Error, summary, details, duration);

    public void ShowNotImplemented(string details = "") => Show(ToastType.NotImplemented, details);

    public void ClearAll()
    {
        notificationService.Messages.Clear();
    }

    private void ShowNotification(NotificationSeverity severity, string summary, string details, int duration)
    {
        if (notificationService.Messages.Count >= MaxNotifications)
        {
            notificationService.Messages.RemoveAt(0);
        }

        notificationService.Notify(new NotificationMessage
        {
            Severity = severity,
            Summary = string.IsNullOrEmpty(summary) ? string.Empty : summary,
            Detail = string.IsNullOrEmpty(details) ? string.Empty : details,
            Duration = duration,
            Style = DefaultStyle,
            ShowProgress = ShowProgress,
            Payload = DateTime.Now
        });
    }

    private static NotificationSeverity GetSeverity(ToastType type) => type switch
    {
        ToastType.SaveSuccess => NotificationSeverity.Success,
        ToastType.DeleteSuccess => NotificationSeverity.Success,
        ToastType.LoadDataError => NotificationSeverity.Error,
        ToastType.DeleteError => NotificationSeverity.Error,
        ToastType.SaveError => NotificationSeverity.Error,
        ToastType.TranslateError => NotificationSeverity.Error,
        ToastType.TranslateSuccess => NotificationSeverity.Success,
        ToastType.AddCartItemSuccess => NotificationSeverity.Success,
        ToastType.AddCartItemError => NotificationSeverity.Error,
        ToastType.UpdateSuccess => NotificationSeverity.Success,
        ToastType.UpdateError => NotificationSeverity.Error,
        ToastType.LoadSaleError => NotificationSeverity.Error,
        ToastType.NotImplemented => NotificationSeverity.Warning,
        ToastType.LockOrderError => NotificationSeverity.Error,
        ToastType.AddOrderItemSuccess => NotificationSeverity.Success,
        ToastType.AddOrderItemError => NotificationSeverity.Error,
        _ => NotificationSeverity.Info
    };

    private string GetSummary(ToastType type) => type switch
    {
        ToastType.SaveSuccess => "Pomyślnie zapisano".Tr(),
        ToastType.DeleteSuccess => "Pomyślnie usunięto".Tr(),
        ToastType.LoadDataError => "Błąd pobierania danych".Tr(),
        ToastType.DeleteError => "Błąd podczas usuwania".Tr(),
        ToastType.SaveError => "Błąd podczas zapisywania".Tr(),
        ToastType.TranslateError => "Błąd podczas tłumaczenia".Tr(),
        ToastType.TranslateSuccess => "Pomyślnie przetłumaczono".Tr(),
        ToastType.AddCartItemSuccess => "Dodano przedmiot do koszyka".Tr(),
        ToastType.AddCartItemError => "Błąd podczas dodawania przedmiotu do koszyka".Tr(),
        ToastType.UpdateSuccess => "Pomyślnie uaktualniono".Tr(),
        ToastType.UpdateError => "Błąd podczas aktualizacji danych".Tr(),
        ToastType.LoadSaleError => "Błąd pobierania promocji".Tr(),
        ToastType.NotImplemented => "Funkcja niezaimplementowana".Tr(),
        ToastType.LockOrderError => "Błąd podczas blokowania edycji zamówienia".Tr(),
        ToastType.AddOrderItemSuccess => "Dodano przedmiot do zamówienia".Tr(),
        ToastType.AddOrderItemError => "Błąd podczas dodawania przedmiotu do zamówienia".Tr(),
        _ => string.Empty
    };
}

public enum ToastType
{
    /// <summary>
    /// Błąd pobierania danych
    /// </summary>
    LoadDataError,
    
    /// <summary>
    /// Zapisano pomyślnie
    /// </summary>
    SaveSuccess,
    
    /// <summary>
    /// Błąd podczas zapisywania
    /// </summary>
    SaveError,
    
    /// <summary>
    /// Usunięto pomyślnie
    /// </summary>
    DeleteSuccess,
    
    /// <summary>
    /// Błąd podczas usuwania
    /// </summary>
    DeleteError,

    /// <summary>
    /// Błąd podczas tłumaczenia
    /// </summary>
    TranslateError,

    /// <summary>
    /// Tłumaczenie powiodło się
    /// </summary>
    TranslateSuccess,

    /// <summary>
    /// Dodano przedmiot do koszyka
    /// </summary>
    AddCartItemSuccess,

    /// <summary>
    /// Błąd podczas dodawania przedmiotu do koszyka
    /// </summary>
    AddCartItemError,

    /// <summary>
    /// Uaktualniono dane
    /// </summary>
    UpdateSuccess,

    /// <summary>
    /// Błąd podczas aktualizacji danych
    /// </summary>
    UpdateError,

    /// <summary>
    /// Błąd pobierania promocji
    /// </summary>
    LoadSaleError,

    /// <summary>
    /// Funkcja niezaimplementowana
    /// </summary>
    NotImplemented,
    
    /// <summary>
    /// Błąd podczas blokowania edycji zamówienia
    /// </summary>
    LockOrderError,

    /// <summary>
    /// Dodano przedmiot do zamówienia
    /// </summary>
    AddOrderItemSuccess,

    /// <summary>
    /// Błąd podczas dodawania przedmiotu do zamówienia
    /// </summary>
    AddOrderItemError,
}
