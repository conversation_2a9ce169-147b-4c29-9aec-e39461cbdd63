@using EMessa.Base.Enums
@using EMessa.Web.Constants
@using EMessa.Web.Extensions.EnumBadges
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor

@inject TooltipService tooltipService

<RadzenIcon Icon="@("\uf71e")"
            class="@ResolvedClass"
            MouseEnter="@(args => ShowTooltip(args) )" />

@code {
    /// <summary>
    /// Status, na podstawie którego przypisywany jest domyślny kolor ikony.
    /// </summary>
    [Parameter] public required RollStatus Status { get; set; }

    /// <summary>
    /// Dodatkowe klasy CSS dodawane do ikony.
    /// </summary>
    [Parameter] public string? Class { get; set; }

    private string ResolvedClass =>
        $"fas text-xl {Status.ColorCssClassFg()} {Class}";

    void ShowTooltip(ElementReference elementReference) =>
        tooltipService.Open(elementReference, Status.GetDisplayName().Tr(), new TooltipOptions() { Position = TooltipPosition.Right });
}
