@using <PERSON><PERSON><PERSON>
@using <PERSON><PERSON><PERSON>.Blazor
@typeparam TValue

<RadzenToggleButton @bind-Value="@InternalValue"
                    Text="@Text"
                    Icon="@GetIcon()"
                    Class="@GetCssClasses()"
                    Disabled="@Disabled"
                    Visible="@Visible"
                    ButtonStyle="@ButtonStyle"
                    ToggleButtonStyle="@ToggleButtonStyle"
                    Size="@Size"
                    Change="@HandleChange"
                    MouseEnter="@MouseEnter"
                    MouseLeave="@MouseLeave"
                    @attributes="AdditionalAttributes" />

@code {

    [Parameter]
    public bool IsAction { get; set; }

    [Parameter]
    public string Text { get; set; } = null!;

    [Parameter]
    public string Icon { get; set; } = "check_box_outline_blank";

    [Parameter]
    public string CheckedIcon { get; set; } = "check_box";

    [Parameter]
    public string UncheckedIcon { get; set; } = "check_box_outline_blank";

    [Parameter]
    public string IndeterminateIcon { get; set; } = "indeterminate_check_box";

    [Parameter]
    public bool Disabled { get; set; }

    [Parameter]
    public bool Visible { get; set; } = true;

    [Parameter]
    public ButtonStyle ButtonStyle { get; set; } = ButtonStyle.Light;

    [Parameter]
    public ButtonStyle ToggleButtonStyle { get; set; } = ButtonStyle.Success;

    [Parameter]
    public ButtonSize Size { get; set; } = ButtonSize.Medium;

    [Parameter]
    public EventCallback<bool> Change { get; set; }

    [Parameter]
    public EventCallback<ElementReference> MouseEnter { get; set; }

    [Parameter]
    public EventCallback<ElementReference> MouseLeave { get; set; }

    [Parameter]
    public RenderFragment? ChildContent { get; set; }

    [Parameter]
    public string? Class { get; set; }

    [Parameter]
    public TValue Value { get; set; }

    [Parameter]
    public EventCallback<TValue> ValueChanged { get; set; }

    [Parameter]
    public bool UseCheckboxIcons { get; set; } = true;

    [Parameter(CaptureUnmatchedValues = true)]
    public Dictionary<string, object> AdditionalAttributes { get; set; } = null!;

    private const string CssClass = "";

    private bool _internalValue;
    private bool? _internalNullableState;

    private bool InternalValue
    {
        get => _internalValue;
        set
        {
            if (_internalValue == value) return;
            _internalValue = value;
            UpdateValueFromInternalState();
        }
    }

    private string GetCssClasses()
    {
        return string.IsNullOrEmpty(Class) ? CssClass : $"{CssClass} {Class}";
    }

    private string GetIcon()
    {
        if (!UseCheckboxIcons)
        {
            return Icon;
        }

        if (Value == null)
        {
            return IndeterminateIcon;
        }

        if (Value is bool isBoolValue)
        {
            return isBoolValue ? CheckedIcon : UncheckedIcon;
        }

        // Próba konwersji dla innych typów
        try
        {
            var boolValue = Convert.ToBoolean(Value);
            return boolValue ? CheckedIcon : UncheckedIcon;
        }
        catch
        {
            return UncheckedIcon;
        }
    }

    private async Task HandleChange(bool value)
    {
        // Wykorzystaj zmianę wewnętrznej wartości do aktualizacji naszej wartości
        if (typeof(TValue) == typeof(bool?))
        {
            // Dla trybu trzystanowego, przełączamy między null -> true -> false -> null
            if (_internalNullableState == null)
            {
                _internalNullableState = true;
            }
            else if (_internalNullableState == true)
            {
                _internalNullableState = false;
            }
            else
            {
                _internalNullableState = null;
            }

            await ValueChanged.InvokeAsync((TValue)(object)_internalNullableState);
        }
        else
        {
            // Dla zwykłego bool lub innych typów, używamy przekonwertowanej wartości z toggle buttona
            await ValueChanged.InvokeAsync((TValue)(object)value);
        }

        if (Change.HasDelegate)
        {
            await Change.InvokeAsync(value);
        }
    }

    private void UpdateValueFromInternalState()
    {
        if (typeof(TValue) == typeof(bool?))
        {
            // W trybie trzystanowym nie używamy bezpośrednio InternalValue
            return;
        }

        if (Value is bool currentValue && currentValue != _internalValue)
        {
            _ = ValueChanged.InvokeAsync((TValue)(object)_internalValue);
        }
    }

    protected override void OnParametersSet()
    {
        // Aktualizuj wewnętrzny stan na podstawie Value
        if (Value == null)
        {
            _internalNullableState = null;
            _internalValue = false; // W trybie trzystanowym domyślnie ustawiamy button jako nie zaznaczony
        }
        else if (Value is bool isBoolValue)
        {
            _internalNullableState = isBoolValue;
            _internalValue = isBoolValue;
        }
        else
        {
            try
            {
                var boolValue = Convert.ToBoolean(Value);
                _internalNullableState = boolValue;
                _internalValue = boolValue;
            }
            catch
            {
                _internalNullableState = null;
                _internalValue = false;
            }
        }

        // Zastosuj dodatkowe dostosowania dla trybu akcji na końcu
        if (IsAction)
        {
            Text = null!;
            Size = ButtonSize.ExtraSmall;
        }
    }

}