@using <PERSON><PERSON><PERSON>
@using <PERSON><PERSON><PERSON>.Blazor

<RadzenButton Text="@Text"
              Icon="@Icon"
              Class="@CssClasses"
              Disabled="@Disabled"
              Visible="@Visible"
              ButtonStyle="@ButtonStyle"
              Size="@Size"
              Click="@Click"
              @attributes="AdditionalAttributes" />

@code {

    [Parameter]
    public bool IsAction { get; set; }

    [Parameter]
    public string Text { get; set; } = "Klonuj".Tr();

    [Parameter]
    public string Icon { get; set; } = "content_copy";

    [Parameter]
    public bool Disabled { get; set; }

    [Parameter]
    public bool Visible { get; set; } = true;

    [Parameter]
    public ButtonStyle ButtonStyle { get; set; } = ButtonStyle.Dark;

    [Parameter]
    public ButtonSize Size { get; set; } = ButtonSize.Medium;

    [Parameter]
    public EventCallback Click { get; set; }

    [Parameter]
    public RenderFragment? ChildContent { get; set; }

    [Parameter]
    public string? Class { get; set; }

    [Parameter(CaptureUnmatchedValues = true)]
    public Dictionary<string, object> AdditionalAttributes { get; set; } = null!;

    private const string CssClass = "bg-esa-add";

    private string CssClasses => string.IsNullOrEmpty(Class) ? CssClass : $"{CssClass} {Class}";

    protected override void OnParametersSet()
    {
        if (IsAction != true) return;
        Text = null!;
        Size = ButtonSize.ExtraSmall;
    }

}