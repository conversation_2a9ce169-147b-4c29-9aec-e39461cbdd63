@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor


<RadzenStack Orientation="Orientation.Horizontal"
             JustifyContent="JustifyContent.Start"
             AlignItems="AlignItems.Center"
             Wrap="FlexWrap.Wrap"
             Gap="0.5rem">
    @if (BeforeLabel != "")
    {
        <RadzenLabel Text="@BeforeLabel" Component="BoolValue" />
    }
    <RadzenCheckBox Value="@Value" ValueChanged="@ValueChanged" Name="BoolValue" Disabled="@Disabled" />
    <RadzenLabel Text="@Label" Component="BoolValue" />
</RadzenStack>

@code{

    [Parameter]
    public bool Value { get; set; }

    [Parameter]
    public EventCallback<bool> ValueChanged { get; set; }

    [Parameter]
    public string BeforeLabel { get; set; } = "";

    [Parameter]
    public string Label { get; set; } = "";

    [Parameter]
    public bool Disabled { get; set; }

}