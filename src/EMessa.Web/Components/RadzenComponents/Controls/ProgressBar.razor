@using <PERSON><PERSON><PERSON>
@using Ra<PERSON>zen.Blazor

<RadzenProgressBar Style="@Style"
                   Visible="@Visible"
                   ProgressBarStyle="@ProgressBarStyle"
                   Mode="@(IsLoading.HasValue ? ProgressBarMode.Indeterminate : ProgressBarMode.Determinate)"
                   Value="@(IsLoading == true ? 100 : IsLoading == false ? 0 : Value)" 
                   ShowValue="@ShowValue" />

@code {
    [Parameter]
    public string Style { get; set; } = "width: 100%; height: 5px;";

    [Parameter]
    public bool Visible { get; set; } = true;

    [Parameter]
    public ProgressBarStyle ProgressBarStyle { get; set; } = ProgressBarStyle.Primary;

    [Parameter]
    public ProgressBarMode Mode { get; set; } = ProgressBarMode.Indeterminate;

    [Parameter]
    public double Value { get; set; } = 100;

    [Parameter]
    public bool ShowValue { get; set; }
    
    [Parameter]
    public bool? IsLoading { get; set; }
}