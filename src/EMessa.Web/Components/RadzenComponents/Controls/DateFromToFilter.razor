@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor

@inject TooltipService TooltipService

<RadzenStack Orientation="Orientation.Horizontal" Gap="2px" Wrap="FlexWrap.Wrap">
    <RadzenStack Orientation="Orientation.Horizontal" Gap="1px">
        <RadzenButton Icon="chevron_backward"
                      Click="@(async () => await ShiftCreatedFrom(-1))"
                      Disabled="@(IsLoading || !DateFrom.HasValue)"
                      ButtonStyle="ButtonStyle.Light"
                      Size="ButtonSize.Small" />
        <RadzenDatePicker @bind-Value="DateFrom"
                          Placeholder="@("Od".Tr())"
                          ShowTime="false"
                          DateFormat="@DateFormatProvider.DisplayDateFormat"
                          AllowClear
                          Change="@(_ => NotifyChange())"
                          class="w-[130px]" 
                          MouseEnter="@((er) => TooltipService.Open(er, "Od tej daty (włącznie)".Tr()))" />
        <RadzenButton Icon="chevron_forward"
                      Click="@(async () => await ShiftCreatedFrom(1))"
                      Disabled="@(IsLoading || !DateFrom.HasValue)"
                      ButtonStyle="ButtonStyle.Light"
                      Size="ButtonSize.Small" />
    </RadzenStack>
    <RadzenStack Orientation="Orientation.Horizontal" Gap="1px">
        <RadzenButton Icon="chevron_backward"
                      Click="@(async () => await ShiftCreatedTo(-1))"
                      Disabled="@(IsLoading || !DateTo.HasValue)"
                      ButtonStyle="ButtonStyle.Light"
                      Size="ButtonSize.Small" />
        <RadzenDatePicker @bind-Value="DateTo"
                          Placeholder="@("Do".Tr())"
                          ShowTime="false"
                          DateFormat="@DateFormatProvider.DisplayDateFormat"
                          AllowClear
                          Change="@(_ => NotifyChange())"
                          class="w-[130px]" 
                          MouseEnter="@((er) => TooltipService.Open(er, "Do tej daty (włącznie)".Tr()))" />
        <RadzenButton Icon="chevron_forward"
                      Click="@(async () => await ShiftCreatedTo(1))"
                      Disabled="@(IsLoading || !DateTo.HasValue)"
                      ButtonStyle="ButtonStyle.Light"
                      Size="ButtonSize.Small" />
        <RadzenButton Icon="event_busy"
                      Click="@(async () => { DateFrom = null; DateTo = null; await NotifyChange(); })"
                      Disabled="@(IsLoading || (!DateFrom.HasValue && !DateTo.HasValue))"
                      ButtonStyle="ButtonStyle.Light"
                      Size="ButtonSize.Small" />
    </RadzenStack>
</RadzenStack>

@code {
    [Parameter] public bool IsLoading { get; set; } = false;
    [Parameter] public EventCallback<(DateTime? CreatedFrom, DateTime? CreatedTo)> OnChange { get; set; }
    [Parameter] public DateTime? DateFrom { get; set; } = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1, 0, 0, 0);
    [Parameter] public DateTime? DateTo { get; set; } = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.DaysInMonth(DateTime.Now.Year, DateTime.Now.Month), 23, 59, 59);
    [Parameter] public DateRangeOption DateRangePreset { get; set; } = DateRangeOption.None;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            SetDates();
            await NotifyChange();
        }

        await base.OnAfterRenderAsync(firstRender);
    }

    protected override async Task OnInitializedAsync()
    {
        // await base.OnInitializedAsync();
        // SetDates();
        // await NotifyChange();
    }

    private async Task ShiftCreatedFrom(int months)
    {
        if (!DateFrom.HasValue)
            return;

        DateFrom = DateFrom.Value.AddMonths(months);

        await NotifyChange();
    }

    private async Task ShiftCreatedTo(int months)
    {
        if (!DateTo.HasValue)
            return;

        DateTo = DateTo.Value.AddMonths(months);

        await NotifyChange();
    }

    private async Task NotifyChange()
    {
        if (OnChange.HasDelegate)
        {
            await OnChange.InvokeAsync((DateFrom, DateTo));
        }
    }

    private void SetDates()
    {
        var now = DateTime.Now;

        switch (DateRangePreset)
        {
            case DateRangeOption.CurrentMonth:
                DateFrom = new DateTime(now.Year, now.Month, 1, 0, 0, 0);
                DateTo = new DateTime(now.Year, now.Month, DateTime.DaysInMonth(now.Year, now.Month), 23, 59, 59);
                break;
            case DateRangeOption.Last30Days:
                var fromDate = now.AddDays(-30).Date;
                DateFrom = new DateTime(fromDate.Year, fromDate.Month, fromDate.Day, 0, 0, 0);
                DateTo = new DateTime(now.Year, now.Month, now.Day, 23, 59, 59);
                break;
            case DateRangeOption.None:
            default:
                break;
        }
    }

    public enum DateRangeOption
    {
        None,
        CurrentMonth,
        Last30Days
    }
}
