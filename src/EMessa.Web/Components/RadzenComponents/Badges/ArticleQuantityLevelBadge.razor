@using EMessa.Base.Enums
@using EMessa.Core.Features.Articles.Queries.GetArticleWarehouseStateForCustomer
@using EMessa.Web.Extensions.EnumBadges
@using EMessa.Web.Helpers
@using <PERSON><PERSON>zen
@using Radzen.Blazor

<RadzenBadge Variant="Variant.Text"
             BadgeStyle="@BadgeStyle.Base"
             class="@ResolvedClass"
             Text="@ResolvedText" />
@code {
    [Parameter] public required ArticleQuantityLevel Status { get; set; }
    [Parameter] public string? CssClass { get; set; }
    [Parameter] public string? AdditionalText { get; set; }

    private string ResolvedClass =>
        $"{ArticleQuantityLevelExtensions.ColorCssClassFg(Status)} {CssClass}";

    private string ResolvedText =>
        string.IsNullOrEmpty(AdditionalText)
            ? EnumDisplayHelper.GetEnumTrDisplayName<ArticleQuantityLevel>(Status)
            : $"{EnumDisplayHelper.GetEnumTrDisplayName<ArticleQuantityLevel>(Status)} {AdditionalText}";
}
