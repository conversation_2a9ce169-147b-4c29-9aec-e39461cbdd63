@using <PERSON><PERSON><PERSON>
@using <PERSON><PERSON>zen.Blazor

<RadzenBadge Text="@($"{Price:N2} {CurrencyCode}{Unit}")"
             BadgeStyle="BadgeStyle.Primary"
             Shade="Shade.Light"
             Variant="Variant.Outlined"
             class="@ResolvedClass" />

@code {
    [Parameter] public required decimal Price { get; set; }
    [Parameter] public required string CurrencyCode { get; set; }
    [Parameter] public string Unit { get; set; } = "/m2";
    [Parameter] public string? CssClass { get; set; }

    private string ResolvedClass =>
        $"text-sm normal-case {CssClass}";
}
