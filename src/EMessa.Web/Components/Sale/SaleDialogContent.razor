@using EMessa.Core.Features.Sales.Queries.CommonResponses
@using EMessa.Core.Features.Sales.Queries.GetSaleArticlesByArticleId
@using EMessa.Web.Constants
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor

@inject DialogService DialogService
@inject ILogger<SaleDialogContent> Logger

<style>
    .emessa > .no-header-grid .rz-data-grid-data thead {
        display: none !important;
    }

    .rz-dialog-content {
        height: 100%;
    }
</style>

<RadzenStack Orientation="Orientation.Vertical" JustifyContent="JustifyContent.SpaceBetween"
             Gap="1rem" class="h-full">
    <RadzenStack Orientation="Orientation.Vertical" Gap="0.5rem" class="flex-grow overflow-hidden pt-1">
        <GridToolbar>
            <RadzenText Text="@("Wybierz promocję".Tr())" TextStyle="TextStyle.Subtitle1" />
            <GridSearch @ref="_gridSearch" ValueChanged="@_grid.Reload" />
            <SaleColorSelector ArticleId="@ArticleId"
                               Value="@_activeColorFilter"
                               ValueChanged="@ColorChanged"
                               Label="@("Kolor".Tr())"
                               CssClass="w-52"
                               Clearable />
            <SaleThicknessSelector ArticleId="@ArticleId"
                                   Value="@_activeThicknessFilter"
                                   ValueChanged="@ThicknessChanged"
                                   Label="@("Grubość".Tr())"
                                   CssClass="w-52"
                                   Clearable />
        </GridToolbar>

        <ProgressBar IsLoading="@_isLoading" />

        <div class="emessa flex-grow overflow-hidden">
            <RadzenDataGrid @ref="_grid" TItem="SaleArticleResponse"
                            Data="@_gridData" Count="@_count" LoadData="@LoadData" IsLoading="@_isLoading"
                            AllowFiltering FilterMode="FilterMode.Simple"
                            FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                            PageSize="@PageSize.DefaultPageSize"
                            PageSizeOptions="@PageSize.Pages20To200"
                            PageSizeText="@PageSize.FormatPaginationInfo(_grid)"
                            AllowPaging AllowSorting AllowMultiColumnSorting AllowColumnResize
                            Density="Density.Compact"
                            Style="height: 100%; width: 100%;"
                            EmptyText="@RadzenDataGridConstants.EmptyText"
                            Class="no-header-grid">
                <LoadingTemplate />

                <Columns>
                    <RadzenDataGridColumn Filterable="false" Sortable="false">
                        <Template Context="saleArticle">
                            <SaleDialogCard
                                SaleArticle="@saleArticle"
                                OnSelectedSale="@OnSelected" />
                        </Template>
                    </RadzenDataGridColumn>
                    @* <RadzenDataGridColumn Visible="@(!AppStateService.IsLargeScreen)" Filterable="false" *@
                    @*                       Sortable="false"> *@
                    @*     <Template Context="saleArticle"> *@
                    @*         <span>Wąski</span> *@
                    @*     </Template> *@
                    @* </RadzenDataGridColumn> *@
                </Columns>
            </RadzenDataGrid>
        </div>
    </RadzenStack>
    <RadzenStack Visible="@ShowClose" Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.End"
                 Gap="0.5rem">
        <RadzenButton Text="@("Zamknij".Tr())"
                      Variant="Variant.Flat"
                      Style="width: 120px"
                      Click="@(_ => DialogService.Close())" />
    </RadzenStack>
</RadzenStack>

@code {
    [Parameter] public int ArticleId { get; set; }
    [Parameter] public bool ShowClose { get; set; } = true;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);
        if (firstRender)
        {
            await _grid.Reload();
        }
    }

    private RadzenDataGrid<SaleArticleResponse> _grid = null!;
    private IEnumerable<SaleArticleResponse> _gridData = [];
    private int _count;
    private bool _isLoading;
    private GridSearch _gridSearch = null!;
    private int? _activeColorFilter;
    private int? _activeThicknessFilter;

    private async Task LoadData(LoadDataArgs args)
    {
        _isLoading = true;

        try
        {
            args.Filters = args.Filters
                .AppendGlobalSearchFilter(_gridSearch.SearchValue)
                .AppendFilterWhenNoNull(nameof(SaleArticleResponse.ColorId), _activeColorFilter)
                .AppendFilterWhenNoNull(nameof(SaleArticleResponse.ThickId), _activeThicknessFilter);
            var sieveModel = args.ToSieveModel();

            var response = await Mediator.Send(new GetSaleArticlesByArticleIdQuery(ArticleId, sieveModel));
            if (response.Succeeded)
            {
                _gridData = response.Data;
                _count = response.TotalCount;
            }
            else
            {
                ToastService.Show(ToastType.LoadDataError);
                _gridData = [];
                _count = 0;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Błąd podczas ładowania danych promocji.");
            ToastService.Show(ToastType.LoadDataError);
            _gridData = [];
            _count = 0;
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private void ColorChanged(int? colorId)
    {
        _activeColorFilter = colorId;
        _grid.Reload();
    }

    private void ThicknessChanged(int? thickId)
    {
        _activeThicknessFilter = thickId;
        _grid.Reload();
    }

    private void OnSelected(SaleArticleResponse selectedSaleArticle)
    {
        DialogService.Close(selectedSaleArticle);
    }

}
