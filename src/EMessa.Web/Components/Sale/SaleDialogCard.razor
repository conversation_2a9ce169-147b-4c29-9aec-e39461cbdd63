@using EMessa.Core.Features.Sales.Queries.CommonResponses
@using EMessa.Core.Features.Sales.Queries.GetSaleImages
@using EMessa.Web.Components.RadzenComponents.Badges
@using EMessa.Web.Models
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor

@inject ILogger<SaleDialogCard> Logger
@inject TooltipService TooltipService

<RadzenRow Gap="1rem">
    @* Informacje o promocji *@
    <RadzenColumn Size="12" SizeSM="6" SizeMD="7" SizeLG="7">
        <RadzenStack Orientation="Orientation.Vertical"
                     JustifyContent="JustifyContent.Start"
                     AlignItems="AlignItems.Start"
                     Gap=".125rem">
            <div class="flex gap-2 items-start w-full max-w-full overflow-hidden">
                <AddRadzenButton Text="@("Wybierz".Tr())" Icon="@null" Click="@OnSelected" />
                <div class="flex-1 min-w-0 max-w-full overflow-hidden">
                    <span class="break-words break-all whitespace-normal max-w-full text-lg font-bold">
                        @SaleArticle.Name (@("nr".Tr()): @SaleArticle.Id)
                    </span>
                </div>
            </div>

            <RadzenStack Orientation="Orientation.Horizontal" Gap=".25rem" AlignItems="AlignItems.Center">
                <RadzenIcon Icon="info" class="text-sky-600" />
                <RadzenText TextStyle="TextStyle.Subtitle1" class="m-0 my-1">
                    @($"{SaleArticle.Coat.Value} - {SaleArticle.Color.Value} - {SaleArticle.Thick.Value}")
                </RadzenText>
            </RadzenStack>

            <RadzenStack Orientation="Orientation.Horizontal"
                         Gap=".5rem"
                         AlignItems="AlignItems.Center"
                         JustifyContent="JustifyContent.SpaceBetween"
                         Wrap="FlexWrap.Wrap"
                         class="w-full">
                <RadzenStack Orientation="Orientation.Horizontal" Gap=".25rem" AlignItems="AlignItems.Center">
                    <RadzenIcon Icon="calendar_month" class="text-sky-600" />
                    <RadzenText class="m-0">
                        @("Ważna od".Tr()):
                        <strong>@SaleArticle.DateFrom.ToDisplayLocalDate()</strong> @("do".Tr()):
                        <strong>@SaleArticle.DateTo.ToDisplayLocalDate()</strong>
                    </RadzenText>
                </RadzenStack>

                <RadzenStack Orientation="Orientation.Horizontal" Gap=".5rem" AlignItems="AlignItems.Center"
                             Wrap="FlexWrap.Wrap">
                    <RadzenStack Orientation="Orientation.Horizontal" Gap=".25rem" AlignItems="AlignItems.Center">
                        <RadzenText Text=@(SaleArticle.RollsCount.ToString()) class="m-0 font-bold"
                                    MouseEnter="@(er => TooltipService.Open(er, "Ilość kręgów".Tr()))" />
                        <RadzenIcon Icon="@("\uf71e")" class="fas text-[22px] mx-1 text-sky-600" />
                        <RadzenText Text=@($"{SaleArticle.Rolls.Sum(x => x.CurrentWeight):n0} kg")
                                    class="m-0 font-bold"
                                    MouseEnter="@(er => TooltipService.Open(er, "Aktualna waga kręgów - Messa".Tr()))" />
                    </RadzenStack>
                    <RadzenStack Orientation="Orientation.Horizontal" Gap=".25rem" AlignItems="AlignItems.Center">
                        <RadzenIcon Icon="balance" class="text-sky-600" />
                        <RadzenText Text=@($"{SaleArticle.AvailableWeight:n0} kg")
                                    class="m-0 font-bold"
                                    Style="@(SaleArticle.AvailableWeight < 0 ? "color: red;" : "color: green;")"
                                    MouseEnter="@(er => TooltipService.Open(er, "Waga pozostała w promocji".Tr()))" />
                        <RadzenText Text=@($" ({SaleArticle.DefinedWeight:n0} kg)")
                                    class="m-0"
                                    MouseEnter="@(er => TooltipService.Open(er, "Waga zdefiniowana w promocji".Tr()))" />

                    </RadzenStack>
                    <RadzenStack Orientation="Orientation.Horizontal" Gap=".25rem" AlignItems="AlignItems.Center"
                                 MouseEnter="@(er => TooltipService.Open(er, "Waga (ilość) zamówień".Tr()))">
                        <RadzenIcon Icon="water" class="text-sky-600" />
                        <RadzenText
                            Text=@($"{SaleArticle.OrderItems.Sum(x => x.OrderItemWeight):n0} kg ({SaleArticle.OrderItemsCount})")
                            class="m-0" />
                    </RadzenStack>
                </RadzenStack>
            </RadzenStack>

            <RadzenTextArea Value="@(SaleArticle.Description)"
                            Rows="3" ReadOnly
                            class="w-full bg-esa-disabled" />
        </RadzenStack>
    </RadzenColumn>

    @* Ceny *@
    <RadzenColumn Size="12" SizeSM="6" SizeMD="2" SizeLG="2">
        <RadzenStack Orientation="Orientation.Vertical"
                     AlignItems="AlignItems.Start"
                     class="@(AppStateService.IsLargeScreen ? "pl-4" : "")"
                     Gap=".5rem">
                <span class="break-words break-all whitespace-normal max-w-full text-lg font-bold">
                    @("Ceny promocji".Tr())
                </span>
            @foreach (var price in SaleArticle.Prices)
            {
                <PriceBadge Price="@price.Price" CurrencyCode="@price.CurrencyCode" />
            }
        </RadzenStack>
    </RadzenColumn>

    @* Karuzela *@
    <RadzenColumn Size="12" SizeSM="6" SizeMD="3" SizeLG="3">
        <ProgressBar IsLoading="@_isLoading" Visible="@_isLoading" />
        @if (_images.Any())
        {
            <RadzenCarousel Auto="false" AllowNavigation="true"
                            ButtonStyle="ButtonStyle.Light" ButtonVariant="Variant.Filled"
                            ButtonShade="Shade.Light" ButtonSize="ButtonSize.Large"
                            AllowPaging="true" PagerOverlay="true" PagerPosition="PagerPosition.Bottom"
                            Style="height: 200px; max-width: 100%;" class="rz-mx-auto bg-black">
                <Items>
                    @foreach (var image in _images)
                    {
                        <RadzenCarouselItem>
                            <div style="aspect-ratio: 16/9; overflow: hidden; ">
                                <RadzenImage Path="@image.PreviewUrl" target="_blank"
                                             MouseEnter="@(er => TooltipService.Open(er, image.FileName))"
                                             Style="width: 100%; height: 100%; object-fit: scale-down;" />
                            </div>

                        </RadzenCarouselItem>
                    }
                </Items>
            </RadzenCarousel>
        }
        else if (!_isLoading)
        {
            <RadzenStack class="p-2">
                <RadzenAlert Text="@("Brak zdjęć dla tej promocji.".Tr())"
                             AlertStyle="AlertStyle.Light" AllowClose="false"
                             Size="AlertSize.Large"
                             class="p-2 w-full" />
            </RadzenStack>
        }
    </RadzenColumn>
</RadzenRow>

@code {

    [Parameter] public required SaleArticleResponse SaleArticle { get; set; }

    [Parameter] public EventCallback<SaleArticleResponse> OnSelectedSale { get; set; }

    private bool _isLoading;
    private List<GalleryImage> _images = [];

    protected override async Task OnInitializedAsync()
    {
        await LoadImages();
    }

    private async Task LoadImages()
    {
        _isLoading = true;

        var result = await Mediator.Send(new GetSaleImagesQuery(SaleArticle.SaleId));

        if (result.Succeeded)
        {
            _images = result.Data
                .Where(x => x.FileContent is not null)
                .Select(x => new GalleryImage
                {
                    Id = x.Id,
                    FileName = x.FileName,
                    PreviewUrl = $"data:image/{x.FileExtension};base64,{Convert.ToBase64String(x.FileContent!)}"
                }).ToList();
        }
        else
        {
            Logger.LogError("Failed to add sale images: {Messages}", string.Join(", ", result.Messages));
            ToastService.Show(ToastType.LoadDataError, result.Messages);
        }

        await InvokeAsync(StateHasChanged);
        _isLoading = false;
    }

    private async Task OnSelected()
    {
        await OnSelectedSale.InvokeAsync(SaleArticle);
    }

}
