using EMessa.Core.Features.CustomerLocalizations.Queries.Get;
using FluentValidation;

namespace EMessa.Web.Components
{
    public class GetCustomerLocalizationsResponseValidator : AbstractValidator<GetCustomerLocalizationsResponse>
    {
        public GetCustomerLocalizationsResponseValidator()
        {
            RuleFor(x => x.Address)
                .NotEmpty().WithMessage("Pole Adres jest wymagane.")
                .MaximumLength(256).WithMessage("Pole Adres może mieć maks. długość 256 znaków.");

            RuleFor(x => x.PostCode)
                .NotEmpty().WithMessage("Pole Kod poczt. jest wymagane.")
                .MaximumLength(20).WithMessage("Pole Kod poczt. może mieć maks. długość 20 znaków.");

            RuleFor(x => x.City)
                .NotEmpty().WithMessage("Pole Miasto jest wymagane.")
                .MaximumLength(120).WithMessage("Pole Miasto może mieć maks. długość 120 znaków.");
        }
    }
}