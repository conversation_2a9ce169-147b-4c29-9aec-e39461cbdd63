@using <PERSON><PERSON><PERSON>
@using <PERSON><PERSON><PERSON>.Blazor

@typeparam TItem
@inherits RadzenDataGridColumn<TItem>

@code {

    [Parameter]
    public bool IsLargeScreen { get; set; } = true;

    [Parameter]
    public string LargeWidth { get; set; } = "100px";

    [Parameter]
    public string TightWidth { get; set; } = "30px";

    [Parameter]
    public RenderFragment<TItem>? ContentTemplate { get; set; }

    [Parameter]
    public Func<TItem, bool>? VisibilityFunc { get; set; }

    protected override void OnParametersSet()
    {
        base.OnParametersSet();

        Title = IsLargeScreen ? "Akcje".Tr() : "";
        TextAlign = TextAlign.Center;
        Filterable = false;
        Sortable = false;
        Frozen = true;
        FrozenPosition = FrozenColumnPosition.Left;
        if (string.IsNullOrEmpty(Width))
        {
            Width = IsLargeScreen ? LargeWidth : TightWidth;
        }

        if (ContentTemplate != null && Visible)
        {
            Template = (dataContext) =>
            {
                if (VisibilityFunc != null && !VisibilityFunc(dataContext))
                {
                    return null!;
                }

                return @<div class="@(IsLargeScreen ? "esa-actions-row" : "esa-actions-col")">
                    @ContentTemplate(dataContext)
                </div>;
            };
        }
        else
        {
            Template = null;
        }
    }

}