@using EMessa.Core.Features.ArticleCategory.Queries.GetArticlesForCategoryAndDependent
@using EMessa.Core.Features.OrderItems.Commands.ValidateSheet
@using EMessa.Core.Features.ShoppingCart.Models
@using EMessa.Web.Pages.Catalog.Parts
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor
@using Scriban

<div class="w-full flex flex-col gap-2 @CssClass">
    <h1 class="font-bold">@Title</h1>
    <div class="flex flex-col gap-y-2">
        @{ var key = 0; }
        @if (OriginalSize != null)
        {
            <div class="bg-orange-50 flex flex-col p-2 gap-y-2 border-2 border-gray-300 rounded @(!DeformationsAccepted ? "opacity-70" : "")">
                <ProposedSheetsList Label="@("Wymiar oryginalny".Tr())"
                                    ProposedSheets="@OriginalSize.ProposedSheets"
                                    TotalLength="OriginalSize.TotalLength" />
                <div class="flex flex-col bg-red-50 p-2 gap-y-1 border-2 border-gray-300 rounded">
                    <div class="text-red-800">
                        <span>@((MarkupString)RenderTemplate())</span>
                    </div>
                    <RadzenCheckBox @bind-Value="DeformationsAccepted" />
                    <RadzenLabel Text="@("Akceptuję powyższe oświadczenie".Tr())" Component="DeformationsAccepted" />
                </div>
                <RadzenButton Disabled="@(!DeformationsAccepted)"
                              Click="@(() => AddProposedSheetsHandler(OriginalSize.ProposedSheets))">
                    @("Wybierz oryginalny".Tr())
                </RadzenButton>
            </div>
        }

        @foreach (var validation in ValidationErrors)
        {
            <div @key="@(key++)"
                 class="bg-white flex flex-col p-2 gap-y-2 border-2 border-gray-300 rounded @(DeformationsAccepted ? "opacity-70" : "")">
                <span>@validation.Message</span>
                @if (validation.ProposedSheets.Count > 0)
                {
                    <ProposedSheetsList Label="@("Proponowany podział arkusza".Tr())"
                                        ProposedSheets="@validation.ProposedSheets"
                                        TotalLength="validation.TotalLength" />
                    <RadzenButton Disabled="@DeformationsAccepted"
                                  Click="@(() => AddProposedSheetsHandler(validation.ProposedSheets))"
                                  ButtonStyle="ButtonStyle.Primary"
                                  Size="ButtonSize.Medium"
                                  Text="@("Wybierz propozycję".Tr())" />
                }
                else
                {
                    <RadzenButton Disabled="@DeformationsAccepted"
                                  Click="@CancelHandler"
                                  ButtonStyle="ButtonStyle.Secondary"
                                  Size="ButtonSize.Medium"
                                  Text="@("Anuluj".Tr())" />
                }
            </div>
        }

        @foreach (var validation in ValidationWarnings)
        {
            <div @key="@(key++)"
                 class="bg-orange-50 flex flex-col p-2 gap-y-2 border-2 border-gray-300 rounded @(DeformationsAccepted ? "opacity-70" : "")">
                <span>@validation.Message</span>
                @if (validation.ProposedSheets.Count > 0)
                {
                    <ProposedSheetsList Label="@("Proponowany podział arkusza".Tr())"
                                        ProposedSheets="@validation.ProposedSheets"
                                        TotalLength="validation.TotalLength" />
                    @if (validation.EmbossZoneError)
                    {
                        <div class="text-red-500">
                            <span>@("Ze względów technologicznych długość ostatniego arkusza została dostosowana, by uniknąć cięcia w strefie przetłoczenia.".Tr())</span>
                        </div>
                    }

                    <RadzenButton Disabled="@(DeformationsAccepted)"
                                  Click="@(() => AddProposedSheetsHandler(validation.ProposedSheets))">
                        @("Wybierz propozycję".Tr())
                    </RadzenButton>
                }
                else
                {
                    <RadzenButton Disabled="@(DeformationsAccepted)"
                                  Click="@CancelHandler"
                                  ButtonStyle="ButtonStyle.Secondary"
                                  Size="ButtonSize.Medium"
                                  Text="@("Anuluj".Tr())" />
                }
            </div>
        }
    </div>
    <hr />
    <RadzenButton Text="@("Anuluj".Tr())"
                  ButtonStyle="ButtonStyle.Secondary"
                  Size="ButtonSize.Medium"
                  Class="w-32"
                  Click="@CancelHandler" />
</div>

@code {

    [Parameter]
    public required OrderItemEditArticleModel Article { get; set; } = new();
    
    [Parameter]
    public required string Title { get; set; }

    [Parameter]
    public required ArticleSheetValidationModel? SheetValidationModel { get; set; }

    [Parameter]
    public required ArticleSheetValidationResult? OriginalSize { get; set; }

    [Parameter]
    public List<ArticleSheetValidationResult> ValidationErrors { get; set; } = [];

    [Parameter]
    public List<ArticleSheetValidationResult> ValidationWarnings { get; set; } = [];

    [Parameter]
    public EventCallback<List<ArticleSheetEditModel>> OnAddProposedSheets { get; set; }

    [Parameter]
    public EventCallback OnCancel { get; set; }

    [Parameter]
    public string CssClass { get; set; } = "";

    private bool DeformationsAccepted { get; set; } = false;
    
    const string TooLongSheetMessage = "Oświadczam, że zostałem poinformowany o możliwości wystąpienia uszkodzeń mechanicznych objawiających się między innymi deformacją modułów blachodachówki (tj. wyciąganie się, nie składanie się arkuszy blachy lub ewentualne złamanie się palety), powstałych w wyniku transportu i rozładunku produktu blachodachówki o długości arkusza przekraczającej długość podziału {{dlugosc_podzialu}} mb, a także podczas montażu w/w blachy.";

    protected override void OnParametersSet()
    {
        base.OnParametersSet();
        DeformationsAccepted = false;
    }

    private string RenderTemplate()
    {
        var name = Article.Name ?? "";
        var splitLength = SheetValidationModel?.SplitLength ?? 0;
        var model = new
        {
            Nazwa = StrongIt(name),
            DlugoscPodzialu = StrongIt(splitLength.ToString("N3")),
        };

        var tooLongSheetMessage = !string.IsNullOrEmpty(Article.TooLongSheet)
            ? Article.TooLongSheet
            : TooLongSheetMessage;
        var template = Template.Parse(tooLongSheetMessage);
        return template.Render(model);
        
        string StrongIt(string text) => $"<strong>{text}</strong>";
    }

    private async Task AddProposedSheetsHandler(List<ArticleSheetEditModel> sheets)
    {
        await OnAddProposedSheets.InvokeAsync(sheets);
    }

    private async Task CancelHandler()
    {
        await OnCancel.InvokeAsync();
    }
    
}