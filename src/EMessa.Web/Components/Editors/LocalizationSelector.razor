@using System.Linq.Expressions
@using EMessa.Core.Features.CustomerLocalizations.Queries.Get
@using Ra<PERSON>zen
@using Radzen.Blazor

@namespace EMessa.Web.Components.Editors
@typeparam TValue

<RadzenDropDown TValue="GetCustomerLocalizationsResponse"
                Placeholder="@("Wybierz".Tr())"
                @bind-SearchText=@_searchText
                @bind-Value=@_selectedValue
                SelectedItemChanged="@(async value => await SetSelectedValueAsync(value as GetCustomerLocalizationsResponse))"
                Data=@_localizationsFiltered
                LoadData=@OnLoadData
                AllowFiltering
                class="w-full">
    <Template>
        @if (context is GetCustomerLocalizationsResponse localization)
        {
            @localization.Name
            if (!localization.IsActive)
            {
                <RadzenBadge BadgeStyle="BadgeStyle.Danger" Variant="Variant.Outlined" Text="@("Nieaktywny".Tr())"
                             class="ml-2" />
            }
        }
    </Template>
    <ValueTemplate>
        @if (context is GetCustomerLocalizationsResponse localization)
        {
            @localization.Name
            if (!localization.IsActive)
            {
                <RadzenBadge BadgeStyle="BadgeStyle.Danger" Variant="Variant.Outlined" Text="@("Nieaktywny".Tr())"
                             class="ml-2" />
            }
        }
    </ValueTemplate>
</RadzenDropDown>

@code {
    [CascadingParameter] public EditContext? EditContext { get; set; }
    [Parameter] public Expression<Func<object>>? For { get; set; }
    [Parameter] public TValue? Value { get; set; }
    [Parameter] public EventCallback<TValue?> ValueChanged { get; set; }
    [Parameter] public EventCallback<GetCustomerLocalizationsResponse?> Selected { get; set; }
    [Parameter] public int? CustomerId { get; set; }
    [Parameter] public bool ShowActiveOnly { get; set; } = true;
    [Parameter] public bool AutoSelect { get; set; }
    [Parameter] public bool TakeFirst { get; set; }

    /// <summary>
    /// Podczas edycji - jeśli wybrany element jest nieaktywny, to i tak będzie widoczny jako wybrany, ale nie będzie go w liście do ponownego wyboru.
    /// </summary>
    [Parameter]
    public bool IncludeSelectedIfInactive { get; set; } = true;

    private GetCustomerLocalizationsResponse? _selectedValue;
    private string _searchText = string.Empty;
    private IEnumerable<GetCustomerLocalizationsResponse> _localizations = [];
    private IEnumerable<GetCustomerLocalizationsResponse> _localizationsFiltered = [];
    private TValue? _value;
    private int? _customerId;

    private void OnLoadData(LoadDataArgs args)
    {
        var filter = args.Filter?.ToLowerInvariant() ?? string.Empty;

        _localizationsFiltered = string.IsNullOrEmpty(filter)
            ? _localizations.ToList()
            : _localizations.Where(x =>
                    (x.Name?.ToLowerInvariant().Contains(filter) ?? false)
                    || (x.City?.ToLowerInvariant().Contains(filter) ?? false))
                .ToList();

        InvokeAsync(StateHasChanged);
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        if (CustomerId.HasValue)
        {
            await LoadLocalizations();
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        if (!EqualityComparer<TValue?>.Default.Equals(_value, Value))
        {
            _value = Value;
            await SetSelectedValueAsync();
        }

        if (_customerId != CustomerId)
        {
            _customerId = CustomerId;
            if (CustomerId.HasValue)
            {
                await LoadLocalizations();
            }
            else
            {
                _localizations = [];
                _localizationsFiltered = [];
                _selectedValue = null;
                _value = default;
                await ValueChanged.InvokeAsync(_value);
                await Selected.InvokeAsync(_selectedValue);
            }
        }
    }

    private async Task LoadLocalizations()
    {
        if (!CustomerId.HasValue) return;

        var request = new GetCustomerLocalizationsQuery(CustomerId.Value);

        if (ShowActiveOnly && !IncludeSelectedIfInactive)
            request.SieveModel.Filters = "IsActive==true";

        var response = await Mediator.Send(request);

        if (response.Succeeded)
        {
            _localizations = response.Data.ToList();
            SetDropDownItems();

            if (AutoSelect && Value == null && _localizations.Any())
            {
                // TakeFirst==true - zawsze
                if (TakeFirst || _localizations.Count() == 1)
                {
                    var defaultLocalization = _localizations.FirstOrDefault(x => x.IsDefault);
                    await SetSelectedValueAsync(defaultLocalization);
                }
            }
            else
            {
                await SetSelectedValueAsync();
            }
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError, "Błąd pobrania lokalizacji".Tr());
        }
    }

    private async Task SetSelectedValueAsync()
    {
        if (!_localizations.Any() || _value is not int intValue)
            return;

        var selected = _localizations.FirstOrDefault(x => x.Id == intValue);
        if (selected != null)
        {
            await SetSelectedValueAsync(selected);
        }
    }


    private async Task SetSelectedValueAsync(GetCustomerLocalizationsResponse? value)
    {
        _selectedValue = value;
        _value = _selectedValue != null ? (TValue)(object)_selectedValue.Id : default;
        _searchText = string.Empty;
        SetDropDownItems();

        await ValueChanged.InvokeAsync(_value);
        await Selected.InvokeAsync(_selectedValue);

        if (EditContext != null && For != null)
        {
            EditContext.NotifyFieldChanged(FieldIdentifier.Create(For));
        }

        await InvokeAsync(StateHasChanged);
    }

    private void SetDropDownItems()
    {
        _localizationsFiltered = ShowActiveOnly
            ? _localizations.Where(x => x.IsActive).ToList()
            : _localizations.ToList();
    }

} 