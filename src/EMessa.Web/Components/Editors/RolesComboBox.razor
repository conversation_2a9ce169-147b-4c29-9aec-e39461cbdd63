@attribute [Obsolete($"This component is deprecated (SF Component).")]

@typeparam TValue
@using System.Linq.Expressions;
@using EMessa.Core.Features.Roles.GetAll
@using Syncfusion.Blazor.DropDowns;

<SfComboBox @ref="_editor"
            Value="@Value"
            ValueChanged="@ChangeValue"
            ValueExpression="@ValueExpression"
            TValue="TValue"
            TItem="RoleComboBoxItem"
            ShowClearButton="true"
            Placeholder="@("Wybierz".Tr())"
            DataSource="@_roles"
            PopupWidth="auto">
    <ComboBoxFieldSettings Text="@nameof(RoleComboBoxItem.DisplayName)" Value="@nameof(RoleComboBoxItem.Name)" />
</SfComboBox>
<AppToast @ref="_toast"></AppToast>

@code
{
    [Parameter]
    public TValue Value
    {
        get => _value;
        set
        {
            if (!HasValueChanged(value, _value)) return;
            _value = value;
            ValueChanged.InvokeAsync(value);
        }
    }

    [Parameter]
    public EventCallback<TValue> ValueChanged { get; set; }

    [Parameter]
    public Expression<Func<TValue>>? ValueExpression { get; set; }

    [Parameter]
    public bool ShowActiveOnly { get; set; } = true;

    private TValue _value = default!;

    SfComboBox<TValue, RoleComboBoxItem>? _editor;
    private AppToast _toast = new();

    private List<RoleComboBoxItem> _roles = [];

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        var result = await Mediator.Send(new GetAllRolesQuery());

        if (result.Succeeded)
        {
            _roles = result.Data.Select(x => new RoleComboBoxItem(x.Name)).ToList();
        }
        else
        {
            await _toast.ShowError("Błąd pobrania ról".Tr());
        }
    }

    private bool HasValueChanged(TValue newValue, TValue currentValue)
    {
        if (newValue == null && currentValue == null)
            return false;

        if (newValue == null || currentValue == null)
            return true;

        return !newValue.Equals(currentValue);
    }


    private void ChangeValue(TValue val)
    {
        Value = val;
    }

    private class RoleComboBoxItem(string? name)
    {
        public string? Name { get; set; } = name;
        public string DisplayName => Name.Tr();
    }
}