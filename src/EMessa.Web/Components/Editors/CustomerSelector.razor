@using System.Linq.Expressions
@using EMessa.Core.Features.Customers.Queries.GetAll
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor

@namespace EMessa.Web.Components.Editors
@typeparam TValue

<RadzenDropDown TValue="GetAllCustomersResponse"
                Placeholder="@("Wybierz".Tr())"
                @bind-SearchText=@_searchText
                @bind-Value=@_selectedValue
                SelectedItemChanged="@(async value => await SetSelectedValueAsync(value as GetAllCustomersResponse))"
                Data="@_customerFiltered"
                LoadData="@OnLoadData"
                AllowFiltering
                class="w-full">
    <Template>
        @if (context is GetAllCustomersResponse customer)
        {
            @customer.Name
            if (!customer.IsActive)
            {
                <RadzenBadge BadgeStyle="BadgeStyle.Danger" Variant="Variant.Outlined" Text="@("Nieaktywny".Tr())"
                             class="ml-2" />
            }
        }
    </Template>
    <ValueTemplate>
        @if (context is GetAllCustomersResponse customer)
        {
            @customer.Name
            if (!customer.IsActive)
            {
                <RadzenBadge BadgeStyle="BadgeStyle.Danger" Variant="Variant.Outlined" Text="@("Nieaktywny".Tr())"
                             class="ml-2" />
            }
        }
    </ValueTemplate>
</RadzenDropDown>

@code {
    [CascadingParameter] public EditContext? EditContext { get; set; }
    [Parameter] public Expression<Func<object>>? For { get; set; }
    [Parameter] public TValue? Value { get; set; }
    [Parameter] public EventCallback<TValue?> ValueChanged { get; set; }
    [Parameter] public EventCallback<GetAllCustomersResponse?> Selected { get; set; }
    [Parameter] public bool ShowActiveOnly { get; set; } = true;
    [Parameter] public bool AutoSelect { get; set; }
    [Parameter] public bool TakeFirst { get; set; }

    /// <summary>
    /// Podczas edycji - jeśli wybrany element jest nieaktywny, to i tak będzie widoczny jako wybrany, ale nie będzie go w liście do ponownego wyboru.
    /// </summary>
    [Parameter]
    public bool IncludeSelectedIfInactive { get; set; } = true;

    private GetAllCustomersResponse? _selectedValue;
    private string _searchText = string.Empty;
    private IEnumerable<GetAllCustomersResponse> _customers = [];
    private IEnumerable<GetAllCustomersResponse> _customerFiltered = [];
    private TValue? _value;

    private void OnLoadData(LoadDataArgs args)
    {
        var filter = args.Filter?.ToLowerInvariant() ?? string.Empty;

        _customerFiltered = string.IsNullOrEmpty(filter)
            ? _customers.ToList()
            : _customers.Where(x =>
                    (x.ShortName?.ToLowerInvariant().Contains(filter) ?? false)
                    || (x.Name?.ToLowerInvariant().Contains(filter) ?? false))
                .ToList();

        InvokeAsync(StateHasChanged);
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        await GetAllCustomers();
    }

    private async Task GetAllCustomers()
    {
        var request = new GetAllCustomersQuery();

        if (ShowActiveOnly && !IncludeSelectedIfInactive)
            request.SieveModel.Filters = "IsActive==true";

        var response = await Mediator.Send(request);

        if (response.Succeeded)
        {
            _customers = response.Data.ToList();
            SetDropDownItems();

            if (AutoSelect && Value == null && _customers.Any())
            {
                // TakeFirst==true - zawsze
                if (TakeFirst || _customers.Count() == 1)
                {
                    await SetSelectedValueAsync(_customers.FirstOrDefault());
                }
            }
            else
            {
                await SetSelectedValueAsync();
            }
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError, "Błąd pobrania klientów".Tr());
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        if (!EqualityComparer<TValue?>.Default.Equals(_value, Value))
        {
            _value = Value;
            await SetSelectedValueAsync();
        }
    }

    private async Task SetSelectedValueAsync()
    {
        if (!_customers.Any() || _value is not int intValue)
            return;

        var selected = _customers.FirstOrDefault(x => x.Id == intValue);

        if (selected != null)
        {
            await SetSelectedValueAsync(selected);
        }
    }

    private async Task SetSelectedValueAsync(GetAllCustomersResponse? value)
    {
        _selectedValue = value;
        _value = _selectedValue != null ? (TValue)(object)_selectedValue.Id : default;
        _searchText = string.Empty;
        SetDropDownItems();

        await ValueChanged.InvokeAsync(_value);
        await Selected.InvokeAsync(_selectedValue);

        if (EditContext != null && For != null)
        {
            EditContext.NotifyFieldChanged(FieldIdentifier.Create(For));
        }

        await InvokeAsync(StateHasChanged);
    }

    private void SetDropDownItems()
    {
        _customerFiltered = ShowActiveOnly
            ? _customers.Where(x => x.IsActive).ToList()
            : _customers.ToList();
    }

}
