@using System.Linq.Expressions;
@using EMessa.Core.Features.Branches.Queries.GetAll
@using Ra<PERSON><PERSON>
@using Radzen.Blazor

@namespace EMessa.Web.Components.Editors
@typeparam TValue

<RadzenDropDown TValue="GetAllBranchesResponse"
                Placeholder="@Placeholder"
                @bind-SearchText="@_searchText"
                Value="@_selectedValue"
                SelectedItemChanged="@(async value => await SetSelectedValueAsync(value as GetAllBranchesResponse))"
                Data="@_branchesFiltered"
                LoadData="@OnLoadData"
                AllowFiltering
                AllowClear="@Clearable"
                class="@CssClass">
    <Template>
        @if (context is GetAllBranchesResponse branch)
        {
            @branch.Name
            if (!branch.IsActive)
            {
                <RadzenBadge BadgeStyle="BadgeStyle.Danger" Variant="Variant.Outlined" Text="@("Nieaktywny".Tr())"
                             class="ml-2"/>
            }
        }
    </Template>
    <ValueTemplate>
        @if (context is GetAllBranchesResponse branch)
        {
            @branch.Name
            if (!branch.IsActive)
            {
                <RadzenBadge BadgeStyle="BadgeStyle.Danger" Variant="Variant.Outlined" Text="@("Nieaktywny".Tr())"
                             class="ml-2"/>
            }
        }
    </ValueTemplate>
</RadzenDropDown>

@code {
    [CascadingParameter] public EditContext? EditContext { get; set; }
    [Parameter] public Expression<Func<object>>? For { get; set; }
    [Parameter] public TValue? Value { get; set; }
    [Parameter] public EventCallback<TValue?> ValueChanged { get; set; }
    [Parameter] public bool ShowActiveOnly { get; set; } = true;

    /// <summary>
    /// Podczas edycji - jeśli wybrany element jest nieaktywny, to i tak będzie widoczny jako wybrany, ale nie będzie go w liście do ponownego wyboru.
    /// </summary>
    [Parameter]
    public bool IncludeSelectedIfInactive { get; set; } = true;

    /// <summary>
    /// Czy pole ma mieć możliwość czyszczenia - domyślnie false.
    /// </summary>
    [Parameter]
    public bool Clearable { get; set; }

    [Parameter] public string Placeholder { get; set; } = "Wybierz".Tr();
    [Parameter] public string CssClass { get; set; } = "w-full";

    private GetAllBranchesResponse? _selectedValue;
    private string _searchText = string.Empty;
    private IEnumerable<GetAllBranchesResponse> _branches = [];
    private IEnumerable<GetAllBranchesResponse> _branchesFiltered = [];
    private TValue? _value;

    private void OnLoadData(LoadDataArgs args)
    {
        var filter = args.Filter?.ToLowerInvariant() ?? string.Empty;

        _branchesFiltered = string.IsNullOrEmpty(filter)
            ? _branches.ToList()
            : _branches.Where(x =>
                    (x.Name?.ToLowerInvariant().Contains(filter) ?? false)
                    || (x.Code?.ToLowerInvariant().Contains(filter) ?? false))
                .ToList();

        InvokeAsync(StateHasChanged);
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        var request = new GetAllBranchesQuery();

        if (ShowActiveOnly && !IncludeSelectedIfInactive)
            request.SieveModel.Filters = "IsActive==true";

        var response = await Mediator.Send(request);

        if (response.Succeeded)
        {
            _branches = response.Data.ToList();
            SetDropDownItems();
            await SetSelectedValueAsync();
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError, "Błąd pobrania zakładów".Tr());
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        if (!EqualityComparer<TValue?>.Default.Equals(_value, Value))
        {
            _value = Value;
            await SetSelectedValueAsync();
        }
    }

    private async Task SetSelectedValueAsync()
    {
        if (_branches == null || !_branches.Any() || _value == null)
            return;

        var targetType = Nullable.GetUnderlyingType(typeof(TValue)) ?? typeof(TValue);

        if (targetType == typeof(int))
        {
            var intValue = Convert.ToInt32(_value);
            var selected = _branches.FirstOrDefault(x => x.Id == intValue);

            if (selected != null)
            {
                await SetSelectedValueAsync(selected);
            }
        }
    }

    private async Task SetSelectedValueAsync(GetAllBranchesResponse? value)
    {
        _selectedValue = value;
        _value = _selectedValue != null ? (TValue)(object)_selectedValue.Id : default;
        _searchText = string.Empty;

        SetDropDownItems();

        await ValueChanged.InvokeAsync(_value);

        if (EditContext != null && For != null)
        {
            EditContext.NotifyFieldChanged(FieldIdentifier.Create(For));
        }

        await InvokeAsync(StateHasChanged);
    }

    private void SetDropDownItems()
    {
        _branchesFiltered = ShowActiveOnly
            ? _branches.Where(x => x.IsActive).ToList()
            : _branches.ToList();
    }

}
