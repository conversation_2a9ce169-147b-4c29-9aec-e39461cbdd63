@namespace EMessa.Web.Components.Editors
@using System.Linq.Expressions;
@using EMessa.Core.Features.Factories.Queries.GetAll
@using Ra<PERSON>zen
@using Radzen.Blazor
@typeparam TValue

<RadzenDropDown TValue="GetAllFactoriesResponse"
                Placeholder="@("Wybierz".Tr())"
                @bind-SearchText=@_searchText
                Value="@_selectedValue"
                ValueChanged="@(async (value) => await SetSelectedValueAsync(value as GetAllFactoriesResponse))"
                Data=@_factoriesFiltered
                LoadData=@OnLoadData
                AllowFiltering
                AllowClear="@Clearable"
                class="w-full">
    <Template>
        @((context as GetAllFactoriesResponse)?.Name)
        @if ((context as GetAllFactoriesResponse)?.IsActive == false)
        {
            <RadzenBadge BadgeStyle="BadgeStyle.Danger" Variant="Variant.Outlined" Text="@("Nieaktywny".Tr())"
                         class="ml-2" />
        }
    </Template>
    <ValueTemplate>
        @((context as GetAllFactoriesResponse)?.Name)
        @if ((context as GetAllFactoriesResponse)?.IsActive == false)
        {
            <RadzenBadge BadgeStyle="BadgeStyle.Danger" Variant="Variant.Outlined" Text="@("Nieaktywny".Tr())"
                         class="ml-2" />
        }
    </ValueTemplate>
</RadzenDropDown>

@code {
    [CascadingParameter] public EditContext? EditContext { get; set; }
    [Parameter] public Expression<Func<object>>? For { get; set; }
    [Parameter] public TValue? Value { get; set; }
    [Parameter] public EventCallback<TValue?> ValueChanged { get; set; }
    [Parameter] public bool ShowActiveOnly { get; set; } = true;
    /// <summary>
    /// Podczas edycji - jeśli wybrany element jest nieaktywny, to i tak będzie widoczny jako wybrany, ale nie będzie go w liście do ponownego wyboru.
    /// </summary>
    [Parameter] public bool IncludeSelectedIfInactive { get; set; } = true;
    /// <summary>
    /// Czy pole ma mieć możliwość czyszczenia - domyślnie false.
    /// </summary>
    [Parameter] public bool Clearable { get; set; } = false;

    private GetAllFactoriesResponse? _selectedValue;
    private string _searchText = string.Empty;
    private IEnumerable<GetAllFactoriesResponse> _factories = [];
    private IEnumerable<GetAllFactoriesResponse> _factoriesFiltered = [];
    private TValue? _value;

    private void OnLoadData(LoadDataArgs args)
    {
        var filter = args.Filter?.ToLowerInvariant() ?? string.Empty;

        _factoriesFiltered = string.IsNullOrEmpty(filter)
            ? _factories.ToList()
            : _factories.Where(x =>
                (x.Name?.ToLowerInvariant().Contains(filter) ?? false)
                || (x.Code?.ToLowerInvariant().Contains(filter) ?? false))
                .ToList();

        SetDropDownItems();

        InvokeAsync(StateHasChanged);
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        var request = new GetAllFactoriesQuery();

        if (ShowActiveOnly && !IncludeSelectedIfInactive)
            request.SieveModel.Filters = "IsActive==true";

        var response = await Mediator.Send(request);

        if (response.Succeeded)
        {
            _factories = response.Data.ToList();
            SetDropDownItems();
            await SetSelectedValueAsync();
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError, "Błąd pobrania zakładów".Tr());
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        if (!EqualityComparer<TValue?>.Default.Equals(_value, Value))
        {
            _value = Value;
            await SetSelectedValueAsync();
        }
    }

    private async Task SetSelectedValueAsync()
    {
        if (_factories == null || !_factories.Any() || _value == null)
            return;

        var targetType = Nullable.GetUnderlyingType(typeof(TValue)) ?? typeof(TValue);

        if (targetType == typeof(int))
        {
            var intValue = Convert.ToInt32(_value);
            var selected = _factories.FirstOrDefault(x => x.Id == intValue);

            if (selected != null)
            {
                await SetSelectedValueAsync(selected);
            }
        }
    }

    private async Task SetSelectedValueAsync(GetAllFactoriesResponse? value)
    {
        _selectedValue = value;
        _value = _selectedValue != null ? (TValue)(object)_selectedValue.Id : default;
        _searchText = string.Empty;

        SetDropDownItems();

        await ValueChanged.InvokeAsync(_value);

        if (EditContext != null && For != null)
        {
            EditContext.NotifyFieldChanged(FieldIdentifier.Create(For));
        }

        await InvokeAsync(StateHasChanged);
    }

    private void SetDropDownItems()
    {
        if (ShowActiveOnly)
            _factoriesFiltered = _factories.Where(x => x.IsActive).ToList();
        else
            _factoriesFiltered = _factories.ToList();
    }
}
