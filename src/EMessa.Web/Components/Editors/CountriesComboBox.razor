@attribute [Obsolete($"This component is deprecated. Use {nameof(CountriesSelector<TValue>)} instead.")]

@namespace EMessa.Web.Components.Editors
@typeparam TValue
@using System.Linq.Expressions;
@using EMessa.Core.Features.Countries.Queries.GetAll;
@using Syncfusion.Blazor.DropDowns;

<SfComboBox @ref="_editor" Value="@Value" ValueChanged="@ChangeValue" ValueExpression="@ValueExpression" TValue="TValue"
            TItem="GetAllCountriesResponse" ShowClearButton="true" Placeholder="@("Wybierz".Tr())"
            DataSource="@_countries">
    <ComboBoxFieldSettings Text="@nameof(GetAllCountriesResponse.Name)" Value="@nameof(GetAllCountriesResponse.Id)">
    </ComboBoxFieldSettings>
</SfComboBox>
<AppToast @ref="_toast" />

@code
{
    [Parameter]
    public TValue Value
    {
        get => _value;
        set
        {
            if (!HasValueChanged(value, _value)) return;
            _value = value;
            ValueChanged.InvokeAsync(value);
        }
    }

    [Parameter]
    public EventCallback<TValue> ValueChanged { get; set; }

    [Parameter]
    public Expression<Func<TValue>>? ValueExpression { get; set; }

    private TValue _value = default!;

    SfComboBox<TValue, GetAllCountriesResponse>? _editor;
    private AppToast _toast = new();

    private List<GetAllCountriesResponse> _countries = [];

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        var res = await Mediator.Send(new GetAllCountriesQuery());
        if (res.Succeeded)
        {
            _countries = res.Data;
        }
        else
        {
            await _toast.ShowError("Błąd pobrania państw".Tr());
        }
    }

    private bool HasValueChanged(TValue newValue, TValue currentValue)
    {
        if (newValue == null && currentValue == null)
            return false;

        if (newValue == null || currentValue == null)
            return true;

        return !newValue.Equals(currentValue);
    }

    private void ChangeValue(TValue val)
    {
        Value = val;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);
        var currentId = (int?)(object?)Value;
        if (currentId <= 0 && _editor != null)
        {
            await _editor.ClearAsync();
        }
    }
}