@using System.Linq.Expressions;
@using EMessa.Core.Features.OptionValues.Queries.GetAllValueForOption
@using EMessa.Core.Features.Sales.Queries.GetAllSaleColorsByArticleId
@using Ra<PERSON>zen
@using Radzen.Blazor

@namespace EMessa.Web.Components.Editors

<RadzenFormField Variant="Variant.Outlined" Text="@Label">
    <ChildContent>
        <RadzenDropDown TValue="GetAllValuesForOptionResponse"
                        Data="@_filteredSaleColors"
                        LoadData="@LoadData"
                        Placeholder="@Placeholder"
                        @bind-SearchText="@_searchText"
                        Value="@_selectedValue"
                        ValueChanged="@SetSelectedValueAsync"
                        AllowFiltering
                        AllowClear="@Clearable"
                        class="@CssClass">
            <Template>
                @if (context is GetAllValuesForOptionResponse color)
                {
                    @color.Value
                    <RadzenBadge BadgeStyle="BadgeStyle.Info" Variant="Variant.Outlined" Text="@color.Code"
                                 class="ml-2" />
                }
            </Template>
            <ValueTemplate>
                @if (context is GetAllValuesForOptionResponse color)
                {
                    @color.Value
                    <RadzenBadge BadgeStyle="BadgeStyle.Info" Variant="Variant.Outlined" Text="@color.Code"
                                 class="ml-2" />
                }
            </ValueTemplate>
        </RadzenDropDown>
    </ChildContent>
</RadzenFormField>

@code {
    [CascadingParameter] public EditContext? EditContext { get; set; }
    [Parameter] public Expression<Func<object>>? For { get; set; }
    [Parameter] public required int ArticleId { get; set; }
    [Parameter] public int? Value { get; set; }
    [Parameter] public EventCallback<int?> ValueChanged { get; set; }
    [Parameter] public string? Label { get; set; }
    [Parameter] public string Placeholder { get; set; } = "Szukaj".Tr();
    [Parameter] public string CssClass { get; set; } = "";

    /// <summary>
    /// Podczas edycji - jeśli wybrany element jest nieaktywny, to i tak będzie widoczny jako wybrany, ale nie będzie go w liście do ponownego wyboru.
    /// </summary>
    [Parameter]
    public bool IncludeSelectedIfInactive { get; set; } = true;

    /// <summary>
    /// Czy pole ma mieć możliwość czyszczenia - domyślnie false.
    /// </summary>
    [Parameter]
    public bool Clearable { get; set; }

    private IEnumerable<GetAllValuesForOptionResponse> _saleColors = [];
    private IEnumerable<GetAllValuesForOptionResponse> _filteredSaleColors = [];
    private GetAllValuesForOptionResponse? _selectedValue;
    private string _searchText = "";
    private int? _value;

    private static readonly EqualityComparer<int?> IntComparer = EqualityComparer<int?>.Default;

    private IEnumerable<GetAllValuesForOptionResponse> FilterColors(string filter)
    {
        if (string.IsNullOrEmpty(filter))
            return _saleColors;

        return _saleColors.Where(x =>
            x.Value.Contains(filter, StringComparison.OrdinalIgnoreCase) ||
            x.Code.Contains(filter, StringComparison.OrdinalIgnoreCase));
    }

    // Uruchamiane tylko, gdy jest wywoływane z poziomu pola wyszukiwania
    private void LoadData(LoadDataArgs args)
    {
        _filteredSaleColors = FilterColors(args.Filter);
        InvokeAsync(StateHasChanged);
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        await ReloadColorsAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        if (!IntComparer.Equals(_value, Value))
        {
            _value = Value;
            await PresetSelectedValueAsync();
        }
    }

    private async Task ReloadColorsAsync()
    {
        var response = await Mediator.Send(new GetAllSaleColorsByArticleIdQuery(ArticleId));

        if (response.Succeeded)
        {
            _saleColors = response.Data;
            await PresetSelectedValueAsync();

            _filteredSaleColors = FilterColors(_searchText);

            await InvokeAsync(StateHasChanged);
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError, "Błąd pobrania kolorów".Tr());
        }
    }

    private async Task PresetSelectedValueAsync()
    {
        if (!_saleColors.Any())
            return;

        if (_value == null)
        {
            await SetSelectedValueAsync(null);
            return;
        }

        var intValue = Convert.ToInt32(_value);
        var selected = _saleColors.FirstOrDefault(x => x.Id == intValue);
        await SetSelectedValueAsync(selected);
    }

    private async Task SetSelectedValueAsync(GetAllValuesForOptionResponse? value)
    {
        _selectedValue = value;
        _value = _selectedValue?.Id;
        _searchText = "";

        await ValueChanged.InvokeAsync(_value);

        if (EditContext != null && For != null)
        {
            EditContext.NotifyFieldChanged(FieldIdentifier.Create(For));
        }

        await InvokeAsync(StateHasChanged);
    }

}