@attribute [Obsolete($"This component is deprecated. Use {nameof(FactoriesSelector<TValue>)} instead.")]
@typeparam TValue
@using System.Linq.Expressions;
@using EMessa.Core.Features.Factories.Queries.GetAll
@using Syncfusion.Blazor.DropDowns;

@inject ILogger<FactoriesComboBox<TValue>> Logger

<SfComboBox @ref="_editor" Value="@Value" CssClass="@GetComboBoxCssClass()" ValueChanged="@ChangeValue"
            ValueExpression="@ValueExpression" TValue="TValue" TItem="GetAllFactoriesResponse" ShowClearButton="true"
            Placeholder="@("Wybierz".Tr())" DataSource="@_factories">
    <ComboBoxFieldSettings Text="@nameof(GetAllFactoriesResponse.Name)" Value="@nameof(GetAllFactoriesResponse.Id)"></ComboBoxFieldSettings>
    <ComboBoxTemplates TItem="GetAllFactoriesResponse">
        <ItemTemplate>
            <span class="@(context.IsActive ? "" : "line-through text-gray-500")">
                @context.Name
            </span>
        </ItemTemplate>
    </ComboBoxTemplates>
</SfComboBox>

<style>
    .disabled-style .e-input {
        text-decoration: line-through;
        color: gray;
    }
</style>

@code
{
    [Parameter]
    public TValue Value
    {
        get => _value;
        set
        {
            if (HasValueChanged(value, _value))
            {
                _value = value;
                ValueChanged.InvokeAsync(value);
            }
            _ = InvokeAsync(LoadSelectedFactoryIfMissing);
        }
    }

    [Parameter]
    public EventCallback<TValue> ValueChanged { get; set; }

    [Parameter]
    public Expression<Func<TValue>>? ValueExpression { get; set; }

    [Parameter]
    public bool ShowActiveOnly { get; set; } = true;

    private TValue _value = default!;

    private AppToast _toast = new();

    SfComboBox<TValue, GetAllFactoriesResponse>? _editor;

    private List<GetAllFactoriesResponse> _factories = [];

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        try
        {
            var request = new GetAllFactoriesQuery();

            if (ShowActiveOnly)
            {
                request.SieveModel.Filters = "IsActive==true";
            }

            var result = await Mediator.Send(request);

            if (result.Succeeded)
            {
                _factories = result.Data;
            }
            else
            {
                ToastService.Show(ToastType.LoadDataError);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, ex.Message);
            ToastService.Show(ToastType.LoadDataError);
        }
    }

    private bool HasValueChanged(TValue newValue, TValue currentValue)
    {
        if (newValue == null && currentValue == null)
            return false;

        if (newValue == null || currentValue == null)
            return true;

        return !newValue.Equals(currentValue);
    }

    private void ChangeValue(TValue val)
    {
        Value = val;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);
        var factoryId = (int?)(object?)Value;
        if (factoryId <= 0 && _editor != null)
        {
            await _editor.ClearAsync();
        }
    }

    private async Task LoadSelectedFactoryIfMissing()
    {
        if (Value != null && int.TryParse(Value.ToString(), out var selectedId))
        {
            if (_factories.All(x => x.Id != selectedId))
            {
                var request = new GetAllFactoriesQuery
                    {
                        SieveModel =
                    {
                        Filters = $"Id=={selectedId}"
                    }
                    };

                var selectedResult = await Mediator.Send(request);
                if (selectedResult is { Succeeded: true, Data: var selectedFactories })
                {
                    _factories.Add(selectedFactories.First());
                    await _editor!.RefreshDataAsync();
                    StateHasChanged();
                }
            }
        }
    }

    private string GetComboBoxCssClass()
    {
        if (Value == null || !_factories.Any())
            return "";

        if (!int.TryParse(Value.ToString(), out var selectedId)) return "";

        var selectedFactory = _factories.FirstOrDefault(x => x.Id == selectedId);
        return selectedFactory?.IsActive == false ? "disabled-style" : "";
    }
}
