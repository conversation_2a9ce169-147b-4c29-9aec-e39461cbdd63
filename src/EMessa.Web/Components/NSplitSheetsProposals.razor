@using EMessa.Core.Features.OrderItems.Commands.ValidateSheet
@using EMessa.Core.Features.ShoppingCart.Models
@using EMessa.Web.Pages.Catalog.Parts
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor

<div class="w-full flex flex-col gap-2 @CssClass">
    <h1 class="font-bold">@Title</h1>
    <div class="flex flex-col gap-y-2">
        @{ var key = 0; }

        @foreach (var validation in ValidationSplits)
        {
            <div @key="@(key++)"
                 class="bg-green-50 flex flex-col p-2 gap-y-2 border-2 border-gray-300 rounded">
                <span>@validation.Message</span>
                @if (validation.ProposedSheets.Count > 0)
                {
                    <ProposedSheetsList Label="@("Proponowany podział arkusza".Tr())"
                                        ProposedSheets="@validation.ProposedSheets"
                                        TotalLength="validation.TotalLength"/>
                    <RadzenButton Click="@(() => AddProposedSheetsHandler(validation.ProposedSheets, validation.SplitN))">
                        @("Wybierz propozycję".Tr())
                    </RadzenButton>
                }
                else
                {
                    <span>@("Brak podziału".Tr())</span>
                    <RadzenButton Click="@CancelHandler">
                        @("Anuluj".Tr())
                    </RadzenButton>
                }
            </div>
        }
    </div>
    <hr/>
    <RadzenButton Text="@("Anuluj".Tr())"
                  ButtonStyle="ButtonStyle.Secondary"
                  Size="ButtonSize.Medium"
                  Class="w-32"
                  Click="@CancelHandler" />
</div>

@code {

    [Parameter]
    public required string Title { get; set; }

    [Parameter]
    public List<ArticleSheetValidationResult> ValidationSplits { get; set; } = [];

    [Parameter]
    public EventCallback<(List<ArticleSheetEditModel> Sheets, int? SplitN)> OnAddProposedSheets { get; set; }

    [Parameter]
    public EventCallback OnCancel { get; set; }

    [Parameter]
    public string CssClass { get; set; } = "";


    private async Task AddProposedSheetsHandler(List<ArticleSheetEditModel> sheets, int? splitN)
    {
        await OnAddProposedSheets.InvokeAsync((Sheets: sheets, SplitN: splitN));
    }

    private async Task CancelHandler()
    {
        await OnCancel.InvokeAsync();
    }

}