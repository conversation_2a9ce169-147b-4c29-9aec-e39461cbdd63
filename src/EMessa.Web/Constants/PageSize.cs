using DocumentFormat.OpenXml.Wordprocessing;
using EMessa.Core.Localizer;
using Radzen.Blazor;

namespace EMessa.Web.Constants
{
    public static class PageSize
    {
        /// <summary>
        /// DefaultPageSize = 20
        /// </summary>
        public static readonly int DefaultPageSize = 20;
        /// <summary>
        /// Pages20To200 = [20, 50, 100, 200]
        /// </summary>
        public static readonly int[] Pages20To200 = [20, 50, 100, 200];
        /// <summary>
        /// Pages50To200 = [50, 100, 200]
        /// </summary>
        public static readonly int[] Pages50To200 = [50, 100, 200];

        /// <summary>
        /// Przetłumaczony tekst "Elementów na stronie"
        /// </summary>
        public static string PageSizeText => "Elementów na stronie".Tr();

        /// <summary>
        /// Przetłumaczony tekst "Strona {0} z {1} ({2} wyników)"
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="dataGrid"></param>
        /// <returns></returns>
        public static string FormatPaginationInfo<T>(RadzenDataGrid<T>? dataGrid)
        {
            if (dataGrid == null)
            {
                return "Strona {0} z {1} ({2} wyników)".Tr("0", "0", "0");
            }
            return FormatPaginationInfo(dataGrid.CurrentPage, dataGrid.PageSize, dataGrid.Count);
        }

        public static string FormatPaginationInfo(int currentPage, int pageSize, int totalResults)
        {
            currentPage += 1; // Adjusting to 1-based index for display

            if (currentPage < 1 || pageSize < 1 || totalResults < 0)
            {
                return "Strona {0} z {1} ({2} wyników)".Tr(currentPage.ToString(), "0", totalResults.ToString());
            }

            int totalPages = (int)Math.Ceiling((double)totalResults / pageSize);

            return "Strona {0} z {1} ({2} wyników)".Tr(currentPage.ToString(), totalPages.ToString(), totalResults.ToString());
        }

        /// <summary>
        /// Przetłumaczony tekst "Brak danych do wyświetlenia"
        /// </summary>
        public static string NoDataText => "Brak danych do wyświetlenia".Tr();
    }
}