using <PERSON><PERSON><PERSON>;

namespace EMessa.Web.Constants;

internal static class RadzenDataGridFilterOptions
{
    public static readonly FilterOperator[] AllSupportedFilters =
    [
        FilterOperator.Equals,
        FilterOperator.NotEquals,
        FilterOperator.GreaterThan,
        FilterOperator.GreaterThanOrEquals,
        FilterOperator.LessThan,
        FilterOperator.LessThanOrEquals,
        FilterOperator.Contains,
        FilterOperator.DoesNotContain,
        FilterOperator.StartsWith,
        FilterOperator.EndsWith
    ];

    public static readonly FilterOperator[] TextFilters =
    [
        FilterOperator.Equals,
        FilterOperator.NotEquals,
        FilterOperator.Contains,
        FilterOperator.DoesNotContain,
        FilterOperator.StartsWith,
        FilterOperator.EndsWith
    ];

    public static readonly FilterOperator[] NumericFilters =
    [
        FilterOperator.Equals,
        FilterOperator.NotEquals,
        FilterOperator.GreaterThan,
        FilterOperator.GreaterThanOrEquals,
        FilterOperator.<PERSON><PERSON>han,
        FilterOperator.LessThanOrEquals,
    ];

    public static readonly FilterOperator[] DateTimeFilters =
    [
        //FilterOperator.Equals, z racji na trudność zapewnienia poprawności (local vs UTC date), na razie nie używamy
        //FilterOperator.NotEquals,
        FilterOperator.GreaterThan,
        FilterOperator.GreaterThanOrEquals,
        FilterOperator.LessThan,
        FilterOperator.LessThanOrEquals,
    ];

    public static readonly FilterOperator[] NullableDateTimeFilters =
    [
        //FilterOperator.Equals,
        //FilterOperator.NotEquals,
        FilterOperator.GreaterThan,
        FilterOperator.GreaterThanOrEquals,
        FilterOperator.LessThan,
        FilterOperator.LessThanOrEquals,
        FilterOperator.IsEmpty,
        FilterOperator.IsNotEmpty
    ];
}
