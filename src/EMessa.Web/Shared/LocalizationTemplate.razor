@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor
@using EMessa.Core.Features.CustomerLocalizations.Queries.Get

@implements IDisposable

<EditForm EditContext="@_editContext">
    <DataAnnotationsValidator />
    <RadzenRow AlignItems="AlignItems.Start" RowGap="0.5rem" Gap="1rem">
        <RadzenColumn Size="12" SizeMD="12" SizeLG="12">
            <RadzenLabel Text=@("Adres".Tr().AddText(!ReadOnly ? "*" : "")) />
            <RadzenTextArea @bind-Value="Localization.Address" ReadOnly="@ReadOnly" class="w-full" />
            <ValidationMessage For="@(() => Localization.Address)" class="validation-message" />
        </RadzenColumn>

        <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
            <RadzenLabel Text=@("Kod pocztowy".Tr().AddText(!ReadOnly ? "*" : "")) />
            <RadzenTextBox @bind-Value="Localization.PostCode" ReadOnly="@ReadOnly" class="w-full border" />
            <ValidationMessage For="@(() => Localization.PostCode)" class="validation-message" />
        </RadzenColumn>

        <RadzenColumn Size="12" SizeMD="12" SizeLG="6">
            <RadzenLabel Text=@("Miasto".Tr().AddText(!ReadOnly ? "*" : "")) />
            <RadzenTextBox @bind-Value="Localization.City" ReadOnly="@ReadOnly" class="w-full border" />
            <ValidationMessage For="@(() => Localization.City)" class="validation-message" />
        </RadzenColumn>
    </RadzenRow>
</EditForm>

@code {

    [Parameter]
    public GetCustomerLocalizationsResponse Localization
    {
        get => _localization;
        set
        {
            if (_localization != value)
            {
                _localization = value;
                // Wymuszenie walidacji przy zmianie właściwości z zewnątrz
                if (_editContext != null)
                {
                    _editContext.NotifyValidationStateChanged();
                    ValidateModel();
                }
            }
        }
    }

    [Parameter] public EventCallback<GetCustomerLocalizationsResponse> LocalizationChanged { get; set; }
    [Parameter] public bool ReadOnly { get; set; }
    [Parameter] public bool ValidateOnInitialized { get; set; }
    [Parameter] public bool IsLoading { get; set; }
    [Parameter] public bool IsValid { get; set; }
    [Parameter] public EventCallback<bool> IsValidChanged { get; set; }
    private GetCustomerLocalizationsResponse _localization = new();
    private EditContext? _editContext;
    private ValidationMessageStore? _messageStore;
    private readonly GetCustomerLocalizationsResponseValidator _validator = new();

    protected override void OnInitialized()
    {
        base.OnInitialized();

        // Tworzenie własnego EditContext
        _editContext = new EditContext(Localization);

        // Tworzenie własnego ValidationMessageStore
        _messageStore = new ValidationMessageStore(_editContext);

        // Dodanie handlerów walidacji
        _editContext.OnValidationRequested += ValidationRequested;
        _editContext.OnFieldChanged += FieldChanged;
        
        if (ValidateOnInitialized)
        {
            ValidateModel();
        }
    }

    protected override void OnParametersSet()
    {
        base.OnParametersSet();

        // Jeśli model się zmienił, zaktualizuj kontekst walidacji
        if (_editContext?.Model != Localization)
        {
            _editContext = new EditContext(Localization);
            _messageStore = new ValidationMessageStore(_editContext);
            _editContext.OnValidationRequested += ValidationRequested;
            _editContext.OnFieldChanged += FieldChanged;

            // Uruchomienie walidacji po zmianie modelu
            ValidateModel();
        }
    }

    private void ValidationRequested(object? sender, ValidationRequestedEventArgs e)
    {
        ValidateModel();
    }

    private void FieldChanged(object? sender, FieldChangedEventArgs e)
    {
        ValidateModel();
    }
 
    public void ValidateModel()
    {
        if (_editContext == null || _messageStore == null)
            return;

        // Wyczyść poprzednie błędy
        _messageStore.Clear();

        // Wykonaj walidację
        var validationResult = _validator.Validate(Localization);

        // Dodaj błędy walidacji do store'a
        foreach (var error in validationResult.Errors)
        {
            _messageStore.Add(_editContext.Field(error.PropertyName), error.ErrorMessage);
        }

        // Powiadom o zmianie stanu walidacji
        _editContext.NotifyValidationStateChanged();

        // Aktualizuj IsValid
        var messages = _editContext.GetValidationMessages();
        var newIsValid = !messages.Any();

        if (IsValid == newIsValid)
            return;

        IsValid = newIsValid;

        // Powiadom o zmianie stanu walidacji
        InvokeAsync(async () => { await IsValidChanged.InvokeAsync(IsValid); });
    }

    public void Dispose()
    {
        if (_editContext != null)
        {
            _editContext.OnValidationRequested -= ValidationRequested;
            _editContext.OnFieldChanged -= FieldChanged;
            _messageStore?.Clear();
        }
    }

} 