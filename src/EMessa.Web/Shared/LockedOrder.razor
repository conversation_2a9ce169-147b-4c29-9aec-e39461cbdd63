@using EMessa.Core.Features.Orders.Queries.Common

@inject NavigationManager Navigation
@inject IAppStateService AppStateService
@implements IDisposable

<EditRadzenButton Text="@(_lockedOrder?.OrderNo ?? null)"
                  Class="@IconClass"
                  Visible="@(_lockedOrder != null)"
                  Click="@GoToOrderDetails" />

@code {
    const string IconClass = "[&_.rz-button-box]:text-black max-sm:[&_.rz-button-text]:hidden max-sm:px-0";
    private LockOrderResponse? _lockedOrder;

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        AppStateService.StateChanged += OnStateChanged;
    }

    void IDisposable.Dispose()
    {
        AppStateService.StateChanged -= OnStateChanged;
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source == this) return;
        switch (property)
        {
            case AppStateNotifyProperties.OwnLockedOrderChanged:
                _lockedOrder = AppStateService.OwnLockedOrder;
                InvokeAsync(StateHasChanged);
                break;
            case AppStateNotifyProperties.ScreenSizeChanged:
                InvokeAsync(StateHasChanged);
                break;
        }
    }

    private void GoToOrderDetails()
    {
        if (_lockedOrder != null)
        {
            Navigation.NavigateTo($"/orders/details/{_lockedOrder.OrderId}");
        }
    }

}
