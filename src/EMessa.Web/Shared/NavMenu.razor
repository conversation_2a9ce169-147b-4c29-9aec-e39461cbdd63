@inject NavigationManager NavigationManager
@inject ILogger<NavMenu> Logger;
@implements IDisposable

<style>
    .material-symbols-outlined {
        font-variation-settings: 'FILL' 0, 'wght' 250, 'GRAD' 0, 'opsz' 24;
        font-size: 28px;
        padding-right: 15px;
    }
</style>

<div id="nav-menu" class="nav-menu scroller">
    <ul class="nav flex-column">
        <li class="nav-item px-2">
            <NavLink class="nav-link" href="" Match="NavLinkMatch.All">
                <span translate="no" class="material-symbols-outlined" style="font-variation-settings: 'FILL' 1;" aria-hidden="true">home</span>
                <span class="displayText">@("Strona główna".Tr())</span>
            </NavLink>
        </li>
        <AuthorizeView Roles=@($"{Role.Administrator},{Role.TradeManager},{Role.Trade},{Role.Production},{Role.ClientManager},{Role.Client}")>
            <Authorized>
                <li class="nav-item px-2">
                    <NavLink class="nav-link" href="/orders/index">
                        <span translate="no" class="material-symbols-outlined" aria-hidden="true">list_alt</span>
                        <span class="displayText">@("Zamówienia".Tr())</span>
                    </NavLink>
                </li>
                <li class="nav-item px-2">
                    <NavLink class="nav-link" href="/catalog">
                        <span translate="no" class="material-symbols-outlined" aria-hidden="true">topic</span>
                        <span class="displayText">@("Katalog".Tr())</span>
                    </NavLink>
                </li>
            </Authorized>
        </AuthorizeView>

        <AuthorizeView Roles="@Role.Administrator">
            <Authorized>
                <li class="nav-item px-2">
                    <NavLink class="nav-link" href="/admin/categories/index">
                        <span translate="no" class="material-symbols-outlined" aria-hidden="true">category</span>
                        <span class="displayText">@("Kategorie".Tr())</span>
                    </NavLink>
                </li>
                <li class="nav-item px-2">
                    <NavLink class="nav-link" href="/admin/articles/index">
                        <span translate="no" class="material-symbols-outlined" aria-hidden="true">water</span>
                        <span class="displayText">@("Produkty".Tr())</span>
                    </NavLink>
                </li>
                <li class="nav-item px-2">
                    <NavLink class="nav-link" href="/admin/options/index">
                        <span translate="no" class="material-symbols-outlined" aria-hidden="true">tune</span>
                        <span class="displayText">@("Opcje produktów".Tr())</span>
                    </NavLink>
                </li>
                <li class="nav-item px-2">
                    <NavLink class="nav-link" href="/admin/articlesValidation/index">
                        <span translate="no" class="material-symbols-outlined"
                              aria-hidden="true">check_circle</span>
                        <span class="displayText">@("Walidacja produktów".Tr())</span>
                    </NavLink>
                </li>
                <li class="nav-item px-2">
                    <NavLink class="nav-link" href="/admin/filterAttributes/index">
                        <span translate="no" class="material-symbols-outlined" aria-hidden="true">filter_list</span>
                        <span class="displayText">@("Atrybuty".Tr())</span>
                    </NavLink>
                </li>
            </Authorized>
        </AuthorizeView>

        <AuthorizeView Roles=@($"{Role.Administrator},{Role.SaleManager}")>
            <Authorized>
                <li class="nav-item px-2">
                    <NavLink class="nav-link" href="/admin/sales/index">
                        <span translate="no" class="material-symbols-outlined" aria-hidden="true">percent</span>
                        <span class="displayText">@("Promocje".Tr())</span>
                    </NavLink>
                </li>
            </Authorized>
        </AuthorizeView>

        <AuthorizeView Roles=@($"{Role.Administrator},{Role.TradeManager},{Role.Trade},{Role.Production},{Role.ClientManager}")>
            <Authorized>
                <li class="nav-item px-2">
                    <NavLink class="nav-link" href="/admin/users/index">
                        <span translate="no" class="material-symbols-outlined" aria-hidden="true">manage_accounts</span>
                        <span class="displayText">@("Użytkownicy".Tr())</span>
                    </NavLink>
                </li>
            </Authorized>
        </AuthorizeView>

        <AuthorizeView Roles=@($"{Role.Administrator},{Role.TradeManager},{Role.Trade},{Role.Production}")>
            <Authorized>
                <li class="nav-item px-2">
                    <NavLink class="nav-link" href="/admin/customers/index">
                        <span translate="no" class="material-symbols-outlined" aria-hidden="true">group</span>
                        <span class="displayText">@("Klienci".Tr())</span>
                    </NavLink>
                </li>

                <li class="nav-item px-2">
                    <NavLink class="nav-link" href="/admin/customerGroups/index">
                        <span translate="no" class="material-symbols-outlined" aria-hidden="true">groups</span>
                        <span class="displayText">@("Grupy klientów".Tr())</span>
                    </NavLink>
                </li>
            </Authorized>
        </AuthorizeView>

        <AuthorizeView Roles="@Role.Administrator">
            <Authorized>
                <li class="nav-item px-2">
                    <NavLink class="nav-link" href="/admin/factories/index">
                        <span translate="no" class="material-symbols-outlined" aria-hidden="true">factory</span>
                        <span class="displayText">@("Zakłady".Tr())</span>
                    </NavLink>
                </li>
                <li class="nav-item px-2">
                    <NavLink class="nav-link" href="/admin/branches/index">
                        <span translate="no" class="material-symbols-outlined" aria-hidden="true">holiday_village</span>
                        <span class="displayText">@("Oddziały".Tr())</span>
                    </NavLink>
                </li>
                <li class="nav-item px-2">
                    <NavLink class="nav-link" href="/admin/countries/index">
                        <span translate="no" class="material-symbols-outlined" aria-hidden="true">flag_circle</span>
                        <span class="displayText">@("Państwa")</span>
                    </NavLink>
                </li>
                <li class="nav-item px-2">
                    <NavLink class="nav-link" href="/admin/files/index">
                        <span translate="no" class="material-symbols-outlined" aria-hidden="true">folder_open</span>
                        <span class="displayText">@("Pliki".Tr())</span>
                    </NavLink>
                </li>
                <li class="nav-item px-2">
                    <NavLink class="nav-link" href="/admin/pages/index">
                        <span translate="no" class="material-symbols-outlined" aria-hidden="true">article</span>
                        <span class="displayText">@("Strony".Tr())</span>
                    </NavLink>
                </li>
                <li class="nav-item px-2">
                    <NavLink class="nav-link" href="/admin/configurations/index">
                        <span translate="no" class="material-symbols-outlined" aria-hidden="true">tune</span>
                        <span class="displayText">@("Konfiguracja".Tr())</span>
                    </NavLink>
                </li>
                <li class="nav-item px-2">
                    <NavLink class="nav-link" href="/admin/notifications/index">
                        <span translate="no" class="material-symbols-outlined" aria-hidden="true">notifications</span>
                        <span class="displayText">@("Komunikaty".Tr())</span>
                    </NavLink>
                </li>
                <li class="nav-item px-2">
                    <NavLink class="nav-link" href="/admin/uilocalizations/index">
                        <span translate="no" class="material-symbols-outlined" aria-hidden="true">translate</span>
                        <span class="displayText">@("Tłumaczenia UI".Tr())</span>
                    </NavLink>
                </li>
                <li class="nav-item px-2">
                    <NavLink class="nav-link" href="/admin/messages/index">
                        <span translate="no" class="material-symbols-outlined" aria-hidden="true">message</span>
                        <span class="displayText">@("Wiadomości".Tr())</span>
                    </NavLink>
                </li>
            </Authorized>
        </AuthorizeView>
    </ul>
</div>

@code {
    private bool _catalogVisible = false;

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        AppStateService.StateChanged += OnStateChanged;
    }

    private async void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.CatalogVisibleChanged)
        {
            _catalogVisible = AppStateService.CategoriesState.CatalogVisible;
            await InvokeAsync(StateHasChanged);
        }
    }

    void IDisposable.Dispose()
    {
        AppStateService.SetCatalogVisibility(this, false);
        AppStateService.StateChanged -= OnStateChanged;
    }
}