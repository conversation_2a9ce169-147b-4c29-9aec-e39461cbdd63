@using EMessa.Core.Features.OrderItems.Commands.SavePng
@using EMessa.Core.Features.OrderItems.Exceptions
@using EMessa.Core.Features.OrderItems.Models
@using EMessa.Core.Features.ShoppingCart.Models
@using EMessa.Web.Pages.CommonComponents


@inject ILogger<EditComplexOrderItem> Logger

<HeadContent>
    <script src="js/draftEditor/DraftEditor.js"></script>
</HeadContent>

<div class="w-full flex flex-col">
    @if (_isDraftEditorVisible)
    {
        <DraftEditorWrapper EditItem="@EditingItem"
                            OnSave="@OnDraftEditorSave"
                            OnCancel="@OnDraftEditorCancel"
                            FileNameReadOnly="true" />
    }
    else
    {
        <ArticleComplexEditComponent EditingItem="@EditingItem"
                                     NewRequestedSheet="@NewRequestedSheet"
                                     ShowSaveButton="@true"
                                     OnCancel="@OnCancel"
                                     OnSave="@OnSave"
                                     OpenDraftEditor="@OpenDraftEditorHandle" />
    }
</div>

@code {

    [Parameter] public required BaseOrderItemEditModel EditingItem { get; set; }
    [Parameter] public required RequestedSheetEditModel NewRequestedSheet { get; set; }
    [Parameter] public EventCallback OnSave { get; set; }
    [Parameter] public EventCallback OnCancel { get; set; }

    #region General

    private EditContext _editContext = null!;

    protected override void OnInitialized()
    {
        base.OnInitializedAsync();
        _editContext = new EditContext(_model);
    }

    #endregion

    #region DraftEditor

    private readonly FileNameModel _model = new();
    private bool _isDraftEditorVisible;
    private string DraftContent { get; set; } = "";

    private void OpenDraftEditorHandle()
    {
        _model.FileName = EditingItem.DraftOriginalFileName ?? throw new NullReferenceException();
        _model.FileName = EditingItem.DraftOriginalFileName ?? "";
        DraftContent = EditingItem.DraftDrawingSource ?? "";
        _isDraftEditorVisible = true;
    }

    private void OnDraftEditorCancel()
    {
        _isDraftEditorVisible = false;
    }

    private async Task OnDraftEditorSave(SaveRecord args)
    {
        // Validate the form
        var isValid = _editContext.Validate();
        if (!isValid)
        {
            ToastService.ShowError("Podaj nazwę szkicu".Tr());
            return;
        }

        DraftContent = args.DraftContent;
        try
        {
            var result = await Mediator.Send(new SavePngCommand
            {
                PngFile = args.PngContent,
                FileName = _model.FileName
            });

            if (result.Succeeded)
            {
                EditingItem.DraftEditable = true;
                EditingItem.DraftOriginalFileName = result.Data.OriginalFileName;
                EditingItem.DraftHashedFileName = result.Data.HashFileName;
                EditingItem.DraftDrawingSource = DraftContent;
            }
            else
            {
                throw new SavePngException(result.Messages);
            }
        }
        catch (Exception e)
        {
            Logger.LogError("Error during saving file", e.Message);
            ToastService.ShowError("Błąd podczas zapisu pliku".Tr());
        }
        finally
        {
            _isDraftEditorVisible = false;
        }
    }

    #endregion

}