@using System.Globalization
@using Radzen.Blazor

@inject NavigationManager NavigationManager

<style>
    .rz-dropdown-items-wrapper {
        max-height: 400px !important;
    }
</style>

<RadzenDropDown TValue="string"
                class="w-12 sm:w-20 border !border-black rounded"
                Data="@SystemConstants.SupportedCultures"
                Value="@_currentLanguageCode"
                ValueChanged="@OnValueChanged"
                TextProperty="Text"
                ValueProperty="Code">
    <Template>
        <div class="flex items-center">
            <span class="hidden sm:inline fi fi-@GetCountryCode(context.Code) mr-2"></span>
            <span>@context.Code.ToUpper()</span>
        </div>
    </Template>
    <ValueTemplate>
        <div class="flex items-center">
            <span class="hidden sm:inline fi fi-@GetCountryCode(_currentLanguageCode!) mr-2"></span>
            <span>@SystemConstants.SupportedCultures.FirstOrDefault(c => c.Code == _currentLanguageCode)?.Code.ToUpper()</span>
        </div>
    </ValueTemplate>
</RadzenDropDown>


@code {
    private string? _currentLanguageCode;

    private readonly Dictionary<string, string> _languageToCountryMapping = new()
    {
        { "pl", "pl" },
        { "hu", "hu" },
        { "sk", "sk" },
        { "cs", "cz" },
        { "en", "gb" },
        { "de", "de" }
    };

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        _currentLanguageCode = CultureInfo.CurrentCulture.Name;
    }

    private string GetCountryCode(string languageCode)
    {
        if (string.IsNullOrEmpty(languageCode))
            return "";

        return _languageToCountryMapping.TryGetValue(languageCode.ToLower(), out var countryCode)
            ? countryCode
            : languageCode.ToLower();
    }

    private void OnValueChanged(string selectedCode)
    {
        _currentLanguageCode = selectedCode;

        var uri = new Uri(NavigationManager.Uri).GetComponents(UriComponents.PathAndQuery, UriFormat.Unescaped);
        var queryString = string.Join("&", $"culture={Uri.EscapeDataString(_currentLanguageCode)}", $"redirectUri={Uri.EscapeDataString(uri)}");
        NavigationManager.NavigateTo($"/Culture/SetCulture?{queryString}", forceLoad: true);
    }
}
