
<div class="flex flex-1 flex-col w-full justify-between items-center bg-gray-100 rounded-md border @Class">
    @if (!string.IsNullOrEmpty(Title) || TitleContent != null)
    {
        <div
            class="w-full flex flex-col sm:flex-row gap-1 justify-start items-start sm:items-center border-b border-black border-opacity-10">
            <h1 class="text-base whitespace-normal sm:whitespace-nowrap @TitleClass">
                @Title
            </h1>
            @if (TitleContent != null)
            {
            <div class="w-full flex flex-row gap-1 justify-start items-center @TitleContentClass">
                @TitleContent
            </div>
            }
        </div>
    }
    <div class="w-full flex-1 p-2 @ContentClass">
        @ChildContent
    </div>
</div>

@code {

    [Parameter]
    public string Title { get; set; } = "";

    [Parameter]
    public RenderFragment? TitleContent { get; set; }

    [Parameter]
    public RenderFragment? ChildContent { get; set; }

    [Parameter]
    public string Class { get; set; } = "";

    [Parameter]
    public string TitleClass { get; set; } = "";

    [Parameter]
    public string TitleContentClass { get; set; } = "";

    [Parameter]
    public string ContentClass { get; set; } = "";
}