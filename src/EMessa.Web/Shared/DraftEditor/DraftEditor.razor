@using Syncfusion.Blazor.Popups

@implements IAsyncDisposable
@inject IJSRuntime JsRuntime

@* Wstaw to w Parent *@
@* <HeadContent> *@
@*     <script src="js/draftEditor/DraftEditor.js"></script> *@
@* </HeadContent> *@

<style>
    .draft-editor {
        padding: 4px;
        background: #f5f5f5;
    }

    .draft-editor canvas {
        border: 1px solid #ddd;
        background: white;
        margin: 10px 0;
    }

    .controls {
        margin: 4px 0;
        display: flex;
        align-items: center;
        gap: 4px;
        flex-wrap: wrap;
    }

    .controls button {
        padding: 6px; /* Reduced padding */
        background-color: #f0f0f0;
        border: 1px solid #ddd;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s ease-in-out;
        font-size: 14px;
        user-select: none;
        display: flex; /* Add flex display */
        align-items: center; /* Vertical centering */
        justify-content: center; /* Horizontal centering */
    }

    /* Adjust icon size if needed */
    .controls button .material-icons {
        font-size: 18px;
    }

    .controls div.space-1 {
        margin-right: 4px;
    }

    .controls div.space-2 {
        margin-right: 8px;
    }

    .controls div.space-4 {
        margin-right: 16px;
    }

    .controls button.space {
        margin-right: 20px;
    }

    .controls div.right-side, button.right-side {
        margin-left: auto;
    }

    .controls button:hover {
        background-color: #e0e0e0;
        border-color: #ccc;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .controls button:active {
        transform: translateY(0);
        box-shadow: none;
    }

    .controls button.active {
        background-color: #9CA3AF; /* gray-400 */
        color: white;
        border-color: #6B7280; /* gray-500 */
    }

    .controls button.active:hover {
        background-color: #6B7280; /* gray-500 */
        border-color: #4B5563; /* gray-600 */
    }

    .events-log {
        margin-top: 15px;
        padding: 10px;
        background: #fff;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    }

    .events-log p {
        margin: 5px 0;
        color: #666;
    }

    .title {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 15px;
        color: #333;
    }

    .text-dialog {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        z-index: 10000; /* Zwiększamy z-index */
    }

    .text-dialog-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 9999; /* Zwiększamy z-index */
    }

    .text-dialog-content {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .text-dialog input {
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        width: 100%;
    }

    .text-dialog-buttons {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }

    .text-dialog button {
        padding: 8px 16px;
        border-radius: 4px;
        border: none;
        cursor: pointer;
    }

    .text-dialog-cancel {
        background: #f0f0f0;
    }

    .text-dialog-confirm {
        background: #4B5563; /* or use bg-gray-600 in Tailwind */
        color: white;
    }

    .material-icons.red {
        color: #ff0000;
    }

    .material-icons.blue {
        color: #0000ff;
    }

    .help-item {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .help-item.full-width {
        grid-column: span 2;
    }
</style>

<div class="draft-editor">
    <div class="controls">
        <button class=""
                title="@("Zapisz szkic".Tr() + (CanSave ? "" : "(Podaj nazwę szkicu)".Tr()))"
                disabled="@(!CanSave)"
                @onclick="@Save">
            <span class="material-icons" translate="no">save</span>
        </button>
        <div class="space-2"></div>
        <button class="@IsActive(StateDrawingNewLine)"
                title="@("Rysuj linię".Tr())"
                @onclick="@(e => ToggleState(StateDrawingNewLine))">
            <span class="material-icons" translate="no">draw</span>
        </button>
        <button class="@IsActive(StateDrawingLineForward)"
                title="@("Rysuj linię w przód".Tr())"
                @onclick="@(e => ToggleState(StateDrawingLineForward))">
            <span class="material-icons red" translate="no">draw</span>
            <span class="material-icons red" translate="no">chevron_right</span>
        </button>
        <button class="@IsActive(StateDrawingLineBackward)"
                title="@("Rysuj linię wstecz".Tr())"
                @onclick="@(e => ToggleState(StateDrawingLineBackward))">
            <span class="material-icons blue" translate="no">chevron_left</span>
            <span class="material-icons blue" translate="no">draw</span>
        </button>
        <button class="@IsActive(StateInsertingColorSide)"
                title="@("Wstaw stronę koloru".Tr())"
                @onclick="@(e => ToggleState(StateInsertingColorSide))">
            <span class="material-icons red" translate="no">circle</span>
        </button>
        @* <div class="space-1"></div> *@
        <button class="@IsActive(StateInsertingLength)"
                title="@("Dodaj wymiar".Tr())"
                @onclick="@(e => ToggleState(StateInsertingLength))">
            <span class="material-icons" translate="no">straighten</span>
        </button>
        <button class="@IsActive(StateInsertingAngle)"
                title="@("Dodaj kąt".Tr())"
                @onclick="@(e => ToggleState(StateInsertingAngle))">
            <span class="material-icons" translate="no">square_foot</span>
        </button>
        <button class="@IsActive(StateInsertingText)"
                title="@("Dodaj tekst".Tr())"
                @onclick="@(e => ToggleState(StateInsertingText))">
            <span class="material-icons" translate="no">text_fields</span>
        </button>
        <button class="@IsActive(StateEditingText)"
                title="@("Edytuj wymiar lub kąt".Tr())"
                @onclick="@(e => ToggleState(StateEditingText))">
            <span class="material-icons" translate="no">edit</span>
        </button>
        <div class="space-1"></div>
        <button class="@IsActive(StateMoving)"
                title="@("Przesuwaj elementy".Tr())"
                @onclick="@(e => ToggleState(StateMoving))">
            <span class="material-icons" translate="no">open_with</span>
        </button>
        <button class="@IsActive(StateErasing)"
                title="@("Usuwaj elementy".Tr())"
                @onclick="@(e => ToggleState(StateErasing))">
            <span class="material-icons" translate="no">delete</span>
        </button>
        <div class="space-1"></div>
        <button title="@("Cofnij".Tr())"
                @onclick="Undo">
            <span class="material-icons" translate="no">undo</span>
        </button>
        <button title="@("Ponów".Tr())"
                @onclick="Redo">
            <span class="material-icons" translate="no">redo</span>
        </button>
        <div class="space-1"></div>
        <button title="@("Zmniejsz".Tr())"
                @onclick="ZoomOut">
            <span class="material-icons" translate="no">zoom_out</span>
        </button>
        <button title="@("Zwiększ".Tr())"
                @onclick="ZoomIn">
            <span class="material-icons" translate="no">zoom_in</span>
        </button>
        <div class="space-1"></div>
        <button title="@("Wyczyść rysunek".Tr())"
                @onclick="ClearCanvas">
            <span class="material-icons" translate="no">delete_sweep</span>
        </button>
        <div class="space-1"></div>
        <button title="@("Pomoc".Tr())"
                @onclick="ToggleHelp">
            <span class="material-icons" translate="no">help_outline</span>
        </button>
        <button title="@("Zamknij".Tr())"
                @onclick="Close">
            <span class="material-icons" translate="no">close</span>
        </button>
    </div>

    @if (_showHelp)
    {
        <div
            class="absolute top-[60px] right-[20px] rounded-lg shadow-md max-w-[500px] bg-white z-[100] p-3">
            <h3>@("Skróty klawiszowe i pomoc".Tr())</h3>
            <button class="absolute top-2 right-2 bg-transparent border-none cursor-pointer p-1" @onclick="@ToggleHelp"
                    title="@("Zamknij".Tr())">
                <span class="material-icons">close</span>
            </button>
            <div
                class="grid grid-cols-2 gap-3 mt-3 max-h-[300px] sm:max-h-svh overflow-auto touch-auto">
                <div class="help-item full-width">
                    <span class="material-icons">save</span>
                    <span>@("Zapisz szkic".Tr())</span>
                </div>
                <div class="help-item">
                    <span class="material-icons">draw</span>
                    <span>@("Rysuj linię".Tr())</span>
                </div>
                <div class="help-item">
                    <span class="material-icons red">draw</span>
                    <span class="material-icons red">chevron_right</span>
                    <span>@("Rysuj linię wprzód".Tr())</span>
                </div>
                <div class="help-item">
                    <span class="material-icons blue">chevron_left</span>
                    <span class="material-icons blue">draw</span>
                    <span>@("Rysuj linię wstecz".Tr())</span>
                </div>
                <div class="help-item">
                    <span class="material-icons red">circle</span>
                    <span>@("Wstaw stronę koloru".Tr())</span>
                </div>
                <div class="help-item">
                    <span class="material-icons">straighten</span>
                    <span>@("Wstaw wymiar".Tr())</span>
                </div>
                <div class="help-item">
                    <span class="material-icons">square_foot</span>
                    <span>@("Wstaw kąt".Tr())</span>
                </div>
                <div class="help-item">
                    <span class="material-icons">text_fields</span>
                    <span>@("Wstaw wieloliniowy tekst".Tr())</span>
                </div>
                <div class="help-item">
                    <span class="material-icons">edit</span>
                    <span>@("Edytuj wymiar, kąt".Tr())</span>
                </div>
                <div class="help-item">
                    <span class="material-icons">open_with</span>
                    <span>@("Przesuń elementy".Tr())</span>
                </div>
                <div class="help-item">
                    <span class="material-icons">delete</span>
                    <span>@("Usuń elementy".Tr())</span>
                </div>
                <div class="help-item">
                    <span class="material-icons">undo</span>
                    <span>@("Cofnij akcję".Tr())</span>
                </div>
                <div class="help-item">
                    <span class="material-icons">redo</span>
                    <span>@("Powtórz akcję".Tr())</span>
                </div>
                <div class="help-item">
                    <span class="material-icons">zoom_out</span>
                    <span>@("Pomniejsz widok".Tr())</span>
                </div>
                <div class="help-item">
                    <span class="material-icons">zoom_in</span>
                    <span>@("Powiększ widok".Tr())</span>
                </div>
                <div class="help-item">
                    <span class="material-icons">delete_sweep</span>
                    <span>@("Wyczyść cały rysunek".Tr())</span>
                </div>
                <div class="help-item">
                    <span class="material-icons">help_outline</span>
                    <span>@("Pomoc".Tr())</span>
                </div>
                <div class="help-item">
                    <span class="material-icons">close</span>
                    <span>@("Zamknij edytor".Tr())</span>
                </div>
                <div class="help-item full-width">
                    <span class="material-icons">info</span>
                    <span>@("Wymiary rysunku podawane są w [mm]".Tr())</span>
                </div>
                <div class="help-item full-width">
                    <span class="material-icons">info</span>
                    <span>@("Podczas wprowadzania można jawnie używać jednostek [mm, cm, dm, m]. Domyślna jednostka to [mm], jeśli nie podano jej jawnie.".Tr())</span>
                </div>
                <div class="help-item full-width">
                    <span class="material-icons">info</span>
                    <span>@("Podczas uzupełniania linii w przód lub w tył, można przełączać kierunek naciskając przycisk Shift".Tr())</span>
                </div>
                <div class="help-item full-width">
                    <span class="material-icons">info</span>
                    <span>@("Prawy przycisk myszy przełącza na tryb przesuwania. Kolejne jego kliknięcie przywraca poprzedni tryb. Umożliwia to tymczasowe przesunięcie obiektu.".Tr())</span>
                </div>
            </div>
        </div>
    }

    <div class="w-[calc(100%)] h-[600px]">
        <canvas @ref="_canvasElement" id="draftEditorCanvas" resize
                class="w-full h-full"></canvas>
    </div>
</div>

<SfDialog @bind-Visible="_showLengthAngleInput"
          Width="300px"
          IsModal="true"
          ShowCloseIcon="true"
          CloseOnEscape="false"
          AllowPrerender="false">
    <DialogTemplates>
        <Header>@_dialogTitle</Header>
        <Content>
            <input type="text"
                   @bind="_currentText"
                   @bind:event="oninput"
                   @onkeydown="@LengthAngleInputKeyDownHandler"
                   @ref="_lengthAngleInput"
                   class="e-input"/>
        </Content>
        <FooterTemplate>
            <button @onclick="@ConfirmLengthAngleDialog" class="e-btn e-primary">@("Dodaj".Tr())</button>
            <button @onclick="@CancelLengthAngleDialog" class="e-btn">@("Anuluj".Tr())</button>
        </FooterTemplate>
    </DialogTemplates>
</SfDialog>

<SfDialog @bind-Visible="_showTextInput"
          Width="300px"
          IsModal="true"
          ShowCloseIcon="true"
          CloseOnEscape="false"
          AllowPrerender="false">
    <DialogTemplates>
        <Header>@_dialogTitle</Header>
        <Content>
            <textarea
                @bind="_currentText"
                @bind:event="oninput"
                @onkeydown="@TextKeyDownHandler"
                @ref="_textInput"
                rows="4"
                class="e-input"></textarea>
        </Content>
        <FooterTemplate>
            <button @onclick="@ConfirmTextDialog" class="e-btn e-primary">@("Dodaj".Tr())</button>
            <button @onclick="@CancelTextDialog" class="e-btn">@("Anuluj".Tr())</button>
        </FooterTemplate>
    </DialogTemplates>
</SfDialog>

@code {
    [Parameter] public EventCallback<string> OnState { get; set; }
    [Parameter] public EventCallback<SaveRecord> OnSave { get; set; }
    [Parameter] public EventCallback OnCancel { get; set; }
    [Parameter] public string? DraftContent { get; set; }
    [Parameter] public EventCallback<string> DraftContentChanged { get; set; }

    private const string StateNone = "None";
    private const string StateDrawingNewLine = "DrawingNewLine";
    private const string StateDrawingLineForward = "DrawingLineForward";
    private const string StateDrawingLineBackward = "DrawingLineBackward";
    private const string StateInsertingLength = "InsertingLength";
    private const string StateInsertingAngle = "InsertingAngle";
    private const string StateInsertingText = "InsertingText";
    private const string StateInsertingColorSide = "InsertingColorSide";
    private const string StateMoving = "Moving";
    private const string StateErasing = "Erasing";
    private const string StateEditingText = "EditingText";

    private string CurrentState = "None";
    public string LengthSum = "0";

    private ElementReference _canvasElement;
    private DotNetObjectReference<DraftEditor>? _dotNetReference;
    private bool _showHelp;
    private bool _showLengthAngleInput;
    private ElementReference _lengthAngleInput;
    private bool _showTextInput;
    private ElementReference _textInput;
    private string _dialogTitle = "";
    private string _currentText = "";
    private double _clickedX;
    private double _clickedY;
    private string _name = "";
    private readonly string _unitWaring = "Wymiary w [{0}]".Tr("mm");
    private string _draftName = "";
    private bool _draftNameChanged;
    private bool CanSave => !string.IsNullOrWhiteSpace(_draftName);

    [Parameter]
    public string DraftName
    {
        get => _draftName;
        set
        {
            if (_draftName == value) return;
            _draftName = value;
            _draftNameChanged = true;
        }
    }

    #region Initialize, Dispoze

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            _dotNetReference = DotNetObjectReference.Create(this);
            await JsRuntime.InvokeVoidAsync("DraftEditor.initialize", _canvasElement, _dotNetReference, DraftContent, _unitWaring);
        }

        if (_draftNameChanged)
        {
            await JsRuntime.InvokeVoidAsync("DraftEditor.setName", _draftName);
            _draftNameChanged = false;
        }
    }

    public async ValueTask DisposeAsync()
    {
        if (_dotNetReference != null)
        {
            await JsRuntime.InvokeVoidAsync("DraftEditor.dispose");
            _dotNetReference.Dispose();
        }
    }

    #endregion

    private string IsActive(string state)
    {
        return CurrentState == state ? "active" : "";
    }

    [JSInvokable]
    public async Task OnContentUpdated(string draftContent)
    {
        DraftContent = draftContent;
        await DraftContentChanged.InvokeAsync(draftContent);
    }

    [JSInvokable]
    public void OnMouseDown(double x, double y)
    {
        _dialogTitle = CurrentState switch
        {
            StateInsertingLength => "Wprowadź długość".Tr(),
            StateInsertingAngle => "Wprowadź kąt".Tr(),
            StateInsertingText => "Wprowadź tekst".Tr(),
            _ => _dialogTitle
        };
        switch (CurrentState)
        {
            case StateInsertingLength:
            case StateInsertingAngle:
                _clickedX = x;
                _clickedY = y;
                _showLengthAngleInput = true;
                _ = FocusTextInput(_lengthAngleInput);
                break;
            case StateInsertingText:
                _clickedX = x;
                _clickedY = y;
                _showTextInput = true;
                _ = FocusTextInput(_textInput);
                break;
        }

        StateHasChanged();
    }

    [JSInvokable]
    public async Task OnStateChanged(string state)
    {
        CurrentState = state;
        StateHasChanged();
        await OnState.InvokeAsync(state);
    }

    [JSInvokable]
    public void OnLengthSumChanged(string lengthSum)
    {
        LengthSum = lengthSum;
        StateHasChanged();
    }

    [JSInvokable]
    public void OnStartLengthAngleEdit(string text)
    {
        _currentText = text;
        _clickedX = 0;
        _clickedY = 0;
        _showLengthAngleInput = true;
        _ = FocusTextInput(_lengthAngleInput);
        StateHasChanged();
    }

    [JSInvokable]
    public void OnStartTextEdit(string text)
    {
        _currentText = text;
        _clickedX = 0;
        _clickedY = 0;
        _showTextInput = true;
        _ = FocusTextInput(_textInput);
        StateHasChanged();
    }

    [JSInvokable]
    public void OnToastWarning(string message)
    {
        ToastService.ShowWarning(message);
    }

    [JSInvokable]
    public void OnToastError(string message)
    {
        ToastService.ShowError(message);
    }

    [JSInvokable]
    public void OnUnknownUnitWarning(string message)
    {
        ToastService.ShowWarning("\"{0}\", użyję mm".Tr(message), "Nieznana jednostka".Tr());
    }

    private async Task Save()
    {
        var draftContent = await JsRuntime.InvokeAsync<string>("DraftEditor.getDraftContent");
        var pngContent = await GetPng(1D);
        await OnSave.InvokeAsync(new SaveRecord(draftContent, pngContent));
    }

    public async Task<byte[]> GetPng(double scale = 1D)
    {
        var blob = await JsRuntime.InvokeAsync<IJSStreamReference>(
            "DraftEditor.getPng",
            scale
        );

        // Pobierz dane jako stream
        await using var streamRef = await blob.OpenReadStreamAsync();
        using var memoryStream = new MemoryStream();
        await streamRef.CopyToAsync(memoryStream);

        return memoryStream.ToArray();
    }

    private async Task ToggleState(string state)
    {
        await JsRuntime.InvokeVoidAsync("DraftEditor.toggleState", state);
    }

    public async Task Undo()
    {
        await JsRuntime.InvokeVoidAsync("DraftEditor.undo");
    }

    public async Task Redo()
    {
        await JsRuntime.InvokeVoidAsync("DraftEditor.redo");
    }

    public async Task ZoomOut()
    {
        await JsRuntime.InvokeVoidAsync("DraftEditor.zoomOut");
    }

    public async Task ZoomIn()
    {
        await JsRuntime.InvokeVoidAsync("DraftEditor.zoomIn");
    }

    public void ToggleHelp()
    {
        _showHelp = !_showHelp;
    }

    public async Task ClearCanvas()
    {
        await JsRuntime.InvokeVoidAsync("DraftEditor.clear");
    }

    public async Task Close()
    {
        await JsRuntime.InvokeVoidAsync("DraftEditor.clear");
        await OnCancel.InvokeAsync();
    }

    private async Task FocusTextInput(ElementReference element)
    {
        await Task.Delay(50); // Daj czas na render
        await JsRuntime.InvokeVoidAsync("DraftEditor.focusElement", element);
    }

    private async Task LengthAngleInputKeyDownHandler(KeyboardEventArgs e)
    {
        switch (e.Key)
        {
            case "Escape":
                CancelLengthAngleDialog();
                break;
            case "Enter":
                await ConfirmLengthAngleDialog();
                break;
        }
    }

    private void CancelLengthAngleDialog()
    {
        _showLengthAngleInput = false;
        _currentText = "";
        _clickedX = 0;
        _clickedY = 0;
        StateHasChanged();
    }

    private async Task ConfirmLengthAngleDialog()
    {
        if (!string.IsNullOrWhiteSpace(_currentText))
        {
            var text = _currentText.Trim();
            var x = _clickedX;
            var y = _clickedY;

            _currentText = "";
            _clickedX = 0;
            _clickedY = 0;
            _showLengthAngleInput = false;

            switch (CurrentState)
            {
                case StateInsertingLength:
                    text = text.Replace(",", ".");
                    var match = System.Text.RegularExpressions.Regex.Match(text, 
                        @"^(\d*\.?\d+)\s*(mm|cm|dm|m)?$", 
                        System.Text.RegularExpressions.RegexOptions.IgnoreCase);
    
                    if (!match.Success)
                    {
                        ToastService.ShowError("Błędna wartość \"{0}\"".Tr(text));
                        return;
                    }
    
                    // Extract numeric value
                    var lengthDouble = double.Parse(match.Groups[1].Value, 
                        System.Globalization.CultureInfo.InvariantCulture);
    
                    // Extract unit
                    var unit = match.Groups[2].Value.ToLowerInvariant();
    
                    // Convert based on unit
                    switch (unit)
                    {
                        case "":
                        case "mm":
                        case "cm":
                        case "dm":
                        case "m":
                            break;
                        default:
                            ToastService.ShowError("Nieprawidłowa jednostka miary".Tr());
                            return;
                    }
    
                    if (lengthDouble < 0.0)
                    {
                        ToastService.ShowError("Wartość nie może być mniejsza od zera".Tr());
                        return;
                    }
    
                    break;
                case StateInsertingAngle:
                    text = text.Replace(",", ".");
                    if (!double.TryParse(text,
                            System.Globalization.NumberStyles.Any,
                            System.Globalization.CultureInfo.InvariantCulture,
                            out var textDouble)
                       )
                    {
                        ToastService.ShowError("Błędna wartość \"{0}\"".Tr(text));
                        return;
                    }

                    if (textDouble < 0.0)
                    {
                        ToastService.ShowError("Wartość nie może być mniejsza od zera".Tr());
                        return;
                    }

                    break;
            }

            switch (CurrentState)
            {
                case StateInsertingLength:
                case StateInsertingAngle:
                    await JsRuntime.InvokeVoidAsync("DraftEditor.addText", x, y, text);
                    break;
                case StateEditingText:
                    await JsRuntime.InvokeVoidAsync("DraftEditor.changeText", text);
                    break;
            }
        }

        _showLengthAngleInput = false;

        StateHasChanged();
    }

    private async Task TextKeyDownHandler(KeyboardEventArgs e)
    {
        switch (e.Key)
        {
            case "Escape":
                CancelTextDialog();
                break;
            case "Enter" when e.ShiftKey || e.CtrlKey:
                await ConfirmTextDialog();
                break;
        }
    }

    private void CancelTextDialog()
    {
        _showTextInput = false;
        _currentText = "";
        _clickedX = 0;
        _clickedY = 0;
        StateHasChanged();
    }

    private async Task ConfirmTextDialog()
    {
        if (!string.IsNullOrWhiteSpace(_currentText))
        {
            var text = _currentText.Trim();
            var x = _clickedX;
            var y = _clickedY;

            _currentText = "";
            _clickedX = 0;
            _clickedY = 0;
            _showTextInput = false;

            switch (CurrentState)
            {
                case StateInsertingText:
                    await JsRuntime.InvokeVoidAsync("DraftEditor.addText", x, y, text);
                    break;
                case StateEditingText:
                    await JsRuntime.InvokeVoidAsync("DraftEditor.changeText", text);
                    break;
            }
        }

        _showLengthAngleInput = false;

        StateHasChanged();
    }

}
