@using EMessa.Core.Features.ShoppingCart.Models
@using FluentValidation
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor

<EditForm EditContext="_editContext">
    <RadzenStack Orientation="@(AppStateService.IsLargeScreen ? Orientation.Horizontal : Orientation.Vertical)"
                 JustifyContent="JustifyContent.Start" AlignItems="AlignItems.Center"
                 Gap="0.5rem">
        <RadzenLabel Text="@("Nazwa szkicu".Tr())" />
        <RadzenTextBox @bind-Value="@_model.FileName"
                       Disabled="@FileNameReadOnly"
                       Placeholder="@("Nazwa szkicu".Tr())"
                       Style="width: 200px;" />
        <FluentValidator TValidator="FileNameValidator" />
        <ValidationMessage For="@(() => _model.FileName)" />
    </RadzenStack>
</EditForm>
<div class="mt-4 w-full transition-all duration-300 ease-in-out">
    <DraftEditor DraftName="@_model.FileName"
                 @bind-DraftContent="@_draftContent"
                 OnSave="@HandleSave"
                 OnCancel="@HandleCancel" />
</div>

@code {

    [Parameter]
    public BaseOrderItemEditModel EditItem { get; set; } = new();

    [Parameter]
    public bool FileNameReadOnly { get; set; }

    [Parameter]
    public EventCallback<SaveRecord> OnSave { get; set; }

    [Parameter]
    public EventCallback OnCancel { get; set; }

    [Parameter]
    public Func<string> GenerateFileName { get; set; } = () => "Default";

    private EditContext _editContext = null!;
    private readonly FileNameModel _model = new();
    private string _draftContent = "";

    protected override void OnInitialized()
    {
        _editContext = new EditContext(_model);

        // Inicjalizuj dane z Item
        _model.FileName = EditItem.DraftOriginalFileName ?? GenerateFileName();
        _draftContent = EditItem.DraftDrawingSource ?? "";
    }

    private async Task HandleSave(SaveRecord args)
    {
        // Walidacja
        var isValid = _editContext.Validate();
        if (!isValid)
        {
            return;
        }

        // Wywołaj callback rodzica
        await OnSave.InvokeAsync(args);
    }

    private async Task HandleCancel()
    {
        await OnCancel.InvokeAsync();
    }

    public class FileNameModel
    {
        public string FileName { get; set; } = "";
    }

    public class FileNameValidator : AbstractValidator<FileNameModel>
    {
        public FileNameValidator()
        {
            RuleFor(x => x.FileName)
                .NotEmpty().WithMessage("Nazwa szkicu jest wymagana");
        }
    }

}