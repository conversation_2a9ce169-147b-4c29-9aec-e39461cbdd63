@using <PERSON><PERSON><PERSON>
@using Ra<PERSON>zen.Blazor

@inject NavigationManager NavigationManager
@implements IDisposable

<style>
    .rz-splitbutton-menu.rz-popup {
        width: 200px;
        transform: translateX(-50%); /* przesuwa menu w lewo o połowę jego szerokości */
        left: 50%; /* centruje względem przycisku */
    }
</style>

<AuthorizeView>
    <Authorized>
        @{ var user = AppStateService.UserData; }
        <RadzenSplitButton Text="@user.Initials" ButtonStyle="ButtonStyle.Base" Variant="Variant.Outlined"
                           class="bg-white hover:shadow-3-strong text-xl font-bold max-sm:[&_.rz-button]:!px-0"
                           Click=@(args => OnSplitButtonClick(args, "splitButton"))>
            <ChildContent>
                @* todo dlaczego menu przesuwa sie w prawo? *@
                <div class="p-2 bg-white min-w-56 rounded-md">
                    <RadzenSplitButtonItem Text="@($"{user.UserFirstName} {user.UserLastName}")" Value=""
                                           class="font-bold text-base"/>
                    <RadzenSplitButtonItem Text="@user.Email" Value=""
                                           class="font-bold text-sm text-blue-800"/>
                    <RadzenSplitButtonItem Text="@("Profil".Tr())" Icon="account_circle" Value="profile"
                                           Style="margin-left: -8px;"/>
                    <RadzenSplitButtonItem Text="@("Wyloguj".Tr())" Icon="logout" Value="logout"
                                           Style="margin-left: -8px;"/>
                </div>
            </ChildContent>
        </RadzenSplitButton>
    </Authorized>

    <NotAuthorized>
    </NotAuthorized>
</AuthorizeView>

@code{

    protected override async Task OnInitializedAsync()
    {
        AppStateService.StateChanged += OnStateChanged;
        await base.OnInitializedAsync();
    }

    void IDisposable.Dispose()
    {
        AppStateService.StateChanged -= OnStateChanged;
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        if (source != this && property == AppStateNotifyProperties.SetUserData)
        {
            _ = InvokeAsync(StateHasChanged);
        }
    }

    void OnSplitButtonClick(RadzenSplitButtonItem? item, string buttonName)
    {
        switch (item?.Value)
        {
            case "profile":
                NavigationManager.NavigateTo("/profile");
                break;
            case "logout":
                NavigationManager.NavigateTo("/account/logout", forceLoad: true);
                break;
        }
    }

}