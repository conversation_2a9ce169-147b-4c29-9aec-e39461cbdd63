using EMessa.Core.Localizer;

namespace EMessa.Web.Shared
{
    public class RadzenDataFilterLocalized<TItem> : Radzen.Blazor.RadzenDataGrid<TItem>
    {
        public RadzenDataFilterLocalized()
        {
            // Operatory porównania
            EqualsText = "Równe".Tr();
            NotEqualsText = "Różne".Tr();
            LessThanText = "Mniejsze niż".Tr();
            LessThanOrEqualsText = "Mniejsze lub równe".Tr();
            GreaterThanText = "Większe niż".Tr();
            GreaterThanOrEqualsText = "Większe lub równe".Tr();

            // Operatory tekstowe
            ContainsText = "Zawiera".Tr();
            DoesNotContainText = "Nie zawiera".Tr();
            StartsWithText = "Zaczyna się od".Tr();
            EndsWithText = "Kończy się na".Tr();

            // Operatory logiczne
            AndOperatorText = "Oraz".Tr();
            OrOperatorText = "Lub".Tr();

            // Operatory null
            IsNullText = "Puste".Tr();
            IsNotNullText = "Niepuste".Tr();

            // Przycisk akcji
            ApplyFilterText = "Filtruj".Tr();
            ClearFilterText = "Wyczyść".Tr();
            FilterText = "Filtr".Tr();
        }
    }
}
