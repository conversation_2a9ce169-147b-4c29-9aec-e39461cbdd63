@using EMessa.Core.Features.ShoppingCart.Queries.GetShoppingCartItems
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor
@using Radzen.Blazor.Rendering

@inject IMediator Mediator
@inject ILogger<ShoppingCartComponent> Logger
@inject NavigationManager Navigation
@implements IDisposable

<RadzenButton @ref="_cartButton"
              ButtonType="ButtonType.Button"
              Variant="Variant.Outlined"
              ButtonStyle="ButtonStyle.Base"
              class="bg-white hover:shadow-3-strong max-sm:px-0 sm:!px-3"
              Shade="Shade.Default"
              Click="@(_ => _cartPopup.ToggleAsync(_cartButton.Element))"
              @ondblclick="@GoToShoppingCart">
    <RadzenStack Orientation="Orientation.Horizontal"
                 AlignItems="AlignItems.Center"
                 class="px-0 sm:px-1 normal-case gap-0">
        <RadzenIcon Icon="shopping_cart" class="max-sm:!hidden !text-3xl"
                    IconColor="@(_items.Count == 0 ? "black" : "red")" />
        <RadzenBadge Variant="Variant.Outlined"
                     BadgeStyle="@(_items.Count == 0 ? BadgeStyle.Dark : BadgeStyle.Danger)"
                     Style="font-size: 1.25rem; color: black;"
                     Text="@_items.Count.ToString()" class="sm:ml-2" />
    </RadzenStack>
</RadzenButton>

<Popup @ref=_cartPopup
       AutoFocusFirstElement="false"
       class="hidden absolute -translate-x-1/2 z-[1000] min-w-max overflow-hidden rounded-lg border-none bg-white bg-clip-padding text-left text-base shadow-lg dark:bg-neutral-700">
    <div class="p-2 w-[350px] flex flex-col gap-2">
        @if (_items.Count == 0)
        {
            <div class="p-1">
                <span>@("Koszyk jest pusty".Tr())</span>
            </div>
        }
        else
        {
            <div class="w-full p-1 flex justify-between hover:bg-gray-200">
               <span>
                   <a class="text-blue-600 hover:text-blue-800 text-md"
                      @onclick="@GoToShoppingCart"
                      href="">@("Pokaż koszyk".Tr())</a>
               </span>
            </div>
            <div class="max-h-[300px] overflow-y-auto f-col-0">
                @foreach (var (item, index) in _items.Select((item, index) => (item, index)))
                {
                    <RadzenStack Orientation="Orientation.Vertical"
                                 Gap="0"
                                 class="@($"p-1 {(index % 2 == 0 ? "bg-gray-50 hover:bg-gray-200" : "bg-gray-100 hover:bg-gray-200")}")">
                        <RadzenStack Orientation="Orientation.Horizontal"
                                     JustifyContent="JustifyContent.SpaceBetween">
                            <RadzenText Text="@($"{index + 1}. {item.ArticleName}")" TextStyle="TextStyle.Subtitle1" />
                            <RadzenText Text="@item.QuantityUnitString" TextStyle="TextStyle.Subtitle1" />
                        </RadzenStack>
                        <RadzenStack Orientation="Orientation.Horizontal"
                                     JustifyContent="JustifyContent.SpaceBetween">
                            @if (!string.IsNullOrEmpty(item.OptionColorString))
                            {
                                <span class="text-gray-600">@item.OptionColorString</span>
                            }
                            @if (item.Weight > 0m)
                            {
                                <span class="text-gray-600">@item.WeightUnitString</span>
                            }
                        </RadzenStack>
                    </RadzenStack>
                }
            </div>
        }
    </div>
</Popup>


@code {
    private RadzenButton _cartButton = null!;
    private Popup _cartPopup = null!;
    private List<GetShoppingCartOrderItemResponse> _items = [];

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        AppStateService.StateChanged += OnStateChanged;

        await LoadShoppingCartItems();
    }

    void IDisposable.Dispose()
    {
        AppStateService.SetCatalogVisibility(this, false);
        AppStateService.StateChanged -= OnStateChanged;
    }

    private void OnStateChanged(ComponentBase? source, string property)
    {
        List<string> props = [AppStateNotifyProperties.ShoppingCartChanged, AppStateNotifyProperties.SetUserData];
        if (source != this && props.Contains(property))
        {
            _ = LoadShoppingCartItems();
        }
    }

    private async Task LoadShoppingCartItems()
    {
        var result = await Mediator.Send(new GetShoppingCartOrderItemsQuery(AppStateService.GetUserProfileId()));
        if (result.Succeeded)
        {
            _items = result.Data;
        }
        else
        {
            Logger.LogError("LoadShoppingCartItems error");
        }

        await InvokeAsync(StateHasChanged);
    }

    private async Task GoToShoppingCart()
    {
        await _cartPopup.CloseAsync();
        Navigation.NavigateTo("/shoppingcart");
    }

}
