@using EMessa.Base.Enums
@using EMessa.Core.Features.CustomerLocalizations.Queries.Get
@using EMessa.Core.Features.Customers.Queries.Get
@using EMessa.Core.Features.Customers.Queries.GetAll
@using EMessa.Core.Features.Orders.Queries.Common
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor
@using EMessa.Web.Pages.Orders.Parts
@using EMessa.Web.Components.RadzenComponents.Badges

<div class="@Class">
    <RadzenStack Orientation="Orientation.Vertical" Gap="10px" class="p-0">
        <RadzenStack Orientation="Orientation.Vertical" Gap=".5rem">
            <RadzenStack Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center">
                <OrderStatusBadge Status="@Order.Status" CssClass="text-base" />
                <RadzenText TextStyle="TextStyle.H6"
                            class="m-0">
                    @("Zamówienie".Tr())
                    @if (!string.IsNullOrWhiteSpace(Order.No))
                    {
                        @(" ")
                        <strong>@Order.No</strong>
                    }
                    @if (!string.IsNullOrWhiteSpace(Order.CustomerNo))
                    {
                        @(" ")
                        <text> (@Order.CustomerNo)</text>
                    }
                </RadzenText>
            </RadzenStack>

            <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start"
                         class="mt-1">
                <RadzenFormField Variant="Variant.Outlined" Text="@("Klient".Tr())">
                    <ChildContent>
                        @if (_isOnlyClientOrClientManager)
                        {
                            if (_customer != null)
                            {
                                <RadzenTextBox Value="@_customer.ShortName" ReadOnly="true"
                                               class="w-full" />
                            }
                            else
                            {
                                <RadzenTextBox Value="@("Ładowanie...".Tr())" ReadOnly="true" class="w-full" />
                            }
                        }
                        else
                        {
                            <CustomerSelector TValue="int?"
                                              Value="@_customer?.Id"
                                              Selected="@OnCustomerSelected"
                                              ShowActiveOnly />
                        }
                    </ChildContent>
                </RadzenFormField>

                <RadzenFormField Variant="Variant.Outlined" Text="@("Lokalizacja".Tr())">
                    <ChildContent>
                        @if (_customer != null)
                        {
                            <LocalizationSelector TValue="int?"
                                                  CustomerId="@_customer.Id"
                                                  Value="@_mainLocalizationFromSelector?.Id"
                                                  Selected="@OnLocalizationSelected"
                                                  ShowActiveOnly />
                        }
                        else
                        {
                            <RadzenTextBox Value="@("Wybierz klienta".Tr())" ReadOnly class="w-full" />
                        }
                    </ChildContent>
                </RadzenFormField>

                <RadzenFormField Variant="Variant.Outlined" Text="@("Nr. własny".Tr())">
                    <ChildContent>
                        <RadzenTextBox @bind-Value="@Order.CustomerNo" />
                    </ChildContent>
                </RadzenFormField>

                @if (Order.Id > 0)
                {
                    <RadzenFormField Variant="Variant.Outlined" Text="@("Nr. Messa".Tr())">
                        <ChildContent>
                            <RadzenTextBox Value="@Order.MessaNo.ToString()" ReadOnly />
                        </ChildContent>
                    </RadzenFormField>
                }
            </RadzenStack>

            <RadzenRow Gap="1rem" class="">
                <RadzenColumn Size="12" SizeMD="6">
                    <RadzenFormField Variant="Variant.Outlined" Text="@("Uwagi do zamówienia".Tr())"
                                     class="w-full">
                        <ChildContent>
                            <RadzenTextArea @bind-Value="@Order.Comments"
                                            Rows="3"
                                            class="w-full" />
                        </ChildContent>
                    </RadzenFormField>
                </RadzenColumn>
            </RadzenRow>


            <RadzenRow Gap="1rem" class="">
                <RadzenColumn Size="12" SizeMD="6">
                    <RadzenFormField Variant="Variant.Outlined" Text="@("Lokalizacja".Tr())"
                                     class="w-full">
                        <ChildContent>
                            <RadzenRow class="p-2">
                                <RadzenColumn>
                                    @if (_mainLocalizationFromSelector != null)
                                    {
                                        <LocalizationTemplate @ref="@_customerLocalizationTemplate"
                                                              @bind-Localization="_mainLocalizationFromSelector"
                                                              @bind-IsValid="_isValidMainLocalization"
                                                              ValidateOnInitialized />
                                    }
                                    else
                                    {
                                        <RadzenTextBox Value="@("Wybierz lokalizację".Tr())" ReadOnly
                                                       class="w-full" />
                                    }
                                </RadzenColumn>
                            </RadzenRow>
                        </ChildContent>
                    </RadzenFormField>
                </RadzenColumn>

                <RadzenColumn Size="12" SizeMD="6">
                    <RadzenFormField Variant="Variant.Outlined" Text="@("Adres dostawy".Tr())"
                                     class="w-full">
                        <ChildContent>
                            <RadzenRow class="p-2">
                                <RadzenColumn>
                                    <div class="flex justify-between gap-2">
                                        <CheckBox Label="@("Dostawa na inny adres".Tr())"
                                                  Disabled="@(_mainLocalizationFromSelector == null)"
                                                  Value="@Order.DifferentDeliveryLocalization"
                                                  ValueChanged="@DeliveryChanged" />
                                        @if (Order.DifferentDeliveryLocalization && _mainLocalizationFromSelector != null)
                                        {
                                            <RadzenButton Text="@("Skopiuj".Tr())"
                                                          Icon="content_copy"
                                                          ButtonStyle="ButtonStyle.Light"
                                                          Size="ButtonSize.Small"
                                                          Click="@CopyDeliveryAddress" />
                                        }
                                    </div>
                                    @if (Order.DifferentDeliveryLocalization && _differentDeliveryLocalization != null)
                                    {
                                        <LocalizationTemplate @ref="@_differentDeliveryLocalizationTemplate"
                                                              @bind-Localization="_differentDeliveryLocalization"
                                                              @bind-IsValid="_isValidDeliveryLocalization"
                                                              ValidateOnInitialized />
                                    }

                                </RadzenColumn>
                            </RadzenRow>
                        </ChildContent>
                    </RadzenFormField>
                </RadzenColumn>
            </RadzenRow>

            <OrderItemGrid OrderItems="@Order.OrderItems"
                           ShowDraftColumn
                           ShowSaleColumn
                           ShowOptionValuesTable
                           ShowSheetsTable
                           ShowActionsColumn
                           EditOrderItem="@EditOrderItem"
                           DeleteOrderItem="@DeleteOrderItem" />
        </RadzenStack>
    </RadzenStack>
    <GridToolbar>
        <SaveRadzenButton Click="@SaveOrder" />
        <CancelRadzenButton Click="@Close" />
    </GridToolbar>
</div>

@code {
    [Parameter] public required OrderResponse Order { get; set; }
    [Parameter] public EventCallback<int> EditOrderItem { get; set; }
    [Parameter] public EventCallback<int> DeleteOrderItem { get; set; }
    [Parameter] public EventCallback Save { get; set; }
    [Parameter] public EventCallback Close { get; set; }
    [Parameter] public string Class { get; set; } = "";

    private bool _isOnlyClientOrClientManager;
    private GetAllCustomersResponse? _customer;
    private GetCustomerLocalizationsResponse? _mainLocalizationFromSelector;
    private GetCustomerLocalizationsResponse? _differentDeliveryLocalization;
    private bool _isValidMainLocalization;
    private bool _isValidDeliveryLocalization;
    private LocalizationTemplate? _customerLocalizationTemplate;
    private LocalizationTemplate? _differentDeliveryLocalizationTemplate;

    #region Initialize, Dispoze

    protected override void OnParametersSet()
    {
        base.OnParametersSet();
        PresetData();
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        _isOnlyClientOrClientManager = Role.HasAnyRole(AppStateService.UserData.Roles, Role.Client, Role.ClientManager);
    }

    #endregion

    /// <summary>
    /// Załaduj dane z Order
    /// </summary>
    private void PresetData()
    {
        if (_customer == null && Order.CustomerId > 0)
        {
            // Klient i KlientMenadżer muszą sobie pobrać klienta
            if (_isOnlyClientOrClientManager)
            {
                _ = GetCustomerForClients(Order.CustomerId);
            }
            else
            {
                _customer = new GetAllCustomersResponse
                {
                    Id = Order.CustomerId
                };
            }
        }

        if (_differentDeliveryLocalization == null && Order.DifferentDeliveryLocalization)
        {
            _differentDeliveryLocalization = Order.Localizations.FirstOrDefault(x => x.Type == LocalizationType.Delivery);
        }

        _mainLocalizationFromSelector = Order.Localizations.FirstOrDefault(x => x.Type == LocalizationType.Main);
        _differentDeliveryLocalization = Order.Localizations.FirstOrDefault(x => x.Type == LocalizationType.Delivery);
    }
    
    private async Task GetCustomerForClients(int customerId)
    {
        var response = await Mediator.Send(new GetCustomerQuery(customerId));
        if (response.Succeeded)
        {
            _customer = Mapper.Map<GetAllCustomersResponse>(response.Data);
            StateHasChanged();
        }
        else
        {
            ToastService.Show(ToastType.LoadDataError, response.Messages);
        }
    }

    private async Task SaveOrder()
    {
        _customerLocalizationTemplate?.ValidateModel();
        _differentDeliveryLocalizationTemplate?.ValidateModel();

        var isInvalid = false;
        if (_customer == null)
        {
            ToastService.ShowWarning("Wybierz klienta".Tr());
            isInvalid = true;
        }

        if (_mainLocalizationFromSelector == null)
        {
            ToastService.ShowWarning("Wybierz lokalizację".Tr());
            isInvalid = true;
        }

        if (!_isValidMainLocalization)
        {
            ToastService.ShowWarning("Popraw dane w lokalizacji".Tr());
            isInvalid = true;
        }

        if (Order.DifferentDeliveryLocalization && !_isValidDeliveryLocalization)
        {
            ToastService.ShowWarning("Uzupełnij dane w lokalizacji dostawy".Tr());
            isInvalid = true;
        }

        if (Order.DifferentDeliveryLocalization && _differentDeliveryLocalization == null)
        {
            ToastService.ShowError("Brak lokalizacji dostawy".Tr());
            isInvalid = true;
        }

        //todo sprawdzenie czy nadal jest dość materiału itp

        if (isInvalid)
            return;

        // Zbuduj kolekcję Order.Localizations na podstawie wybranych danych
        Order.Localizations = [];
        if (_mainLocalizationFromSelector != null)
        {
            Order.Localizations.Add(new GetCustomerLocalizationsResponse
            {
                Type = LocalizationType.Main,
                Address = _mainLocalizationFromSelector.Address,
                City = _mainLocalizationFromSelector.City,
                PostCode = _mainLocalizationFromSelector.PostCode
            });
        }

        if (Order.DifferentDeliveryLocalization && _differentDeliveryLocalization != null)
        {
            Order.Localizations.Add(new GetCustomerLocalizationsResponse
            {
                Type = LocalizationType.Delivery,
                Address = _differentDeliveryLocalization.Address,
                City = _differentDeliveryLocalization.City,
                PostCode = _differentDeliveryLocalization.PostCode
            });
        }

        await Save.InvokeAsync();
    }

    private void OnCustomerSelected(GetAllCustomersResponse? customer)
    {
        _customer = customer; 
        _mainLocalizationFromSelector = null;
    }

    private void OnLocalizationSelected(GetCustomerLocalizationsResponse? localization)
    {
        _mainLocalizationFromSelector = localization;
    }

    private void DeliveryChanged(bool differentDeliveryAddress)
    {
        Order.DifferentDeliveryLocalization = differentDeliveryAddress;
        if (differentDeliveryAddress && _mainLocalizationFromSelector != null)
        {
            _differentDeliveryLocalization = new GetCustomerLocalizationsResponse
            {
                Address = "",
                City = "",
                PostCode = "",
            };
        }
        else
        {
            _differentDeliveryLocalization = null;
        }
    }

    private void CopyDeliveryAddress()
    {
        if (_mainLocalizationFromSelector != null && _differentDeliveryLocalization != null)
        {
            _differentDeliveryLocalization = new GetCustomerLocalizationsResponse
            {
                Address = _mainLocalizationFromSelector.Address,
                City = _mainLocalizationFromSelector.City,
                PostCode = _mainLocalizationFromSelector.PostCode,
            };
        }
    }

}