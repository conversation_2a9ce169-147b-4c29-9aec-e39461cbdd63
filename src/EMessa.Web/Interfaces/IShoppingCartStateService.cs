using EMessa.Base.Models;
using EMessa.Core.Features.ShoppingCart.Models;
using EMessa.Core.Models;
using EMessa.Web.StateServices;

namespace EMessa.Web.Interfaces;

public interface IShoppingCartStateService
{
    Task<List<CartOrderItem>> GetOrderItems(int userProfileId);
    Task<SelectedOptionAndOptionValue?> PresetOptionValues(List<ArticleOptionEditModel> optionValues, int? orderItemId);

    event Action<SelectedOptionItem>? OptionItemChanged;
    void NotifyOptionItemChanged(SelectedOptionItem optionsMethodItem);

    SelectedOptionItem DisabledOptionItem { get; }
    List<DropDownListItem<SelectedOptionItem>> DisabledOptionsList { get; }
    List<DropDownListItem<SelectedOptionItem>> OptionsList { get; set; }
    SelectedOptionItem SelectedOptionItem { get; set; }
    SelectedOptionAndOptionValue? SelectedOptionAndOptionValue { get; set; }
}