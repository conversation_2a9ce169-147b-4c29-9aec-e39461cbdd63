using Ra<PERSON>zen;
using FilterOperator = Radzen.FilterOperator;

namespace EMessa.Web.Extensions;

public static class RadzenFiltersExtensions
{
    public static IEnumerable<FilterDescriptor> AppendGlobalSearchFilter(
        this IEnumerable<FilterDescriptor> filters,
        string? globalSearch)
    {
        return filters.Append(new FilterDescriptor
        {
            Property = "GlobalSearch",
            FilterValue = globalSearch ?? "",
            FilterOperator = FilterOperator.Contains
        });
    }

    public static IEnumerable<FilterDescriptor> AppendFilter(
        this IEnumerable<FilterDescriptor> filters,
        string property,
        object? value,
        FilterOperator filterOperator = FilterOperator.Equals)
    {
        return filters.Append(new FilterDescriptor
        {
            Property = property,
            FilterValue = value,
            FilterOperator = filterOperator
        });
    }

    public static IEnumerable<FilterDescriptor> AppendFilterWhenNoNull(
        this IEnumerable<FilterDescriptor> filters,
        string property,
        object? value,
        FilterOperator filterOperator = FilterOperator.Equals)
    {
        if (value == null)
            return filters;
        return filters.Append(new FilterDescriptor
        {
            Property = property,
            FilterValue = value,
            FilterOperator = filterOperator
        });
    }
}