namespace EMessa.Web.Extensions;

public static class TextExtensions
{
    public static string AddText(this string text, string? appendText)
    {
        if (string.IsNullOrEmpty(appendText))
            return text;

        return text + appendText;
    }

    public static string AddText(this string text, char? appendText)
    {
        if (appendText == null)
            return text;

        return text + appendText;
    }
}
