namespace EMessa.Web.Extensions;

public static class DateFormatProvider
{
    /// <summary>
    /// dd.MM.yyyy
    /// </summary>
    public static string DisplayDateFormat => "dd.MM.yyyy";

    /// <summary>
    /// dd.MM.yyyy HH:mm
    /// </summary>
    public static string DisplayDateTimeFormat => "dd.MM.yyyy HH:mm";

    /// <summary>
    /// yyyy-MM-ddTHH:mm:ss.fffffffZ - używane do filtrów Sieve
    /// </summary>
    public static string UtcDateTimeFormat => "yyyy-MM-ddTHH:mm:ss.fffffffZ";

    public static string? ToDisplayDate(this DateTime? date) =>
        date?.ToString(DisplayDateFormat);

    public static string? ToDisplayLocalDate(this DateTime? date) =>
        date?.ToLocalTime().ToString(DisplayDateFormat);

    public static string ToDisplayLocalDate(this DateTime date) =>
        date.ToLocalTime().ToString(DisplayDateFormat);

    public static string? ToDisplayLocalDateTime(this DateTime? dateTime) =>
        dateTime?.ToLocalTime().ToString(DisplayDateTimeFormat);

    public static string ToDisplayLocalDateTime(this DateTime dateTime) =>
        dateTime.ToLocalTime().ToString(DisplayDateTimeFormat);

    public static DateTime? ToLocalDateTime(this DateTime? dateTime) =>
        dateTime?.ToLocalTime();

    public static DateTime ToLocalDateTime(this DateTime dateTime) =>
        dateTime.ToLocalTime();

    public static string? ToUtcIsoString(this DateTime? dateTime) =>
        dateTime?.ToUniversalTime().ToString("o"); // ISO 8601 w UTC

    public static DateTime? ToUtcDateTime(this DateTime? dateTime) =>
        dateTime?.ToUniversalTime();

    public static DateTime ToUtcDateTime(this DateTime dateTime) =>
        dateTime.ToUniversalTime();

    public static string? ToUtcDateTimeStringForSieveFilter(this DateTime? dateTime)
    {
        if (dateTime == null)
            return null;

        return dateTime.Value.ToUtcDateTime().ToString(UtcDateTimeFormat);
    }
}
