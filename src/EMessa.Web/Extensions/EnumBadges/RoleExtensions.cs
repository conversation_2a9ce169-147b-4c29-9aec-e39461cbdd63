using EMessa.Base.Constants;

namespace EMessa.Web.Extensions.EnumBadges;

public static class RoleExtensions
{
    public static Dictionary<string, string> RoleColorMap => new()
    {
        [Role.Client] = "#81c784",
        [Role.ClientManager] = "#4caf50",
        [Role.Trade] = "#64b5f6",
        [Role.TradeManager] = "#2196f3",
        [Role.Production] = "#ff9800",
        [Role.Administrator] = "#e53935",
        [Role.SaleManager] = "#ba68c8"
    };
}
