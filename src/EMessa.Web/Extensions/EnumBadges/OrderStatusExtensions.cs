using EMessa.Base.Enums;

namespace EMessa.Web.Extensions;

public static class OrderStatusExtensions
{
    public static string ColorCssClassBg(this OrderStatus s)
    {
        return $"!bg-{s.ColorStatusCssSubclass()}";
    }

    public static string ColorStatusCssSubclass(this OrderStatus s)
    {
        return s switch
        {
            OrderStatus.New => "green-600",
            OrderStatus.Saved => "emerald-600",
            OrderStatus.Sent => "lime-600",
            OrderStatus.Accepted => "teal-700",
            OrderStatus.Rejected => "orange-500",
            OrderStatus.Preparation => "cyan-600",
            OrderStatus.Production => "blue-600",
            OrderStatus.Warehouse => "indigo-600",
            OrderStatus.Transport => "purple-600",
            OrderStatus.Delivered => "slate-500",
            OrderStatus.Deleted => "red-500",
            OrderStatus.Unknown => "zinc-600",
            _ => "zinc-600"
        };
    }
}
