using EMessa.Base.Enums;

namespace EMessa.Web.Extensions.EnumBadges;

public static class ArticleQuantityLevelExtensions
{
    public static string ColorCssClassFg(this ArticleQuantityLevel s)
    {
        return $"!text-{s.ColorStatusCssSubclass()}";
    }

    public static string ColorStatusCssSubclass(this ArticleQuantityLevel s)
    {
        return s switch
        {
            ArticleQuantityLevel.None => "zinc-600",
            ArticleQuantityLevel.Low => "red-500",
            ArticleQuantityLevel.Medium => "orange-500",
            ArticleQuantityLevel.High=> "green-600",
            _ => "zinc-600"
        };
    }
}
