using Messa.Core.BL.eMessa.Sales.ViewModels;

namespace EMessa.Web.Extensions.EnumBadges;

public static class SaleStatusExtensions
{
    public static string ColorCssClassBg(dynamic status)
    {
        if (status is SaleStatus saleStatus)
        {
            return saleStatus.ColorCssClassBg();
        }

        if (status is string statusStr && Enum.TryParse<SaleStatus>(statusStr, ignoreCase: true, out var parsedStatus))
        {
            return parsedStatus.ColorCssClassBg();
        }

        return $"!bg-zinc-500";
    }

    public static string ColorCssClassBg(this SaleStatus s)
    {
        return $"!bg-{s.ColorStatusCssSubclass()}";
    }

    public static string ColorStatusCssSubclass(this SaleStatus s)
    {
        return s switch
        {
            SaleStatus.New => "green-600",
            SaleStatus.Active => "blue-500",
            SaleStatus.Closed => "gray-600",
            SaleStatus.Canceled => "red-500",
            _ => "zinc-500"
        };
    }
}
