using EMessa.Base.Enums;

namespace EMessa.Web.Extensions.EnumBadges;

public static class RollStatusExtensions
{
    public static string ColorCssClassFg(this RollStatus s)
    {
        return s switch
        {
            RollStatus.New => "!text-green-600",
            RollStatus.Registered => "!text-amber-700",
            RollStatus.Active => "!text-blue-500",
            RollStatus.Closed => "!text-zinc-900",
            RollStatus.Sent => "!text-gray-500",
            RollStatus.Unknown => "!text-red-500",
            _ => "!text-zinc-500"
        };
    }
}
