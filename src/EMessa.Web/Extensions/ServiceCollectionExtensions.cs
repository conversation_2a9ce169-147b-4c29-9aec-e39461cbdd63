using EMessa.Core.Data;
using EMessa.Core.Interfaces;
using EMessa.Core.Services;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Identity;
using MessaApi;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using OptApi;
using Serilog;
using TranslationService;
using ILogger = Serilog.ILogger;

namespace EMessa.Web.Extensions;

public static class AppBuilderSerilogExtensions
{
    public static void SetupSerilog(this WebApplicationBuilder builder)
    {
        builder.Logging.ClearProviders();
        ILogger logger = new LoggerConfiguration().ReadFrom.Configuration(builder.Configuration).CreateLogger();
        builder.Logging.AddSerilog(logger);
        builder.Services.AddSingleton(logger);
    }
}

public static class ServiceCollectionExtensions
{
    internal static IServiceCollection AddDataAccess(this IServiceCollection services, IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("DefaultConnection") ?? throw new InvalidOperationException("Connection string 'DefaultConnection' not found.");

        services.AddTransient<IDatabaseSeeder, DatabaseSeeder>();
        services.AddDbContextFactory<ApplicationDbContext>(options =>
        {
            options.UseSqlServer(
                connectionString,
                optionsBuilder =>
                {
                    optionsBuilder.MigrationsAssembly(typeof(ApplicationDbContext).Assembly.FullName);
                    //optionsBuilder.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery);
                });
            options.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
            //options.ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning));
        });

        services.AddDatabaseDeveloperPageExceptionFilter();

        services.AddAuthentication(o =>
            {
                o.DefaultScheme = IdentityConstants.ApplicationScheme;
                o.DefaultSignInScheme = IdentityConstants.ExternalScheme;
            })
            .AddIdentityCookies();

        services.ConfigureApplicationCookie(options =>
        {
            options.LoginPath = "/Account/Login";
            options.LogoutPath = "/Account/Logout";
        });

        services.AddIdentityCore<ApplicationUser>(options =>
            {
                options.Stores.MaxLengthForKeys = 128;
                options.SignIn.RequireConfirmedAccount = true;
            })
            .AddRoles<ApplicationRole>()
            .AddEntityFrameworkStores<ApplicationDbContext>()
            .AddDefaultTokenProviders()
            .AddSignInManager();

        return services;
    }

    internal static IServiceCollection AddTranslationService(this IServiceCollection services, IConfiguration configuration)
    {
        var config = configuration.GetSection("AzureTranslationApi");
        var address = config.GetValue<string>("ApiAddress") ?? "";
        var region = config.GetValue<string>("ApiRegion") ?? "";
        var key1 = config.GetValue<string>("ApiKey1") ?? "";
        var key2 = config.GetValue<string>("ApiKey2") ?? "";

        services.AddSingleton<ISimpleTranslationService>(new AzureTranslationService(address, region, key1, key2));
        return services;
    }

    public static IServiceCollection AddRollDataHttpClient(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddHttpClient<RollDataService>((serviceProvider, client) =>
        {
            var config = configuration.GetSection("RollsApi");
            var rollsApiBaseAddress = config.GetValue<string>("RollsApiBaseAddress");
            var timeoutSeconds = config.GetValue<int>("TimeoutSeconds", 10);

            if (!string.IsNullOrWhiteSpace(rollsApiBaseAddress) &&
                Uri.TryCreate(rollsApiBaseAddress, UriKind.Absolute, out var uri))
            {
                client.BaseAddress = uri;
            }
            client.Timeout = TimeSpan.FromSeconds(timeoutSeconds);
        });

        return services;
    }

    public static IServiceCollection AddMessaApiClient(this IServiceCollection services, IConfiguration configuration)
    {
        var config = configuration.GetSection("MessaApi");
        var messaApiBaseAddress = config.GetValue<string>("MessaApiBaseAddress") ?? "";
        var timeoutSeconds = config.GetValue<int>("TimeoutSeconds", 10);

        MessaApiRegisterer.Register(services, messaApiBaseAddress, timeoutSeconds);

        return services;
    }

    public static IServiceCollection AddOptimaApiClient(this IServiceCollection services, IConfiguration configuration)
    {
        var config = configuration.GetSection("OptimaApi");
        var optimaApiBaseAddress = config.GetValue<string>("OptimaApiBaseAddress") ?? "";
        var timeoutSeconds = config.GetValue<int>("TimeoutSeconds", 10);

        CapiApiRegisterer.Register(services, optimaApiBaseAddress, timeoutSeconds);

        return services;
    }
}
