using System.Collections;
using System.Globalization;
using Microsoft.IdentityModel.Tokens;
using Ra<PERSON>zen;
using Sieve.Models;

namespace EMessa.Web.Extensions;

public static class RadzenSieveExtensions
{
    private static readonly ILogger Logger = LoggerFactory.Create(builder => builder.AddConsole())
        .CreateLogger(nameof(RadzenSieveExtensions));

    /// <summary>
    /// Converts a Radzen LoadDataArgs object to a simple, non-generic SieveModel
    /// containing Filters, Sorts, Page, and PageSize.
    /// </summary>
    /// <param name="args">The Radzen LoadDataArgs instance.</param>
    /// <returns>A new SieveModel populated from the LoadDataArgs.</returns>
    public static SieveModel ToSieveModel(this LoadDataArgs args)
    {
        ArgumentNullException.ThrowIfNull(args);

        var sieveModel = new SieveModel();

        // --- Paging ---
        if (args.Top is > 0)
        {
            sieveModel.PageSize = args.Top.Value;
            sieveModel.Page = args.Skip.HasValue ? args.Skip.Value / args.Top.Value + 1 : 1;
        }
        else
        {
            sieveModel.Page = null;
            sieveModel.PageSize = null;
        }

        // --- Sorting ---
        if (args.Sorts != null && args.Sorts.Any())
        {
            var sortStrings = args.Sorts
                .Where(s => s.SortOrder.HasValue && !string.IsNullOrWhiteSpace(s.Property))
                .Select(s => s.SortOrder == SortOrder.Descending ? $"-{s.Property}" : s.Property);

            sieveModel.Sorts = string.Join(",", sortStrings);
        }
        else if (!string.IsNullOrWhiteSpace(args.OrderBy))
        {
            var parts = args.OrderBy.Split(new[] { ' ', ',' }, StringSplitOptions.RemoveEmptyEntries);
            var sieveSorts = new List<string>();
            for (var i = 0; i < parts.Length; i++)
            {
                var property = parts[i];
                var direction =
                    i + 1 < parts.Length && parts[i + 1].Equals("DESC", StringComparison.OrdinalIgnoreCase)
                        ? "-"
                        : "";
                sieveSorts.Add($"{direction}{property}");
                if (!string.IsNullOrEmpty(direction)) i++;
            }
            sieveModel.Sorts = string.Join(",", sieveSorts);
        }

        // --- Filtering ---
        if (args.Filters != null && args.Filters.Any())
        {
            var filterStrings = new List<string>();
            foreach (var filter in args.Filters)
            {
                if (filter == null) continue;
                
                var propName = !string.IsNullOrWhiteSpace(filter.FilterProperty)
                    ? filter.FilterProperty
                    : filter.Property;
                if (string.IsNullOrWhiteSpace(propName)) continue;

                var primaryFilterString =
                    ConvertFilterDescriptorToSieveString(propName, filter.FilterOperator, filter.FilterValue);
                if (primaryFilterString == null) continue;

                if (filter.SecondFilterValue != null ||
                    filter.SecondFilterOperator == Radzen.FilterOperator.IsNotNull ||
                    filter.SecondFilterOperator == Radzen.FilterOperator.IsNull ||
                    filter.SecondFilterOperator == Radzen.FilterOperator.IsNotEmpty ||
                    filter.SecondFilterOperator == Radzen.FilterOperator.IsEmpty)
                {
                    var secondaryFilterString = ConvertFilterDescriptorToSieveString(propName,
                        filter.SecondFilterOperator, filter.SecondFilterValue!);
                    if (secondaryFilterString != null)
                    {
                        if (filter.LogicalFilterOperator == LogicalFilterOperator.Or)
                        {
                            filterStrings.Add($"({primaryFilterString}|{secondaryFilterString})");
                        }
                        else // Default to AND
                        {
                            filterStrings.Add(primaryFilterString);
                            filterStrings.Add(secondaryFilterString);
                        }
                    }
                    else
                    {
                        filterStrings.Add(primaryFilterString);
                    }
                }
                else
                {
                    filterStrings.Add(primaryFilterString);
                }
            }
            sieveModel.Filters = string.Join(",", filterStrings);
        }
        else if (!string.IsNullOrWhiteSpace(args.Filter))
        {
            Logger.LogWarning("Direct conversion from Radzen's Filter string to Sieve's Filters string is not fully supported. Using structured Filters is recommended.");
            // Można rozważyć przypisanie bezpośrednie, ale z ostrożnością:
            // sieveModel.Filters = args.Filter;
        }

        return sieveModel;
    }

    // --- Prywatne metody pomocnicze (pozostają bez zmian) ---
    // Te metody są nadal potrzebne do konwersji filtrów

    private static string? ConvertFilterDescriptorToSieveString(string propertyName, Radzen.FilterOperator op, object value)
    {
        var sieveOp = MapFilterOperatorToSieve(op);
        if (sieveOp == null)
            return null;

        if (op is Radzen.FilterOperator.IsNull or Radzen.FilterOperator.IsNotNull)
            return $"{propertyName}{sieveOp}";

        if (op is Radzen.FilterOperator.IsEmpty or Radzen.FilterOperator.IsNotEmpty)
            return $"{propertyName}{sieveOp}{FormatSieveValue("")}";

        if (op is Radzen.FilterOperator.In or Radzen.FilterOperator.NotIn
            && value is IEnumerable enumerable and not string)
        {
            var values = enumerable.Cast<object>().Select(FormatSieveValue).Where(v => v != null).ToList();
            if (values.Count == 0)
                return null;

            var combinedValueFilter = string.Join("|", values.Select(v => $"{propertyName}=={v}"));
            return op == Radzen.FilterOperator.In ? $"({combinedValueFilter})" : $"!({combinedValueFilter})";
        }

        var formattedValue = FormatSieveValue(value);
        if (formattedValue == null && op != Radzen.FilterOperator.IsNull && op != Radzen.FilterOperator.IsNotNull)
            return null;

        return $"{propertyName}{sieveOp}{formattedValue}";
    }

    private static string? MapFilterOperatorToSieve(Radzen.FilterOperator radzenOp)
    {
        return radzenOp switch
        {
            Radzen.FilterOperator.Equals => "==",
            Radzen.FilterOperator.NotEquals => "!=",
            Radzen.FilterOperator.LessThan => "<",
            Radzen.FilterOperator.LessThanOrEquals => "<=",
            Radzen.FilterOperator.GreaterThan => ">",
            Radzen.FilterOperator.GreaterThanOrEquals => ">=",
            Radzen.FilterOperator.Contains => "@=*",
            Radzen.FilterOperator.DoesNotContain => "!@=*",
            Radzen.FilterOperator.StartsWith => "_=",
            Radzen.FilterOperator.EndsWith => "_-=",
            Radzen.FilterOperator.IsNull => "==null",
            Radzen.FilterOperator.IsNotNull => "!=null",
            Radzen.FilterOperator.IsEmpty => "==",
            Radzen.FilterOperator.IsNotEmpty => "!=",
            Radzen.FilterOperator.In => "==", // Placeholder
            Radzen.FilterOperator.NotIn => "==", // Placeholder
            Radzen.FilterOperator.Custom => LogWarningAndReturnNull(radzenOp),
            _ => LogWarningAndReturnNull(radzenOp)
        };
    }

    private static string? LogWarningAndReturnNull(Radzen.FilterOperator radzenOp)
    {
        Logger.LogWarning("Radzen.FilterOperator '{radzenOp}' not directly mappable to Sieve.", radzenOp);
        return null;
    }

    private static string? FormatSieveValue(object? value)
    {
        if (value == null)
            return null;

        if (value is string strValue)
        {
            // --- ZMIANA ---
            // Usuwamy otaczające cudzysłowy " "
            // Nadal escapujemy przecinek i backslash, aby uniknąć problemów z separatorami i samym escapowaniem
            // Escapowanie cudzysłowu " nie jest już konieczne
            return strValue.Replace("\\", "\\\\").Replace(",", "\\,");
            // --- KONIEC ZMIANY ---
        }
        
        var type = value.GetType();

        // Reszta typów bez zmian
        return value switch
        {
            DateTime dt => dt.ToString("o", CultureInfo.InvariantCulture),
            DateTimeOffset dto => dto.ToString("o", CultureInfo.InvariantCulture),
            bool b => b.ToString().ToLowerInvariant(),
            Guid guid => guid.ToString(),
            _ when type.IsEnum => value.ToString(),
            _ when IsNumericType(type) => Convert.ToString(value, CultureInfo.InvariantCulture),

            // Dla innych typów - zwracamy ToString() bez cudzysłowów. Może to być problematyczne.
            _ => value.ToString()?.Replace("\\", "\\\\").Replace(",", "\\,")
        };
    }

    private static bool IsNumericType(Type? type)
    {
        if (type == null)
            return false;

        type = Nullable.GetUnderlyingType(type) ?? type;

        return NumericTypes.Contains(type);
    }

    private static readonly HashSet<Type> NumericTypes =
    [
        // Typy całkowite
        typeof(int), typeof(uint), typeof(long), typeof(ulong),
        typeof(short), typeof(ushort), typeof(byte), typeof(sbyte),
        // Typy zmiennoprzecinkowe
        typeof(float), typeof(double), typeof(decimal)
    ];

    public static SieveModel ApplyGlobalSearchFilter(this SieveModel sieveModel, string? globalSearch = "")
    {
        return sieveModel.AddFilterString($"GlobalSearch@={globalSearch ?? ""}");
    }

    public static SieveModel AddFilterString(this SieveModel sieveModel, string filterString)
    {
        if (sieveModel.Filters.IsNullOrEmpty())
            sieveModel.Filters = filterString;
        else
            sieveModel.Filters += $",{filterString}";

        return sieveModel;
    }

    public static SieveModel AddFilter(
        this SieveModel sieveModel, string propertyName, object? value, string operatorSymbol = "==")
    {
        var filterString = $"{propertyName}{operatorSymbol}{FormatSieveValue(value)}";
        return sieveModel.AddFilterString(filterString);
    }

    public static SieveModel AddFilterWhenNoNull(
        this SieveModel sieveModel, string propertyName, object? value, string operatorSymbol = "==")
    {
        if (value == null)
        {
            sieveModel.Filters ??= "";
            return sieveModel;
        }

        var filterString = $"{propertyName}{operatorSymbol}{FormatSieveValue(value)}";
        return sieveModel.AddFilterString(filterString);
    }

}
