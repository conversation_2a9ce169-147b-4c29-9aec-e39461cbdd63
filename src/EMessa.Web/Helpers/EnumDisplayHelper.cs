using EMessa.Core.Common;
using EMessa.Core.Localizer;

namespace EMessa.Web.Helpers;

public static class EnumDisplayHelper
{
    public static List<TEnum> GetAllEnumValues<TEnum>() where TEnum : struct, Enum
    {
        return [.. Enum.GetValues<TEnum>()];
    }

    public static string GetEnumTrDisplayName<TEnum>(dynamic enumValue)
        where TEnum : struct, Enum
    {
        try
        {
            if (enumValue is null)
                return string.Empty;

            if (enumValue is TEnum articleType)
            {
                return articleType.GetDisplayName().Tr();
            }

            if (enumValue is string enumStr && Enum.TryParse<TEnum>(enumStr, ignoreCase: true, out var parsedStatus))
            {
                return parsedStatus.GetDisplayName().Tr();
            }
        }
        catch (Exception)
        {
            return "";
        }

        return "";
    }
}
