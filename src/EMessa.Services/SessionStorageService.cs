using Microsoft.JSInterop;
using System.Text.Json;
using EMessa.Services.Interfaces;

namespace EMessa.Services;

/// <summary>
/// Implementacja serwisu do obsługi przechowywania danych w sessionStorage przeglądarki.
/// </summary>
public class SessionStorageService(IJSRuntime jsRuntime) : ISessionStorageService
{
    public async Task SetAsync<T>(string key, T value)
    {
        var serializedValue = JsonSerializer.Serialize(value);
        await jsRuntime.InvokeVoidAsync("sessionStorage.setItem", key, serializedValue);
    }

    public async Task<T> GetAsync<T>(string key)
    {
        return await GetAsync<T>(key, default!);
    }

    public async Task<T> GetAsync<T>(string key, T defaultValue)
    {
        var serializedValue = await jsRuntime.InvokeAsync<string>("sessionStorage.getItem", key);

        if (string.IsNullOrEmpty(serializedValue))
            return defaultValue;

        try
        {
            return JsonSerializer.Deserialize<T>(serializedValue)!;
        }
        catch
        {
            return defaultValue;
        }
    }

    public async Task RemoveAsync(string key)
    {
        await jsRuntime.InvokeVoidAsync("sessionStorage.removeItem", key);
    }
} 