namespace EMessa.Services.Interfaces;

/// <summary>
/// Bazowy interfejs dla serwisów obsługujących przechowywanie danych w przeglądarce.
/// </summary>
public interface IStorageService
{
    /// <summary>
    /// Zapisuje warto<PERSON> w magazynie (localStorage lub sessionStorage).
    /// </summary>
    /// <typeparam name="T">Typ zapisywanej warto<PERSON>.</typeparam>
    /// <param name="key"><PERSON><PERSON><PERSON>, pod którym zostanie zapisana wartość.</param>
    /// <param name="value">Wartość do zapisania.</param>
    /// <returns>Task reprezentujący operację asynchroniczną.</returns>
    Task SetAsync<T>(string key, T value);

    /// <summary>
    /// Odczytuje wartość z magazynu (localStorage lub sessionStorage).
    /// </summary>
    /// <typeparam name="T">Typ odczyt<PERSON><PERSON><PERSON> warto<PERSON>.</typeparam>
    /// <param name="key"><PERSON><PERSON><PERSON>, pod którym jest zapisana wartość.</param>
    /// <returns>Odczytana wartość lub default(T), jeśli klucz nie istnieje.</returns>
    Task<T> GetAsync<T>(string key);

    /// <summary>
    /// Odczytuje wartość z magazynu (localStorage lub sessionStorage) z podaną wartością domyślną.
    /// </summary>
    /// <typeparam name="T">Typ odczytywanej wartości.</typeparam>
    /// <param name="key">Klucz, pod którym jest zapisana wartość.</param>
    /// <param name="defaultValue">Wartość domyślna zwracana, gdy klucz nie istnieje.</param>
    /// <returns>Odczytana wartość lub defaultValue, jeśli klucz nie istnieje.</returns>
    Task<T> GetAsync<T>(string key, T defaultValue);

    /// <summary>
    /// Usuwa wartość z magazynu (localStorage lub sessionStorage).
    /// </summary>
    /// <param name="key">Klucz do usunięcia.</param>
    /// <returns>Task reprezentujący operację asynchroniczną.</returns>
    Task RemoveAsync(string key);
} 