<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <Company>BlachdomPlus</Company>
        <Version>1.5.10</Version>
        <FileVersion>1.5.10</FileVersion>
        <GeneratePackageOnBuild>True</GeneratePackageOnBuild>
        <Description>EMessa data access layer</Description>
        <PackageProjectUrl>https://github.com/blachdomplus/eMessa7</PackageProjectUrl>
        <PackageReadmeFile>README.md</PackageReadmeFile>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.Identity" Version="2.2.0" />
        <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.8" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.8" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.8" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.8" />
        <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Identity.Stores" Version="8.0.8" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="Serilog" Version="4.0.2" />
        <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
        <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
        <None Include="README.md" Pack="true" PackagePath="\" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\EMessa.Base\EMessa.Base.csproj" />
    </ItemGroup>
    <!--Dodaje EMessa.Base.dll tdo tego nugeta ale to chyba średni poysł. EMessa.Base będzie osobnym pakietem-->
    <!--<ItemGroup>
        <None Include="..\EMessa.Base\bin\$(Configuration)\net8.0\EMessa.Base.dll" Pack="true" PackagePath="lib\net8.0\" />
    </ItemGroup>-->

</Project>
