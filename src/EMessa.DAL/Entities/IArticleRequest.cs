using EMessa.Base.Enums;

namespace EMessa.DAL.Entities;

public interface IArticleRequest
{
    int Id { get; set; }

    /// <summary>
    /// Nazwa produktu
    /// </summary>
    string Name { get; set; }

    /// <summary>
    /// Kod produktu
    /// </summary>
    string Code { get; set; }

    /// <summary>
    /// Określa czy produkt jest aktywny
    /// </summary>
    bool IsActive { get; set; }

    /// <summary>
    /// Typ artykułu
    /// </summary>
    ArticleType Type { get; set; }

    /// <summary>
    /// Określa czy produkt został usunięty
    /// </summary>
    bool IsDeleted { get; set; }

    /// <summary>
    /// Numer kolejny w katalogu
    /// </summary>
    int No { get; set; }

    /// <summary>
    /// Waga jednostkowa
    /// </summary>
    decimal UnitWeight { get; set; }

    int? ArticleUnitId { get; set; }
    //ArticleUnit? Unit { get; set; } 

    /// <summary>
    /// Opis produktu w formacie HTML
    /// </summary>
    string Description { get; set; }

    /// <summary>
    /// Kod produktu w obcym systemie (akronim)
    /// </summary>
    string ForeignCode { get; set; }

    /// <summary>
    /// Określa czy można edytować uwagi w pozycji zamówienia dla produktu
    /// </summary>
    bool EditOrderCommentsEnable { get; set; }

    /// <summary>
    /// Cena bazowa (stosowana głównie dla produktów handlowych)
    /// </summary>
    decimal BasePrice { get; set; }

    /// <summary>
    /// Próg ostrzeżenia o dużej ilości sztuk
    /// </summary>
    int QuantityWarning { get; set; }

    // opcje technologiczne
    /// <summary>
    /// Określa czy długość jest edytowalna
    /// </summary>
    bool LengthEditable { get; set; }

    /// <summary>
    /// Domyślna długość
    /// </summary>
    decimal DefaultLength { get; set; }

    /// <summary>
    /// Minimalna długość
    /// </summary>
    decimal MinLength { get; set; }

    /// <summary>
    /// Maksymalna długość
    /// </summary>
    decimal MaxLength { get; set; }

    /// <summary>
    /// Określa czy szerokość jest edytowalna
    /// </summary>
    bool WidthEditable { get; set; }

    /// <summary>
    /// Domyślna szerokość
    /// </summary>
    decimal DefaultWidth { get; set; }

    /// <summary>
    /// Minimalna szerokość
    /// </summary>
    decimal MinWidth { get; set; }

    /// <summary>
    /// Maksymalna szerokość
    /// </summary>
    decimal MaxWidth { get; set; }

    /// <summary>
    /// Określa czy można dzielić arkusze, uruchomienie mechanizmy propozycji podziału
    /// </summary>
    bool IsSplitteable { get; set; }

    /// <summary>
    /// Określa czy można edytować profile (dla obróbki)
    /// </summary>
    bool ProfileEditable { get; set; }

    /// <summary>
    /// Określa, czy profil jest wymagany (dla obróbki).
    /// </summary>
    public bool RequireProfile { get; set; }

    /// <summary>
    /// Określa, czy można podzielić na N części
    /// </summary>
    public bool IsSplittableNParts { get; set; }
    
    /// <summary>
    /// Długość podziału opcjonalnego
    /// </summary>
    public decimal SplitLength { get; set; }

    /// <summary>
    /// Długość zakładki
    /// </summary>
    public decimal OverlapLength { get; set; }

    /// <summary>
    /// Długość zakazanej strefy przetłoczenia (w lewo od następnego pełnego modułu), np. 30mm
    /// </summary>
    public decimal EmbossZoneLength { get; set; }

    /// <summary>
    /// Maksymalna długość łapy, odcinek przed przetłoczeniem
    /// </summary>
    [Obsolete("Użyj EmbossZoneLength", true)]
    decimal MaxLongPaw { get; set; }

    /// <summary>
    /// Długość początkowego odcinka przed przetłoczeniem
    /// </summary>
    [Obsolete("Użyj OverlapLength", true)]
    decimal InitialLength { get; set; }
}