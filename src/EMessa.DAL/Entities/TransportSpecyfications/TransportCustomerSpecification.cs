using EMessa.DAL.Entities.Customers;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace EMessa.DAL.Entities.TransportSpecyfications
{
    public class TransportCustomerSpecification : IEntity
    {
        public int Id { get; set; }
        public string No { get; set; } = null!;
        public DateTime? TransportDate { get; set; }
        public string? PdfFilePath { get; set; }
        public string? DriverFullName { get; set; }
        public string? DriverPhone { get; set; }
        public long MessaSpcyficationId { get; set; }
        public string TransportNo { get; set; } = null!;
        public int? CustomerId { get; set; }
        public virtual Customer? Customer { get; set; }
        public int? LocalizationId { get; set; }
        public virtual CustomerLocalization? Localization { get; set; }
        public long MessaClientId { get; set; }
    }
}
