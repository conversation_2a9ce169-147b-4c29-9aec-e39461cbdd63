using EMessa.DAL.Entities.Branches;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EMessa.DAL.Entities.TransportSpecyfications
{
    public class TransportBranchSpecification : IEntity
    {
        public int Id { get; set; }
        public string No { get; set; } = null!;
        public DateTime? TransportDate { get; set; }
        public string? PdfFilePath { get; set; }
        public string? DriverFullName { get; set; }
        public string? DriverPhone { get; set; }
        public long? MessaSpcyficationId { get; set; }
        public string TransportNo { get; set; } = null!;
        public int BranchId { get; set; }
        public virtual Branch Branch { get; set; } = null!;
        public long? MessaBranchId { get; set; }
    }
}
