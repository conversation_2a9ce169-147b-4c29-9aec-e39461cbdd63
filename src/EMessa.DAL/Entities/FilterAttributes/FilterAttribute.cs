using EMessa.DAL.Entities.Categories;

namespace EMessa.DAL.Entities.FilterAttributes;

public class FilterAttribute : IEntity
{
    public int Id { get; set; }
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public int OrdinaryNumber { get; set; }
    public bool IsActive { get; set; } = true;
    public bool IsDeleted { get; set; } = false;
    public ICollection<FilterAttributeValue> FilterAttibuteValues { get; set; } = [];
    public ICollection<Category> Categories { get; set; } = [];
    public ICollection<FilterAttributeTranslation> Translations { get; set; } = [];
}