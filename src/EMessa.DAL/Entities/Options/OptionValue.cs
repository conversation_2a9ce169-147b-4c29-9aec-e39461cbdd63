using EMessa.DAL.Entities.ArticleOptions;

namespace EMessa.DAL.Entities.Options;

public class OptionValue : IEntity
{
    public int Id { get; set; }

    /// <summary>
    /// Numer kolejny wartości opcji (lp)
    /// </summary>
    public int No { get; set; }

    /// <summary>
    /// Wartośc opcji
    /// </summary>
    public string Value { get; set; } = null!;

    /// <summary>
    /// Kod wartości opcji (systemowy kod wartości opcji)
    /// </summary>
    /// <remarks>OptionCodesConstans</remarks>
    public string Code { get; set; } = null!;

    public int OptionId { get; set; }
    public Option Option { get; set; } = null!;

    public bool IsDefault { get; set; }
    public decimal WeightFactor { get; set; }

    /// <summary>
    /// Dodatkowa strefa przetłoczenia (w prawo od długości modułowej)
    /// </summary>
    public decimal EmbossZoneAddition { get; set; }

    /// <summary>
    /// Info dla użytkownika, jeśli wartość opcji zostawie wybrana
    /// </summary>
    public string ValueInfo { get; set; } = string.Empty;

    public ICollection<OptionValueTranslation> Translations { get; set; } = [];
    public ICollection<ArticleOptionValue> ArticleOptionValues { get; set; } = [];
}