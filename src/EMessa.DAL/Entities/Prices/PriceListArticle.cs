namespace EMessa.DAL.Entities.Prices;

public class PriceListArticle : IEntity
{
    public int Id { get; set; }
    public int PriceListId { get; set; }
    public PriceList PriceList { get; set; } = null!;

    public string ArticleIndex { get; set; } = null!;

    public decimal PriceNet { get; set; }
    public decimal PriceGross { get; set; }
    public decimal PriceVatRate { get; set; }

    //public ICollection<PriceListArticleCurrency> Currencies { get; set; } = [];
}
