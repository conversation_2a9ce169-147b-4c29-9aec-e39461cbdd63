using EMessa.Base.Enums;
using EMessa.DAL.Entities.Countries;

namespace EMessa.DAL.Entities.Prices;

public class PriceList : IEntity
{
    public int Id { get; set; }
    public string Name { get; set; } = null!;
    public string Description { get; set; } = null!;
    public bool IsActive { get; set; }
    public PriceListType Type { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }

    public DateTime? ValidFrom { get; set; }
    public DateTime? ValidTo { get; set; }

    public int CountryId { get; set; }
    public Country Country { get; set; } = null!;

    public ICollection<PriceListArticle> Articles { get; set; } = [];
}
