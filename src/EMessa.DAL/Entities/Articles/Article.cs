using EMessa.Base.Enums;
using EMessa.DAL.Entities.ArticleOptions;
using EMessa.DAL.Entities.Categories;
using EMessa.DAL.Entities.FilterAttributes;
using EMessa.DAL.Entities.Options;
using EMessa.DAL.Entities.Sales;

namespace EMessa.DAL.Entities.Articles;

public class Article : IEntity, IArticleRequest
{
    public int Id { get; set; }

    public string Name { get; set; } = string.Empty;

    public string Code { get; set; } = string.Empty;

    public bool IsActive { get; set; } = true;

    public ArticleType Type { get; set; }

    public bool IsDeleted { get; set; } = false;

    public List<Category> Categories { get; set; } = [];

    public List<ArticleTranslation> Translations { get; set; } = [];
    public List<FilterAttributeValue> FilterAttributeValues { get; set; } = [];

    public List<ArticleOption> Options { get; set; } = [];

    // ograniczenia kombinacji wartości opcji
    public List<ArticleOptionValuesRestriction> OptionValuesRestrictions { get; set; } = [];

    // nr kolejny w katalogu
    public int No { get; set; }

    // waga jednostkowa
    public decimal UnitWeight { get; set; }

    public int? ArticleUnitId { get; set; }
    public ArticleUnit? Unit { get; set; } = null!;

    public string TooLongSheet { get; set; } = string.Empty;

    public string Description { get; set; } = string.Empty;

    public string ForeignCode { get; set; } = string.Empty;

    public bool EditOrderCommentsEnable { get; set; }

    public decimal BasePrice { get; set; }

    public int QuantityWarning { get; set; }


    // opcje technologiczne
    public bool LengthEditable { get; set; } = true;

    public decimal DefaultLength { get; set; }

    public decimal MinLength { get; set; }

    public decimal MaxLength { get; set; }

    public bool WidthEditable { get; set; } = false;

    public decimal DefaultWidth { get; set; }

    public decimal MinWidth { get; set; }

    public decimal MaxWidth { get; set; }

    public bool IsSplitteable { get; set; }

    public bool ProfileEditable { get; set; }

    public bool RequireProfile { get; set; }

    public bool IsSplittableNParts { get; set; }

    public decimal SplitLength { get; set; }

    public decimal OverlapLength { get; set; }

    public decimal EmbossZoneLength { get; set; }

    [Obsolete("Użyj EmbossZoneLength")]
    public decimal MaxLongPaw { get; set; }

    [Obsolete("Użyj OverlapLength")]
    public decimal InitialLength { get; set; }

    public ICollection<SaleArticle> SalesArticles { get; set; } = [];
}

