
using EMessa.DAL.Entities.Options;

namespace EMessa.DAL.Entities.ArticleOptions
{
    public class ArticleOptionValue : IEntity
    {
        public int Id { get; set; }
        
        public int ArticleOptionId { get; set; }
        public ArticleOption ArticleOption { get; set; } = null!;
        
        public int OptionValueId { get; set; }
        public OptionValue OptionValue { get; set; } = null!;

        public bool? IsDefault { get; set; }
        public decimal? WeightFactor { get; set; }

        /// <summary>
        /// Dodatkowa strefa wyłączona w prawo (dotyczy np. filcu)
        /// </summary>
        public decimal? EmbossZoneAddition { get; set; } = 0;
    }
}
