using EMessa.Base.Enums;

namespace EMessa.DAL.Entities.Customers;

public class CustomerGroup : IEntity
{
    public int Id { get; set; }
    public string Name { get; set; } = null!;
    public string Code { get; set; } = null!;
    public string? Description { get; set; } //może notnull?
    public CustomerGroupType Type { get; set; }

    public ICollection<CustomerGroupCustomer> CustomersGroupCustomers { get; set; } = [];
}
