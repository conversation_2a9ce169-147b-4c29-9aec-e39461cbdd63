
using EMessa.DAL.Entities.Branches;
using EMessa.DAL.Entities.Countries;
using EMessa.DAL.Entities.Factories;
using EMessa.DAL.Entities.Options;
using EMessa.DAL.Entities.Orders;
using EMessa.DAL.Entities.Sales;
using EMessa.DAL.Entities.TransportSpecyfications;

namespace EMessa.DAL.Entities.Customers;

public class Customer : IEntity, IAddressableEntity
{
    public int Id { get; set; }
    public string ShortName { get; set; } = null!;
    public string Name { get; set; } = null!;

    public string Address { get; set; } = null!;
    public string City { get; set; } = null!;
    public string PostCode { get; set; } = null!;
    public string Phone { get; set; } = string.Empty;
    public string Fax { get; set; } = string.Empty;
    public string Email { get; set; } = null!;

    public bool IsPrivate { get; set; }
    public bool IsActive { get; set; }
    public bool FastProductionTrack { get; set; }
    public bool Cmr { get; set; }
    public string? NationalOfficialBusinessRegister { get; set; }
    public string? TaxIdentificationNumber { get; set; }
    public string? BusinessTerms { get; set; }
    public string? ProfilInfo { get; set; }

    public int CountryId { get; set; }
    public Country Country { get; set; } = null!;

    public int BranchId { get; set; }
    public Branch Branch { get; set; } = null!;

    public int? FactoryId { get; set; }
    public Factory? Factory { get; set; }
    public List<CustomerLocalization> Localisations { get; set; } = [];

    public List<Order> Orders { get; set; } = [];

    public bool IsAppOwner { get; set; } = false;

    // ograniczenia kombinacji wartości opcji dla klienta
    public List<ArticleOptionValuesRestriction> OptionValuesRestrictions { get; set; } = [];

    public ICollection<SaleCustomer> SalesCustomers { get; set; } = [];
    public ICollection<CustomerGroupCustomer> CustomersGroupCustomers { get; set; } = [];
    public ICollection<TransportCustomerSpecification> TransportClientSpecifications { get; set; } = [];
}