using EMessa.DAL.Entities.Users;

namespace EMessa.DAL.Entities.Orders;

public class LockOrder : IEntity
{
    public int Id { get; set; }
    public int OrderId { get; set; }
    public Order Order { get; set; } = null!;
    public int LockedByUserProfileId { get; set; }
    public ApplicationUserProfile LockedBy { get; set; } = null!;
    public DateTime ExpiresAt { get; set; } = DateTime.UtcNow; // not used yet
}
