using EMessa.DAL.Entities.Options;

namespace EMessa.DAL.Entities.Orders;

public class OrderItemOptionValue : IEntity
{
    public int Id { get; set; }
    public int OrderItemId { get; set; }
    public OrderItem OrderItem { get; set; } = null!;

    public int OptionId { get; set; }
    public Option Option { get; set; } = null!;

    public int? OptionValueId { get; set; }
    public OptionValue? OptionValue { get; set; }
}