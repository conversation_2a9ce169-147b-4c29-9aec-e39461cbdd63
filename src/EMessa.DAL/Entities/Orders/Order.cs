
using EMessa.DAL.Entities.Branches;
using EMessa.DAL.Entities.Customers;
using EMessa.DAL.Entities.Factories;
using EMessa.DAL.Entities.Users;
using EMessa.DAL.Entities.Localizations;
using EMessa.Base.Enums;

namespace EMessa.DAL.Entities.Orders;

public class Order : IEntity
{
    public int Id { get; set; }
    public string No { get; set; } = null!;// 1/2025
    public int? MessaNo { get; set; }
    public string CustomerNo { get; set; } = string.Empty;

    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    public int CreatedById { get; set; }
    public ApplicationUserProfile CreatedBy { get; set; } = null!;
    public DateTime? LastEditDate { get; set; }
    public int? EditedById { get; set; }
    public ApplicationUserProfile? EditedBy { get; set; }
    public int? DeletedById { get; set; }
    public ApplicationUserProfile? DeletedBy { get; set; }
    public DateTime? DeletedDate { get; set; }

    public int CustomerId { get; set; }
    public Customer Customer { get; set; } = null!;
    public int CustomerLocalizationId { get; set; } //ask to chyba nie ma sensu bo moze sie zmienic po zlozeniu zamowienia
    public CustomerLocalization CustomerLocalization { get; set; } = null!; // to chyba nie ma sensu bo moze sie zmienic po zlozeniu zamowienia
    
    public int BranchId { get; set; }
    public Branch Branch { get; set; } = null!;
    public int? FactoryId { get; set; }
    public Factory? Factory { get; set; }

    public string Comments { get; set; } = string.Empty; //ask Może `string Note` oraz `ICollection<string> Comments - w produkcji dodaje sie komentarze

    public OrderType Type { get; set; }
    public OrderStatus Status { get; set; }
    public DateTime? PlanedRealizationDate { get; set; }
    public DateTime? RealizationDate { get; set; }

    public bool IsEdited { get; set; }
    public bool IsDeleted { get; set; }

    public ICollection<OrderItem> OrderItems { get; set; } = [];
    
    public bool DifferentDeliveryLocalization { get; set; }
    public ICollection<Localization> Localizations { get; set; } = [];
}
