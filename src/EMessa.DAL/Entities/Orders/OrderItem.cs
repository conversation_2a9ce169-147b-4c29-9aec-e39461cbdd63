using EMessa.Base.Enums;
using EMessa.DAL.Entities.Articles;
using EMessa.DAL.Entities.Sales;

namespace EMessa.DAL.Entities.Orders;

public class OrderItem : IEntity, IOrderItemDraft
{
    public int Id { get; set; }
    public int OrderId { get; set; }
    public Order Order { get; set; } = null!;

    public int ArticleId { get; set; }
    public Article Article { get; set; } = null!;
    public string Index { get; set; } = string.Empty;

    public decimal Quantity { get; set; }
    public int SumQuantity { get; set; }
    public decimal SumM2 { get; set; }
    public decimal SumMb { get; set; }
    public decimal Weight { get; set; }

    public string Comments { get; set; } = string.Empty;
    public List<RequestedOrderItemSheet> RequestedSheets { get; set; } = [];
    public List<OrderItemOptionValue> OptionValues { get; set; } = [];

    public bool DraftEditable { get; set; }
    public string? DraftOriginalFileName { get; set; }
    public string? DraftHashedFileName { get; set; }
    public string? DraftDrawingSource { get; set; }

    public bool SelectedForOrder { get; set; } = true;

    public decimal BasePrice { get; set; }
    public decimal FinalPrice { get; set; }
    public string CurrencyCode { get; set; } = null!;

    public OrderStatus Status { get; set; }
    public DateTime? ProductionDate { get; set; }
    public DateTime? TransportDate { get; set; }

    public int? SaleId { get; set; }
    public Sale? Sale { get; set; }
}

