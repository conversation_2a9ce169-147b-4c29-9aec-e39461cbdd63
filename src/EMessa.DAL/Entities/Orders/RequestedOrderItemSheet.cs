using EMessa.Base.Constants;

namespace EMessa.DAL.Entities.Orders;

public class RequestedOrderItemSheet : AbstractArticleSheet, IEntity
{
    public int Id { get; set; }
    // public decimal Width { get; set; }
    // public decimal Length { get; set; }
    // public int Quantity { get; set; }

    /// <summary>
    /// Pole to zapamiętuje czy podzielono arkusz na mniejsze na życzenie klienta. Ważne przy rewalidacji arkuszy 
    /// </summary>
    public int? SplitN { get; set; }

    public int OrderItemId { get; set; }
    public OrderItem OrderItem { get; set; } = null!;

    public List<OrderItemSheet> OrderItemSheets { get; set; } = [];

    public decimal SumMb => Math.Round(OrderItemSheets.Sum(x => x.Mb),
        SystemConstants.RoundingSumsDecimals, MidpointRounding.AwayFromZero);

    public decimal SumM2 => Math.Round(OrderItemSheets.Sum(x => x.M2),
        SystemConstants.RoundingSumsDecimals, MidpointRounding.AwayFromZero);
}
