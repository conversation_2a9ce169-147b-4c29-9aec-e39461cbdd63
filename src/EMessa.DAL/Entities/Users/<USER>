
using EMessa.DAL.Entities.Customers;
using EMessa.DAL.Entities.Factories;
using EMessa.Base.Enums;
using EMessa.DAL.Entities.Sales;

namespace EMessa.DAL.Entities.Users
{
    public class ApplicationUserProfile : IEntity
    {
        public int Id { get; set; }
        public string UserName { get; set; } = null!;
        public string Email { get; set; } = null!;
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public bool Active { get; set; }
        public string FirstName { get; set; } = null!;
        public string LastName { get; set; } = null!;
        public string? FirstPhoneNumber { get; set; }
        public string? SecondPhoneNumber { get; set; }
        public UserTypes UserType { get; set; }
        public string? XlLogin { get; set; }
        public string? XlPassword { get; set; }
        public bool VatPl { get; set; }
        public bool VatOutsidePl { get; set; }
        public bool RegulationsAccepted { get; set; }
        public bool GeneralSaleTermsAccepted { get; set; }
        public bool FirstConfiguration { get; set; }
        public bool EmailNotifications { get; set; }
        public bool SmsNotifications { get; set; }
        public string? NotificationsLang { get; set; }
        public DateTime? LastLoginDate { get; set; }
        public string? LastLoginIp { get; set; }

        public int? FactoryId { get; set; }
        public Factory? Factory { get; set; }

        public int CustomerId { get; set; }
        public Customer Customer { get; set; } = null!;

        public bool CanRepresentCustomer { get; set; }

        public ICollection<CustomerUser> CustomerUsers { get; set; } = null!;
        public ICollection<UserBranch> UserBranches { get; set; } = [];

        public List<UserCustomerLocalization> UserLocalizations { get; set; } = [];

        public ICollection<Sale> CreatedSales { get; set; } = [];
        public ICollection<Sale> ActivatedSales { get; set; } = [];
        public ICollection<Sale> ClosedSales { get; set; } = [];
    }
}
