
using EMessa.DAL.Entities.Orders;
using EMessa.DAL.Entities.TransportSpecyfications;

namespace EMessa.DAL.Entities.Branches
{
    public class Branch : IAddressableEntity
    {
        public int Id { get; set; }
        public string Code { get; set; } = null!;
        public string Name { get; set; } = null!;
        public bool IsActive { get; set; } = true;
        public string Address { get; set; } = null!;
        public string City { get; set; } = null!;
        public string PostCode { get; set; } = null!;
        public string Phone { get; set; } = string.Empty;
        public string Fax { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;

        public string? SellWarehouseCode { get; set; } = string.Empty;
        public List<Order> Orders { get; set; } = null!;
        public virtual List<TransportBranchSpecification> TransportBranchSpecifications { get; set; } = [];
    }
}
