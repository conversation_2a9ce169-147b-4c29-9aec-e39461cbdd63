
using Microsoft.AspNetCore.Identity;

namespace EMessa.DAL.Entities.Identity
{
    public class ApplicationUser : IdentityUser<int>, IEntity
    {
        public virtual ICollection<ApplicationUserClaim> Claims { get; set; } = null!;
        public virtual ICollection<ApplicationUserLogin> Logins { get; set; } = null!;
        public virtual ICollection<ApplicationUserToken> Tokens { get; set; } = null!;
        public virtual ICollection<ApplicationUserRole> UserRoles { get; set; } = null!;
        public virtual ICollection<UserRefreshToken> RefreshTokens { get; set; } = null!;
    }
}
