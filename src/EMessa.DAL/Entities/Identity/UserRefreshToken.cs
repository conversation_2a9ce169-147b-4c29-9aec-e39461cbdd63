namespace EMessa.DAL.Entities.Identity
{
    public class UserRefreshToken
    {
        public int Id { get; set; }
        
        public int ApplicationUserId { get; set; }
        
        public string RefreshToken { get; set; } = string.Empty;
        
        public DateTime ExpiryDate { get; set; }
        
        public DateTime CreatedDate { get; set; }
        
        public bool IsRevoked { get; set; }
        
        public DateTime? RevokedDate { get; set; }
        
        public string? DeviceInfo { get; set; }
        
        public string? IpAddress { get; set; }
        
        // Navigation property
        public virtual ApplicationUser User { get; set; } = null!;
    }
} 