using EMessa.DAL.Entities.Options;
using EMessa.DAL.Entities.Orders;
using EMessa.DAL.Entities.Users;
using Messa.Core.BL.eMessa.Sales.ViewModels;

namespace EMessa.DAL.Entities.Sales;

public class Sale : IEntity, ITranslatable<SaleTranslation>
{
    public int Id { get; set; }
    public string Name { get; set; } = null!;
    public string Description { get; set; } = string.Empty;
    public SaleStatus Status { get; set; }
    public DateTime DateFrom { get; set; }
    public DateTime DateTo { get; set; }
    public bool IsDeleted { get; set; }
    public decimal DefinedWeight { get; set; }

    public int CoatId { get; set; }
    public int ColorId { get; set; }
    public int ThickId { get; set; }

    /// <summary>
    /// Waga na metr blachy
    /// </summary>
    public decimal Efficiency { get; set; }

    public DateTime CreatedDate { get; set; }
    public int CreatedById { get; set; }
    public ApplicationUserProfile CreatedBy { get; set; } = null!;

    public DateTime? ActivatedDate { get; set; }
    public int? ActivatedById { get; set; }

    public ApplicationUserProfile? ActivatedBy { get; set; }

    public DateTime? ClosedDate { get; set; }
    public int? ClosedById { get; set; }
    public ApplicationUserProfile? ClosedBy { get; set; }

    public OptionValue Coat { get; set; } = null!;
    public OptionValue Color { get; set; } = null!;
    public OptionValue Thick { get; set; } = null!;

    public ICollection<OrderItem> OrderItems { get; set; } = [];

    public ICollection<SaleImage> Images { get; set; } = [];
    public ICollection<SaleRoll> Rolls { get; set; } = [];
    public ICollection<SalePrice> Prices { get; set; } = [];
    public ICollection<SaleTranslation> Translations { get; set; } = [];

    public ICollection<SaleCustomer> SalesCustomers { get; set; } = [];

    public ICollection<SaleArticle> SalesArticles { get; set; } = [];
}
