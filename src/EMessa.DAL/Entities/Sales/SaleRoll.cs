namespace EMessa.DAL.Entities.Sales;

public class SaleRoll : IEntity
{
    public int Id { get; set; }
    public long RollId { get; set; }
    public int SaleId { get; set; }
    public DateTime RollRegisteredDate { get; set; }
    public string Supplier { get; set; } = null!;
    /// <summary>
    /// Numer krążka
    /// </summary>
    public int RollNo { get; set; }
    /// <summary>
    /// Numer producenta krążka
    /// </summary>
    public string OrigNo { get; set; } = null!;
    public string ColorCode { get; set; } = null!;
    public string CoatCode { get; set; } = null!;
    public string ThickCode { get; set; } = null!;
    public decimal SaleWeight { get; set; }
    public decimal Weight { get; set; }
    public DateTime CreatedDate { get; set; }

    public Sale Sale { get; set; } = null!;
}
