using EMessa.DAL.Entities.Articles;

namespace EMessa.DAL.Entities.Sales;

public class SaleArticle : IEntity
{
    public int Id { get; set; }
    public int SaleId { get; set; }
    public Sale Sale { get; set; } = null!;
    public int ArticleId { get; set; }
    public Article Article { get; set; } = null!;
    /// <summary>
    /// Szerokość arkusza dla danej promocji
    /// </summary>
    public decimal Width { get; set; }
}
