using EMessa.Base.Enums;
using EMessa.DAL.Entities.Orders;

namespace EMessa.DAL.Entities.Localizations;

public class Localization : IEntity
{
    public int Id { get; set; }
    public int OrderId { get; set; }
    public Order Order { get; set; } = null!;
    public LocalizationType Type { get; set; }
    public string Address { get; set; } = null!;
    public string City { get; set; } = null!;
    public string PostCode { get; set; } = null!;
}