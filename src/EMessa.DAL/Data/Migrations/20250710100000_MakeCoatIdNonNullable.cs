using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EMessa.DataAccess.Migrations
{
    /// <inheritdoc />
    public partial class MakeCoatIdNonNullable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // First, we need to update any existing records with NULL CoatId to a valid value
            // This is a placeholder SQL - in a real scenario, you would need to choose an appropriate default value
            // or handle this differently based on business requirements
            // To nie ma sensu
            // migrationBuilder.Sql("UPDATE Sales SET CoatId = 1 WHERE CoatId IS NULL");
    
            // Then alter the column to make it non-nullable
            migrationBuilder.AlterColumn<int>(
                name: "CoatId",
                table: "Sales",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            // Update the foreign key relationship to be required
            // First drop the existing foreign key
            migrationBuilder.DropForeignKey(
                name: "FK_Sales_OptionValues_CoatId",
                table: "Sales");

            // Then add the new foreign key with ON DELETE NO ACTION
            migrationBuilder.AddForeignKey(
                name: "FK_Sales_OptionValues_CoatId",
                table: "Sales",
                column: "CoatId",
                principalTable: "OptionValues",
                principalColumn: "Id",
                onDelete: ReferentialAction.NoAction);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // First drop the foreign key
            migrationBuilder.DropForeignKey(
                name: "FK_Sales_OptionValues_CoatId",
                table: "Sales");

            // Then add back the original foreign key
            migrationBuilder.AddForeignKey(
                name: "FK_Sales_OptionValues_CoatId",
                table: "Sales",
                column: "CoatId",
                principalTable: "OptionValues",
                principalColumn: "Id");

            // Finally, alter the column back to nullable
            migrationBuilder.AlterColumn<int>(
                name: "CoatId",
                table: "Sales",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int",
                oldDefaultValue: 0);
        }
    }
}