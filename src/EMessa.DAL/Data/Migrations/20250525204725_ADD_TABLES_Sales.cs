using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EMessa.DataAccess.Migrations
{
    /// <inheritdoc />
    public partial class ADD_TABLES_Sales : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "SaleId",
                table: "OrderItems",
                type: "int",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "CustomerGroups",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(300)", maxLength: 300, nullable: false),
                    Code = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Type = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerGroups", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Sales",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(220)", maxLength: 220, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Status = table.Column<int>(type: "int", nullable: false),
                    DateFrom = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DateTo = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    DefinedWeight = table.Column<decimal>(type: "decimal(18,3)", precision: 18, scale: 3, nullable: false, defaultValue: 0.00m),
                    CoatId = table.Column<int>(type: "int", nullable: true),
                    ColorId = table.Column<int>(type: "int", nullable: true),
                    ThickId = table.Column<int>(type: "int", nullable: true),
                    Efficiency = table.Column<decimal>(type: "decimal(18,3)", precision: 18, scale: 3, nullable: false, defaultValue: 0.00m),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedById = table.Column<int>(type: "int", nullable: false),
                    ActivatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ActivatedById = table.Column<int>(type: "int", nullable: true),
                    ClosedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ClosedById = table.Column<int>(type: "int", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Sales", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Sales_OptionValues_CoatId",
                        column: x => x.CoatId,
                        principalTable: "OptionValues",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Sales_OptionValues_ColorId",
                        column: x => x.ColorId,
                        principalTable: "OptionValues",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Sales_OptionValues_ThickId",
                        column: x => x.ThickId,
                        principalTable: "OptionValues",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Sales_UserProfiles_ActivatedById",
                        column: x => x.ActivatedById,
                        principalSchema: "User",
                        principalTable: "UserProfiles",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Sales_UserProfiles_ClosedById",
                        column: x => x.ClosedById,
                        principalSchema: "User",
                        principalTable: "UserProfiles",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Sales_UserProfiles_CreatedById",
                        column: x => x.CreatedById,
                        principalSchema: "User",
                        principalTable: "UserProfiles",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CustomersGroupCustomers",
                columns: table => new
                {
                    CustomerGroupsId = table.Column<int>(type: "int", nullable: false),
                    CustomersId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomersGroupCustomers", x => new { x.CustomerGroupsId, x.CustomersId });
                    table.ForeignKey(
                        name: "FK_CustomersGroupCustomers_CustomerGroups_CustomerGroupsId",
                        column: x => x.CustomerGroupsId,
                        principalTable: "CustomerGroups",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CustomersGroupCustomers_Customers_CustomersId",
                        column: x => x.CustomersId,
                        principalTable: "Customers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "SaleImages",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    SaleId = table.Column<int>(type: "int", nullable: false),
                    OriginalName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    FilePath = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SaleImages", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SaleImages_Sales_SaleId",
                        column: x => x.SaleId,
                        principalTable: "Sales",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "SalePrices",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    SaleId = table.Column<int>(type: "int", nullable: false),
                    Price = table.Column<decimal>(type: "decimal(18,2)", precision: 18, scale: 2, nullable: false),
                    CurrencyCode = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SalePrices", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SalePrices_Sales_SaleId",
                        column: x => x.SaleId,
                        principalTable: "Sales",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "SaleRolls",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    RollId = table.Column<long>(type: "bigint", nullable: false),
                    SaleId = table.Column<int>(type: "int", nullable: false),
                    RollRegisteredDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Supplier = table.Column<string>(type: "nvarchar(120)", maxLength: 120, nullable: false),
                    RollNo = table.Column<int>(type: "int", nullable: false),
                    OrigNo = table.Column<string>(type: "nvarchar(120)", maxLength: 120, nullable: false),
                    Color = table.Column<string>(type: "nvarchar(60)", maxLength: 60, nullable: false),
                    ColorCode = table.Column<string>(type: "nvarchar(60)", maxLength: 60, nullable: false),
                    Coat = table.Column<string>(type: "nvarchar(60)", maxLength: 60, nullable: false),
                    CoatCode = table.Column<string>(type: "nvarchar(60)", maxLength: 60, nullable: false),
                    Thick = table.Column<decimal>(type: "decimal(12,2)", precision: 12, scale: 2, nullable: false),
                    SaleWeight = table.Column<decimal>(type: "decimal(12,2)", precision: 12, scale: 2, nullable: false),
                    Weight = table.Column<decimal>(type: "decimal(12,2)", precision: 12, scale: 2, nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SaleRolls", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SaleRolls_Sales_SaleId",
                        column: x => x.SaleId,
                        principalTable: "Sales",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "SalesArticles",
                columns: table => new
                {
                    ArticlesId = table.Column<int>(type: "int", nullable: false),
                    SalesId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SalesArticles", x => new { x.ArticlesId, x.SalesId });
                    table.ForeignKey(
                        name: "FK_SalesArticles_Articles_ArticlesId",
                        column: x => x.ArticlesId,
                        principalTable: "Articles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_SalesArticles_Sales_SalesId",
                        column: x => x.SalesId,
                        principalTable: "Sales",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "SalesCustomers",
                columns: table => new
                {
                    CustomersId = table.Column<int>(type: "int", nullable: false),
                    SalesId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SalesCustomers", x => new { x.CustomersId, x.SalesId });
                    table.ForeignKey(
                        name: "FK_SalesCustomers_Customers_CustomersId",
                        column: x => x.CustomersId,
                        principalTable: "Customers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_SalesCustomers_Sales_SalesId",
                        column: x => x.SalesId,
                        principalTable: "Sales",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "SaleTranslations",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    SaleId = table.Column<int>(type: "int", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(220)", maxLength: 220, nullable: false),
                    Comment = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LanguageCode = table.Column<string>(type: "nvarchar(450)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SaleTranslations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SaleTranslations_Sales_SaleId",
                        column: x => x.SaleId,
                        principalTable: "Sales",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_OrderItems_SaleId",
                table: "OrderItems",
                column: "SaleId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomersGroupCustomers_CustomersId",
                table: "CustomersGroupCustomers",
                column: "CustomersId");

            migrationBuilder.CreateIndex(
                name: "IX_SaleImages_SaleId",
                table: "SaleImages",
                column: "SaleId");

            migrationBuilder.CreateIndex(
                name: "IX_SalePrices_SaleId",
                table: "SalePrices",
                column: "SaleId");

            migrationBuilder.CreateIndex(
                name: "IX_SaleRolls_SaleId",
                table: "SaleRolls",
                column: "SaleId");

            migrationBuilder.CreateIndex(
                name: "IX_Sales_ActivatedById",
                table: "Sales",
                column: "ActivatedById");

            migrationBuilder.CreateIndex(
                name: "IX_Sales_ClosedById",
                table: "Sales",
                column: "ClosedById");

            migrationBuilder.CreateIndex(
                name: "IX_Sales_CoatId",
                table: "Sales",
                column: "CoatId");

            migrationBuilder.CreateIndex(
                name: "IX_Sales_ColorId",
                table: "Sales",
                column: "ColorId");

            migrationBuilder.CreateIndex(
                name: "IX_Sales_CreatedById",
                table: "Sales",
                column: "CreatedById");

            migrationBuilder.CreateIndex(
                name: "IX_Sales_ThickId",
                table: "Sales",
                column: "ThickId");

            migrationBuilder.CreateIndex(
                name: "IX_SalesArticles_SalesId",
                table: "SalesArticles",
                column: "SalesId");

            migrationBuilder.CreateIndex(
                name: "IX_SalesCustomers_SalesId",
                table: "SalesCustomers",
                column: "SalesId");

            migrationBuilder.CreateIndex(
                name: "IX_SaleTranslations_LanguageCode",
                table: "SaleTranslations",
                column: "LanguageCode");

            migrationBuilder.CreateIndex(
                name: "IX_SaleTranslations_SaleId_LanguageCode",
                table: "SaleTranslations",
                columns: new[] { "SaleId", "LanguageCode" },
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_OrderItems_Sales_SaleId",
                table: "OrderItems",
                column: "SaleId",
                principalTable: "Sales",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_OrderItems_Sales_SaleId",
                table: "OrderItems");

            migrationBuilder.DropTable(
                name: "CustomersGroupCustomers");

            migrationBuilder.DropTable(
                name: "SaleImages");

            migrationBuilder.DropTable(
                name: "SalePrices");

            migrationBuilder.DropTable(
                name: "SaleRolls");

            migrationBuilder.DropTable(
                name: "SalesArticles");

            migrationBuilder.DropTable(
                name: "SalesCustomers");

            migrationBuilder.DropTable(
                name: "SaleTranslations");

            migrationBuilder.DropTable(
                name: "CustomerGroups");

            migrationBuilder.DropTable(
                name: "Sales");

            migrationBuilder.DropIndex(
                name: "IX_OrderItems_SaleId",
                table: "OrderItems");

            migrationBuilder.DropColumn(
                name: "SaleId",
                table: "OrderItems");
        }
    }
}
