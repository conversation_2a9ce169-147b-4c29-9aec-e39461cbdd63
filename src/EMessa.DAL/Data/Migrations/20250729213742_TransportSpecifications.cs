using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EMessa.DataAccess.Migrations
{
    /// <inheritdoc />
    public partial class TransportSpecifications : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "TransportBranchSpecifications",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    No = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false),
                    TransportDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    PdfFilePath = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    DriverFullName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    DriverPhone = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    MessaSpcyficationId = table.Column<long>(type: "bigint", maxLength: 20, nullable: false),
                    TransportNo = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    BranchId = table.Column<int>(type: "int", nullable: false),
                    MessaBranchId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TransportBranchSpecifications", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TransportBranchSpecifications_Branches_BranchId",
                        column: x => x.BranchId,
                        principalTable: "Branches",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "TransportClientSpecifications",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    No = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false),
                    TransportDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    PdfFilePath = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    DriverFullName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    DriverPhone = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    MessaSpcyficationId = table.Column<long>(type: "bigint", maxLength: 20, nullable: false),
                    TransportNo = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    CustomerId = table.Column<int>(type: "int", nullable: true),
                    LocalizationId = table.Column<int>(type: "int", nullable: true),
                    MessaClientId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TransportClientSpecifications", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TransportClientSpecifications_CustomerLocalizations_LocalizationId",
                        column: x => x.LocalizationId,
                        principalTable: "CustomerLocalizations",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_TransportClientSpecifications_Customers_CustomerId",
                        column: x => x.CustomerId,
                        principalTable: "Customers",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_TransportBranchSpecifications_BranchId",
                table: "TransportBranchSpecifications",
                column: "BranchId");

            migrationBuilder.CreateIndex(
                name: "IX_TransportBranchSpecifications_MessaBranchId",
                table: "TransportBranchSpecifications",
                column: "MessaBranchId");

            migrationBuilder.CreateIndex(
                name: "IX_TransportBranchSpecifications_MessaSpcyficationId",
                table: "TransportBranchSpecifications",
                column: "MessaSpcyficationId");

            migrationBuilder.CreateIndex(
                name: "IX_TransportBranchSpecifications_TransportDate",
                table: "TransportBranchSpecifications",
                column: "TransportDate");

            migrationBuilder.CreateIndex(
                name: "IX_TransportClientSpecifications_CustomerId",
                table: "TransportClientSpecifications",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_TransportClientSpecifications_LocalizationId",
                table: "TransportClientSpecifications",
                column: "LocalizationId");

            migrationBuilder.CreateIndex(
                name: "IX_TransportClientSpecifications_MessaClientId",
                table: "TransportClientSpecifications",
                column: "MessaClientId");

            migrationBuilder.CreateIndex(
                name: "IX_TransportClientSpecifications_MessaSpcyficationId",
                table: "TransportClientSpecifications",
                column: "MessaSpcyficationId");

            migrationBuilder.CreateIndex(
                name: "IX_TransportClientSpecifications_TransportDate",
                table: "TransportClientSpecifications",
                column: "TransportDate");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "TransportBranchSpecifications");

            migrationBuilder.DropTable(
                name: "TransportClientSpecifications");
        }
    }
}
