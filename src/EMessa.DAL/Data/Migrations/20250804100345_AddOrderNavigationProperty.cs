using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EMessa.DataAccess.Migrations
{
    /// <inheritdoc />
    public partial class AddOrderNavigationProperty : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_LockOrders_UserProfiles_LockedById",
                table: "LockOrders");

            migrationBuilder.DropIndex(
                name: "IX_LockOrders_LockedById",
                table: "LockOrders");

            migrationBuilder.CreateIndex(
                name: "IX_LockOrders_LockedById",
                table: "LockOrders",
                column: "LockedById",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_LockOrders_OrderId_LockedById",
                table: "LockOrders",
                columns: new[] { "OrderId", "LockedById" });

            migrationBuilder.AddForeignKey(
                name: "FK_LockOrders_Orders_OrderId",
                table: "LockOrders",
                column: "OrderId",
                principalTable: "Orders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_LockOrders_UserProfiles_LockedById",
                table: "LockOrders",
                column: "LockedById",
                principalSchema: "User",
                principalTable: "UserProfiles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_LockOrders_Orders_OrderId",
                table: "LockOrders");

            migrationBuilder.DropForeignKey(
                name: "FK_LockOrders_UserProfiles_LockedById",
                table: "LockOrders");

            migrationBuilder.DropIndex(
                name: "IX_LockOrders_LockedById",
                table: "LockOrders");

            migrationBuilder.DropIndex(
                name: "IX_LockOrders_OrderId_LockedById",
                table: "LockOrders");

            migrationBuilder.CreateIndex(
                name: "IX_LockOrders_LockedById",
                table: "LockOrders",
                column: "LockedById");

            migrationBuilder.AddForeignKey(
                name: "FK_LockOrders_UserProfiles_LockedById",
                table: "LockOrders",
                column: "LockedById",
                principalSchema: "User",
                principalTable: "UserProfiles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
