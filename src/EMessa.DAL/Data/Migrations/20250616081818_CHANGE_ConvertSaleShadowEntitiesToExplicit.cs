using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EMessa.DataAccess.Migrations
{
    /// <inheritdoc />
    public partial class CHANGE_ConvertSaleShadowEntitiesToExplicit : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CustomersGroupCustomers_CustomerGroups_CustomerGroupsId",
                table: "CustomersGroupCustomers");

            migrationBuilder.DropForeignKey(
                name: "FK_CustomersGroupCustomers_Customers_CustomersId",
                table: "CustomersGroupCustomers");

            migrationBuilder.DropForeignKey(
                name: "FK_SalesArticles_Articles_ArticlesId",
                table: "SalesArticles");

            migrationBuilder.DropForeignKey(
                name: "FK_SalesArticles_Sales_SalesId",
                table: "SalesArticles");

            migrationBuilder.DropForeignKey(
                name: "FK_SalesCustomers_Customers_CustomersId",
                table: "SalesCustomers");

            migrationBuilder.DropForeignKey(
                name: "FK_SalesCustomers_Sales_SalesId",
                table: "SalesCustomers");

            migrationBuilder.DropPrimaryKey(
                name: "PK_SalesCustomers",
                table: "SalesCustomers");

            migrationBuilder.DropIndex(
                name: "IX_SalesCustomers_SalesId",
                table: "SalesCustomers");

            migrationBuilder.DropPrimaryKey(
                name: "PK_SalesArticles",
                table: "SalesArticles");

            migrationBuilder.DropIndex(
                name: "IX_SalesArticles_SalesId",
                table: "SalesArticles");

            migrationBuilder.DropPrimaryKey(
                name: "PK_CustomersGroupCustomers",
                table: "CustomersGroupCustomers");

            migrationBuilder.RenameColumn(
                name: "Comment",
                table: "SaleTranslations",
                newName: "Description");

            migrationBuilder.RenameColumn(
                name: "SalesId",
                table: "SalesCustomers",
                newName: "SaleId");

            migrationBuilder.RenameColumn(
                name: "CustomersId",
                table: "SalesCustomers",
                newName: "CustomerId");

            migrationBuilder.RenameColumn(
                name: "SalesId",
                table: "SalesArticles",
                newName: "SaleId");

            migrationBuilder.RenameColumn(
                name: "ArticlesId",
                table: "SalesArticles",
                newName: "ArticleId");

            migrationBuilder.RenameColumn(
                name: "CustomersId",
                table: "CustomersGroupCustomers",
                newName: "CustomerId");

            migrationBuilder.RenameColumn(
                name: "CustomerGroupsId",
                table: "CustomersGroupCustomers",
                newName: "CustomerGroupId");

            migrationBuilder.RenameIndex(
                name: "IX_CustomersGroupCustomers_CustomersId",
                table: "CustomersGroupCustomers",
                newName: "IX_CustomersGroupCustomers_CustomerId");

            migrationBuilder.AddColumn<int>(
                name: "Id",
                table: "SalesCustomers",
                type: "int",
                nullable: false,
                defaultValue: 0)
                .Annotation("SqlServer:Identity", "1, 1");

            migrationBuilder.AddColumn<int>(
                name: "Id",
                table: "SalesArticles",
                type: "int",
                nullable: false,
                defaultValue: 0)
                .Annotation("SqlServer:Identity", "1, 1");

            migrationBuilder.AddColumn<decimal>(
                name: "Width",
                table: "SalesArticles",
                type: "decimal(18,3)",
                precision: 18,
                scale: 3,
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<int>(
                name: "Id",
                table: "CustomersGroupCustomers",
                type: "int",
                nullable: false,
                defaultValue: 0)
                .Annotation("SqlServer:Identity", "1, 1");

            migrationBuilder.AddPrimaryKey(
                name: "PK_SalesCustomers",
                table: "SalesCustomers",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_SalesArticles",
                table: "SalesArticles",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_CustomersGroupCustomers",
                table: "CustomersGroupCustomers",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_SalesCustomers_CustomerId",
                table: "SalesCustomers",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_SalesCustomers_SaleId_CustomerId",
                table: "SalesCustomers",
                columns: new[] { "SaleId", "CustomerId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SalesArticles_ArticleId",
                table: "SalesArticles",
                column: "ArticleId");

            migrationBuilder.CreateIndex(
                name: "IX_SalesArticles_SaleId_ArticleId",
                table: "SalesArticles",
                columns: new[] { "SaleId", "ArticleId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CustomersGroupCustomers_CustomerGroupId_CustomerId",
                table: "CustomersGroupCustomers",
                columns: new[] { "CustomerGroupId", "CustomerId" },
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_CustomersGroupCustomers_CustomerGroups_CustomerGroupId",
                table: "CustomersGroupCustomers",
                column: "CustomerGroupId",
                principalTable: "CustomerGroups",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_CustomersGroupCustomers_Customers_CustomerId",
                table: "CustomersGroupCustomers",
                column: "CustomerId",
                principalTable: "Customers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SalesArticles_Articles_ArticleId",
                table: "SalesArticles",
                column: "ArticleId",
                principalTable: "Articles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SalesArticles_Sales_SaleId",
                table: "SalesArticles",
                column: "SaleId",
                principalTable: "Sales",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SalesCustomers_Customers_CustomerId",
                table: "SalesCustomers",
                column: "CustomerId",
                principalTable: "Customers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SalesCustomers_Sales_SaleId",
                table: "SalesCustomers",
                column: "SaleId",
                principalTable: "Sales",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CustomersGroupCustomers_CustomerGroups_CustomerGroupId",
                table: "CustomersGroupCustomers");

            migrationBuilder.DropForeignKey(
                name: "FK_CustomersGroupCustomers_Customers_CustomerId",
                table: "CustomersGroupCustomers");

            migrationBuilder.DropForeignKey(
                name: "FK_SalesArticles_Articles_ArticleId",
                table: "SalesArticles");

            migrationBuilder.DropForeignKey(
                name: "FK_SalesArticles_Sales_SaleId",
                table: "SalesArticles");

            migrationBuilder.DropForeignKey(
                name: "FK_SalesCustomers_Customers_CustomerId",
                table: "SalesCustomers");

            migrationBuilder.DropForeignKey(
                name: "FK_SalesCustomers_Sales_SaleId",
                table: "SalesCustomers");

            migrationBuilder.DropPrimaryKey(
                name: "PK_SalesCustomers",
                table: "SalesCustomers");

            migrationBuilder.DropIndex(
                name: "IX_SalesCustomers_CustomerId",
                table: "SalesCustomers");

            migrationBuilder.DropIndex(
                name: "IX_SalesCustomers_SaleId_CustomerId",
                table: "SalesCustomers");

            migrationBuilder.DropPrimaryKey(
                name: "PK_SalesArticles",
                table: "SalesArticles");

            migrationBuilder.DropIndex(
                name: "IX_SalesArticles_ArticleId",
                table: "SalesArticles");

            migrationBuilder.DropIndex(
                name: "IX_SalesArticles_SaleId_ArticleId",
                table: "SalesArticles");

            migrationBuilder.DropPrimaryKey(
                name: "PK_CustomersGroupCustomers",
                table: "CustomersGroupCustomers");

            migrationBuilder.DropIndex(
                name: "IX_CustomersGroupCustomers_CustomerGroupId_CustomerId",
                table: "CustomersGroupCustomers");

            migrationBuilder.DropColumn(
                name: "Id",
                table: "SalesCustomers");

            migrationBuilder.DropColumn(
                name: "Id",
                table: "SalesArticles");

            migrationBuilder.DropColumn(
                name: "Width",
                table: "SalesArticles");

            migrationBuilder.DropColumn(
                name: "Id",
                table: "CustomersGroupCustomers");

            migrationBuilder.RenameColumn(
                name: "Description",
                table: "SaleTranslations",
                newName: "Comment");

            migrationBuilder.RenameColumn(
                name: "SaleId",
                table: "SalesCustomers",
                newName: "SalesId");

            migrationBuilder.RenameColumn(
                name: "CustomerId",
                table: "SalesCustomers",
                newName: "CustomersId");

            migrationBuilder.RenameColumn(
                name: "SaleId",
                table: "SalesArticles",
                newName: "SalesId");

            migrationBuilder.RenameColumn(
                name: "ArticleId",
                table: "SalesArticles",
                newName: "ArticlesId");

            migrationBuilder.RenameColumn(
                name: "CustomerId",
                table: "CustomersGroupCustomers",
                newName: "CustomersId");

            migrationBuilder.RenameColumn(
                name: "CustomerGroupId",
                table: "CustomersGroupCustomers",
                newName: "CustomerGroupsId");

            migrationBuilder.RenameIndex(
                name: "IX_CustomersGroupCustomers_CustomerId",
                table: "CustomersGroupCustomers",
                newName: "IX_CustomersGroupCustomers_CustomersId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_SalesCustomers",
                table: "SalesCustomers",
                columns: new[] { "CustomersId", "SalesId" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_SalesArticles",
                table: "SalesArticles",
                columns: new[] { "ArticlesId", "SalesId" });

            migrationBuilder.AddPrimaryKey(
                name: "PK_CustomersGroupCustomers",
                table: "CustomersGroupCustomers",
                columns: new[] { "CustomerGroupsId", "CustomersId" });

            migrationBuilder.CreateIndex(
                name: "IX_SalesCustomers_SalesId",
                table: "SalesCustomers",
                column: "SalesId");

            migrationBuilder.CreateIndex(
                name: "IX_SalesArticles_SalesId",
                table: "SalesArticles",
                column: "SalesId");

            migrationBuilder.AddForeignKey(
                name: "FK_CustomersGroupCustomers_CustomerGroups_CustomerGroupsId",
                table: "CustomersGroupCustomers",
                column: "CustomerGroupsId",
                principalTable: "CustomerGroups",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_CustomersGroupCustomers_Customers_CustomersId",
                table: "CustomersGroupCustomers",
                column: "CustomersId",
                principalTable: "Customers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SalesArticles_Articles_ArticlesId",
                table: "SalesArticles",
                column: "ArticlesId",
                principalTable: "Articles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SalesArticles_Sales_SalesId",
                table: "SalesArticles",
                column: "SalesId",
                principalTable: "Sales",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SalesCustomers_Customers_CustomersId",
                table: "SalesCustomers",
                column: "CustomersId",
                principalTable: "Customers",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_SalesCustomers_Sales_SalesId",
                table: "SalesCustomers",
                column: "SalesId",
                principalTable: "Sales",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
