// <auto-generated />
using System;
using EMessa.DAL.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace EMessa.DataAccess.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250616081818_CHANGE_ConvertSaleShadowEntitiesToExplicit")]
    partial class CHANGE_ConvertSaleShadowEntitiesToExplicit
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("ArticleCategory", b =>
                {
                    b.Property<int>("ArticlesId")
                        .HasColumnType("int");

                    b.Property<int>("CategoriesId")
                        .HasColumnType("int");

                    b.HasKey("ArticlesId", "CategoriesId");

                    b.HasIndex("CategoriesId");

                    b.ToTable("ArticleCategories", (string)null);
                });

            modelBuilder.Entity("ArticleFilterAttributeValue", b =>
                {
                    b.Property<int>("ArticlesId")
                        .HasColumnType("int");

                    b.Property<int>("FilterAttributeValuesId")
                        .HasColumnType("int");

                    b.HasKey("ArticlesId", "FilterAttributeValuesId");

                    b.HasIndex("FilterAttributeValuesId");

                    b.ToTable("ArticleFilterValues", (string)null);
                });

            modelBuilder.Entity("ArticleOptionValuesRestrictionCustomer", b =>
                {
                    b.Property<int>("CustomersId")
                        .HasColumnType("int");

                    b.Property<int>("OptionValuesRestrictionsId")
                        .HasColumnType("int");

                    b.HasKey("CustomersId", "OptionValuesRestrictionsId");

                    b.HasIndex("OptionValuesRestrictionsId");

                    b.ToTable("ArticleOptionValuesRestrictionCustomer");
                });

            modelBuilder.Entity("CategoryFilterAttribute", b =>
                {
                    b.Property<int>("CategoriesId")
                        .HasColumnType("int");

                    b.Property<int>("FilterAttributesId")
                        .HasColumnType("int");

                    b.HasKey("CategoriesId", "FilterAttributesId");

                    b.HasIndex("FilterAttributesId");

                    b.ToTable("CategoryFilterAttribute");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.ArticleOptions.ArticleOption", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ArticleId")
                        .HasColumnType("int");

                    b.Property<int>("OptionId")
                        .HasColumnType("int");

                    b.Property<bool>("UseDefaultValue")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.HasKey("Id");

                    b.HasIndex("ArticleId");

                    b.HasIndex("OptionId");

                    b.ToTable("ArticleOption", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.ArticleOptions.ArticleOptionValue", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ArticleOptionId")
                        .HasColumnType("int");

                    b.Property<bool?>("AutoSetValue")
                        .HasColumnType("bit");

                    b.Property<decimal?>("EmbossZoneAddition")
                        .HasPrecision(16, 3)
                        .HasColumnType("decimal(16,3)");

                    b.Property<bool?>("IsDefault")
                        .HasColumnType("bit");

                    b.Property<int>("OptionValueId")
                        .HasColumnType("int");

                    b.Property<decimal?>("WeightFactor")
                        .HasPrecision(16, 3)
                        .HasColumnType("decimal(16,3)");

                    b.HasKey("Id");

                    b.HasIndex("ArticleOptionId");

                    b.HasIndex("OptionValueId");

                    b.ToTable("ArticleOptionValues");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.ArticleValidators.ArticleValidator", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ArticleId")
                        .HasColumnType("int");

                    b.Property<int>("ArticleValidatorConditionId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ArticleId");

                    b.HasIndex("ArticleValidatorConditionId");

                    b.HasIndex("ArticleId", "ArticleValidatorConditionId")
                        .IsUnique();

                    b.ToTable("ArticleValidators", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.ArticleValidators.ArticleValidatorCondition", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Condition")
                        .IsRequired()
                        .HasMaxLength(10000)
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ConfirmMessage")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("ErrorMessage")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("ValidatorType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("IsActive");

                    b.HasIndex("Name");

                    b.HasIndex("ValidatorType");

                    b.ToTable("ArticleValidatorConditions", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.ArticleValidators.ArticleValidatorConditionTranslation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ArticleValidatorConditionId")
                        .HasColumnType("int");

                    b.Property<string>("ConfirmMessage")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasDefaultValue("");

                    b.Property<string>("ErrorMessage")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasDefaultValue("");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("LanguageCode")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.HasKey("Id");

                    b.HasIndex("ArticleValidatorConditionId", "LanguageCode")
                        .IsUnique();

                    b.ToTable("ArticleValidatorConditionTranslations", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Articles.Article", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("ArticleUnitId")
                        .HasColumnType("int");

                    b.Property<decimal>("BasePrice")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(60)
                        .HasColumnType("nvarchar(60)");

                    b.Property<decimal>("DefaultLength")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,3)")
                        .HasDefaultValue(0m);

                    b.Property<decimal>("DefaultWidth")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,3)")
                        .HasDefaultValue(0m);

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("");

                    b.Property<bool>("EditOrderCommentsEnable")
                        .HasColumnType("bit");

                    b.Property<decimal>("EmbossZoneLength")
                        .ValueGeneratedOnAdd()
                        .HasPrecision(16, 3)
                        .HasColumnType("decimal(16,3)")
                        .HasDefaultValue(0m);

                    b.Property<string>("ForeignCode")
                        .IsRequired()
                        .HasMaxLength(60)
                        .HasColumnType("nvarchar(60)");

                    b.Property<decimal>("InitialLength")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,3)")
                        .HasDefaultValue(0m);

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSplittableNParts")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSplitteable")
                        .HasColumnType("bit");

                    b.Property<bool>("LengthEditable")
                        .HasColumnType("bit");

                    b.Property<decimal>("MaxLength")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,3)")
                        .HasDefaultValue(0m);

                    b.Property<decimal>("MaxLongPaw")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,3)")
                        .HasDefaultValue(0m);

                    b.Property<decimal>("MaxWidth")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,3)")
                        .HasDefaultValue(0m);

                    b.Property<decimal>("MinLength")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,3)")
                        .HasDefaultValue(0m);

                    b.Property<decimal>("MinWidth")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,3)")
                        .HasDefaultValue(0m);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("No")
                        .HasColumnType("int");

                    b.Property<decimal>("OverlapLength")
                        .ValueGeneratedOnAdd()
                        .HasPrecision(16, 3)
                        .HasColumnType("decimal(16,3)")
                        .HasDefaultValue(0m);

                    b.Property<bool>("ProfileEditable")
                        .HasColumnType("bit");

                    b.Property<int>("QuantityWarning")
                        .HasColumnType("int");

                    b.Property<bool>("RequireProfile")
                        .HasColumnType("bit");

                    b.Property<decimal>("SplitLength")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,3)")
                        .HasDefaultValue(0m);

                    b.Property<string>("TooLongSheet")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<decimal>("UnitWeight")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,3)")
                        .HasDefaultValue(0m);

                    b.Property<bool>("WidthEditable")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.HasIndex("ArticleUnitId");

                    b.HasIndex(new[] { "Code" }, "CodeIndex")
                        .IsUnique();

                    b.HasIndex(new[] { "Name" }, "NameIndex")
                        .IsUnique();

                    b.ToTable("Articles", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Articles.ArticleTranslation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ArticleId")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("nvarchar(max)")
                        .HasDefaultValue("");

                    b.Property<string>("LanguageCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("ArticleId");

                    b.HasIndex("LanguageCode");

                    b.ToTable("ArticleTranslations", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Articles.ArticleUnit", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(60)
                        .HasColumnType("nvarchar(60)");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "Code" }, "CodeIndex")
                        .IsUnique();

                    b.ToTable("ArticleUnits", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Branches.Branch", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("City")
                        .IsRequired()
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(60)
                        .HasColumnType("nvarchar(60)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)");

                    b.Property<string>("Fax")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("PostCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("SellWarehouseCode")
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "Code" }, "CodeIndex")
                        .IsUnique();

                    b.HasIndex(new[] { "Name" }, "NameIndex")
                        .IsUnique();

                    b.ToTable("Branches", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Categories.Category", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("OrdinaryNumber")
                        .HasColumnType("int");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("ParentId");

                    b.ToTable("Categories");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Categories.CategoryTranslation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CategoryId")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("LanguageCode")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId", "LanguageCode")
                        .IsUnique();

                    b.ToTable("CategoryTranslations", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Configurations.Configuration", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.ToTable("Configurations", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Countries.Country", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(60)
                        .HasColumnType("nvarchar(60)");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)")
                        .HasDefaultValue("EUR");

                    b.Property<string>("LangCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)");

                    b.Property<string>("VatCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "Code" }, "CodeIndex")
                        .IsUnique();

                    b.HasIndex(new[] { "Name" }, "NameIndex")
                        .IsUnique();

                    b.ToTable("Countries", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Customers.Customer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("BranchId")
                        .HasColumnType("int");

                    b.Property<string>("BusinessTerms")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("City")
                        .IsRequired()
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)");

                    b.Property<bool>("Cmr")
                        .HasColumnType("bit");

                    b.Property<int>("CountryId")
                        .HasColumnType("int");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)");

                    b.Property<int?>("FactoryId")
                        .HasColumnType("int");

                    b.Property<bool>("FastProductionTrack")
                        .HasColumnType("bit");

                    b.Property<string>("Fax")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsAppOwner")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("IsPrivate")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<string>("NationalOfficialBusinessRegister")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("PostCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("ProfilInfo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ShortName")
                        .IsRequired()
                        .HasMaxLength(60)
                        .HasColumnType("nvarchar(60)");

                    b.Property<string>("TaxIdentificationNumber")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("Id");

                    b.HasIndex("BranchId");

                    b.HasIndex("CountryId");

                    b.HasIndex("FactoryId");

                    b.HasIndex("ShortName")
                        .IsUnique();

                    b.ToTable("Customers", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Customers.CustomerGroup", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(300)
                        .HasColumnType("nvarchar(300)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("CustomerGroups");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Customers.CustomerGroupCustomer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CustomerGroupId")
                        .HasColumnType("int");

                    b.Property<int>("CustomerId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("CustomerGroupId", "CustomerId")
                        .IsUnique();

                    b.ToTable("CustomersGroupCustomers", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Customers.CustomerLocalization", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("City")
                        .IsRequired()
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)");

                    b.Property<int>("CustomerId")
                        .HasColumnType("int");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)");

                    b.Property<string>("Fax")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("PostCode")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.ToTable("CustomerLocalizations", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Customers.CustomerUser", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CustomerId")
                        .HasColumnType("int");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<int>("UserProfileId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("UserProfileId");

                    b.ToTable("CustomerUsers", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Factories.Factory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("City")
                        .IsRequired()
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(60)
                        .HasColumnType("nvarchar(60)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(60)
                        .HasColumnType("nvarchar(60)");

                    b.Property<string>("Fax")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("Phone")
                        .IsRequired()
                        .HasMaxLength(16)
                        .HasColumnType("nvarchar(16)");

                    b.Property<string>("PostCode")
                        .IsRequired()
                        .HasMaxLength(12)
                        .HasColumnType("nvarchar(12)");

                    b.HasKey("Id");

                    b.HasIndex(new[] { "Code" }, "CodeIndex")
                        .IsUnique();

                    b.ToTable("Factories", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.FilterAttributes.FilterAttribute", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(60)
                        .HasColumnType("nvarchar(60)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("OrdinaryNumber")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("FilterAttributes", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.FilterAttributes.FilterAttributeTranslation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("FilterAttributeId")
                        .HasColumnType("int");

                    b.Property<string>("LanguageCode")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.HasKey("Id");

                    b.HasIndex("FilterAttributeId");

                    b.ToTable("FilterAttributeTranslations");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.FilterAttributes.FilterAttributeValue", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(60)
                        .HasColumnType("nvarchar(60)");

                    b.Property<string>("DisplayValue")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("FilterAttributeId")
                        .HasColumnType("int");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("FilterAttributeId");

                    b.HasIndex("Value")
                        .IsUnique();

                    b.ToTable("FilterAttributesValues", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.FilterAttributes.FilterAttributeValueTranslation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("DisplayValue")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<int>("FilterAttributeValueId")
                        .HasColumnType("int");

                    b.Property<string>("LanguageCode")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.HasKey("Id");

                    b.HasIndex("FilterAttributeValueId");

                    b.ToTable("FilterAttributeValueTranslations");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Identity.ApplicationRole", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("Roles", "Identity");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Identity.ApplicationRoleClaim", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("RoleClaims", "Identity");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Identity.ApplicationUser", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.ToTable("Users", "Identity");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Identity.ApplicationUserClaim", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("UserClaims", "Identity");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Identity.ApplicationUserLogin", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("ProviderKey")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("UserLogins", "Identity");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Identity.ApplicationUserRole", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<int>("RoleId")
                        .HasColumnType("int");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("UserRoles", "Identity");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Identity.ApplicationUserToken", b =>
                {
                    b.Property<int>("UserId")
                        .HasColumnType("int");

                    b.Property<string>("LoginProvider")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("Name")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("UserTokens", "Identity");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Messages.Message", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Body")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(60)
                        .HasColumnType("nvarchar(60)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.ToTable("Messages", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Messages.MessageTranslation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Body")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LanguageCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("MessageId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.HasKey("Id");

                    b.HasIndex("MessageId");

                    b.ToTable("MessageTranslations", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Notifications.Notification", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CreatedById")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("EditedById")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<DateTime>("LastEditDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("EditedById");

                    b.ToTable("Notifications", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Notifications.NotificationUser", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("ConfirmedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("NotificationId")
                        .HasColumnType("int");

                    b.Property<int>("UserProfileId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("UserProfileId");

                    b.HasIndex(new[] { "NotificationId", "UserProfileId" }, "IX_NotificationUser_NotificationId_UserProfileId")
                        .IsUnique();

                    b.ToTable("NotificationUsers", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Options.ArticleOptionValuesRestriction", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ArticleId")
                        .HasColumnType("int");

                    b.Property<int?>("CoatId")
                        .HasColumnType("int");

                    b.Property<int?>("ColorId")
                        .HasColumnType("int");

                    b.Property<int?>("EmbossHeightId")
                        .HasColumnType("int");

                    b.Property<int?>("FeltId")
                        .HasColumnType("int");

                    b.Property<int?>("FoilId")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsEdited")
                        .HasColumnType("bit");

                    b.Property<int?>("ModuleId")
                        .HasColumnType("int");

                    b.Property<bool>("OnlyInSale")
                        .HasColumnType("bit");

                    b.Property<int?>("ThickId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ArticleId");

                    b.HasIndex("CoatId");

                    b.HasIndex("ColorId");

                    b.HasIndex("EmbossHeightId");

                    b.HasIndex("FeltId");

                    b.HasIndex("FoilId");

                    b.HasIndex("ModuleId");

                    b.HasIndex("ThickId");

                    b.ToTable("ArticleOptionValuesRestrictions", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Options.Option", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(60)
                        .HasColumnType("nvarchar(60)");

                    b.Property<string>("Description")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("HideEditor")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("InIndex")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)");

                    b.Property<int>("No")
                        .HasColumnType("int");

                    b.Property<bool>("UseDefaultValue")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.ToTable("Options", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Options.OptionTranslation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("LanguageCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)");

                    b.Property<int>("OptionId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("OptionId");

                    b.HasIndex("LanguageCode", "OptionId")
                        .IsUnique();

                    b.ToTable("OptionTranslations", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Options.OptionValue", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("AutoSetValue")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(60)
                        .HasColumnType("nvarchar(60)");

                    b.Property<decimal>("EmbossZoneAddition")
                        .ValueGeneratedOnAdd()
                        .HasPrecision(16, 3)
                        .HasColumnType("decimal(16,3)")
                        .HasDefaultValue(0m);

                    b.Property<bool>("IsDefault")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<int>("No")
                        .HasColumnType("int");

                    b.Property<int>("OptionId")
                        .HasColumnType("int");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ValueInfo")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasDefaultValue("");

                    b.Property<decimal>("WeightFactor")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18,2)")
                        .HasDefaultValue(0m);

                    b.HasKey("Id");

                    b.HasIndex("OptionId");

                    b.HasIndex("Code", "OptionId")
                        .IsUnique();

                    b.ToTable("OptionValues", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Options.OptionValueTranslation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("LanguageCode")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<int>("OptionValueId")
                        .HasColumnType("int");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ValueInfo")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.HasKey("Id");

                    b.HasIndex("OptionValueId");

                    b.ToTable("OptionValueTranslations", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Orders.Order", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("BranchId")
                        .HasColumnType("int");

                    b.Property<string>("Comments")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("CreatedById")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("CustomerId")
                        .HasColumnType("int");

                    b.Property<int>("CustomerLocalizationId")
                        .HasColumnType("int");

                    b.Property<string>("CustomerNo")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int?>("DeletedById")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("EditedById")
                        .HasColumnType("int");

                    b.Property<int?>("FactoryId")
                        .HasColumnType("int");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<DateTime?>("LastEditDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("MessaNo")
                        .HasColumnType("int");

                    b.Property<int>("No")
                        .HasColumnType("int");

                    b.Property<DateTime?>("PlanedRealizationDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("RealizationDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("BranchId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("CustomerId");

                    b.HasIndex("CustomerLocalizationId");

                    b.HasIndex("DeletedById");

                    b.HasIndex("EditedById");

                    b.HasIndex("FactoryId");

                    b.ToTable("Orders", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Orders.OrderItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ArticleId")
                        .HasColumnType("int");

                    b.Property<string>("Comments")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("DraftDrawingSource")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("DraftEditable")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("DraftHashedFileName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("DraftOriginalFileName")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("Index")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("OrderId")
                        .HasColumnType("int");

                    b.Property<decimal>("Quantity")
                        .ValueGeneratedOnAdd()
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)")
                        .HasDefaultValue(1m);

                    b.Property<int?>("SaleId")
                        .HasColumnType("int");

                    b.Property<bool>("SelectedForOrder")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<decimal>("SumM2")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal>("SumMb")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<int>("SumQuantity")
                        .HasColumnType("int");

                    b.Property<decimal>("Weight")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.HasKey("Id");

                    b.HasIndex("ArticleId");

                    b.HasIndex("OrderId");

                    b.HasIndex("SaleId");

                    b.ToTable("OrderItems", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Orders.OrderItemOptionValue", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("OptionId")
                        .HasColumnType("int");

                    b.Property<int?>("OptionValueId")
                        .HasColumnType("int");

                    b.Property<int>("OrderItemId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("OptionId");

                    b.HasIndex("OptionValueId");

                    b.HasIndex("OrderItemId");

                    b.ToTable("OrderItemOptionValues", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Orders.OrderItemSheet", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Length")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<int>("RequestedOrderItemSheetId")
                        .HasColumnType("int");

                    b.Property<decimal>("Width")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.HasKey("Id");

                    b.HasIndex("RequestedOrderItemSheetId");

                    b.ToTable("OrderItemSheets", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Orders.RequestedOrderItemSheet", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("Length")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.Property<int>("OrderItemId")
                        .HasColumnType("int");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<int?>("SplitN")
                        .HasColumnType("int");

                    b.Property<decimal>("Width")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.HasKey("Id");

                    b.HasIndex("OrderItemId");

                    b.ToTable("RequestedOrderItemSheets", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Pages.Page", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("CreatedById")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(220)
                        .HasColumnType("nvarchar(220)");

                    b.Property<int>("EditedById")
                        .HasColumnType("int");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("bit");

                    b.Property<bool>("IsPublic")
                        .HasColumnType("bit");

                    b.Property<bool>("IsPublished")
                        .HasColumnType("bit");

                    b.Property<DateTime>("LastEditDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Link")
                        .IsRequired()
                        .HasMaxLength(220)
                        .HasColumnType("nvarchar(220)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(220)
                        .HasColumnType("nvarchar(220)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("EditedById");

                    b.HasIndex(new[] { "Link" }, "LinkIndex")
                        .IsUnique();

                    b.ToTable("Pages", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Pages.PageTranslation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("EditedById")
                        .HasColumnType("int");

                    b.Property<string>("LanguageCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("LastEditDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("PageId")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(220)
                        .HasColumnType("nvarchar(220)");

                    b.HasKey("Id");

                    b.HasIndex("EditedById");

                    b.HasIndex(new[] { "PageId", "LanguageCode" }, "PageIdLangIndex")
                        .IsUnique();

                    b.ToTable("PageTranslations", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Sales.Sale", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int?>("ActivatedById")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ActivatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("ClosedById")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ClosedDate")
                        .HasColumnType("datetime2");

                    b.Property<int?>("CoatId")
                        .HasColumnType("int");

                    b.Property<int?>("ColorId")
                        .HasColumnType("int");

                    b.Property<int>("CreatedById")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateFrom")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateTo")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("DefinedWeight")
                        .ValueGeneratedOnAdd()
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)")
                        .HasDefaultValue(0.00m);

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("Efficiency")
                        .ValueGeneratedOnAdd()
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)")
                        .HasDefaultValue(0.00m);

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(220)
                        .HasColumnType("nvarchar(220)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<int?>("ThickId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ActivatedById");

                    b.HasIndex("ClosedById");

                    b.HasIndex("CoatId");

                    b.HasIndex("ColorId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ThickId");

                    b.ToTable("Sales");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Sales.SaleArticle", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ArticleId")
                        .HasColumnType("int");

                    b.Property<int>("SaleId")
                        .HasColumnType("int");

                    b.Property<decimal>("Width")
                        .HasPrecision(18, 3)
                        .HasColumnType("decimal(18,3)");

                    b.HasKey("Id");

                    b.HasIndex("ArticleId");

                    b.HasIndex("SaleId", "ArticleId")
                        .IsUnique();

                    b.ToTable("SalesArticles", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Sales.SaleCustomer", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CustomerId")
                        .HasColumnType("int");

                    b.Property<int>("SaleId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("SaleId", "CustomerId")
                        .IsUnique();

                    b.ToTable("SalesCustomers", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Sales.SaleImage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("FilePath")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OriginalName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SaleId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("SaleId");

                    b.ToTable("SaleImages");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Sales.SalePrice", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)");

                    b.Property<decimal>("Price")
                        .HasPrecision(18, 2)
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("SaleId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("SaleId");

                    b.ToTable("SalePrices");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Sales.SaleRoll", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Coat")
                        .IsRequired()
                        .HasMaxLength(60)
                        .HasColumnType("nvarchar(60)");

                    b.Property<string>("CoatCode")
                        .IsRequired()
                        .HasMaxLength(60)
                        .HasColumnType("nvarchar(60)");

                    b.Property<string>("Color")
                        .IsRequired()
                        .HasMaxLength(60)
                        .HasColumnType("nvarchar(60)");

                    b.Property<string>("ColorCode")
                        .IsRequired()
                        .HasMaxLength(60)
                        .HasColumnType("nvarchar(60)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("OrigNo")
                        .IsRequired()
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)");

                    b.Property<long>("RollId")
                        .HasColumnType("bigint");

                    b.Property<int>("RollNo")
                        .HasColumnType("int");

                    b.Property<DateTime>("RollRegisteredDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("SaleId")
                        .HasColumnType("int");

                    b.Property<decimal>("SaleWeight")
                        .HasPrecision(12, 2)
                        .HasColumnType("decimal(12,2)");

                    b.Property<string>("Supplier")
                        .IsRequired()
                        .HasMaxLength(120)
                        .HasColumnType("nvarchar(120)");

                    b.Property<decimal>("Thick")
                        .HasPrecision(12, 2)
                        .HasColumnType("decimal(12,2)");

                    b.Property<decimal>("Weight")
                        .HasPrecision(12, 2)
                        .HasColumnType("decimal(12,2)");

                    b.HasKey("Id");

                    b.HasIndex("SaleId");

                    b.ToTable("SaleRolls");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Sales.SaleTranslation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LanguageCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(220)
                        .HasColumnType("nvarchar(220)");

                    b.Property<int>("SaleId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("LanguageCode");

                    b.HasIndex("SaleId", "LanguageCode")
                        .IsUnique();

                    b.ToTable("SaleTranslations");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Translations.Translation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("BaseText")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.HasKey("Id");

                    b.HasIndex("BaseText")
                        .IsUnique();

                    b.ToTable("Translations");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Translations.TranslationEntry", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("LanguageCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("nvarchar(3)");

                    b.Property<bool>("Translate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<string>("TranslatedText")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("TranslationId")
                        .HasColumnType("int");

                    b.Property<bool>("Verify")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.HasKey("Id");

                    b.HasIndex("LanguageCode");

                    b.HasIndex("TranslationId", "LanguageCode")
                        .IsUnique();

                    b.ToTable("TranslationEntries");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Users.ApplicationUserProfile", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<bool>("CanRepresentCustomer")
                        .HasColumnType("bit");

                    b.Property<DateTime>("CreatedDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<int>("CustomerId")
                        .HasColumnType("int");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailNotifications")
                        .HasColumnType("bit");

                    b.Property<int?>("FactoryId")
                        .HasColumnType("int");

                    b.Property<bool>("FirstConfiguration")
                        .HasColumnType("bit");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("FirstPhoneNumber")
                        .HasMaxLength(60)
                        .HasColumnType("nvarchar(60)");

                    b.Property<bool>("GeneralSaleTermsAccepted")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastLoginDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastLoginIp")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(60)
                        .HasColumnType("nvarchar(60)");

                    b.Property<DateTime?>("ModifiedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("NotificationsLang")
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<bool>("RegulationsAccepted")
                        .HasColumnType("bit");

                    b.Property<string>("SecondPhoneNumber")
                        .HasMaxLength(60)
                        .HasColumnType("nvarchar(60)");

                    b.Property<bool>("SmsNotifications")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<byte>("UserType")
                        .HasColumnType("tinyint");

                    b.Property<bool>("VatOutsidePl")
                        .HasColumnType("bit");

                    b.Property<bool>("VatPl")
                        .HasColumnType("bit");

                    b.Property<string>("XlLogin")
                        .HasMaxLength(8)
                        .HasColumnType("nvarchar(8)");

                    b.Property<string>("XlPassword")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("FactoryId");

                    b.HasIndex(new[] { "UserName" }, "UserNameIndex")
                        .IsUnique();

                    b.ToTable("UserProfiles", "User");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Users.UserBranch", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("BranchId")
                        .HasColumnType("int");

                    b.Property<int>("UserProfileId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("BranchId");

                    b.HasIndex("UserProfileId");

                    b.ToTable("UserBranches", (string)null);
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Users.UserCustomerLocalization", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("CustomerLocalizationId")
                        .HasColumnType("int");

                    b.Property<int>("UserProfileId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("CustomerLocalizationId");

                    b.HasIndex("UserProfileId");

                    b.ToTable("UserCustomerLocalizations", (string)null);
                });

            modelBuilder.Entity("ArticleCategory", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Articles.Article", null)
                        .WithMany()
                        .HasForeignKey("ArticlesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.Categories.Category", null)
                        .WithMany()
                        .HasForeignKey("CategoriesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ArticleFilterAttributeValue", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Articles.Article", null)
                        .WithMany()
                        .HasForeignKey("ArticlesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.FilterAttributes.FilterAttributeValue", null)
                        .WithMany()
                        .HasForeignKey("FilterAttributeValuesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ArticleOptionValuesRestrictionCustomer", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Customers.Customer", null)
                        .WithMany()
                        .HasForeignKey("CustomersId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.Options.ArticleOptionValuesRestriction", null)
                        .WithMany()
                        .HasForeignKey("OptionValuesRestrictionsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("CategoryFilterAttribute", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Categories.Category", null)
                        .WithMany()
                        .HasForeignKey("CategoriesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.FilterAttributes.FilterAttribute", null)
                        .WithMany()
                        .HasForeignKey("FilterAttributesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("EMessa.DAL.Entities.ArticleOptions.ArticleOption", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Articles.Article", "Article")
                        .WithMany("Options")
                        .HasForeignKey("ArticleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.Options.Option", "Option")
                        .WithMany("ArticleOptions")
                        .HasForeignKey("OptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Article");

                    b.Navigation("Option");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.ArticleOptions.ArticleOptionValue", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.ArticleOptions.ArticleOption", "ArticleOption")
                        .WithMany("Values")
                        .HasForeignKey("ArticleOptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.Options.OptionValue", "OptionValue")
                        .WithMany("ArticleOptionValues")
                        .HasForeignKey("OptionValueId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("ArticleOption");

                    b.Navigation("OptionValue");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.ArticleValidators.ArticleValidator", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Articles.Article", "Article")
                        .WithMany()
                        .HasForeignKey("ArticleId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.ArticleValidators.ArticleValidatorCondition", "ArticleValidatorCondition")
                        .WithMany()
                        .HasForeignKey("ArticleValidatorConditionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Article");

                    b.Navigation("ArticleValidatorCondition");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.ArticleValidators.ArticleValidatorConditionTranslation", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.ArticleValidators.ArticleValidatorCondition", "ArticleValidatorCondition")
                        .WithMany("Translations")
                        .HasForeignKey("ArticleValidatorConditionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ArticleValidatorCondition");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Articles.Article", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Articles.ArticleUnit", "Unit")
                        .WithMany()
                        .HasForeignKey("ArticleUnitId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Unit");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Articles.ArticleTranslation", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Articles.Article", "Article")
                        .WithMany("Translations")
                        .HasForeignKey("ArticleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Article");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Categories.Category", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Categories.Category", "Parent")
                        .WithMany("SubCategories")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Categories.CategoryTranslation", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Categories.Category", "Category")
                        .WithMany("Translations")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Category");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Customers.Customer", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Branches.Branch", "Branch")
                        .WithMany()
                        .HasForeignKey("BranchId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.Countries.Country", "Country")
                        .WithMany()
                        .HasForeignKey("CountryId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.Factories.Factory", "Factory")
                        .WithMany()
                        .HasForeignKey("FactoryId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Branch");

                    b.Navigation("Country");

                    b.Navigation("Factory");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Customers.CustomerGroupCustomer", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Customers.CustomerGroup", "CustomerGroup")
                        .WithMany("CustomersGroupCustomers")
                        .HasForeignKey("CustomerGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.Customers.Customer", "Customer")
                        .WithMany("CustomersGroupCustomers")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("CustomerGroup");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Customers.CustomerLocalization", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Customers.Customer", "Customer")
                        .WithMany("Localisations")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Customers.CustomerUser", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Customers.Customer", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.Users.ApplicationUserProfile", "UserProfile")
                        .WithMany("CustomerUsers")
                        .HasForeignKey("UserProfileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("UserProfile");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.FilterAttributes.FilterAttributeTranslation", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.FilterAttributes.FilterAttribute", "FilterAttribute")
                        .WithMany("Translations")
                        .HasForeignKey("FilterAttributeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("FilterAttribute");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.FilterAttributes.FilterAttributeValue", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.FilterAttributes.FilterAttribute", "FilterAttribute")
                        .WithMany("FilterAttibuteValues")
                        .HasForeignKey("FilterAttributeId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("FilterAttribute");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.FilterAttributes.FilterAttributeValueTranslation", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.FilterAttributes.FilterAttributeValue", "FilterAttributeValue")
                        .WithMany("Translations")
                        .HasForeignKey("FilterAttributeValueId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("FilterAttributeValue");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Identity.ApplicationRoleClaim", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Identity.ApplicationRole", "Role")
                        .WithMany("RoleClaims")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Identity.ApplicationUserClaim", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Identity.ApplicationUser", "User")
                        .WithMany("Claims")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Identity.ApplicationUserLogin", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Identity.ApplicationUser", "User")
                        .WithMany("Logins")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Identity.ApplicationUserRole", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Identity.ApplicationRole", "Role")
                        .WithMany("UserRoles")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.Identity.ApplicationUser", "User")
                        .WithMany("UserRoles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Identity.ApplicationUserToken", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Identity.ApplicationUser", "User")
                        .WithMany("Tokens")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Messages.MessageTranslation", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Messages.Message", "Message")
                        .WithMany("Translations")
                        .HasForeignKey("MessageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Message");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Notifications.Notification", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Users.ApplicationUserProfile", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.Users.ApplicationUserProfile", "EditedBy")
                        .WithMany()
                        .HasForeignKey("EditedById");

                    b.Navigation("CreatedBy");

                    b.Navigation("EditedBy");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Notifications.NotificationUser", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Notifications.Notification", "Notification")
                        .WithMany("NotificationUsers")
                        .HasForeignKey("NotificationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.Users.ApplicationUserProfile", "UserProfile")
                        .WithMany()
                        .HasForeignKey("UserProfileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Notification");

                    b.Navigation("UserProfile");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Options.ArticleOptionValuesRestriction", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Articles.Article", "Article")
                        .WithMany("OptionValuesRestrictions")
                        .HasForeignKey("ArticleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.Options.OptionValue", "Coat")
                        .WithMany()
                        .HasForeignKey("CoatId");

                    b.HasOne("EMessa.DAL.Entities.Options.OptionValue", "Color")
                        .WithMany()
                        .HasForeignKey("ColorId");

                    b.HasOne("EMessa.DAL.Entities.Options.OptionValue", "EmbossHeight")
                        .WithMany()
                        .HasForeignKey("EmbossHeightId");

                    b.HasOne("EMessa.DAL.Entities.Options.OptionValue", "Felt")
                        .WithMany()
                        .HasForeignKey("FeltId");

                    b.HasOne("EMessa.DAL.Entities.Options.OptionValue", "Foil")
                        .WithMany()
                        .HasForeignKey("FoilId");

                    b.HasOne("EMessa.DAL.Entities.Options.OptionValue", "Module")
                        .WithMany()
                        .HasForeignKey("ModuleId");

                    b.HasOne("EMessa.DAL.Entities.Options.OptionValue", "Thick")
                        .WithMany()
                        .HasForeignKey("ThickId");

                    b.Navigation("Article");

                    b.Navigation("Coat");

                    b.Navigation("Color");

                    b.Navigation("EmbossHeight");

                    b.Navigation("Felt");

                    b.Navigation("Foil");

                    b.Navigation("Module");

                    b.Navigation("Thick");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Options.OptionTranslation", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Options.Option", "Option")
                        .WithMany("Translations")
                        .HasForeignKey("OptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Option");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Options.OptionValue", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Options.Option", "Option")
                        .WithMany("Values")
                        .HasForeignKey("OptionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Option");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Options.OptionValueTranslation", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Options.OptionValue", "OptionValue")
                        .WithMany("Translations")
                        .HasForeignKey("OptionValueId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("OptionValue");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Orders.Order", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Branches.Branch", "Branch")
                        .WithMany("Orders")
                        .HasForeignKey("BranchId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.Users.ApplicationUserProfile", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.Customers.Customer", "Customer")
                        .WithMany("Orders")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.Customers.CustomerLocalization", "CustomerLocalization")
                        .WithMany("Orders")
                        .HasForeignKey("CustomerLocalizationId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.Users.ApplicationUserProfile", "DeletedBy")
                        .WithMany()
                        .HasForeignKey("DeletedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("EMessa.DAL.Entities.Users.ApplicationUserProfile", "EditedBy")
                        .WithMany()
                        .HasForeignKey("EditedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("EMessa.DAL.Entities.Factories.Factory", "Factory")
                        .WithMany()
                        .HasForeignKey("FactoryId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("Branch");

                    b.Navigation("CreatedBy");

                    b.Navigation("Customer");

                    b.Navigation("CustomerLocalization");

                    b.Navigation("DeletedBy");

                    b.Navigation("EditedBy");

                    b.Navigation("Factory");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Orders.OrderItem", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Articles.Article", "Article")
                        .WithMany()
                        .HasForeignKey("ArticleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.Orders.Order", "Order")
                        .WithMany("OrderItems")
                        .HasForeignKey("OrderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.Sales.Sale", "Sale")
                        .WithMany("OrderItems")
                        .HasForeignKey("SaleId");

                    b.Navigation("Article");

                    b.Navigation("Order");

                    b.Navigation("Sale");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Orders.OrderItemOptionValue", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Options.Option", "Option")
                        .WithMany()
                        .HasForeignKey("OptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.Options.OptionValue", "OptionValue")
                        .WithMany()
                        .HasForeignKey("OptionValueId");

                    b.HasOne("EMessa.DAL.Entities.Orders.OrderItem", "OrderItem")
                        .WithMany("OptionValues")
                        .HasForeignKey("OrderItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Option");

                    b.Navigation("OptionValue");

                    b.Navigation("OrderItem");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Orders.OrderItemSheet", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Orders.RequestedOrderItemSheet", "RequestedOrderItemSheet")
                        .WithMany("OrderItemSheets")
                        .HasForeignKey("RequestedOrderItemSheetId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("RequestedOrderItemSheet");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Orders.RequestedOrderItemSheet", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Orders.OrderItem", "OrderItem")
                        .WithMany("RequestedSheets")
                        .HasForeignKey("OrderItemId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("OrderItem");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Pages.Page", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Users.ApplicationUserProfile", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.Users.ApplicationUserProfile", "EditedBy")
                        .WithMany()
                        .HasForeignKey("EditedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("EditedBy");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Pages.PageTranslation", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Users.ApplicationUserProfile", "EditedBy")
                        .WithMany()
                        .HasForeignKey("EditedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.Pages.Page", "Page")
                        .WithMany("Translations")
                        .HasForeignKey("PageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("EditedBy");

                    b.Navigation("Page");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Sales.Sale", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Users.ApplicationUserProfile", "ActivatedBy")
                        .WithMany("ActivatedSales")
                        .HasForeignKey("ActivatedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("EMessa.DAL.Entities.Users.ApplicationUserProfile", "ClosedBy")
                        .WithMany("ClosedSales")
                        .HasForeignKey("ClosedById")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.HasOne("EMessa.DAL.Entities.Options.OptionValue", "Coat")
                        .WithMany()
                        .HasForeignKey("CoatId");

                    b.HasOne("EMessa.DAL.Entities.Options.OptionValue", "Color")
                        .WithMany()
                        .HasForeignKey("ColorId");

                    b.HasOne("EMessa.DAL.Entities.Users.ApplicationUserProfile", "CreatedBy")
                        .WithMany("CreatedSales")
                        .HasForeignKey("CreatedById")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.Options.OptionValue", "Thick")
                        .WithMany()
                        .HasForeignKey("ThickId");

                    b.Navigation("ActivatedBy");

                    b.Navigation("ClosedBy");

                    b.Navigation("Coat");

                    b.Navigation("Color");

                    b.Navigation("CreatedBy");

                    b.Navigation("Thick");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Sales.SaleArticle", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Articles.Article", "Article")
                        .WithMany("SalesArticles")
                        .HasForeignKey("ArticleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.Sales.Sale", "Sale")
                        .WithMany("SalesArticles")
                        .HasForeignKey("SaleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Article");

                    b.Navigation("Sale");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Sales.SaleCustomer", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Customers.Customer", "Customer")
                        .WithMany("SalesCustomers")
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.Sales.Sale", "Sale")
                        .WithMany("SalesCustomers")
                        .HasForeignKey("SaleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Customer");

                    b.Navigation("Sale");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Sales.SaleImage", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Sales.Sale", "Sale")
                        .WithMany("Images")
                        .HasForeignKey("SaleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Sale");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Sales.SalePrice", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Sales.Sale", "Sale")
                        .WithMany("Prices")
                        .HasForeignKey("SaleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Sale");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Sales.SaleRoll", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Sales.Sale", "Sale")
                        .WithMany("Rolls")
                        .HasForeignKey("SaleId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Sale");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Sales.SaleTranslation", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Sales.Sale", "Sale")
                        .WithMany("Translations")
                        .HasForeignKey("SaleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Sale");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Translations.TranslationEntry", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Translations.Translation", "Translation")
                        .WithMany("TranslationEntries")
                        .HasForeignKey("TranslationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Translation");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Users.ApplicationUserProfile", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Customers.Customer", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.Factories.Factory", "Factory")
                        .WithMany()
                        .HasForeignKey("FactoryId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Customer");

                    b.Navigation("Factory");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Users.UserBranch", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Branches.Branch", "Branch")
                        .WithMany()
                        .HasForeignKey("BranchId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.Users.ApplicationUserProfile", "UserProfile")
                        .WithMany("UserBranches")
                        .HasForeignKey("UserProfileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Branch");

                    b.Navigation("UserProfile");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Users.UserCustomerLocalization", b =>
                {
                    b.HasOne("EMessa.DAL.Entities.Customers.CustomerLocalization", "CustomerLocalization")
                        .WithMany()
                        .HasForeignKey("CustomerLocalizationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("EMessa.DAL.Entities.Users.ApplicationUserProfile", "UserProfile")
                        .WithMany("UserLocalizations")
                        .HasForeignKey("UserProfileId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("CustomerLocalization");

                    b.Navigation("UserProfile");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.ArticleOptions.ArticleOption", b =>
                {
                    b.Navigation("Values");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.ArticleValidators.ArticleValidatorCondition", b =>
                {
                    b.Navigation("Translations");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Articles.Article", b =>
                {
                    b.Navigation("OptionValuesRestrictions");

                    b.Navigation("Options");

                    b.Navigation("SalesArticles");

                    b.Navigation("Translations");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Branches.Branch", b =>
                {
                    b.Navigation("Orders");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Categories.Category", b =>
                {
                    b.Navigation("SubCategories");

                    b.Navigation("Translations");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Customers.Customer", b =>
                {
                    b.Navigation("CustomersGroupCustomers");

                    b.Navigation("Localisations");

                    b.Navigation("Orders");

                    b.Navigation("SalesCustomers");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Customers.CustomerGroup", b =>
                {
                    b.Navigation("CustomersGroupCustomers");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Customers.CustomerLocalization", b =>
                {
                    b.Navigation("Orders");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.FilterAttributes.FilterAttribute", b =>
                {
                    b.Navigation("FilterAttibuteValues");

                    b.Navigation("Translations");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.FilterAttributes.FilterAttributeValue", b =>
                {
                    b.Navigation("Translations");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Identity.ApplicationRole", b =>
                {
                    b.Navigation("RoleClaims");

                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Identity.ApplicationUser", b =>
                {
                    b.Navigation("Claims");

                    b.Navigation("Logins");

                    b.Navigation("Tokens");

                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Messages.Message", b =>
                {
                    b.Navigation("Translations");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Notifications.Notification", b =>
                {
                    b.Navigation("NotificationUsers");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Options.Option", b =>
                {
                    b.Navigation("ArticleOptions");

                    b.Navigation("Translations");

                    b.Navigation("Values");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Options.OptionValue", b =>
                {
                    b.Navigation("ArticleOptionValues");

                    b.Navigation("Translations");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Orders.Order", b =>
                {
                    b.Navigation("OrderItems");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Orders.OrderItem", b =>
                {
                    b.Navigation("OptionValues");

                    b.Navigation("RequestedSheets");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Orders.RequestedOrderItemSheet", b =>
                {
                    b.Navigation("OrderItemSheets");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Pages.Page", b =>
                {
                    b.Navigation("Translations");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Sales.Sale", b =>
                {
                    b.Navigation("Images");

                    b.Navigation("OrderItems");

                    b.Navigation("Prices");

                    b.Navigation("Rolls");

                    b.Navigation("SalesArticles");

                    b.Navigation("SalesCustomers");

                    b.Navigation("Translations");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Translations.Translation", b =>
                {
                    b.Navigation("TranslationEntries");
                });

            modelBuilder.Entity("EMessa.DAL.Entities.Users.ApplicationUserProfile", b =>
                {
                    b.Navigation("ActivatedSales");

                    b.Navigation("ClosedSales");

                    b.Navigation("CreatedSales");

                    b.Navigation("CustomerUsers");

                    b.Navigation("UserBranches");

                    b.Navigation("UserLocalizations");
                });
#pragma warning restore 612, 618
        }
    }
}
