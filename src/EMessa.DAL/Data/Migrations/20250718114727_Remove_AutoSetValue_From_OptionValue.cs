using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EMessa.DataAccess.Migrations
{
    /// <inheritdoc />
    public partial class Remove_AutoSetValue_From_OptionValue : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AutoSetValue",
                table: "OptionValues");

            migrationBuilder.DropColumn(
                name: "AutoSetValue",
                table: "ArticleOptionValues");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "AutoSetValue",
                table: "OptionValues",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "AutoSetValue",
                table: "ArticleOptionValues",
                type: "bit",
                nullable: true);
        }
    }
}
