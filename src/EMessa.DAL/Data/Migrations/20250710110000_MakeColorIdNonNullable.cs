using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EMessa.DataAccess.Migrations
{
    /// <inheritdoc />
    public partial class MakeColorIdNonNullable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // First, we need to update any existing records with NULL ColorId to a valid value
            // To nie ma sensu
            // migrationBuilder.Sql("UPDATE Sales SET ColorId = 1 WHERE ColorId IS NULL");
    
            // Then alter the column to make it non-nullable
            migrationBuilder.AlterColumn<int>(
                name: "ColorId",
                table: "Sales",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            // Update the foreign key relationship to be required
            // First drop the existing foreign key
            migrationBuilder.DropForeignKey(
                name: "FK_Sales_OptionValues_ColorId",
                table: "Sales");

            // Then add the new foreign key with ON DELETE NO ACTION
            migrationBuilder.AddForeignKey(
                name: "FK_Sales_OptionValues_ColorId",
                table: "Sales",
                column: "ColorId",
                principalTable: "OptionValues",
                principalColumn: "Id",
                onDelete: ReferentialAction.NoAction);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // First drop the foreign key
            migrationBuilder.DropForeignKey(
                name: "FK_Sales_OptionValues_ColorId",
                table: "Sales");

            // Then add back the original foreign key
            migrationBuilder.AddForeignKey(
                name: "FK_Sales_OptionValues_ColorId",
                table: "Sales",
                column: "ColorId",
                principalTable: "OptionValues",
                principalColumn: "Id");

            // Finally, alter the column back to nullable
            migrationBuilder.AlterColumn<int>(
                name: "ColorId",
                table: "Sales",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int",
                oldDefaultValue: 0);
        }
    }
}