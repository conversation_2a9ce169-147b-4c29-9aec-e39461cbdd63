using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EMessa.DataAccess.Migrations
{
    /// <inheritdoc />
    public partial class MakeDescriptionNonNullable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Najpierw aktualizuj istniejące rekordy z NULL na pusty string
            migrationBuilder.Sql("UPDATE Sales SET Description = '' WHERE Description IS NULL");
    
            // Następnie zmień kolumnę na non-nullable
            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "Sales",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "Description",
                table: "Sales",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldDefaultValue: "");
        }
    }
}
