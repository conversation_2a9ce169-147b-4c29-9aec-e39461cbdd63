using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace EMessa.DataAccess.Migrations
{
    /// <inheritdoc />
    public partial class RenamedPropertyLockedByUserProfileId : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_LockOrders_UserProfiles_LockedById",
                table: "LockOrders");

            migrationBuilder.RenameColumn(
                name: "LockedById",
                table: "LockOrders",
                newName: "LockedByUserProfileId");

            migrationBuilder.RenameIndex(
                name: "IX_LockOrders_OrderId_LockedById",
                table: "LockOrders",
                newName: "IX_LockOrders_OrderId_LockedByUserProfileId");

            migrationBuilder.RenameIndex(
                name: "IX_LockOrders_LockedById",
                table: "LockOrders",
                newName: "IX_LockOrders_LockedByUserProfileId");

            migrationBuilder.CreateTable(
                name: "PriceLists",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    Type = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ValidFrom = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ValidTo = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CountryId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PriceLists", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PriceLists_Countries_CountryId",
                        column: x => x.CountryId,
                        principalTable: "Countries",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PriceListArticles",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    PriceListId = table.Column<int>(type: "int", nullable: false),
                    ArticleIndex = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: false),
                    PriceNet = table.Column<decimal>(type: "decimal(14,2)", precision: 14, scale: 2, nullable: false),
                    PriceGross = table.Column<decimal>(type: "decimal(14,2)", precision: 14, scale: 2, nullable: false),
                    PriceVatRate = table.Column<decimal>(type: "decimal(5,2)", precision: 5, scale: 2, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PriceListArticles", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PriceListArticles_PriceLists_PriceListId",
                        column: x => x.PriceListId,
                        principalTable: "PriceLists",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_PriceListArticles_ArticleIndex",
                table: "PriceListArticles",
                column: "ArticleIndex");

            migrationBuilder.CreateIndex(
                name: "IX_PriceListArticles_PriceListId",
                table: "PriceListArticles",
                column: "PriceListId");

            migrationBuilder.CreateIndex(
                name: "IX_PriceLists_CountryId",
                table: "PriceLists",
                column: "CountryId");

            migrationBuilder.AddForeignKey(
                name: "FK_LockOrders_UserProfiles_LockedByUserProfileId",
                table: "LockOrders",
                column: "LockedByUserProfileId",
                principalSchema: "User",
                principalTable: "UserProfiles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_LockOrders_UserProfiles_LockedByUserProfileId",
                table: "LockOrders");

            migrationBuilder.DropTable(
                name: "PriceListArticles");

            migrationBuilder.DropTable(
                name: "PriceLists");

            migrationBuilder.RenameColumn(
                name: "LockedByUserProfileId",
                table: "LockOrders",
                newName: "LockedById");

            migrationBuilder.RenameIndex(
                name: "IX_LockOrders_OrderId_LockedByUserProfileId",
                table: "LockOrders",
                newName: "IX_LockOrders_OrderId_LockedById");

            migrationBuilder.RenameIndex(
                name: "IX_LockOrders_LockedByUserProfileId",
                table: "LockOrders",
                newName: "IX_LockOrders_LockedById");

            migrationBuilder.AddForeignKey(
                name: "FK_LockOrders_UserProfiles_LockedById",
                table: "LockOrders",
                column: "LockedById",
                principalSchema: "User",
                principalTable: "UserProfiles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
