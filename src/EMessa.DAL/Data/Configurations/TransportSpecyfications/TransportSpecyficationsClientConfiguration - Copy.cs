using EMessa.DAL.Entities.TransportSpecyfications;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EMessa.DAL.Data.Configurations.TransportSpecyfications;
public class TransportBranchSpecificationConfiguration : IEntityTypeConfiguration<TransportBranchSpecification>
{
    public void Configure(EntityTypeBuilder<TransportBranchSpecification> builder)
    {
        builder.ToTable("TransportBranchSpecifications");

        builder.HasKey(x => x.Id);
        
        builder.Property(s => s.No)
        .IsRequired()
        .HasMaxLength(40);

        builder.Property(s => s.TransportDate)
            .IsRequired(false);

        builder.Property(s => s.PdfFilePath).HasMaxLength(250)
            .IsRequired(false);

        builder.Property(s => s.DriverFullName).HasMaxLength(100)
            .IsRequired(false);
        builder.Property(s => s.DriverPhone).HasMaxLength(20)
            .IsRequired(false);

        builder.Property(s => s.MessaSpcyficationId).HasMaxLength(20)
            .IsRequired();

        builder.Property(s => s.TransportNo).HasMaxLength(20)
            .IsRequired();

        builder.HasOne(s => s.Branch).WithMany(x=> x.TransportBranchSpecifications)
            .HasForeignKey(s => s.BranchId)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired(false);

        builder.Property(s => s.MessaBranchId)
            .IsRequired();

        builder.HasIndex(s => s.TransportDate);
        builder.HasIndex(s => s.MessaBranchId);
        builder.HasIndex(s => s.MessaSpcyficationId);

    }
}

