using EMessa.DAL.Entities.Articles;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EMessa.DAL.Data.Configurations.Articles
{
    public class ArticleTranslationConfiguration : IEntityTypeConfiguration<ArticleTranslation>
    {
        public void Configure(EntityTypeBuilder<ArticleTranslation> builder)
        {
            builder.HasKey(e => e.Id);

            builder.Property(e => e.Name)
                .HasMaxLength(256).IsRequired();
            builder.Property(e => e.Description).HasDefaultValue("");

            builder.HasOne(x => x.Article)
                .WithMany(x => x.Translations)
                .HasForeignKey(x => x.ArticleId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasIndex(e => e.LanguageCode);

            // TODO: Moim zdaniem warto to dodać, aby z<PERSON><PERSON> się przed podwójnymi tłumaczeniami
            //builder.HasIndex(at => new { at.ArticleId, at.LanguageCode })
            //    .IsUnique();

            builder.ToTable("ArticleTranslations");
        }
    }
}
