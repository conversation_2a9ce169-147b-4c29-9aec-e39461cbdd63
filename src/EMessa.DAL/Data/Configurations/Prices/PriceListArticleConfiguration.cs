using EMessa.DAL.Entities.Prices;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

public class PriceListArticleConfiguration : IEntityTypeConfiguration<PriceListArticle>
{
    public void Configure(EntityTypeBuilder<PriceListArticle> builder)
    {
        builder.ToTable("PriceListArticles");

        builder.<PERSON><PERSON>ey(pa => pa.Id);

        builder.Property(pa => pa.PriceListId)
            .IsRequired();


        builder.Property(pa => pa.ArticleIndex)
            .IsRequired()
            .HasMaxLength(40);

        builder.Property(pa => pa.PriceGross).IsRequired().HasPrecision(14, 2);
        builder.Property(pa => pa.PriceNet).IsRequired().HasPrecision(14, 2);
        builder.Property(pa => pa.PriceVatRate).IsRequired().HasPrecision(5, 2);

        builder.HasIndex(pa => pa.ArticleIndex)
            .IsUnique(false);

        builder.HasOne(pa => pa.PriceList)
            .WithMany(pl => pl.Articles)
            .HasForeignKey(pa => pa.PriceListId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}



