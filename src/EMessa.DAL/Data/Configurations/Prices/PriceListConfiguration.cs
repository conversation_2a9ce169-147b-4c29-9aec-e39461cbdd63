using EMessa.DAL.Entities.Prices;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

public class PriceListConfiguration : IEntityTypeConfiguration<PriceList>
{
    public void Configure(EntityTypeBuilder<PriceList> builder)
    {
        builder.ToTable("PriceLists");

        builder.HasKey(pl => pl.Id);

        builder.Property(pl => pl.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(pl => pl.Description)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(pl => pl.IsActive)
            .IsRequired();

        builder.Property(pl => pl.Type)
            .IsRequired();

        builder.Property(pl => pl.CreatedAt)
            .IsRequired();

        builder.Property(pl => pl.UpdatedAt)
            .IsRequired();

        builder.Property(pl => pl.ValidFrom);

        builder.Property(pl => pl.ValidTo);

        builder.HasMany(pl => pl.Articles)
            .WithOne(pa => pa.PriceList)
            .HasForeignKey("PriceListId")
            .OnDelete(DeleteBehavior.Cascade);
    }
}



