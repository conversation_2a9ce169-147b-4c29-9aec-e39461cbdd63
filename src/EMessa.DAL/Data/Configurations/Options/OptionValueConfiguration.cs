using EMessa.DAL.Entities.Options;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EMessa.DAL.Data.Configurations.Options
{
    public class OptionValueConfiguration : IEntityTypeConfiguration<OptionValue>
    {
        public void Configure(EntityTypeBuilder<OptionValue> builder)
        {
            builder.Property(x => x.No).IsRequired();
            builder.Property(x => x.Value).IsRequired();
            builder.Property(x => x.Code).HasMaxLength(60).IsRequired();
            builder.Property(x => x.WeightFactor).HasColumnType("decimal(18,2)").HasDefaultValue(0.00).IsRequired();
            builder.Property(x => x.ValueInfo).HasMaxLength(500).HasDefaultValue(string.Empty);

            builder.Property(x => x.IsDefault).IsRequired().HasDefaultValue(false);
            builder.Property(x => x.EmbossZoneAddition).IsRequired().HasPrecision(16, 3).HasDefaultValue(0);

            builder.HasIndex(x => new { x.Code, x.OptionId }).IsUnique();

            builder.ToTable("OptionValues");
        }
    }
}
