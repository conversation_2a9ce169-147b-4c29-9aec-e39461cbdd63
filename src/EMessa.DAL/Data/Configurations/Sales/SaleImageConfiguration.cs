using EMessa.DAL.Entities.Sales;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EMessa.DAL.Data.Configurations.Sales;

public class SaleImageConfiguration : IEntityTypeConfiguration<SaleImage>
{
    public void Configure(EntityTypeBuilder<SaleImage> builder)
    {
        builder.HasOne(x => x.Sale)
            .WithMany(x => x.Images)
            .HasForeignKey(x => x.SaleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasQueryFilter(si => !si.Sale.IsDeleted);
    }
}
