using EMessa.DAL.Entities.Sales;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EMessa.DAL.Data.Configurations.Sales;

public class SaleTranslationConfiguration : IEntityTypeConfiguration<SaleTranslation>
{
    public void Configure(EntityTypeBuilder<SaleTranslation> builder)
    {
        builder.Property(st => st.Name)
            .IsRequired()
            .HasMaxLength(220);

        builder.Property(st => st.LanguageCode)
            .IsRequired();

        builder.HasOne(st => st.Sale)
            .WithMany(s => s.Translations)
            .HasForeignKey(st => st.SaleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasIndex(st => st.LanguageCode);

        builder.HasIndex(st => new { st.SaleId, st.LanguageCode })
            .IsUnique();
        
        builder.HasQueryFilter(si => !si.Sale.IsDeleted);
    }
}
