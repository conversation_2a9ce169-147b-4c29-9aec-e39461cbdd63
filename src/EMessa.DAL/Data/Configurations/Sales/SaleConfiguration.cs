using EMessa.DAL.Entities.Sales;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EMessa.DAL.Data.Configurations.Sales;

public class SaleConfiguration : IEntityTypeConfiguration<Sale>
{
    public void Configure(EntityTypeBuilder<Sale> builder)
    {
        builder.Property(s => s.Name)
            .IsRequired()
            .HasMaxLength(220);

        builder.Property(s => s.Description)
            .IsRequired()
            .HasDefaultValue("");

        builder.Property(s => s.DefinedWeight)
            .HasPrecision(18, 3)
            .HasDefaultValue(0.00m);

        builder.Property(s => s.Efficiency)
            .HasPrecision(18, 3)
            .HasDefaultValue(0.00m);

        builder.Property(s => s.IsDeleted)
            .HasDefaultValue(false);

        builder.Property(s => s.DateFrom)
            .IsRequired();

        builder.Property(s => s.DateTo)
            .IsRequired();

        builder.Property(s => s.CoatId)
            .IsRequired();

        builder.Property(s => s.ColorId)
            .IsRequired();

        builder.Property(s => s.ThickId)
            .IsRequired();

        builder.HasOne(s => s.Coat)
            .WithMany()
            .HasForeignKey(s => s.CoatId)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired();

        builder.HasOne(s => s.Color)
            .WithMany()
            .HasForeignKey(s => s.ColorId)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired();

        builder.HasOne(s => s.Thick)
            .WithMany()
            .HasForeignKey(s => s.ThickId)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired();

        builder.HasOne(s => s.ActivatedBy)
            .WithMany(aup => aup.ActivatedSales)
            .HasForeignKey(s => s.ActivatedById)
            .OnDelete(DeleteBehavior.NoAction);

        builder.HasOne(s => s.ClosedBy)
            .WithMany(aup => aup.ClosedSales)
            .HasForeignKey(s => s.ClosedById)
            .OnDelete(DeleteBehavior.NoAction);

        builder.HasOne(s => s.CreatedBy)
            .WithMany(aup => aup.CreatedSales)
            .HasForeignKey(s => s.CreatedById)
            .OnDelete(DeleteBehavior.NoAction);


        builder.HasIndex(s => s.ActivatedById);
        builder.HasIndex(s => s.ClosedById);
        builder.HasIndex(s => s.CreatedById);


        builder.HasQueryFilter(s => !s.IsDeleted);
    }
}
