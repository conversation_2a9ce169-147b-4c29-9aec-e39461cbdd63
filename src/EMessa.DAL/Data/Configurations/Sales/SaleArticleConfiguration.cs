using EMessa.DAL.Entities.Sales;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EMessa.DAL.Data.Configurations.Sales;

public class SaleArticleConfiguration : IEntityTypeConfiguration<SaleArticle>
{
    public void Configure(EntityTypeBuilder<SaleArticle> builder)
    {
        builder.ToTable("SalesArticles");

        builder.<PERSON><PERSON><PERSON>(sa => sa.Id);

        builder.Property(sa => sa.Width)
            .HasPrecision(18, 3);

        builder.HasIndex(sa => new { sa.SaleId, sa.ArticleId }).IsUnique();

        builder.HasOne(sa => sa.Sale)
            .WithMany(s => s.SalesArticles)
            .HasForeignKey(sa => sa.SaleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(sa => sa.Article)
            .WithMany(a => a.SalesArticles)
            .HasForeignKey(sa => sa.ArticleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasQueryFilter(sa => !sa.Sale.IsDeleted);
    }
}
