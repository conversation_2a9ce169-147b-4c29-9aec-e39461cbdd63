using EMessa.DAL.Entities.Sales;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EMessa.DAL.Data.Configurations.Sales;

public class SaleCustomerConfiguration : IEntityTypeConfiguration<SaleCustomer>
{
    public void Configure(EntityTypeBuilder<SaleCustomer> builder)
    {
        builder.ToTable("SalesCustomers");

        builder.<PERSON><PERSON><PERSON>(sc => sc.Id);

        builder.HasIndex(sc => new { sc.SaleId, sc.CustomerId }).IsUnique();

        builder.HasOne(sc => sc.Sale)
            .WithMany(s => s.SalesCustomers)
            .HasForeignKey(sc => sc.SaleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(sc => sc.Customer)
            .WithMany(c => c.SalesCustomers)
            .HasForeignKey(sc => sc.CustomerId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasQueryFilter(sc => !sc.Sale.IsDeleted);
    }
}
