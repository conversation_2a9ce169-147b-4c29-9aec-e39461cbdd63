using EMessa.DAL.Entities.Sales;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EMessa.DAL.Data.Configurations.Sales;

public class SaleRollConfiguration : IEntityTypeConfiguration<SaleRoll>
{
    void IEntityTypeConfiguration<SaleRoll>.Configure(EntityTypeBuilder<SaleRoll> builder)
    {
        builder.Property(e => e.CoatCode).IsRequired().HasMaxLength(60);
        builder.Property(e => e.ColorCode).IsRequired().HasMaxLength(60);
        builder.Property(e => e.ThickCode).IsRequired().HasMaxLength(60);

        builder.Property(e => e.SaleWeight).HasPrecision(12, 2);
        builder.Property(e => e.Weight).HasPrecision(12, 2);

        builder.Property(e => e.OrigNo).HasMaxLength(120);
        builder.Property(e => e.Supplier).HasMaxLength(120);

        builder.HasOne(d => d.Sale)
            .WithMany(p => p.Rolls)
            .HasForeignKey(d => d.SaleId)
            .OnDelete(DeleteBehavior.NoAction);

        builder.HasIndex(e => e.SaleId);

        builder.HasQueryFilter(si => !si.Sale.IsDeleted);
    }
}
