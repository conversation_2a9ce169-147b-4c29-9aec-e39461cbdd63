using EMessa.DAL.Entities.Sales;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EMessa.DAL.Data.Configurations.Sales;

public class SalePriceConfiguration : IEntityTypeConfiguration<SalePrice>
{
    public void Configure(EntityTypeBuilder<SalePrice> builder)
    {
        builder.Property(x => x.Price).IsRequired().HasPrecision(18, 2);
        builder.Property(x => x.CurrencyCode).IsRequired().HasMaxLength(3);

        builder.HasOne(x => x.Sale)
            .WithMany(x => x.Prices)
            .HasForeignKey(x => x.SaleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasIndex(x => x.SaleId);
        
        builder.HasQueryFilter(si => !si.Sale.IsDeleted);
    }
}
