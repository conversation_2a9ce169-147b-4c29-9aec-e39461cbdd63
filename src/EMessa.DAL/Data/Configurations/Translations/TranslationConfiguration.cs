using EMessa.DAL.Entities.Translations;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EMessa.DAL.Data.Configurations.Translations;

public class TranslationConfiguration : IEntityTypeConfiguration<Translation>
{
    public void Configure(EntityTypeBuilder<Translation> builder)
    {
        builder.HasKey(t => t.Id);
        builder.Property(t => t.BaseText)
            .IsRequired()
            .HasMaxLength(500)
            .UseCollation("SQL_Latin1_General_CP1_CS_AS"); // Case-sensitive collation

        builder.HasIndex(t => t.BaseText)
            .IsUnique();

        builder.HasMany(t => t.TranslationEntries)
            .WithOne(te => te.Translation)
            .HasForeignKey(te => te.TranslationId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}