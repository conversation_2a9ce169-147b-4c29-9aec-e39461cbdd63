using EMessa.DAL.Entities.Translations;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EMessa.DAL.Data.Configurations.Translations;

public class TranslationEntryConfiguration : IEntityTypeConfiguration<TranslationEntry>
{
    public void Configure(EntityTypeBuilder<TranslationEntry> builder)
    {
        builder.HasKey(te => te.Id);
        builder.Property(te => te.LanguageCode).IsRequired().HasMaxLength(3);
        builder.Property(te => te.TranslatedText).HasMaxLength(500);
        builder.Property(te => te.Translate).HasDefaultValue(true);
        builder.Property(te => te.Verify).HasDefaultValue(true);

        builder.HasIndex(te => te.LanguageCode);
        // Unikalny indeks na TranslationId + LanguageCode (zapobiega duplikatom tłumaczeń)
        builder.HasIndex(te => new { te.TranslationId, te.LanguageCode }).IsUnique();
    }
}