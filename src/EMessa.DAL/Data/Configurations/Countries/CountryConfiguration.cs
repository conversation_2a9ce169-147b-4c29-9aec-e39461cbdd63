using EMessa.DAL.Entities.Countries;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EMessa.DAL.Data.Configurations.Countries;

public class CountryConfiguration : IEntityTypeConfiguration<Country>
{
    public void Configure(EntityTypeBuilder<Country> builder)
    {
        builder.Property(e => e.Code)
            .IsRequired()
            .HasMaxLength(60);

        builder.Property(e => e.Name)
            .IsRequired()
            .HasMaxLength(120);

        builder.Property(e => e.LangCode)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(e => e.VatCode)
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(x => x.CurrencyCode)
            .IsRequired()
            .HasMaxLength(3)
            .HasDefaultValue("EUR");

        builder.HasIndex(e => e.Code, "CodeIndex").IsUnique();
        builder.HasIndex(e => e.Name, "NameIndex").IsUnique();

        builder.ToTable("Countries");
    }
}
