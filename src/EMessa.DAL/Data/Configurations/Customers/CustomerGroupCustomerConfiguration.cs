using EMessa.DAL.Entities.Customers;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EMessa.DAL.Data.Configurations.Customers;

public class CustomerGroupCustomerConfiguration : IEntityTypeConfiguration<CustomerGroupCustomer>
{
    public void Configure(EntityTypeBuilder<CustomerGroupCustomer> builder)
    {
        builder.ToTable("CustomersGroupCustomers");

        builder.<PERSON><PERSON><PERSON>(sc => sc.Id);

        builder.HasIndex(sc => new { sc.CustomerGroupId, sc.CustomerId }).IsUnique();

        builder.HasOne(sc => sc.CustomerGroup)
            .WithMany(s => s.CustomersGroupCustomers)
            .HasForeignKey(sc => sc.CustomerGroupId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(sc => sc.Customer)
            .WithMany(c => c.CustomersGroupCustomers)
            .HasForeignKey(sc => sc.CustomerId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
