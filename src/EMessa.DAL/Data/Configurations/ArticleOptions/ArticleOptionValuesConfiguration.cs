using EMessa.DAL.Entities.ArticleOptions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EMessa.DAL.Data.Configurations.ArticleOptions
{
    public class ArticleOptionValuesConfiguration : IEntityTypeConfiguration<ArticleOptionValue>
    {
        public void Configure(EntityTypeBuilder<ArticleOptionValue> builder)
        {
            builder.Property(x => x.WeightFactor).HasPrecision(16, 3);
            builder.Property(x => x.IsDefault);
            builder.Property(x => x.EmbossZoneAddition).HasPrecision(16,3);

            builder.HasOne(x=>x.OptionValue).WithMany(x=>x.ArticleOptionValues).HasForeignKey(x=>x.OptionValueId).OnDelete(DeleteBehavior.Restrict);
        }
    }
}
