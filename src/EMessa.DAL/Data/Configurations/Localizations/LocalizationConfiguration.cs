using EMessa.DAL.Entities.Localizations;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EMessa.DAL.Data.Configurations.Localizations;

public class LocalizationConfiguration : IEntityTypeConfiguration<Localization>
{
    public void Configure(EntityTypeBuilder<Localization> builder)
    {
        builder.Property(x => x.OrderId).IsRequired();
        builder.Property(x => x.Type).IsRequired();
        builder.Property(x => x.Address).IsRequired().HasMaxLength(500);
        builder.Property(x => x.City).IsRequired().HasMaxLength(100);
        builder.Property(x => x.PostCode).IsRequired().HasMaxLength(20);

        builder.HasIndex(x => x.OrderId);

        builder.ToTable("Localizations");
    }
}