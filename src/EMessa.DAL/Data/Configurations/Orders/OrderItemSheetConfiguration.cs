using EMessa.DAL.Entities.Orders;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;


namespace EMessa.DAL.Data.Configurations.Orders;

public class OrderItemSheetConfiguration : IEntityTypeConfiguration<OrderItemSheet>
{
    public void Configure(EntityTypeBuilder<OrderItemSheet> builder)
    {
        builder.Property(x => x.Width).HasPrecision(18, 3).IsRequired();
        builder.Property(x => x.Length).HasPrecision(18, 3).IsRequired();

        builder.Property(x => x.RequestedOrderItemSheetId).IsRequired();
        builder.HasIndex(x => x.RequestedOrderItemSheetId); // Indexing for foreign key to improve query performance
        builder.HasOne(x => x.RequestedOrderItemSheet)
            .WithMany(x => x.OrderItemSheets)
            .HasForeignKey(x => x.RequestedOrderItemSheetId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.ToTable("OrderItemSheets");
    }
}