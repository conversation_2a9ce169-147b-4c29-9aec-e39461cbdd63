using EMessa.DAL.Entities.Orders;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EMessa.DAL.Data.Configurations.Orders;

public class LockOrderConfiguration : IEntityTypeConfiguration<LockOrder>
{
    public void Configure(EntityTypeBuilder<LockOrder> builder)
    {
        builder.ToTable("LockOrders");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.Id)
            .ValueGeneratedOnAdd();

        builder.Property(x => x.OrderId)
            .IsRequired();

        builder.Property(x => x.LockedByUserProfileId)
            .IsRequired();

        builder.Property(x => x.ExpiresAt)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");
        //.HasDefaultValueSql("DATEADD(minute, 10, GETUTCDATE())"); // np. 10 min od teraz. Jeszcze nie uzywane

        // Relacje
        builder.HasOne(x => x.Order)
            .WithMany()
            .HasForeignKey(x => x.OrderId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(x => x.LockedBy)
            .WithMany()
            .HasForeignKey(x => x.LockedByUserProfileId)
            .OnDelete(DeleteBehavior.Restrict);

        // INDEKSY BIZNESOWE
        // 1. Jeden order = jeden lock
        builder.HasIndex(x => x.OrderId)
            .IsUnique();

        // 2. Jeden user = jeden aktywny lock
        builder.HasIndex(x => x.LockedByUserProfileId)
            .IsUnique();

        // 3. Indeks dla wydajności zapytań
        builder.HasIndex(x => new { x.OrderId, LockedById = x.LockedByUserProfileId });

    }
}