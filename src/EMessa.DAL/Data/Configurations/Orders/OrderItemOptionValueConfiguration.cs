using EMessa.DAL.Entities.Orders;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;


namespace EMessa.DAL.Data.Configurations.Orders
{
    public class OrderItemOptionValueConfiguration : IEntityTypeConfiguration<OrderItemOptionValue>
    {
        public void Configure(EntityTypeBuilder<OrderItemOptionValue> builder)
        {
            builder.Property(x => x.Id)
                .UseIdentityColumn(1, 1);
            
            builder.Property(x => x.OrderItemId)
                .IsRequired();

            builder.HasOne(x => x.OrderItem)
                .WithMany(x => x.OptionValues)
                .HasForeignKey(x => x.OrderItemId);

            builder.HasOne(x => x.Option)
                .WithMany()
                .HasForeignKey(x=>x.OptionId);
            
            builder.HasOne(x => x.OptionValue)
                .WithMany()
                .HasForeign<PERSON>ey(x=>x.OptionValueId);

            builder.ToTable("OrderItemOptionValues");
        }
    }
}
