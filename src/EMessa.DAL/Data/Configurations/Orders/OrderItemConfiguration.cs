using EMessa.DAL.Entities.Orders;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EMessa.DAL.Data.Configurations.Orders;

public class OrderItemConfiguration : IEntityTypeConfiguration<OrderItem>
{
    public void Configure(EntityTypeBuilder<OrderItem> builder)
    {
        builder.Property(x => x.OrderId).IsRequired();
        builder.Property(x => x.Index).HasMaxLength(100).IsRequired();
        builder.Property(x => x.Comments).HasMaxLength(1024);

        builder.Property(x => x.BasePrice).IsRequired().HasPrecision(18, 2);
        builder.Property(x => x.FinalPrice).IsRequired().HasPrecision(18, 2);
        builder.Property(x => x.CurrencyCode).IsRequired().HasMaxLength(3);

        builder.Property(x => x.ArticleId).IsRequired();

        builder.Property(x => x.Quantity).HasDefaultValue(1).HasPrecision(18, 3).IsRequired();
        builder.Property(x => x.Weight).HasPrecision(18, 3).IsRequired();

        builder.Property(x => x.SumM2).HasPrecision(18, 3).IsRequired();
        builder.Property(x => x.SumMb).HasPrecision(18, 3).IsRequired();
        builder.Property(x => x.SumQuantity).IsRequired();

        builder.Property(x => x.DraftEditable).HasDefaultValue(false);
        builder.Property(x => x.DraftOriginalFileName).HasMaxLength(255);
        builder.Property(x => x.DraftHashedFileName).HasMaxLength(255);
        builder.Property(x => x.DraftDrawingSource);
        
        builder.Property(x => x.SelectedForOrder).HasDefaultValue(true);

        builder.HasOne(x => x.Order)
                .WithMany(x => x.OrderItems)
                .HasForeignKey(x => x.OrderId);

        builder.HasOne(x => x.Sale)
            .WithMany(x => x.OrderItems)
            .HasForeignKey(x => x.SaleId);

        builder.ToTable("OrderItems");
    }
}