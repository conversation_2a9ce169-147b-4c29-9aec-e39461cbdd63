using EMessa.DAL.Entities.Orders;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EMessa.DAL.Data.Configurations.Orders;

public class OrderConfiguration : IEntityTypeConfiguration<Order>
{
    public void Configure(EntityTypeBuilder<Order> builder)
    {
        builder.Property(x => x.No).IsRequired().HasMaxLength(100);
        builder.Property(x => x.MessaNo);
        builder.Property(x => x.CustomerNo).HasMaxLength(100);

        builder.Property(x => x.CreatedDate).IsRequired();
        builder.Property(x => x.CustomerId).IsRequired();
        builder.Property(x => x.CreatedById).IsRequired();
        builder.Property(x => x.CustomerLocalizationId).IsRequired();
        builder.Property(x => x.BranchId).IsRequired();
        builder.Property(x => x.DifferentDeliveryLocalization).HasDefaultValue(false).IsRequired();

        builder.Property(x => x.Type).IsRequired();
        builder.Property(x => x.Status).IsRequired();
        builder.Property(x => x.IsEdited).HasDefaultValue(false).IsRequired();
        builder.Property(x => x.IsDeleted).HasDefaultValue(false).IsRequired();
        builder.Property(x => x.Comments).IsRequired();

        builder.HasOne(x => x.CreatedBy).WithMany()
            .HasForeignKey(x => x.CreatedById).IsRequired().OnDelete(DeleteBehavior.NoAction);

        builder.HasOne(x => x.EditedBy).WithMany()
            .HasForeignKey(x => x.EditedById).OnDelete(DeleteBehavior.NoAction);

        builder.HasOne(x => x.DeletedBy).WithMany()
            .HasForeignKey(x => x.DeletedById).OnDelete(DeleteBehavior.NoAction);

        builder.HasOne(x => x.Customer).WithMany(x => x.Orders)
            .HasForeignKey(x => x.CustomerId).IsRequired().OnDelete(DeleteBehavior.NoAction);

        builder.HasOne(x => x.CustomerLocalization).WithMany(x => x.Orders)
            .HasForeignKey(x => x.CustomerLocalizationId).IsRequired().OnDelete(DeleteBehavior.NoAction);

        builder.HasOne(x => x.Branch).WithMany(x => x.Orders)
            .HasForeignKey(x => x.BranchId).IsRequired().OnDelete(DeleteBehavior.NoAction);

        builder.HasOne(x => x.Factory).WithMany()
            .HasForeignKey(x => x.FactoryId).OnDelete(DeleteBehavior.NoAction);

        builder.HasMany(x => x.OrderItems).WithOne(x => x.Order)
            .HasForeignKey(x => x.OrderId).OnDelete(DeleteBehavior.NoAction);
        
        // // DeliveryLocalizations (1:Many) - przez OrderId
        // builder.HasMany(x => x.DeliveryLocalizations).WithOne(x => x.Order)
        //     .HasForeignKey(x => x.OrderId)
        //     .OnDelete(DeleteBehavior.Cascade);
        // DeliveryLocalizations (1:Many) - przez OrderId
        builder.HasMany(order => order.Localizations)
            .WithOne(localization => localization.Order)
            .HasForeignKey(localization => localization.OrderId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.ToTable("Orders");
    }
}