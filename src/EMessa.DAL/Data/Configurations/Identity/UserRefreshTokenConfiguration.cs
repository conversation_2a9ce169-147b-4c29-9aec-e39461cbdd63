using EMessa.DAL.Entities.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EMessa.DAL.Data.Configurations.Identity
{
    public class UserRefreshTokenConfiguration : IEntityTypeConfiguration<UserRefreshToken>
    {
        public void Configure(EntityTypeBuilder<UserRefreshToken> builder)
        {
            builder.ToTable("UserRefreshTokens", "Identity");
            
            builder.HasKey(x => x.Id);
            
            builder.Property(x => x.RefreshToken)
                .IsRequired()
                .HasMaxLength(500);
                
            builder.Property(x => x.ExpiryDate)
                .IsRequired();
                
            builder.Property(x => x.CreatedDate)
                .IsRequired();
                
            builder.Property(x => x.IsRevoked)
                .HasDefaultValue(false);
                
            builder.Property(x => x.DeviceInfo)
                .HasMaxLength(100);
                
            builder.Property(x => x.IpAddress)
                .HasMaxLength(45);
            
            // Foreign key relationship
            builder.HasOne(x => x.User)
                .WithMany(x=>x.RefreshTokens)
                .HasForeignKey(x => x.ApplicationUserId)
                .OnDelete(DeleteBehavior.Cascade);
                
            // Indexes for better performance
            builder.HasIndex(x => x.RefreshToken)
                .IsUnique();
                
            builder.HasIndex(x => x.ApplicationUserId);
                
            builder.HasIndex(x => x.ExpiryDate);
        }
    }
} 