using EMessa.DAL.Entities.Branches;
using EMessa.DAL.Entities.Factories;
using EMessa.DAL.Entities.Identity;
using EMessa.DAL.Entities.Users;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using System.Reflection;
using EMessa.DAL.Entities.Configurations;
using EMessa.DAL.Entities.Countries;
using EMessa.DAL.Entities.Customers;
using EMessa.DAL.Entities.Notifications;
using EMessa.DAL.Entities.Pages;
using EMessa.DAL.Entities.Categories;
using EMessa.DAL.Entities.Articles;
using EMessa.DAL.Entities.FilterAttributes;
using EMessa.DAL.Entities.Options;
using EMessa.DAL.Entities.ArticleOptions;
using EMessa.DAL.Entities.ArticleValidators;
using EMessa.DAL.Entities.Messages;
using EMessa.DAL.Entities.Orders;
using EMessa.DAL.Entities.Translations;
using EMessa.DAL.Entities.Sales;
using EMessa.DAL.Entities.Localizations;

namespace EMessa.DAL.Data;

public class ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : IdentityDbContext<
    ApplicationUser, ApplicationRole, int,
    ApplicationUserClaim, ApplicationUserRole, ApplicationUserLogin,
    ApplicationRoleClaim, ApplicationUserToken>(options)
{
    public DbSet<ApplicationUserProfile> UserProfiles { get; set; }
    public DbSet<Factory> Factories { get; set; }
    public DbSet<Branch> Branches { get; set; }
    public DbSet<Country> Countries { get; set; }
    public DbSet<Translation> Translations { get; set; }
    public DbSet<TranslationEntry> TranslationEntries { get; set; }
    public DbSet<Configuration> Configurations { get; set; }
    public DbSet<Page> Pages { get; set; }
    public DbSet<PageTranslation> PageTranslations { get; set; }

    public DbSet<Notification> Notifications { get; set; }
    public DbSet<NotificationUser> NotificationUsers { get; set; }
    public DbSet<Customer> Customers { get; set; }
    public DbSet<CustomerUser> CustomerUsers { get; set; }
    public DbSet<CustomerLocalization> CustomersLocalizations { get; set; }
    public DbSet<UserCustomerLocalization> UserCustomerLocalizations { get; set; }
    public DbSet<CustomerGroup> CustomerGroups { get; set; }
    public DbSet<CustomerGroupCustomer> CustomersGroupCustomers { get; set; }
    public DbSet<UserBranch> UserBranches { get; set; }
    public DbSet<Category> Categories { get; set; }
    public DbSet<CategoryTranslation> CategoryTranslations { get; set; }
    public DbSet<FilterAttribute> FilterAttributes { get; set; }
    public DbSet<FilterAttributeTranslation> FilterAttributeTranslations { get; set; }
    public DbSet<FilterAttributeValue> FilterAttributeValues { get; set; }
    public DbSet<FilterAttributeValueTranslation> FilterAttributeValueTranslations { get; set; }

    public DbSet<Article> Articles { get; set; }
    public DbSet<ArticleTranslation> ArticleTranslations { get; set; }
    public DbSet<Option> Options { get; set; }
    public DbSet<OptionTranslation> OptionTranslations { get; set; }
    public DbSet<OptionValue> OptionValues { get; set; }
    public DbSet<OptionValueTranslation> OptionValueTranslations { get; set; }
    public DbSet<ArticleOption> ArticleOptions { get; set; }
    public DbSet<ArticleOptionValue> ArticleOptionValues { get; set; }
    public DbSet<ArticleUnit> ArticleUnits { get; set; }
    public DbSet<Message> Messages { get; set; }
    public DbSet<MessageTranslation> MessageTranslations { get; set; }
    public DbSet<Order> Orders { get; set; }
    public DbSet<OrderItem> OrderItems { get; set; }
    public DbSet<RequestedOrderItemSheet> RequestedOrderItemSheets { get; set; }
    public DbSet<OrderItemSheet> OrderItemSheets { get; set; }
    public DbSet<OrderItemOptionValue> OrderItemOptionValues { get; set; }
    public DbSet<ArticleOptionValuesRestriction> ArticleOptionValuesRestrictions { get; set; }
    public DbSet<ArticleValidator> ArticleValidators { get; set; }
    public DbSet<ArticleValidatorCondition> ArticleValidatorConditions { get; set; }
    public DbSet<ArticleValidatorConditionTranslation> ArticleValidatorConditionTranslations { get; set; }
    public DbSet<LockOrder> LockOrders { get; set; }

    public DbSet<Sale> Sales { get; set; }
    public DbSet<SaleTranslation> SaleTranslations { get; set; }
    public DbSet<SalePrice> SalePrices { get; set; }
    public DbSet<SaleRoll> SaleRolls { get; set; }
    public DbSet<SaleImage> SaleImages { get; set; }
    public DbSet<SaleCustomer> SalesCustomers { get; set; }
    public DbSet<SaleArticle> SalesArticles { get; set; }
    public DbSet<UserRefreshToken> UserRefreshTokens { get; set; }
    public DbSet<Localization> Localizations { get; set; }

    //NOTE Poniższe wyłapuje "pazerne Include" które mogą powodować problemy z wydajnością. AxBxCxD...
    // Używaj świadomie .AsSplitQuery()
    // protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    // {
    //     optionsBuilder.ConfigureWarnings(warnings => 
    //         warnings.Throw(RelationalEventId.MultipleCollectionIncludeWarning));
    // }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = new())
    {
        var result = await base.SaveChangesAsync(cancellationToken);
        ChangeTracker.Clear();
        return result;
    }
}