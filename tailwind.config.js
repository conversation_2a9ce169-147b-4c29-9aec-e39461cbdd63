const plugin = require("tailwindcss/plugin");
const colors = require("tailwindcss/colors");

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
      "./src/**/*.{razor,cshtml,html}", 
    "./src/**/*.js", 
    "./src/**/*.cs"
  ],
  theme: {
    screens: {
      sm: '640px',// tw 640px, emessa 640
      md: '1008px',// tw 768px emessa 1008
      lg: '1600px',// tw 1024px emessa 1600
      xl: '1920px', // tw 1280px emessa 
      '2xl': '2560px'// tw 1536px emessa 
    },
    extend: {
      colors: {},
    },
  },
  plugins: [
    plugin(({ addBase }) => {
      addBase({
        ":root": {
          "--color-emerald-300": colors.emerald['300'],
          "--color-emerald-400": colors.emerald['400'],
        },
      });
    }),
    plugin(({ addUtilities }) => {
      // https://v3.tailwindcss.com/docs/plugins#overview
      addUtilities({});
    }),
  ],
  safelist: [
    "bg-amber-200",
    "bg-amber-500",
    "bg-blue-200",
    "bg-blue-500",
    "bg-blue-600",
    "bg-cyan-200",
    "bg-emerald-200",
    "bg-emerald-600",
    "bg-fuchsia-200",
    "bg-gray-200",
    "bg-gray-600",
    "bg-green-200",
    "bg-green-600",
    "bg-indigo-200",
    "bg-indigo-600",
    "bg-lime-200",
    "bg-lime-600",
    "bg-neutral-200",
    "bg-orange-200",
    "bg-orange-500",
    "bg-pink-200",
    "bg-purple-200",
    "bg-purple-600",
    "bg-red-200",
    "bg-red-500",
    "bg-rose-200",
    "bg-sky-200",
    "bg-slate-200",
    "bg-slate-500",
    "bg-stone-200",
    "bg-teal-200",
    "bg-teal-400",
    "bg-teal-500",
    "bg-teal-700",
    "bg-cyan-600",
    "bg-violet-200",
    "bg-zinc-200",
    "bg-zinc-500",
    "bg-zinc-600",
  ],
};
