#!/bin/bash

echo ">> Tworzę nową migrację"

# Usage: ./add-migration.sh [nazwa_migracji]

# Jeśli podano argument (nazwę migracji) jako parametr
if [ $# -eq 1 ]; then
    MIGRATION_NAME=$1
else
    # Jeśli nie podano argumentu, pytamy o nazwę
    echo ">> Podaj nazwę migracji:"
    read MIGRATION_NAME
fi

# Sprawdzamy czy nazwa nie jest pusta
if [ -z "$MIGRATION_NAME" ]; then
    echo ">> Nazwa migracji nie może być pusta!"
    exit 1
fi

# Wykonujemy migrację
echo ">> Tworzenie migracji o nazwie: $MIGRATION_NAME"
dotnet ef migrations add $MIGRATION_NAME -p src/EMessa.DAL -s src/EMessa.Web
exit_code=$?
echo ">> Kod wyjścia: $exit_code"

# Sprawdzamy czy komenda się powiodła
if [ $exit_code -eq 0 ]; then
    echo ">> Migracja została utworzona pomyślnie!"
    
    # Możesz dodać automatyczne wykonanie update - odkomentuj poniższe linie jeśli chcesz
    # echo "Wykonywanie update bazy danych..."
    # dotnet ef database update -p src/EMessa.DAL -s src/EMessa.Web
else
    echo ">> Wystąpił błąd podczas tworzenia migracji!"
    dotnet build --nologo --verbosity quiet --property WarningLevel=0
fi