name: Deploy (develop) (SSH)

on:
  workflow_dispatch:  # zachowuje możliwość ręcznego uruchomienia
  push:
    branches:
      - develop

jobs:
  #  test:
  #    uses: ./.github/workflows/test.yml  # Wywołuje test.yml
  #    with:
  #      environment: production
  
  deploy:
    runs-on: ubuntu-latest
    env:
      DEPLOY_FOLDER: ${{ vars.DEPLOY_FOLDER }}
      PUBLISH_FOLDER: ${{ vars.PUBLISH_FOLDER }}
      PUBLISH_BACKUP_FOLDER: ${{ vars.PUBLISH_BACKUP_FOLDER }}
      APP_POOL_NAME: ${{ vars.APP_POOL_NAME }}
      # App settings
      DATABASE_CONNECTION_STRING: ${{ secrets.DATABASE_CONNECTION_STRING }}
      SYNCFUSION_LICENSE_KEY: ${{ secrets.SYNCFUSION_LICENSE_KEY }}
      ADMIN_EMAIL: ${{ secrets.ADMIN_EMAIL }}
      ADMIN_PASSWORD: ${{ secrets.ADMIN_PASSWORD }}
      EMAIL_FROM_ADDRESS: ${{ vars.EMAIL_FROM_ADDRESS }}
      SMTP_SERVER: ${{ vars.SMTP_SERVER }}
      SMTP_PORT: ${{ vars.SMTP_PORT }} # nie uzywane
      SMTP_USERNAME: ${{ vars.SMTP_USERNAME }}
      SMTP_PASSWORD: ${{ secrets.SMTP_PASSWORD }}
      AZURE_TRANSLATION_API_KEY1: ${{ secrets.AZURE_TRANSLATION_API_KEY1 }}
      AZURE_TRANSLATION_API_KEY2: ${{ secrets.AZURE_TRANSLATION_API_KEY2 }}
      GUS_API_USER_KEY: ${{ secrets.GUS_API_USER_KEY }}
      GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
      ROLLS_API_BASE_ADDRESS: ${{ vars.ROLLS_API_BASE_ADDRESS }}
      MESSA_API_BASE_ADDRESS: ${{ vars.MESSA_API_BASE_ADDRESS }}
      OPTIMA_API_BASE_ADDRESS: ${{ vars.OPTIMA_API_BASE_ADDRESS }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        
      - name: Extract PR info
        run: |
          COMMIT_MSG="${{ github.event.head_commit.message }}"
          echo "Commit message: $COMMIT_MSG"
          
          # Wydobywanie PR number
          if [[ $COMMIT_MSG =~ "Merge pull request #"([0-9]+) ]]; then
            PR_NUMBER="${BASH_REMATCH[1]}"
            echo "PR Number: $PR_NUMBER"
            echo "PR_NUMBER=$PR_NUMBER" >> $GITHUB_ENV
          fi
          
          # Wydobywanie drugiej linii jako opis (bez względu na format)
          DESCRIPTION=$(echo "$COMMIT_MSG" | sed -n '2p')
          if [[ -n "$DESCRIPTION" ]]; then
            echo "Description: $DESCRIPTION"
            echo "DESCRIPTION=$DESCRIPTION" >> $GITHUB_ENV
          fi

      - name: Setup SSH key
        run: |
          if [ -z "${{ secrets.DEPLOY_KEY }}" ]; then
            echo "ERROR: DEPLOY_KEY secret is not set"
            exit 1
          fi
          mkdir -p /home/<USER>/.ssh
          echo "${{ secrets.DEPLOY_KEY }}" > /home/<USER>/.ssh/id_rsa
          chmod 600 /home/<USER>/.ssh/id_rsa

      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '8.0.x'

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2

      - name: Publish (dotnet)
        run: |
          dotnet publish ./src/EMessa.Web/EMessa.Web.csproj -r win-x64 -c BDP_Develop -f net8.0 -o publish --no-self-contained

      - name: Inject secrets
        run: |
          cd publish
          bun -v
          bun run inject-secrets.ts

      - name: GitHub revision
        run: |
          echo "BASH: $BASH_VERSION, PS: $($PSVersionTable.PSVersion)"
          REV=$(git rev-parse --short HEAD)
          DATE=$(git show -s --format=%ci HEAD)
          echo "REV|DATE: $REV|$DATE"
          echo "$REV|$DATE" > publish/git-rev-info.txt
          cat publish/git-rev-info.txt

      - name: Create archive with random name (7z)
        run: |
          echo "BASH: $BASH_VERSION, PS: $($PSVersionTable.PSVersion)"
          ARCHIVE_NAME="deploy-$(date +%Y%m%d-%H%M%S)-${{ github.run_number }}.7z"
          echo "ARCHIVE_NAME: $ARCHIVE_NAME"
          echo "ARCHIVE_NAME=$ARCHIVE_NAME" >> $GITHUB_ENV
          cd publish
          # mx0 - bez kompresji, mx5 - balans, mx9 - ultra
          7z a -mx2 "../$ARCHIVE_NAME" ./*

      - name: Prepare deploy folder on the server (ssh-action)
        uses: appleboy/ssh-action@v1
        with:
          host: ${{ secrets.DEPLOY_HOST }}
          port: ${{ secrets.DEPLOY_PORT }}
          username: ${{ secrets.DEPLOY_USERNAME }}
          key: ${{ secrets.DEPLOY_KEY }}
          script: |
            Write-Host "=== Preparing deploy folder ${{ env.DEPLOY_FOLDER }} ==="
            Remove-Item "${{ env.DEPLOY_FOLDER }}" -Recurse -Force -ErrorAction SilentlyContinue
            New-Item -ItemType Directory -Path "${{ env.DEPLOY_FOLDER }}" -Force

      - name: Copy archive to the server (scp)
        run: |
          echo "Copying ${{ env.ARCHIVE_NAME }} to ${{ secrets.DEPLOY_HOST }}:${{ env.DEPLOY_FOLDER }}/"
          scp -i /home/<USER>/.ssh/id_rsa -o StrictHostKeyChecking=no -v \
          -P ${{ secrets.DEPLOY_PORT }} \
          "${{ env.ARCHIVE_NAME }}" \
          ${{ secrets.DEPLOY_USERNAME }}@${{ secrets.DEPLOY_HOST }}:${{ env.DEPLOY_FOLDER }}/

          if [ $? -eq 0 ]; then
            echo "✅ File copied successfully"
          else
            echo "❌ SCP failed with exit code $?"
            exit 1
          fi

      - name: Extract, backup and cleanup on the server (ssh-action)
        uses: appleboy/ssh-action@v1
        with:
          host: ${{ secrets.DEPLOY_HOST }}
          port: ${{ secrets.DEPLOY_PORT }}
          username: ${{ secrets.DEPLOY_USERNAME }}
          key: ${{ secrets.DEPLOY_KEY }}
          script: |
            Write-Host "=== Deploying ${{ env.APP_POOL_NAME }} ==="
            echo "BASH: $BASH_VERSION, PS: $($PSVersionTable.PSVersion)"

            Write-Host "mkdir ${{ env.PUBLISH_FOLDER }} -Force"
            mkdir ${{ env.PUBLISH_FOLDER }} -Force

            # Zaimportuj moduł IIS
            Import-Module WebAdministration #-SkipEditionCheck
            $status = Get-WebAppPoolState -Name "${{ env.APP_POOL_NAME }}"
            Write-Host "${{ env.APP_POOL_NAME }}: $($status.Value)"

            # Zapisz backup.
            Write-Host "=== Creating backup of ${{ env.PUBLISH_FOLDER }} to ${{ env.PUBLISH_BACKUP_FOLDER }} ==="
            robocopy "${{ env.PUBLISH_FOLDER }}" "${{ env.PUBLISH_BACKUP_FOLDER }}" /E /PURGE /R:3 /W:1 /NFL /NDL /NJH /NJS /NC /NS /NP
            Write-Host "Backup completed"

            # Zatrzymaj app pool, 0-20s
            Write-Host "=== Stopping app pool ${{ env.APP_POOL_NAME }}... ==="
            Stop-WebAppPool -Name "${{ env.APP_POOL_NAME }}"

            # Poczekaj i sprawdź status
            Write-Host "=== Waiting for app pool to fully stop... ==="
            # Powinnismy dodac tutaj timeout?
            do {
              $status = Get-WebAppPoolState -Name "${{ env.APP_POOL_NAME }}"
              Write-Host "${{ env.APP_POOL_NAME }}: $($status.Value)"
              if ($status.Value -eq "Stopped") {
                break
              }
              Start-Sleep -Seconds 2
            } while ($true)   

            Write-Host "=== Clearing publish folder contents ==="
            Remove-Item "${{ env.PUBLISH_FOLDER }}\*" -Recurse -Force -ErrorAction SilentlyContinue

            Write-Host "=== Extract ${{ env.ARCHIVE_NAME }} to ${{ env.PUBLISH_FOLDER }} ==="
            7z x "${{ env.DEPLOY_FOLDER }}/${{ env.ARCHIVE_NAME }}" -o"${{ env.PUBLISH_FOLDER }}" -y

            # Uruchom z powrotem
            Start-WebAppPool -Name "${{ env.APP_POOL_NAME }}"
            Start-Sleep -Seconds 2  # krótka pauza na uruchomienie
            $status = Get-WebAppPoolState -Name "${{ env.APP_POOL_NAME }}"
            Write-Host "${{ env.APP_POOL_NAME }}: $($status.Value)"
            
            Write-Host "=== Remove (*.7z) ==="
            Remove-Item "${{ env.DEPLOY_FOLDER }}/*.7z" -Force

            Write-Host "=== Remove App_Offline.htm ==="
            Remove-Item "${{ env.PUBLISH_FOLDER }}/App_Offline.htm" -Force -ErrorAction SilentlyContinue

            Write-Host "=== Deploy completed ==="
      
      - name: Slack Notification
        uses: rtCamp/action-slack-notify@v2
        if: always()
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK_URL }}
          SLACK_CHANNEL: '#ci-cd'
          SLACK_COLOR: ${{ job.status == 'success' && 'good' || job.status == 'cancelled' && 'warning' || 'danger' }}
          SLACK_TITLE: ${{ job.status == 'success' && '✅ Deploy Succeeded' || job.status == 'cancelled' && '⚠️ Deploy Cancelled' || '❌ Deploy Failed' }}
          SLACK_MESSAGE: |
            Deploy dla ${{ github.repository }} zakończony ze statusem: ${{ job.status }}
            ${{ env.PR_NUMBER != '' && format('**PR #{0}:** {1}', env.PR_NUMBER, env.DESCRIPTION) || format('**Branch:** {0}', github.ref_name) }}
          SLACK_FOOTER: 'GitHub Actions'

# NOTE
# W Stormshield - firewall ustailismy geolokacje i geopass.
# Geolocation
# Geopass na niektóre państwa EUR i USA
#
# Write-Host "=== Creating backup archive ==="
# $backupFile = "${{ env.PUBLISH_BACKUP_FOLDER }}\backup-$(Get-Date -Format 'yyyyMMdd-HHmmss').7z"
# 7z a -mx5 "$backupFile" "${{ env.PUBLISH_FOLDER }}\*"