using EMessa.Base.Extensions;
using Microsoft.Extensions.DependencyInjection;
using OptApi.ArticleStates;
using Refit;
using System;
using System.Collections.Frozen;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OptApi.Tests.ArticleStates;

/// <summary>
/// Integration tests for CapiApiClient ArticleStates functionality
/// Note: These tests require a running API server
/// </summary>
public class CapiApiClientArticleStatesIntegrationTests : IDisposable
{
    private readonly ICapiApiClient _capiApiClient;
    private readonly ICapiArticleStateApi _articleStateApi;
    private readonly ServiceProvider _serviceProvider;
    private readonly List<string> Articles = [
        "WFPOW20.3009",
        "R/LC-ZRS100",
        "UPPDACH \"B\""
    ];

    private readonly List<string> Warehouses = ["RYB1", "WRO1"];

    public CapiApiClientArticleStatesIntegrationTests()
    {
        var services = new ServiceCollection();

        // Configuration for integration tests
        // You can change the URL to the actual API address
        var baseApiAddress = Environment.GetEnvironmentVariable("CAPI_API_BASE_URL") ?? "http://**************:5007/api"; // "http://*********:8010/api";

        // Register CapiApi services
        CapiApiRegisterer.Register(services, baseApiAddress);

        _serviceProvider = services.BuildServiceProvider();

        _capiApiClient = _serviceProvider.GetRequiredService<ICapiApiClient>();
    }

    [Fact(Skip = "Requires running API server")]
    public async Task GetArticleState_WithValidWarehouseAndArticleCode_ShouldReturnArticleState()
    {
        // Arrange
        var warehouseCode = Warehouses.First();
        var articleCode = Articles.First();

        // Act
        var resultList = await _capiApiClient.ArticleStateApi.GetArticelState(warehouseCode.ToBase64(), articleCode.ToBase64());

        // Assert
        Assert.NotNull(resultList);
        Assert.NotEmpty(resultList);
        ArticleStateDTO? result = resultList.FirstOrDefault();
        Assert.NotNull(result);
        Assert.Equal(articleCode, result.Code, true);
        Assert.Equal(warehouseCode, result.WarehouseCode, true);
        Assert.True(result.Id > 0);
        Assert.NotNull(result.Name);
        Assert.NotNull(result.Unit);
    }

    [Fact(Skip = "Requires running API server")]
    public async Task GetArticleState_WithInvalidWarehouseCode_ShouldThrowException()
    {
        // Arrange
        var warehouseCode = "INVALID";
        var articleCode = Articles.First();

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(async () =>
        {
            var resultList = await _capiApiClient.ArticleStateApi.GetArticelState(warehouseCode.ToBase64(), articleCode.ToBase64());
            Assert.Empty(resultList);
        });
    }

    [Fact(Skip = "Requires running API server")]
    public async Task GetArticleState_WithInvalidArticleCode_ShouldThrowException()
    {
        // Arrange
        var warehouseCode = Warehouses.First();
        var articleCode = "INVALID";

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(async () =>
        {
            var resultList = await _capiApiClient.ArticleStateApi.GetArticelState(warehouseCode.ToBase64(), articleCode.ToBase64());
            Assert.Empty(resultList);
        });
    }

    [Fact(Skip = "Requires running API server")]
    public async Task GetArticleState_WithEmptyWarehouseCode_ShouldThrowArgumentException()
    {
        // Arrange
        var warehouseCode = "";
        var articleCode = Articles.First();

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(async () =>
        {
            var resultList = await _capiApiClient.ArticleStateApi.GetArticelState(warehouseCode.ToBase64(), articleCode.ToBase64());
            Assert.Empty(resultList);
        });
    }

    [Fact(Skip = "Requires running API server")]
    public async Task GetArticleState_WithEmptyArticleCode_ShouldThrowArgumentException()
    {
        // Arrange
        var warehouseCode = Warehouses.First();
        var articleCode = "";

        // Act & Assert
        await Assert.ThrowsAsync<ApiException>(async () =>
        {
            var resultList = await _capiApiClient.ArticleStateApi.GetArticelState(warehouseCode.ToBase64(), articleCode.ToBase64());
            Assert.Empty(resultList);
        });
    }

    [Fact(Skip = "Requires running API server")]
    public async Task GetArticleStates_WithValidQuery_ShouldReturnArticleStatesList()
    {
        // Arrange
        var query = new GetWarehouseArticleStateQuery
        {
            WarehouseCodes = Warehouses,
            ArticleCodes = Articles,
            Date = DateTime.Now
        };

        // Act
        var result = await _capiApiClient.ArticleStateApi.GetArticelState(query);

        // Assert
        Assert.NotNull(result);
        Assert.IsType<List<ArticleStateDTO>>(result);
        Assert.All(result, articleState =>
        {
            Assert.Contains(articleState.WarehouseCode, query.WarehouseCodes);
            Assert.Contains(articleState.Code, query.ArticleCodes);
            Assert.True(articleState.Id > 0);
            Assert.NotNull(articleState.Name);
            Assert.NotNull(articleState.Unit);
        });
    }

    [Fact(Skip = "Requires running API server")]
    public async Task GetArticleStates_WithSingleWarehouseAndMultipleArticles_ShouldReturnFilteredResults()
    {
        // Arrange
        var query = new GetWarehouseArticleStateQuery
        {
            WarehouseCodes = [Warehouses.First()],
            ArticleCodes = Articles
        };

        // Act
        var result = await _capiApiClient.ArticleStateApi.GetArticelState(query);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, articleState =>
        {
            Assert.Equal(Warehouses.First(), articleState.WarehouseCode);
            Assert.Contains(articleState.Code, query.ArticleCodes);
        });
    }

    [Fact(Skip = "Requires running API server")]
    public async Task GetArticleStates_WithMultipleWarehousesAndSingleArticle_ShouldReturnFilteredResults()
    {
        // Arrange
        var query = new GetWarehouseArticleStateQuery
        {
            WarehouseCodes = Warehouses,
            ArticleCodes = [Articles.First()]
        };

        // Act
        var result = await _capiApiClient.ArticleStateApi.GetArticelState(query);

        // Assert
        Assert.NotNull(result);
        Assert.All(result, articleState =>
        {
            Assert.Equal("ART001", articleState.Code);
            Assert.Contains(articleState.WarehouseCode, query.WarehouseCodes);
        });
    }

    [Fact(Skip = "Requires running API server")]
    public async Task GetArticleStates_WithEmptyWarehouseCodes_ShouldReturnEmptyList()
    {
        // Arrange
        var query = new GetWarehouseArticleStateQuery
        {
            WarehouseCodes = new List<string>(),
            ArticleCodes = Articles
        };

        // Act
        var result = await _capiApiClient.ArticleStateApi.GetArticelState(query);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact(Skip = "Requires running API server")]
    public async Task GetArticleStates_WithEmptyArticleCodes_ShouldReturnEmptyList()
    {
        // Arrange
        var query = new GetWarehouseArticleStateQuery
        {
            WarehouseCodes = Warehouses,
            ArticleCodes = new List<string>()
        };

        // Act
        var result = await _capiApiClient.ArticleStateApi.GetArticelState(query);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }

    [Fact(Skip = "Requires running API server")]
    public async Task GetArticleStates_WithSpecificDate_ShouldReturnStateForThatDate()
    {
        // Arrange
        var specificDate = new DateTime(2025, 1, 15);
        var query = new GetWarehouseArticleStateQuery
        {
            WarehouseCodes = [Warehouses.First()],
            ArticleCodes = [Articles.First()],
            Date = specificDate
        };

        // Act
        var result = await _capiApiClient.ArticleStateApi.GetArticelState(query);

        // Assert
        Assert.NotNull(result);
        // Note: Actual date validation would depend on API implementation
        // This test assumes the API respects the date parameter
    }

    [Fact(Skip = "Requires running API server")]
    public async Task GetArticleStates_WithNullQuery_ShouldThrowArgumentNullException()
    {
        // Arrange
        GetWarehouseArticleStateQuery? query = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(async () =>
        {
            await _capiApiClient.ArticleStateApi.GetArticelState(query!);
        });
    }

    [Fact(Skip = "Requires running API server")]
    public async Task ArticleStateDto_ShouldHaveCorrectProperties()
    {
        // Arrange
        var warehouseCode = Warehouses.First();
        var articleCode = Articles.First();

        // Act
        var resultList = await _capiApiClient.ArticleStateApi.GetArticelState(warehouseCode.ToBase64(), articleCode.ToBase64());

        // Assert
        Assert.NotNull(resultList);
        Assert.NotEmpty(resultList);
        var result = resultList.FirstOrDefault();
        Assert.NotNull(result);
        // Test all properties are accessible
        Assert.True(result.Id >= 0);
        Assert.NotNull(result.Code);
        Assert.NotNull(result.Name);
        Assert.True(result.Quantity >= 0);
        Assert.NotNull(result.Unit);
        Assert.True(result.MinQuantity >= 0);
        Assert.True(result.MaxQuantity >= 0);
        Assert.True(result.QuantityReserved >= 0);
        Assert.True(result.QuantityNet >= 0);
        Assert.True(result.OrderQuantity >= 0);
        Assert.True(result.WarehouseId >= 0);
        Assert.NotNull(result.WarehouseCode);
    }

    [Fact(Skip = "Requires running API server")]
    public async Task GetArticleStates_PerformanceTest_ShouldCompleteWithinReasonableTime()
    {
        // Arrange
        var query = new GetWarehouseArticleStateQuery
        {
            WarehouseCodes = Warehouses,
            ArticleCodes = Articles
        };
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act
        var result = await _capiApiClient.ArticleStateApi.GetArticelState(query);
        stopwatch.Stop();

        // Assert
        Assert.NotNull(result);
        Assert.True(stopwatch.ElapsedMilliseconds < 5000, "API call should complete within 5 seconds");
    }

    [Fact(Skip = "Requires running API server")]
    public async Task GetArticleState_ConcurrentCalls_ShouldHandleMultipleSimultaneousRequests()
    {
        // Arrange
        var warehouseCode = Warehouses.First();
        var articleCodes = Articles;

        // Act
        var tasks = articleCodes.Select(async articleCode =>
        {
            var resultList = await _capiApiClient.ArticleStateApi.GetArticelState(warehouseCode.ToBase64(), articleCode.ToBase64());
            return resultList.FirstOrDefault();
        }).ToArray();

        var results = await Task.WhenAll(tasks);

        // Assert
        Assert.Equal(articleCodes.Count, results.Length);
        Assert.All(results, result => Assert.NotNull(result));
        for (int i = 0; i < results.Length; i++)
        {
            Assert.Equal(articleCodes[i], results[i].Code);
            Assert.Equal(warehouseCode, results[i].WarehouseCode);
        }
    }

    public void Dispose()
    {
        _serviceProvider?.Dispose();
    }
}

















