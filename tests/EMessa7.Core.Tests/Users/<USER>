using EMessa.Base.Constants;
using FluentAssertions;
using Xunit;

namespace EMessa7.Core.Tests.Users;

public class MaxRoleTests
{
    [Fact]
    public void MaxRole_Should_Return_Empty_When_List_Is_Empty()
    {
        // Arrange
        var roles = new List<string>();

        // Act
        var result = Role.MaxRole(roles);

        // Assert
        result.Should().Be("");
    }

    [Fact]
    public void MaxRole_Should_Prefer_Administrator_Over_All()
    {
        var roles = new List<string> { Role.Client, Role.Trade, Role.Administrator };

        var result = Role.MaxRole(roles);

        result.Should().Be(Role.Administrator);
    }

    [Fact]
    public void MaxRole_Should_Prefer_Production_Over_TradeManager()
    {
        var roles = new List<string> { Role.Production, Role.TradeManager };

        var result = Role.MaxRole(roles);

        result.Should().Be(Role.Production);
    }

    [Fact]
    public void MaxRole_Should_Prefer_TradeManager_Over_Trade()
    {
        var roles = new List<string> { Role.Trade, Role.TradeManager };

        var result = Role.MaxRole(roles);

        result.Should().Be(Role.TradeManager);
    }

    [Fact]
    public void MaxRole_Should_Prefer_Trade_Over_ClientManager()
    {
        var roles = new List<string> { Role.ClientManager, Role.Trade };

        var result = Role.MaxRole(roles);

        result.Should().Be(Role.Trade);
    }

    [Fact]
    public void MaxRole_Should_Prefer_ClientManager_Over_Client()
    {
        var roles = new List<string> { Role.Client, Role.ClientManager };

        var result = Role.MaxRole(roles);

        result.Should().Be(Role.ClientManager);
    }

    [Fact]
    public void MaxRole_Should_Return_Single_Role_When_Only_One()
    {
        var roles = new List<string> { Role.Trade };

        var result = Role.MaxRole(roles);

        result.Should().Be(Role.Trade);
    }

    [Fact]
    public void MaxRole_Should_Return_Empty_When_No_Known_Roles()
    {
        var roles = new List<string> { "Unknown", "AnotherUnknown" };

        var result = Role.MaxRole(roles);

        result.Should().Be("");
    }
}


