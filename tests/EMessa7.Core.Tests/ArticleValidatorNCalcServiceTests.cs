using EMessa.Base.Constants;
using EMessa.Base.Enums;
using EMessa.Core.Features.ArticleValidators.Mappings;
using EMessa.Core.Features.ArticleValidators.Models;
using EMessa.Core.Services;
using EMessa.DAL.Data;
using EMessa.DAL.Entities.Articles;
using EMessa.DAL.Entities.Options;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit.Abstractions;

namespace EMessa7.Core.Tests;

public class ArticleValidatorNCalcServiceTests : IAsyncLifetime
{
    private ArticleValidatorNCalcService _sut;
    private readonly Mock<IDbContextFactory<ApplicationDbContext>> _dbFactoryMock;
    private readonly ApplicationDbContext _dbContext;
    private readonly ArticleValidatorSettingsService _settings;
    private readonly ITestOutputHelper _testOutputHelper;
    private readonly ILogger<ArticleValidatorNCalcService> _logger;

    public ArticleValidatorNCalcServiceTests(ITestOutputHelper testOutputHelper)
    {
        _testOutputHelper = testOutputHelper;
        
        // Create a logger factory that writes to the test output
        var loggerFactory = LoggerFactory.Create(builder =>
        {
            builder.AddProvider(new XunitLoggerProvider(_testOutputHelper));
        });
    
        // Create the logger instance
        _logger = loggerFactory.CreateLogger<ArticleValidatorNCalcService>();

        var dbName = "TestDatabase_NCalc_" + Guid.NewGuid();
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: dbName)
            .Options;

        _dbContext = new ApplicationDbContext(options);

        _dbFactoryMock = new Mock<IDbContextFactory<ApplicationDbContext>>();
        _dbFactoryMock
            .Setup(x => x.CreateDbContextAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(() =>
            {
                var newOptions = new DbContextOptionsBuilder<ApplicationDbContext>()
                    .UseInMemoryDatabase(databaseName: dbName)
                    .Options;
                return new ApplicationDbContext(newOptions);
            });

        var option1 = new Option
        {
            Code = "option1",
            Name = "Test Option 1",
            No = 1,
            Description = "Test Description 1",
            InIndex = true,
            HideEditor = false,
            UseDefaultValue = true
        };
        var optionKol = new Option
        {
            Code = OptionCode.Color,
            Name = "Kolor",
            No = 2,
            Description = "Test Description KOL",
            InIndex = true,
            HideEditor = false,
            UseDefaultValue = true
        };

        _dbContext.Options.AddRange(new List<Option> { option1, optionKol });

        _dbContext.OptionValues.AddRange(new List<OptionValue>
        {
            new()
            {
                Option = option1,
                Code = "value1",
                No = 1,
                Value = "Value 1",
                OptionId = option1.Id,
                IsDefault = false,
                WeightFactor = 1.0m,
                EmbossZoneAddition = 0,
                ValueInfo = "Info 1",
            },
            new()
            {
                Option = option1,
                Code = "value2",
                No = 2,
                Value = "Value 2",
                OptionId = option1.Id,
                IsDefault = true,
                WeightFactor = 1.5m,
                EmbossZoneAddition = 10,
                ValueInfo = "Info 2",
            },
            new()
            {
                Code = "RAL8017",
                No = 2,
                Value = "RAL 8017",
                OptionId = optionKol.Id,
                Option = optionKol,
                IsDefault = true,
                WeightFactor = 1.5m,
                EmbossZoneAddition = 10,
                ValueInfo = "Info optionKOL",
            },
            new()
            {
                Code = "RAL8004",
                No = 2,
                Value = "RAL 8004",
                OptionId = optionKol.Id,
                Option = optionKol,
                IsDefault = true,
                WeightFactor = 1.5m,
                EmbossZoneAddition = 10,
                ValueInfo = "Info optionKOL",
            }
        });

        _dbContext.Articles.AddRange(new List<Article>
        {
            new()
            {
                Name = "Bona",
                Code = "BONA",
                Type = 0,
                IsActive = true,
                IsDeleted = false,
            },
            new()
            {
                Name = "T-8",
                Code = "T-8",
                Type = 0,
                IsActive = true,
                IsDeleted = false,
            },
            new()
            {
                Name = "T-20",
                Code = "T-20",
                Type = 0,
                IsActive = true,
                IsDeleted = false,
            }
        });

        _dbContext.SaveChanges();

        _settings = new ArticleValidatorSettingsService(_dbFactoryMock.Object);
        _sut = new ArticleValidatorNCalcService(_settings, _logger);
    }

    // wykonuje się przed każdym testem
    public async Task InitializeAsync()
    {
        await _settings.Load();
        _sut = new ArticleValidatorNCalcService(_settings, _logger);
    }

    // wykonuje się po każdym teście
    public Task DisposeAsync()
    {
        return Task.CompletedTask;
    }

    [Fact]
    public void DatabaseShouldHaveData()
    {
        var optionsCount = _dbContext.Options.Count();
        var optionValuesCount = _dbContext.OptionValues.Count();
        var articlesCount = _dbContext.Articles.Count();

        Assert.Equal(2, optionsCount); // powinno być 2 (option1 i optionKol)
        Assert.Equal(4, optionValuesCount); // powinno być 4 (2 dla option1 i 2 dla optionKol)
        Assert.Equal(3, articlesCount); // powinno być 3 (Bona, T-8, T-20)
    }

    [Fact]
    public void CheckExpression_ValidSimpleExpression_ReturnsTrue()
    {
        var expression = "2 + 2";
        var result = _sut.CheckCondition(expression);
        Assert.True(result);
    }

    [Fact]
    public void CheckExpression_ValidExpressionWithOrderItemArguments_ReturnsTrue()
    {
        var expression = "DLUG * SZER";
        var result = _sut.CheckCondition(expression);
        Assert.True(result);
    }

    [Fact]
    public void CheckExpression_ValidExpressionWithConstants_ReturnsTrue()
    {
        var expression = "OK + BLAD";
        var result = _sut.CheckCondition(expression);
        Assert.True(result);
    }

    [Theory]
    [InlineData("MAKS_DLUG > MIN_DLUG")]
    [InlineData("SUMA_M2 > 0")]
    [InlineData("WAGA * ILOSC")]
    public void CheckExpression_ValidExpressionsWithPredefinedVariables_ReturnsTrue(string expression)
    {
        var result = _sut.CheckCondition(expression);
        Assert.True(result);
    }

    [Theory]
    [InlineData("")]
    // [InlineData("2 +")]
    [InlineData("unknown_variable + 2")]
    [InlineData("2 + /")]
    [InlineData("if()")]
    public void CheckExpression_InvalidExpressions_ReturnsFalse(string expression)
    {
        var result = _sut.CheckCondition(expression);
        Assert.False(result);
    }

    [Fact]
    public void CheckExpression_ComplexValidExpression_ReturnsTrue()
    {
        var expression = "if(ILOSC * WAGA > 100, OK, POTW)";
        var result = _sut.CheckCondition(expression);
        Assert.True(result);
    }

    [Fact]
    public void CheckExpression_ValidExpressionWithErrorCondition_ReturnsTrue()
    {
        var expression = "if(ILOSC * WAGA > 100, OK, BLAD)";
        var result = _sut.CheckCondition(expression);
        Assert.True(result);
    }

    [Fact]
    public void Load_WhenCalled_PopulatesOptionValuesAndArguments()
    {
        Assert.Contains("option1", _settings.OptionCodeArguments);
        Assert.Contains("KOL", _settings.OptionCodeArguments);
        Assert.NotEmpty(_settings.OptionValueCodeConstants);
    }

    [Theory]
    [InlineData("test.123", "test_123")]
    [InlineData("test,test", "test_test")]
    [InlineData("test test", "test_test")]
    [InlineData("test+test", "test_test")]
    [InlineData("test-test", "test_test")]
    public void Sanitize_ReplacesSpecialCharacters_WithUnderscore(string input, string expected)
    {
        var result = ArticleValidatorSettingsService.Sanitize(input);
        Assert.Equal(expected, result);
    }

    [Fact]
    public void Sanitize_ListInput_RemovesDuplicatesAfterSanitization()
    {
        var input = new List<string> { "test.123", "test,123", "test 123" };
        var result = ArticleValidatorSettingsService.Sanitize(input);
        Assert.Single(result);
        Assert.Equal("test_123", result.First());
    }

    [Fact]
    public void CheckExpression_ValidProductCodeAndColorCondition_ReturnsTrue()
    {
        var result = _sut.CheckCondition("if(PROD_KOD == T_8 && KOL==KOL_RAL8017, 0, 1)");

        Assert.True(result);
    }

    [Theory]
    [InlineData("if(PROD_KOD == T_8, OK, BLAD)", false, false, "")] // OK
    [InlineData("if(PROD_KOD == T_20, OK, BLAD)", true, false, "ErrorMessage")] // BLAD
    [InlineData("if(PROD_KOD == T_20, OK, POTW)", false, true, "ConfirmMessage")] // POTW
    public void Validate_ArticleCodeWithDifferentConditions_ReturnsExpectedValidationState(
        string condition,
        bool hasErrors,
        bool hasConfirmations,
        string validationMessage
    )
    {
        // Przygotowanie danych testowych
        var sheet = new SheetValidatorData
        {
            ArticleCode = "T-8", // PROD_KOD
            Sheet = new ArticleValidatorSheet(12, 1, 3),
        };

        // Przygotowanie walidatorów
        var validators = new List<ArticleValidatorConditionMap>
        {
            new()
            {
                Condition = condition,
                ErrorMessage = "ErrorMessage",
                ConfirmMessage = "ConfirmMessage",
                TranslatedErrorMessage = "ErrorMessage",
                TranslatedConfirmMessage = "ConfirmMessage"
            },
        };

        // Wykonanie testu
        var result = _sut.Validate(sheet, validators);

        // Asercje
        Assert.Equal(hasErrors, result.HasErrors);
        Assert.Equal(hasConfirmations, result.HasConfirmations);
        if (hasErrors)
        {
            Assert.Contains(validationMessage, result.ErrorMessages);
            Assert.Empty(result.ConfirmationMessages);
        }
        else if (hasConfirmations)
        {
            Assert.Contains(validationMessage, result.ConfirmationMessages);
            Assert.Empty(result.ErrorMessages);
        }
        else
        {
            Assert.Empty(result.ErrorMessages);
            Assert.Empty(result.ConfirmationMessages);
        }
    }

    [Theory]
    [InlineData("if(DLUG < 20, OK, BLAD)", 12, false)]
    [InlineData("if(DLUG < 20, OK, BLAD)", 20, true)]
    public void Validate_Sheet_LengthCondition_ReturnsExpectedValidationState(
        string condition,
        decimal length,
        bool hasErrors
    )
    {
        var sheet = new SheetValidatorData
        {
            ArticleCode = "T-8",
            Sheet = new ArticleValidatorSheet { Length = length, Width = 1, Quantity = 20 },
        };

        var validators = new List<ArticleValidatorConditionMap>
        {
            new()
            {
                Condition = condition,
                ErrorMessage = "Długość musi być mniejsza od 20m",
                TranslatedErrorMessage = "Długość musi być mniejsza od 20m",
                IsActive = true,
                ValidatorType = ArticleValidatorType.Sheet,
            }
        };

        var result = _sut.Validate(sheet, validators);

        Assert.Equal(hasErrors, result.HasErrors);
        if (hasErrors)
        {
            Assert.Contains("Długość musi być mniejsza od 20m", result.ErrorMessages);
        }
        else
        {
            Assert.Empty(result.ErrorMessages);
        }

        Assert.Empty(result.ConfirmationMessages);
    }

    [Theory]
    [InlineData("if(WAGA < 200, OK, BLAD)", 120, false)]
    [InlineData("if(WAGA < 200, OK, BLAD)", 200, true)]
    public void Validate_Article_LengthCondition_ReturnsExpectedValidationState(
        string condition,
        decimal weight,
        bool hasErrors
    )
    {
        var article = new ArticleValidatorData
        {
            ArticleCode = "T-8",
            Sheets = [new ArticleValidatorSheet(2, 1, 20)],
            Weight = weight,
            SumM2 = 0,
            SumMb = 0,
            SumQuantity = 0,
        };

        var validators = new List<ArticleValidatorConditionMap>
        {
            new()
            {
                Condition = condition,
                ErrorMessage = "Waga musi być mniejsza od 200 kg",
                TranslatedErrorMessage = "Waga musi być mniejsza od 200 kg",
                IsActive = true,
                ValidatorType = ArticleValidatorType.Article,
            }
        };

        var result = _sut.Validate(article, validators);

        Assert.Equal(hasErrors, result.HasErrors);
        if (hasErrors)
        {
            Assert.Contains("Waga musi być mniejsza od 200 kg", result.ErrorMessages);
        }
        else
        {
            Assert.Empty(result.ErrorMessages);
        }

        Assert.Empty(result.ConfirmationMessages);
    }

    [Theory]
    [InlineData(6, true)]
    [InlineData(5, false)]
    public void Validate_QuantityMultipliedByTwoExceedsTen_ReturnsExpectedError(
        int quantity,
        bool hasError)
    {
        // Arrange
        var sheet = new SheetValidatorData
        {
            Sheet = new ArticleValidatorSheet { Quantity = quantity },
        };

        var validators = new List<ArticleValidatorConditionMap>
        {
            new()
            {
                Condition = "if((ILOSC * 2) > 10, BLAD, OK)",
                IsActive = true,
                ValidatorType = ArticleValidatorType.Article,
                ErrorMessage = "ErrorMessage",
                TranslatedErrorMessage = "ErrorMessage",
            }
        };

        // Act
        var result = _sut.Validate(sheet, validators);

        // Assert
        Assert.Equal(hasError, result.HasErrors);
        if (hasError)
        {
            Assert.Single(result.ErrorMessages);
            Assert.Equal("ErrorMessage", result.ErrorMessages[0]);
        }
        else
        {
            Assert.Empty(result.ErrorMessages);
        }
    }

    internal void Dispose()
    {
        _dbContext.Database.EnsureDeleted();
        _dbContext.Dispose();
    }
}