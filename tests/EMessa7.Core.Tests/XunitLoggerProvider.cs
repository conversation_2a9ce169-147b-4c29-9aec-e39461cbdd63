using Microsoft.Extensions.Logging;
using Xunit.Abstractions;

namespace EMessa7.Core.Tests;

public class XunitLoggerProvider(ITestOutputHelper testOutputHelper) : ILoggerProvider
{
    public ILogger CreateLogger(string categoryName)
    {
        return new XunitLogger(testOutputHelper, categoryName);
    }

    public void Dispose() { }
}

public class XunitLogger(ITestOutputHelper testOutputHelper, string categoryName) : ILogger
{
    public IDisposable BeginScope<TState>(TState state) => NullScope.Instance;

    public bool IsEnabled(LogLevel logLevel) => true;

    public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
    {
        try
        {
            testOutputHelper.WriteLine($"{logLevel}: {categoryName} - {formatter(state, exception)}");
            if (exception != null)
            {
                testOutputHelper.WriteLine(exception.ToString());
            }
        }
        catch
        {
            // Suppress exceptions from test output
        }
    }

    private class NullScope : IDisposable
    {
        public static NullScope Instance { get; } = new();
        public void Dispose() { }
    }
}