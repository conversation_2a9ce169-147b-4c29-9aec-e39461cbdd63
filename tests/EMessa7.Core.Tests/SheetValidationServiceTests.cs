using EMessa.Core.Features.OrderItems.Commands.ValidateSheet;
using EMessa.Core.Features.OrderItems.Services;
using EMessa.Core.Features.ShoppingCart.Models;
using EMessa.Core.Interfaces;
using EMessa.Core.Localizer;
using FluentAssertions;
using Moq;

namespace EMessa7.Core.Tests;

public class TestableSheetValidationService : SheetValidationService
{
    public GetProposedLengthsByModuleResult PublicGetProposedLengths(
        ArticleSheetValidationModel articleData,
        SheetValidationData sheet,
        bool addOneMoreSheet = false)
    {
        return GetProposedLengths(articleData, sheet, addOneMoreSheet: addOneMoreSheet);
    }

    public GetProposedLengthsByModuleResult PublicGetProposedLengthsByModule(
        ArticleSheetValidationModel articleData,
        SheetValidationData sheet,
        bool addOneMoreSheet = false)
    {
        return GetProposedLengthsByModule(articleData, sheet, addOneMoreSheet: addOneMoreSheet);
    }

    public List<ArticleSheetEditModel> PublicGetProposedSheets(
        List<decimal> proposedLengths,
        int quantity,
        decimal width)
    {
        return GetProposedSheets(proposedLengths, quantity, width);
    }
}

public class SheetValidationServiceTests
{
    private readonly TestableSheetValidationService _service = new();

    public SheetValidationServiceTests()
    {
        // W konstruktorze klasy testowej konfigurujemy mockowy lokalizer
        var mockLocalizer = new Mock<IStringLocalizer>();
        mockLocalizer
            .Setup(x => x.GetText(It.IsAny<string>()))
            .Returns<string>(x => x); // zwraca ten sam tekst, który dostał

        StringLocalizerExtension.Configure(mockLocalizer.Object);
    }

    private ArticleSheetValidationModel GetArticleDataT18()
    {
        return new ArticleSheetValidationModel
        {
            LengthEditable = true,
            IsSplitteable = true,
            DefaultLength = 0m,
            MinLength = 0.100m,
            MaxLength = 9.500m,
            SplitLength = 8.500m,
            OverlapLength = 0.200m,
            EmbossZoneLength = 0m,
            Module = 0.0m,
        };
    }

    private ArticleSheetValidationModel GetArticleDataBona()
    {
        return new ArticleSheetValidationModel
        {
            LengthEditable = true,
            IsSplitteable = true,
            DefaultLength = 0m,
            MinLength = 0.470m,
            MaxLength = 8.000m,
            SplitLength = 6.500m,
            OverlapLength = 120m,
            EmbossZoneLength = 0.030m,
            Module = 0.350m,
        };
    }

    private ArticleSheetValidationModel GetArticleDataAmalfi()
    {
        return new ArticleSheetValidationModel
        {
            LengthEditable = true,
            IsSplitteable = true,
            DefaultLength = 0m,
            MinLength = 0.380m,
            MaxLength = 8.000m,
            SplitLength = 6.000m,
            OverlapLength = 0.030m,
            EmbossZoneLength = 0.030m,
            Module = 0.350m,
        };
    }

    [Fact]
    public void GetProposedLengthsByAmalfi_ShouldReturn17_88()
    {
        // Arrange
        var articleData = GetArticleDataAmalfi();
        var sheet = new SheetValidationData { Length = 17.86m };
        const decimal additionalEmbossLength = 0.00m;
        articleData.Module = 0.35m;
        articleData.AdditionalEmbossLength = additionalEmbossLength;
        articleData.OverlapLength += additionalEmbossLength;
        articleData.EmbossZoneLength += additionalEmbossLength;

        // Act
        var result = _service.PublicGetProposedLengthsByModule(articleData, sheet);
        var (proposedLengths, totalLength, embossZoneError) = result;

        // Assert
        proposedLengths.Should().BeEquivalentTo([5.98m, 5.98m, 5.98m]);
        totalLength.Should().Be(17.88m);
        embossZoneError.Should().BeTrue();
    }

    [Fact]
    public void GetProposedLengthsByAmalfi_ShouldReturn17_93()
    {
        // Arrange
        var articleData = GetArticleDataAmalfi();
        var sheet = new SheetValidationData { Length = 17.86m };
        const decimal additionalEmbossLength = 0.05m;
        articleData.Module = 0.35m;
        articleData.AdditionalEmbossLength = additionalEmbossLength;
        articleData.OverlapLength += additionalEmbossLength;
        articleData.EmbossZoneLength += additionalEmbossLength;

        // Act
        var result = _service.PublicGetProposedLengthsByModule(articleData, sheet);
        var (proposedLengths, totalLength, embossZoneError) = result;

        // Assert
        proposedLengths.Should().BeEquivalentTo([4.63m, 4.63m, 4.63m, 4.28m]);
        totalLength.Should().Be(17.93m);
        embossZoneError.Should().BeTrue();
    }

    [Fact]
    public void GetProposedLengthsByAmalfi_ShouldReturn6_02()
    {
        // Arrange
        var articleData = GetArticleDataAmalfi();
        var sheet = new SheetValidationData { Length = 6.02m };
        const decimal additionalEmbossLength = 0.00m;
        articleData.Module = 0.35m;
        articleData.AdditionalEmbossLength = additionalEmbossLength;
        articleData.OverlapLength += additionalEmbossLength;
        articleData.EmbossZoneLength += additionalEmbossLength;

        // Act
        var result = _service.PublicGetProposedLengthsByModule(articleData, sheet);
        var (proposedLengths, totalLength, embossZoneError) = result;

        // Assert
        proposedLengths.Should().BeEquivalentTo([3.18m, 2.87m]);
        totalLength.Should().Be(6.02m);
        embossZoneError.Should().BeFalse();
    }

    [Fact]
    public void GetProposedLengthsByAmalfi_ShouldReturn6_03()
    {
        // Arrange
        var articleData = GetArticleDataAmalfi();
        var sheet = new SheetValidationData { Length = 6.02m };
        const decimal additionalEmbossLength = 0.05m;
        articleData.Module = 0.35m;
        articleData.AdditionalEmbossLength = additionalEmbossLength;
        articleData.OverlapLength += additionalEmbossLength;
        articleData.EmbossZoneLength += additionalEmbossLength;

        // Act
        var result = _service.PublicGetProposedLengthsByModule(articleData, sheet);
        var (proposedLengths, totalLength, embossZoneError) = result;

        // Assert
        proposedLengths.Should().BeEquivalentTo([3.23m, 2.88m]);
        totalLength.Should().Be(6.03m);
        embossZoneError.Should().BeTrue();
    }

    [Fact]
    public void GetProposedLengths_ShouldNeverExceedSplitLength()
    {
        // Arrange
        var articleData = new ArticleSheetValidationModel
        {
            SplitLength = 8.500m,
            OverlapLength = 0.200m
        };

        // Act & Assert
        for (var length = 0.001m; length <= 20m; length += 0.001m)
        {
            var sheet = new SheetValidationData { Length = length };
            var result = _service.PublicGetProposedLengths(articleData, sheet);
            var (proposedLengths, totalLength, embossZoneError) = result;

            // Sprawdzamy czy którakolwiek część nie przekracza dozwolonej długości
            var anyPartTooLong = proposedLengths.Any(partLength => partLength > articleData.SplitLength);
            if (anyPartTooLong)
            {
                Assert.Fail($"For length {length}, found parts exceeding split length: {string.Join(", ", result)}");
            }
        }
    }

    [Theory]
    [InlineData(0.350)]
    [InlineData(0.400)]
    public void GetProposedLengthsByModule_ShouldNeverExceedSplitLength(decimal module)
    {
        // Arrange
        var articleData = new ArticleSheetValidationModel
        {
            SplitLength = 6.500m,
            OverlapLength = 0.120m,
            Module = module,
        };

        // Act & Assert
        for (var length = 0.001m; length <= 20m; length += 0.001m)
        {
            var sheet = new SheetValidationData { Length = length };
            var result = _service.PublicGetProposedLengthsByModule(articleData, sheet);
            var (proposedLengths, totalLength, embossZoneError) = result;

            // Sprawdzamy czy którakolwiek część nie przekracza dozwolonej długości
            var anyPartTooLong = proposedLengths.Any(partLength => partLength > articleData.SplitLength);

            if (anyPartTooLong)
            {
                Assert.Fail($"For length {length}, found parts exceeding split length: {string.Join(", ", result)}");
            }
        }
    }

    [Fact]
    public void GetProposedSheets_WithEmptyList_ShouldReturnEmptyList()
    {
        // Arrange
        var proposedLengths = new List<decimal>();
        const int quantity = 1;
        const decimal width = 1.0m;

        // Act
        var result = _service.PublicGetProposedSheets(proposedLengths, quantity, width);

        // Assert
        result.Should().BeEmpty();
    }

    #region ValidateMinLength

    [Theory]
    [InlineData(false, 0.82, 0.82, true)] // not editable, lengths match - should pass
    [InlineData(false, 0.82, 1.0, false)] // not editable, lengths different - should fail
    [InlineData(true, 0.82, 0.82, true)] // editable - should pass regardless of length
    [InlineData(true, 0.82, 1.0, true)] // editable - should pass regardless of length
    public void ValidateSheet_LengthEditableValidation(
        bool lengthEditable,
        decimal defaultLength,
        decimal actualLength,
        bool shouldPass)
    {
        // Arrange
        var articleData = new ArticleSheetValidationModel
        {
            LengthEditable = lengthEditable,
            DefaultLength = defaultLength,
            MaxLength = 9.500m
        };

        var sheet = new SheetValidationData
        {
            Length = actualLength
        };

        // Act
        var results = _service.ValidateRequestedSheet(articleData, sheet);

        // Assert
        var lengthEditableError = results.FirstOrDefault(r =>
            r is { Type: ArticleSheetValidationResultType.Warning, Message: "Długość arkusza nie może być edytowana" });

        if (shouldPass)
        {
            lengthEditableError.Should().BeNull("No error should be present when validation passes");
        }
        else
        {
            lengthEditableError.Should().NotBeNull("Error should be present when validation fails");
        }
    }

    [Fact]
    public void ValidateSheet_WhenNotEditableAndLengthsDiffer_ShouldReturnSpecificError()
    {
        // Arrange
        var articleData = new ArticleSheetValidationModel
        {
            LengthEditable = false,
            DefaultLength = 10.0m
        };

        var sheet = new SheetValidationData
        {
            Length = 11.0m
        };

        // Act
        var results = _service.ValidateRequestedSheet(articleData, sheet);

        // Assert
        results.Should().ContainSingle(r =>
            r.Type == ArticleSheetValidationResultType.Warning &&
            r.Message == "Długość arkusza nie może być edytowana");
    }

    [Fact]
    public void ValidateSheet_WhenEditable_ShouldNotReturnLengthError()
    {
        // Arrange
        var articleData = new ArticleSheetValidationModel
        {
            LengthEditable = true,
            DefaultLength = 1.0m
        };

        var sheet = new SheetValidationData
        {
            Length = 1.5m // różna długość, ale edytowalna
        };

        // Act
        var results = _service.ValidateRequestedSheet(articleData, sheet);

        // Assert
        results.Should().NotContain(r =>
            r.Type == ArticleSheetValidationResultType.Error &&
            r.Message == "Długość arkusza nie może być edytowana");
    }

    #endregion

    #region ValidateAllowEditWidth

    [Theory]
    [InlineData(false, 0.82, 0.82, true)] // nie edytowalna, szerokości się zgadzają - powinno przejść
    [InlineData(false, 0.82, 1.0, false)] // nie edytowalna, różne szerokości - powinno zwrócić błąd
    [InlineData(true, 0.82, 0.82, true)] // edytowalna - powinno przejść niezależnie od szerokości
    [InlineData(true, 0.82, 1.0, true)] // edytowalna - powinno przejść niezależnie od szerokości
    public void ValidateSheet_WidthEditableValidation(
        bool widthEditable,
        decimal defaultWidth,
        decimal actualWidth,
        bool shouldPass)
    {
        // Arrange
        var articleData = new ArticleSheetValidationModel
        {
            WidthEditable = widthEditable,
            DefaultWidth = defaultWidth
        };

        var sheet = new SheetValidationData
        {
            Width = actualWidth
        };

        // Act
        var results = _service.ValidateRequestedSheet(articleData, sheet);

        // Assert
        var widthEditableError = results.FirstOrDefault(r =>
            r is { Type: ArticleSheetValidationResultType.Error, Message: "Szerokość arkusza nie może być edytowana" });

        if (shouldPass)
        {
            widthEditableError.Should().BeNull("No error should be present when validation passes");
        }
        else
        {
            widthEditableError.Should().NotBeNull("Error should be present when validation fails");
        }
    }

    [Fact]
    public void ValidateSheet_WhenNotEditableAndWidthsDiffer_ShouldReturnSpecificError()
    {
        // Arrange
        var articleData = new ArticleSheetValidationModel
        {
            WidthEditable = false,
            DefaultWidth = 10.0m
        };

        var sheet = new SheetValidationData
        {
            Width = 11.0m
        };

        // Act
        var results = _service.ValidateRequestedSheet(articleData, sheet);

        // Assert
        results.Should().ContainSingle(r =>
            r.Type == ArticleSheetValidationResultType.Error &&
            r.Message == "Szerokość arkusza nie może być edytowana");
    }

    [Fact]
    public void ValidateSheet_WhenWidthEditable_ShouldNotReturnWidthError()
    {
        // Arrange
        var articleData = new ArticleSheetValidationModel
        {
            WidthEditable = true,
            DefaultWidth = 1.0m
        };

        var sheet = new SheetValidationData
        {
            Width = 1.5m // różna szerokość, ale edytowalna
        };

        // Act
        var results = _service.ValidateRequestedSheet(articleData, sheet);

        // Assert
        results.Should().NotContain(r =>
            r.Type == ArticleSheetValidationResultType.Error &&
            r.Message == "Szerokość arkusza nie może być edytowana");
    }

    #endregion

    #region ValidateMinLength

    [Theory]
    [InlineData(false, 1.0, 0.5, 0.8, true)] // nie edytowalna - powinno przejść bez sprawdzania
    [InlineData(true, 1.0, 0.5, 0.8, false)] // za krótka długość (0.8 < 1.0)
    [InlineData(true, 1.0, 0.5, 1.0, true)] // minimalna długość (1.0 == 1.0)
    [InlineData(true, 1.0, 0.5, 1.2, true)] // prawidłowa długość (1.2 > 1.0)
    public void ValidateSheet_MinLengthValidation_WithoutModule(
        bool lengthEditable,
        decimal minLength,
        decimal overlapLength,
        decimal actualLength,
        bool shouldPass)
    {
        // Arrange
        var articleData = new ArticleSheetValidationModel
        {
            LengthEditable = lengthEditable,
            DefaultLength = actualLength,
            MinLength = minLength,
            MaxLength = 10m,
            Module = 0,
            OverlapLength = overlapLength
        };

        var sheet = new SheetValidationData
        {
            Length = actualLength
        };

        // Act
        var results = _service.ValidateRequestedSheet(articleData, sheet);

        // Assert
        var minLengthError = results.FirstOrDefault(r =>
            r.Type == ArticleSheetValidationResultType.Warning);

        if (shouldPass)
        {
            minLengthError.Should().BeNull("No error should be present when validation passes");
        }
        else
        {
            minLengthError.Should().NotBeNull("Error should be present when length is too short");
            minLengthError!.Message.Should().Be($"Minimalna długość arkusza to ({minLength.ToString("n3")}) m");
        }
    }

    [Theory]
    [InlineData(true, 0.5, 0.3, 0.7, 0.6, false)] // za krótka długość (0.6 < 0.8 [0.5 + 0.3])
    [InlineData(true, 0.5, 0.3, 0.7, 0.8, true)] // minimalna długość (0.8 == 0.8 [0.5 + 0.3])
    [InlineData(true, 0.5, 0.3, 0.7, 1.0, true)] // prawidłowa długość (1.0 > 0.8 [0.5 + 0.3])
    public void ValidateSheet_MinLengthValidation_WithModule(
        bool lengthEditable,
        decimal module,
        decimal overlapLength,
        decimal minLength,
        decimal actualLength,
        bool shouldPass)
    {
        // Arrange
        var articleData = new ArticleSheetValidationModel
        {
            LengthEditable = lengthEditable,
            Module = module, // HasModule będzie true bo Module > 0
            OverlapLength = overlapLength,
            MinLength = minLength,
            MaxLength = 10m
        };
        var sheet = new SheetValidationData
        {
            Length = actualLength
        };
        var expectedMinLength = module + overlapLength;

        // Act
        var results = _service.ValidateRequestedSheet(articleData, sheet);

        // Assert
        var minLengthError = results.FirstOrDefault(r =>
            r.Type == ArticleSheetValidationResultType.Warning);

        if (shouldPass)
        {
            minLengthError.Should().BeNull("No error should be present when validation passes");
        }
        else
        {
            minLengthError.Should().NotBeNull("Error should be present when length is too short");
            minLengthError!.Message.Should().Be($"Minimalna długość arkusza to ({expectedMinLength.ToString("n3")}) m");
        }
    }

    #endregion

    #region ValidateMaxLength

    [Theory]
    [InlineData(false, 10.0, 11.0, true)] // nie edytowalna - powinno przejść bez sprawdzania
    [InlineData(true, 10.0, 9.0, true)] // długość w normie (9.0 < 10.0)
    [InlineData(true, 10.0, 10.0, true)] // długość równa max (10.0 == 10.0)
    [InlineData(true, 10.0, 11.0, false)] // przekroczona długość, nie można dzielić
    public void ValidateSheet_MaxLengthValidation_NonSplittable(
        bool lengthEditable,
        decimal maxLength,
        decimal actualLength,
        bool shouldPass)
    {
        // Arrange
        var articleData = new ArticleSheetValidationModel
        {
            LengthEditable = lengthEditable,
            DefaultLength = actualLength,
            MinLength = 0.5m,
            MaxLength = maxLength,
            IsSplitteable = false
        };
        var sheet = new SheetValidationData
        {
            Length = actualLength
        };

        // Act
        var results = _service.ValidateRequestedSheet(articleData, sheet);

        // Assert
        var maxLengthError = results.FirstOrDefault(r =>
            r.Type == ArticleSheetValidationResultType.Warning &&
            r.Message == $"Maksymalna długość arkusza to ({maxLength.ToString("n3")}) m");

        if (shouldPass)
        {
            maxLengthError.Should().BeNull("No error should be present when validation passes");
        }
        else
        {
            maxLengthError.Should().NotBeNull("Error should be present when length exceeds maximum");
        }
    }

    [Theory]
    [InlineData(true, 10.0, 11.0)] // przekroczona długość, ale można dzielić
    public void ValidateSheet_MaxLengthValidation_Splittable(
        bool lengthEditable,
        decimal maxLength,
        decimal actualLength)
    {
        // Arrange
        var articleData = new ArticleSheetValidationModel
        {
            LengthEditable = lengthEditable,
            MaxLength = maxLength,
            IsSplitteable = true
        };

        var sheet = new SheetValidationData
        {
            Length = actualLength
        };

        // Act
        var results = _service.ValidateRequestedSheet(articleData, sheet);

        // Assert
        results.Should().NotContain(r =>
            r.Type == ArticleSheetValidationResultType.Error &&
            r.Message == $"Maksymalna długość arkusza to {maxLength:n3}");
    }

    [Fact]
    public void ValidateSheet_WhenNotEditable_ShouldNotValidateMaxLength()
    {
        // Arrange
        var articleData = new ArticleSheetValidationModel
        {
            LengthEditable = false,
            MaxLength = 10.0m,
            IsSplitteable = false
        };

        var sheet = new SheetValidationData
        {
            Length = 11.0m // za długi, ale nie edytowalny
        };

        // Act
        var results = _service.ValidateRequestedSheet(articleData, sheet);

        // Assert
        results.Should().NotContain(r =>
            r.Message == $"Maksymalna długość arkusza to {articleData.MaxLength.ToString("n3")}");
    }

    #endregion

    // TODO Missing tests
    // ValidateMaxTechnologyLength
    // ValidateSplitLength
    // ValidateEmbossLength
}