using EMessa.Base.Enums;
using EMessa.Core.Features.Sales.Queries.CommonResponses;
using EMessa.Core.Features.Sales.Queries.GetSaleRolls;
using FluentAssertions;

namespace EMessa7.Core.Tests.Sales.SaleWeightCalculator;

public class SaleWeightCalculatorBaseTests
{
    private class TestSaleWeightCalculator : SaleWeightCalculatorBase { }

    public static IEnumerable<object[]> AvailableWeightTestCases =>
        [
            // definedWeight, orderItems, rolls, expectedAvailableWeight

            [
                100m,
                new List<OrderItemResponseForSale>
                {
                    new() { OrderStatus = OrderStatus.Sent, OrderItemWeight = 30 },
                    new() { OrderStatus = OrderStatus.Sent, OrderItemWeight = 20 }
                },
                new List<SaleRollResponse>
                {
                    new() { SaleWeight = 80, CurrentWeight = 70 },
                    new() { SaleWeight = 10, CurrentWeight = 15 }
                },
                50m // min(100 - 50, 85) = 50
            ],

            [
                100m,
                new List<OrderItemResponseForSale>(), // brak zamówień
                new List<SaleRollResponse>
                {
                    new() { SaleWeight = 30, CurrentWeight = 30 }
                },
                30m // 100 - 0 = 100, min(100, 30)
            ],

            [
                0m,
                new List<OrderItemResponseForSale>
                {
                    new() { OrderStatus = OrderStatus.Sent, OrderItemWeight = 20 }
                },
                new List<SaleRollResponse>
                {
                    new() { SaleWeight = 50, CurrentWeight = 60 }
                },
                30m // 50 - 20 = 30, min(30, 50)
            ],

            [
                50m,
                new List<OrderItemResponseForSale>
                {
                    new() { OrderStatus = OrderStatus.Sent, OrderItemWeight = 60 }
                },
                new List<SaleRollResponse>
                {
                    new() { SaleWeight = 70, CurrentWeight = 80 }
                },
                -10m // 50 - 60 = -10, min(-10, 70)
            ],

            [
                100m,
                new List<OrderItemResponseForSale>
                {
                    new() { OrderStatus = OrderStatus.Rejected, OrderItemWeight = 100 },
                    new() { OrderStatus = OrderStatus.Unknown, OrderItemWeight = 100 },
                    new() { OrderStatus = OrderStatus.Sent, OrderItemWeight = 20 }
                },
                new List<SaleRollResponse>
                {
                    new() { SaleWeight = 40, CurrentWeight = 50 }
                },
                40m // 100 - 20 = 80, min(80, 40)
            ],

            [
                100m,
                new List<OrderItemResponseForSale>
                {
                    new() { OrderStatus = OrderStatus.Sent, OrderItemWeight = 20 }
                },
                new List<SaleRollResponse>
                {
                    new() { SaleWeight = 80, CurrentWeight = 30 }
                },
                30m // 100 - 20 = 80, min(80, 30)
            ],

            [
                100m,
                new List<OrderItemResponseForSale>(),
                new List<SaleRollResponse>(),
                0m // 100 - 0 = 100, min(100, 0)
            ],

            [
                0m,
                new List<OrderItemResponseForSale>(),
                new List<SaleRollResponse>(),
                0m // 0 - 0 = 0, min(0, 0)
            ],

            [
                140m,
                new List<OrderItemResponseForSale>(),
                new List<SaleRollResponse>
                {
                    new() { SaleWeight = 80, CurrentWeight = 30 },
                    new() { SaleWeight = 100, CurrentWeight = 100 }
                },
                130m // 140m - 0 = 140m, min(140m, 130)
            ],

            [
                0m,
                new List<OrderItemResponseForSale>(),
                new List<SaleRollResponse>
                {
                    new() { SaleWeight = 80, CurrentWeight = 30 },
                    new() { SaleWeight = 100, CurrentWeight = 100 }
                },
                130m // 180 - 0 = 180, min(180, 130)
            ],

            [
                100m,
                new List<OrderItemResponseForSale>
                {
                    new() { OrderStatus = OrderStatus.Rejected, OrderItemWeight = 100 },
                    new() { OrderStatus = OrderStatus.Unknown, OrderItemWeight = 100 }
                },
                new List<SaleRollResponse>
                {
                    new() { SaleWeight = 40, CurrentWeight = 50 }
                },
                40m // 100 - 0 = 100, min(100, 40)
            ],
        ];

    [Theory]
    [MemberData(nameof(AvailableWeightTestCases))]
    public void AvailableWeight_ShouldReturnExpectedValue_WhenVariousConditionsMatch(
        decimal definedWeight,
        List<OrderItemResponseForSale> orderItems,
        List<SaleRollResponse> rolls,
        decimal expectedAvailableWeight)
    {
        // Arrange
        var calculator = new TestSaleWeightCalculator
        {
            DefinedWeight = definedWeight,
            OrderItems = orderItems,
            Rolls = rolls
        };

        // Act
        var result = calculator.AvailableWeight;

        // Assert
        result.Should().Be(expectedAvailableWeight);
    }

    public static IEnumerable<object[]> AvailableLengthTestCases =>
        [
            // availableWeight, efficiency, rolls, expectedAvailableLength
            [100m, 10m, new List<SaleRollResponse>(), 10m], // brak kręgów, efficiency > 0
            [100m, 0m, new List<SaleRollResponse>(), 0m], // brak kręgów, efficiency = 0
            [0m, 10m, new List<SaleRollResponse>(), 0m], // brak kręgów, availableWeight = 0
            [100m, 10m, new List<SaleRollResponse> { new() { Weight = 10, CurrentWeight = 10, Efficiency = 5 } }, 20m], // 100 / 5 = 20
            [100m, 10m, new List<SaleRollResponse> { new() { Weight = 10, CurrentWeight = 10, Efficiency = 0 } }, 0m], // efficiency = 0 na kręgu
            [100m, 10m, new List<SaleRollResponse> { new() { Weight = 0, CurrentWeight = 10, Efficiency = 5 } }, 0m], // weight = 0 na kręgu
            [100m, 10m, new List<SaleRollResponse> { new() { Weight = 10, CurrentWeight = 0, Efficiency = 5 } }, 0m], // currentWeight = 0 na kręgu
            [100m, 10m, new List<SaleRollResponse> {
                new() { Weight = 10, CurrentWeight = 10, Efficiency = 5 },
                new() { Weight = 20, CurrentWeight = 20, Efficiency = 10 }
            }, 12m], // średnia ważona: (5*10+10*20)/30=8.33, 100/8.33=12, zaokrąglone do 12
            [100m, 10m, new List<SaleRollResponse> {
                new() { Weight = 10, CurrentWeight = 10, Efficiency = 5 },
                new() { Weight = 20, CurrentWeight = 20, Efficiency = 0 }
            }, 20m], // tylko pierwszy krąg się liczy
        ];

    [Theory]
    [MemberData(nameof(AvailableLengthTestCases))]
    public void AvailableLength_ShouldReturnExpectedValue_WhenVariousConditionsMatch(
        decimal availableWeight,
        decimal efficiency,
        List<SaleRollResponse> rolls,
        decimal expectedAvailableLength)
    {
        // Act
        var result = SaleWeightCalculatorBase.CalculateAvailableLength(availableWeight, efficiency, rolls);

        // Assert
        result.Should().Be(expectedAvailableLength);
    }
}
