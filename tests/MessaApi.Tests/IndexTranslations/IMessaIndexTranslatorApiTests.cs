using MessaApi.IndexTranslations;
using Moq;
using Refit;
using System.Net;
using System.Text.Json;

namespace MessaApi.Tests.IndexTranslations;

public class IMessaIndexTranslatorApiTests
{
    private readonly Mock<IMessaIndexTranslatorApi> _mockApi;
    private readonly IMessaIndexTranslatorApi _api;

    public IMessaIndexTranslatorApiTests()
    {
        _mockApi = new Mock<IMessaIndexTranslatorApi>();
        _api = _mockApi.Object;
    }

    [Fact]
    public async Task GetIndexTranslation_WithValidIndex_ShouldReturnTranslation()
    {
        // Arrange
        var indexBase64 = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes("test-index"));
        var expectedTranslation = new MessaIndexTranslation
        {
            Id = 1,
            eMessaIndex = "test-index",
            Akronim = "TEST"
        };

        _mockApi
            .Setup(x => x.GetIndexTranslation(indexBase64))
            .ReturnsAsync(expectedTranslation);

        // Act
        var result = await _api.GetIndexTranslation(indexBase64);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedTranslation.Id, result.Id);
        Assert.Equal(expectedTranslation.eMessaIndex, result.eMessaIndex);
        Assert.Equal(expectedTranslation.Akronim, result.Akronim);

        _mockApi.Verify(x => x.GetIndexTranslation(indexBase64), Times.Once);
    }

    [Fact]
    public async Task GetIndexTranslation_WithEmptyIndex_ShouldReturnTranslation()
    {
        // Arrange
        var indexBase64 = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(""));
        var expectedTranslation = new MessaIndexTranslation
        {
            Id = 2,
            eMessaIndex = "",
            Akronim = "EMPTY"
        };

        _mockApi
            .Setup(x => x.GetIndexTranslation(indexBase64))
            .ReturnsAsync(expectedTranslation);

        // Act
        var result = await _api.GetIndexTranslation(indexBase64);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedTranslation.Id, result.Id);
        Assert.Equal(expectedTranslation.eMessaIndex, result.eMessaIndex);
        Assert.Equal(expectedTranslation.Akronim, result.Akronim);

        _mockApi.Verify(x => x.GetIndexTranslation(indexBase64), Times.Once);
    }

    [Fact]
    public async Task GetIndexTranslation_WithSpecialCharacters_ShouldReturnTranslation()
    {
        // Arrange
        var specialIndex = "test-index-ąćęłńóśźż";
        var indexBase64 = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(specialIndex));
        var expectedTranslation = new MessaIndexTranslation
        {
            Id = 3,
            eMessaIndex = specialIndex,
            Akronim = "SPECIAL"
        };

        _mockApi
            .Setup(x => x.GetIndexTranslation(indexBase64))
            .ReturnsAsync(expectedTranslation);

        // Act
        var result = await _api.GetIndexTranslation(indexBase64);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedTranslation.Id, result.Id);
        Assert.Equal(expectedTranslation.eMessaIndex, result.eMessaIndex);
        Assert.Equal(expectedTranslation.Akronim, result.Akronim);

        _mockApi.Verify(x => x.GetIndexTranslation(indexBase64), Times.Once);
    }

    [Fact]
    public async Task GetIndexTranslation_WithLongIndex_ShouldReturnTranslation()
    {
        // Arrange
        var longIndex = new string('a', 1000);
        var indexBase64 = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(longIndex));
        var expectedTranslation = new MessaIndexTranslation
        {
            Id = 4,
            eMessaIndex = longIndex,
            Akronim = "LONG"
        };

        _mockApi
            .Setup(x => x.GetIndexTranslation(indexBase64))
            .ReturnsAsync(expectedTranslation);

        // Act
        var result = await _api.GetIndexTranslation(indexBase64);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedTranslation.Id, result.Id);
        Assert.Equal(expectedTranslation.eMessaIndex, result.eMessaIndex);
        Assert.Equal(expectedTranslation.Akronim, result.Akronim);

        _mockApi.Verify(x => x.GetIndexTranslation(indexBase64), Times.Once);
    }

    [Fact]
    public async Task GetIndexTranslation_WithNullIndex_ShouldThrowException()
    {
        // Arrange
        string? indexBase64 = null;

        _mockApi
            .Setup(x => x.GetIndexTranslation(indexBase64))
            .ThrowsAsync(new ArgumentNullException(nameof(indexBase64)));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<ArgumentNullException>(
            () => _api.GetIndexTranslation(indexBase64));

        Assert.Equal(nameof(indexBase64), exception.ParamName);
        _mockApi.Verify(x => x.GetIndexTranslation(indexBase64), Times.Once);
    }

    [Fact]
    public async Task GetIndexTranslation_WithInvalidBase64_ShouldThrowException()
    {
        // Arrange
        var invalidBase64 = "invalid-base64-string";

        _mockApi
            .Setup(x => x.GetIndexTranslation(invalidBase64))
            .ThrowsAsync(new FormatException("Invalid base64 string"));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<FormatException>(
            () => _api.GetIndexTranslation(invalidBase64));

        Assert.Equal("Invalid base64 string", exception.Message);
        _mockApi.Verify(x => x.GetIndexTranslation(invalidBase64), Times.Once);
    }

    [Fact]
    public async Task GetIndexTranslation_WithHttpError_ShouldThrowException()
    {
        // Arrange
        var indexBase64 = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes("test-index"));

        _mockApi
            .Setup(x => x.GetIndexTranslation(indexBase64))
            .ThrowsAsync(new HttpRequestException("HTTP request failed", null, HttpStatusCode.NotFound));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<HttpRequestException>(
            () => _api.GetIndexTranslation(indexBase64));

        Assert.Equal("HTTP request failed", exception.Message);
        Assert.Equal(HttpStatusCode.NotFound, exception.StatusCode);
        _mockApi.Verify(x => x.GetIndexTranslation(indexBase64), Times.Once);
    }

    [Fact]
    public async Task GetIndexTranslation_WithTimeout_ShouldThrowException()
    {
        // Arrange
        var indexBase64 = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes("test-index"));

        _mockApi
            .Setup(x => x.GetIndexTranslation(indexBase64))
            .ThrowsAsync(new TaskCanceledException("Request timeout"));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<TaskCanceledException>(
            () => _api.GetIndexTranslation(indexBase64));

        Assert.Equal("Request timeout", exception.Message);
        _mockApi.Verify(x => x.GetIndexTranslation(indexBase64), Times.Once);
    }

    [Theory]
    [InlineData("test1", "TEST1")]
    [InlineData("test2", "TEST2")]
    [InlineData("test3", "TEST3")]
    public async Task GetIndexTranslation_WithDifferentIndexes_ShouldReturnCorrectTranslations(string index, string expectedAcronym)
    {
        // Arrange
        var indexBase64 = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(index));
        var expectedTranslation = new MessaIndexTranslation
        {
            Id = 5,
            eMessaIndex = index,
            Akronim = expectedAcronym
        };

        _mockApi
            .Setup(x => x.GetIndexTranslation(indexBase64))
            .ReturnsAsync(expectedTranslation);

        // Act
        var result = await _api.GetIndexTranslation(indexBase64);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(expectedTranslation.Id, result.Id);
        Assert.Equal(expectedTranslation.eMessaIndex, result.eMessaIndex);
        Assert.Equal(expectedTranslation.Akronim, result.Akronim);

        _mockApi.Verify(x => x.GetIndexTranslation(indexBase64), Times.Once);
    }

    [Fact]
    public async Task GetIndexTranslation_VerifyApiContract_ShouldMatchInterface()
    {
        // Arrange
        var indexBase64 = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes("contract-test"));
        var expectedTranslation = new MessaIndexTranslation
        {
            Id = 6,
            eMessaIndex = "contract-test",
            Akronim = "CONTRACT"
        };

        _mockApi
            .Setup(x => x.GetIndexTranslation(indexBase64))
            .ReturnsAsync(expectedTranslation);

        // Act
        var result = await _api.GetIndexTranslation(indexBase64);

        // Assert
        Assert.NotNull(result);
        Assert.IsType<MessaIndexTranslation>(result);
        Assert.True(result.Id > 0);
        Assert.NotNull(result.eMessaIndex);
        Assert.NotNull(result.Akronim);

        _mockApi.Verify(x => x.GetIndexTranslation(indexBase64), Times.Once);
    }
} 