using MessaApi.IndexTranslations;
using Microsoft.Extensions.DependencyInjection;
using Refit;

namespace MessaApi.Tests.IndexTranslations;

/// <summary>
/// Testy integracyjne dla IMessaIndexTranslatorApi
/// Uwaga: Te testy wymagają uruchomionego serwera API
/// </summary>
public class IMessaIndexTranslatorApiIntegrationTests : IDisposable
{
    private readonly IMessaApiClient _messaApiClient;
    private readonly IMessaIndexTranslatorApi _indexTranslatorApi;
    private readonly ServiceProvider _serviceProvider;

    public IMessaIndexTranslatorApiIntegrationTests()
    {
        var services = new ServiceCollection();

        // Konfiguracja dla testów integracyjnych
        // Możesz zmienić URL na rzeczywisty adres API
        var baseApiAddress = "http://*********:8009/api";// Environment.GetEnvironmentVariable("MESSA_API_BASE_URL") ?? "http://localhost:5000/api";

        MessaApi.MessaApiRegisterer.Register(services, baseApiAddress);

        //services.AddRefitClient<IMessaIndexTranslatorApi>()
        //    .ConfigureHttpClient(c => 
        //    {
        //        c.BaseAddress = new Uri(baseApiAddress);
        //        c.Timeout = TimeSpan.FromSeconds(30);
        //    });

        _serviceProvider = services.BuildServiceProvider();

        _indexTranslatorApi = _serviceProvider.GetRequiredService<IMessaIndexTranslatorApi>();
        _messaApiClient = _serviceProvider.GetRequiredService<IMessaApiClient>();
    }

    [Fact]//(Skip = "Wymaga uruchomionego serwera API")]
    public async Task GetIndexTranslation_WithRealApi_ShouldReturnTranslation()
    {
        //BESKID-POL-1002-350-0,5-T-T-35	BDACH"E"35 1002 +K

        // Arrange
        var indexBase64 = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes("BESKID-POL-1002-350-0,5-T-T-35"));


        // Act
        var result = await _messaApiClient.IndexTranslator.GetIndexTranslation(indexBase64);
        //var result = await _indexTranslatorApi.GetIndexTranslation(indexBase64);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Id > 0);
        Assert.NotNull(result.eMessaIndex);
        Assert.NotNull(result.Akronim);
        Assert.Equal("BDACH\"E\"35 1002 +K", result.Akronim);
    }

    [Fact(Skip = "Wymaga uruchomionego serwera API")]
    public async Task GetIndexTranslation_WithEmptyIndex_ShouldReturnTranslation()
    {
        // Arrange
        var indexBase64 = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(""));

        // Act
        var result = await _indexTranslatorApi.GetIndexTranslation(indexBase64);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Id > 0);
        Assert.NotNull(result.eMessaIndex);
        Assert.NotNull(result.Akronim);
    }

    [Fact(Skip = "Wymaga uruchomionego serwera API")]
    public async Task GetIndexTranslation_WithSpecialCharacters_ShouldReturnTranslation()
    {
        // Arrange
        var specialIndex = "test-index-ąćęłńóśźż";
        var indexBase64 = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(specialIndex));

        // Act
        var result = await _indexTranslatorApi.GetIndexTranslation(indexBase64);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Id > 0);
        Assert.NotNull(result.eMessaIndex);
        Assert.NotNull(result.Akronim);
    }

    [Theory(Skip = "Wymaga uruchomionego serwera API")]
    [InlineData("test1")]
    [InlineData("test2")]
    [InlineData("test3")]
    public async Task GetIndexTranslation_WithDifferentIndexes_ShouldReturnCorrectTranslations(string index)
    {
        // Arrange
        var indexBase64 = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(index));

        // Act
        var result = await _indexTranslatorApi.GetIndexTranslation(indexBase64);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Id > 0);
        Assert.NotNull(result.eMessaIndex);
        Assert.NotNull(result.Akronim);
    }

    [Fact(Skip = "Wymaga uruchomionego serwera API")]
    public async Task GetIndexTranslation_WithInvalidBase64_ShouldThrowException()
    {
        // Arrange
        var invalidBase64 = "invalid-base64-string";

        // Act & Assert
        await Assert.ThrowsAsync<HttpRequestException>(
            () => _indexTranslatorApi.GetIndexTranslation(invalidBase64));
    }

    [Fact(Skip = "Wymaga uruchomionego serwera API")]
    public async Task GetIndexTranslation_WithNonExistentIndex_ShouldThrowException()
    {
        // Arrange
        var nonExistentIndex = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes("non-existent-index"));

        // Act & Assert
        await Assert.ThrowsAsync<HttpRequestException>(
            () => _indexTranslatorApi.GetIndexTranslation(nonExistentIndex));
    }

    [Fact(Skip = "Wymaga uruchomionego serwera API")]
    public async Task GetIndexTranslation_VerifyApiResponseStructure_ShouldMatchExpectedFormat()
    {
        // Arrange
        var indexBase64 = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes("structure-test"));

        // Act
        var result = await _indexTranslatorApi.GetIndexTranslation(indexBase64);

        // Assert
        Assert.NotNull(result);
        Assert.IsType<MessaIndexTranslation>(result);
        Assert.True(result.Id > 0);
        Assert.NotNull(result.eMessaIndex);
        Assert.NotNull(result.Akronim);
        
        // Sprawdzenie, czy wszystkie wymagane pola są wypełnione
        Assert.NotEmpty(result.eMessaIndex);
        Assert.NotEmpty(result.Akronim);
    }

    [Fact(Skip = "Wymaga uruchomionego serwera API")]
    public async Task GetIndexTranslation_VerifyApiPerformance_ShouldCompleteWithinReasonableTime()
    {
        // Arrange
        var indexBase64 = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes("performance-test"));
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act
        var result = await _indexTranslatorApi.GetIndexTranslation(indexBase64);
        stopwatch.Stop();

        // Assert
        Assert.NotNull(result);
        Assert.True(stopwatch.ElapsedMilliseconds < 5000, 
            $"API call took {stopwatch.ElapsedMilliseconds}ms, which is longer than expected 5 seconds");
    }

    public void Dispose()
    {
        _serviceProvider?.Dispose();
    }
} 