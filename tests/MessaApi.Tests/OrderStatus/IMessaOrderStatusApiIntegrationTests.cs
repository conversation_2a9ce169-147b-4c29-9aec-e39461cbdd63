using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MessaApi.OrderStatus;
using Microsoft.Extensions.DependencyInjection;
using Refit;
using Xunit;

namespace MessaApi.Tests.MessaOrderStatus;

/// <summary>
/// Integration tests for IMessaOrderStatusApi
/// Note: These tests require a running API server
/// </summary>
public class IMessaOrderStatusApiIntegrationTests : IDisposable
{
    private readonly IMessaApiClient _messaApiClient;
    private readonly IMessaOrderStatusApi _orderStatusApi;
    private readonly ServiceProvider _serviceProvider;

    public IMessaOrderStatusApiIntegrationTests()
    {
        var services = new ServiceCollection();
        // Change the URL to your actual API address if needed
        var baseApiAddress = "http://**************:82/api";// "http://*********:8009/api";
        MessaApi.MessaApiRegisterer.Register(services, baseApiAddress);
        _serviceProvider = services.BuildServiceProvider();
        _orderStatusApi = _serviceProvider.GetRequiredService<IMessaOrderStatusApi>();
        _messaApiClient = _serviceProvider.GetRequiredService<IMessaApiClient>();
    }

    [Fact(Skip = "Requires running API server and valid data")]
    public async Task GetByEMessaId_WithValidId_ShouldReturnOrderStatus()
    {
        // Arrange
        int validEMessaId = 308794; // Provide a valid eMessaId from your test data
        // Act
        MessaOrderStatusDTO result = await _messaApiClient.OrderStatus.GetByEMessaId(validEMessaId);
        // Assert
        Assert.NotNull(result);
        //Assert.Equal(validEMessaId, result. eme MessaNo); // or result.EMessaNo if mapping
    }

    [Fact(Skip = "Requires running API server and valid data")]
    public async Task GetByEMessaNo_WithValidNo_ShouldReturnOrderStatus()
    {
        // Arrange
        string emessaNo = "2325/2022"; // Provide a valid order no
        string emessaNoBase64 = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(emessaNo));
        // Act
        var result = await _orderStatusApi.GetByEMessaNo(emessaNoBase64);
        // Assert
        Assert.NotNull(result);
        Assert.Equal(emessaNo, result.EMessaNo);
    }

    [Fact(Skip = "Requires running API server and valid data")]
    public async Task GetByMessaId_WithValidId_ShouldReturnOrderStatus()
    {
        // Arrange
        long validMessaId = 605485; // Provide a valid messaId from your test data
        // Act
        var result = await _orderStatusApi.GetByMessaId(validMessaId);
        // Assert
        Assert.NotNull(result);
        Assert.Equal(validMessaId, result.MessaId);
    }

    [Fact(Skip = "Requires running API server and valid data")]
    public async Task GetByMessaNo_WithValidNo_ShouldReturnOrderStatus()
    {
        // Arrange
        int validMessaNo = 1677221; // Provide a valid messaNo from your test data
        // Act
        var result = await _orderStatusApi.GetByMessaNo(validMessaNo);
        // Assert
        Assert.NotNull(result);
        Assert.Equal(validMessaNo, result.MessaNo);
    }

    [Fact(Skip = "Requires running API server and valid data")]
    public async Task GetByEMessaNo_WithInvalidEMessaNoEncoding_ShouldThrowException()
    {
        // Arrange
        string emessaNo = "2325-2022"; // Provide a valid order no
        string emessaNoBase64 = emessaNo;// Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(emessaNo));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<Refit.ApiException>(
           () => _orderStatusApi.GetByEMessaNo(emessaNoBase64)
        );

        Assert.Contains("Niepoprawny format eMessaNo encoded base64", exception.Content);
    }

    public void Dispose()
    {
        _serviceProvider?.Dispose();
    }
}